// export function validatorIDCard(idcode) {
//   if (typeof idcode !== 'string') {
//     return false
//   }
//   const idcard_patter = /^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/;
//   // 判断格式是否正确
//   const format = idcard_patter.test(idcode);
//   if (!format) {
//     return false
//   }
//   // 加权因子
//   const weight_factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
//   // 校验码
//   const check_code = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
//   const last = idcode[17];//最后一位
//   const seventeen = idcode.substring(0, 17);
//   // ISO 7064:1983.MOD 11-2
//   // 判断最后一位校验码是否正确
//   const arr = seventeen.split("");
//   const len = arr.length;
//   let num = 0;
//   for (let i = 0; i < len; i++) {
//     num += arr[i] * weight_factor[i];
//   }
//   // 获取余数
//   const resisue = num % 11;
//   const last_no = check_code[resisue];
//   // 返回验证结果，校验码和格式同时正确才算是合法的身份证号码
//   const result = last === last_no ? true : false;
//   // return {
//   //   code: result ? 1 : -1,
//   //   msg: !result ? "身份证号码格式错误" : ""
//   // }
//   return result
// }
// 校验身份证号码
export function validateCNIDCard(id) {
  //15位和18位身份证号码的正则表达式
  const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  return reg.test(id);
}
// 校验香港|澳门身份证号
function validateHKMCIDCard(id) {
  return /^([A-Z]\d{6,10}(\(\w{1}\))?)$/.test(id)
}

// 校验台湾身份证号
function validateTWIDCard(id) {
  return /^\d{8}|^[a-zA-Z0-9]{10}|^\d{18}$/.test(id);
}
//校验澳门身份证号码
function validateMCCard(id) {
  return /^[1|5|7][0-9]{6}\([0-9Aa]\)$/.test(id);
}
//校验身份证号码
export function validateIDCard(id) {
  //15位和18位身份证号码的正则表达式
  // const len = id.length;
  // if (len === 15 || len === 18) {
  //   return  validateIDCard(id);
  // }
  // switch (len) {
  //   case 15:
  //   case 18:
  //     return validateIDCard(id);
  //   case 8:
  //     return validateHKIDCard(id)||validateMCIDCard(id);
  //   case 9:
  //     return validateTWIDCard(id);
  //   case 10:
  //     return validateMCIDCard(id);
  // }
  // const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
  // return reg.test(id);
  return validateCNIDCard(id) || validateHKMCIDCard(id) || validateTWIDCard(id)||validateMCCard(id);
}
