/**
 * 请求接口
 * <AUTHOR>
 */
import { Message } from 'view-design';
import moment from 'moment';
import axios from 'axios';
import global_config from '@/config';
import S from '@/libs/util';
// import { ulid } from 'ulid'
import {
  getClinicid,
  getUid,
  getAuthId,
  getAuthSeq,
  authExpiredTime,
  prolongCookie,
  logout,
  getUlid,
  getAssetAppKey,
} from '@/libs/runtime';
import store from '@/store';
import router from '../router';
// import Cookies from 'js-cookie';
// 创建 axios 实例
export const createInstance = (options = { headers: {} }) => {
  const instance = axios.create({
    baseURL: options.baseURL || global_config.ApiDomain,
    timeout: 30000,
    headers: { 'content-type': 'application/x-www-form-urlencoded', 'X-SIGN-VERSION': '2', ...options.headers },
  });
  const EXCLUDE_PRE_URLS = ['/services/behavior.replay.save'];

function changeParams(config, params) {
  Object.keys(params).forEach(item => {
    if (typeof params[item] == 'object' && params[item] !== null) {
      params[item] = JSON.stringify(params[item]);
    }
    if (typeof params[item] == 'undefined' || params[item] === null) {
      params[item] = '';
    }
  });
  config.params = params;
  config.params['_nstr'] = S.getRequestUniqKey(16);
  config.params['ifsign'] = S.makeApiSign(config.params);
  config.data = undefined;
}

  // 获取剩余有效时间
  function getSignKeyType(config) {
    if (isMTRequest(config.url)) {
      return 'assets';
    }
    return 'default';
  }

  function isMTRequest(url) {
    return url.startsWith('/asset-center');
  }

  async function proxyRequest(config) {
    if (isMTRequest(config.url)) {
      config.baseURL = global_config.AssetsApiDomain;
      config.headers['X-Env'] = JSON.stringify({ app_key: getAssetAppKey() });
      config.headers['content-type'] = 'application/json;charset=UTF-8';
      getUlid() && (config.headers['X-Device-Id'] = getUlid());
    }
  }

  // 请求拦截器
  instance.interceptors.request.use(
    config => {
      // console.log(config,"请求拦截器");
      // 设置请求签名参数
      // 并重新设置请求的参数
      // LoadingBar.start()
      // if (config.url.startsWith('/clinic/yztpay') || config.url.startsWith('/clinic/setting.authentication')) {
      //   config.headers.sys_code = 'RSJYZT';
      // }

    // 没有auth_id,才刷新时间
    if (getAuthId() == '' && !EXCLUDE_PRE_URLS.includes(config.url)) {
      const preHours = localStorage.getItem('is_jj') ? 1 / 12 : 24;
      prolongCookie(preHours, preHours);
    }
    const isGoReq = isMTRequest(config.url);
    proxyRequest(config);
    if (config.method == 'get') {
      let params = config.params || config.data || {};
      params._ts = moment().unix();

      if (!isGoReq) {
        changeParams(config, params);
      } else {
        params.ts = moment().unix();
        config.params = {
          ...params,
          ifsign: S.makeApiSign(params, config, getSignKeyType(config)),
        };
        config.data = undefined;
      }
    } else {
      const blockList = ['/services/behavior.replay.save'];
      if (config.method === 'post' && !blockList.includes(config.url)) {
        store.commit('app/CHANGE_FRESH_STATUS', true);
      }

      if (!isGoReq) {
        let data = config.data || config.params || {};
        // config.params = {
        //   _ts: moment().unix(),
        // };
        Object.keys(data).forEach(item => {
          if (typeof data[item] == 'object') {
            data[item] = JSON.stringify(data[item]);
          }
          if (typeof data[item] == 'undefined') {
            data[item] = '';
          }
        });
        data._ts = moment().unix();
        data['_nstr'] = S.getRequestUniqKey(16);
        data['ifsign'] = S.makeApiSign(data, config, getSignKeyType(config));
        const params = new URLSearchParams();
        for (let k in data) params.append(k, data[k]);
        config.data = params;
        // config.params = undefined;
      } else {
        config.data.ts = moment().unix();
        let data = config.data || config.params || {};
        config.params = {
          ifsign: S.makeApiSign(data, config, getSignKeyType(config)),
        };
      }
    }

      // 环境参数
      let spm = '';
      let uid = getUid();
      let clinicid = getClinicid();
      let auth_id = getAuthId();
      // console.log("-> %c auth_id  === %o", "font-size: 15px;color: green;", auth_id)
      let auth_seq = getAuthSeq();
      let auth_expired_time = authExpiredTime();
      let evnParams = {
        timezone: 8,
        resolution: document.body.clientWidth + '*' + document.body.clientHeight,
        channel: 'h5',
        os: 'h5',
        device_id: '',
        uidentity: uid,
        clinic_id: clinicid,
        spm: spm,
        app_version: global_config.codeVersion,
        ts: Date.now(),
      };
      /**
       * @description: auth_id,auth_seq,auth_expired_time有值就加到环境参数中
       * */
      auth_id && (evnParams.auth_id = auth_id);
      auth_seq && (evnParams.auth_seq = auth_seq);
      auth_expired_time && (evnParams.auth_expired_time = auth_expired_time);

      config.headers['IFENV'] = JSON.stringify(evnParams);
      return config;
    },
    error => {
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  instance.interceptors.response.use(
    response => {
      // console.log(response,"响应拦截器");
      const res = response.data;
      // LoadingBar.finish()
      if (res.errcode == '4007') {
        // Message.error(res.errmsg)
        logout();
        router.replace({
          path: '/login',
          query: {
            from: encodeURIComponent(router.currentRoute.fullPath),
          },
        });
      } else {
        if (res.errcode != 0 && res.code != 0) {
          // 登录超时
          Message.error(res.errmsg || res.message);
          return Promise.reject(res || 'Error');
          // return res
        }
      }

      let last_version = response.headers['x-version'];
      if (!S.isServeRun && last_version != undefined && last_version != '') {
        store.dispatch('version/checkVersion', last_version).then();
      }

      return res.data;
    },
    error => {
      return Promise.reject({
        errmsg: String(error),
      });
    }
  );
  return instance;
};

export default createInstance();

export const assistantInstance = axios.create({
  baseURL: global_config.AssistantApiDomain,
  timeout: 30000,
});

assistantInstance.interceptors.response.use(response => {
  return response.data?.data;
});
