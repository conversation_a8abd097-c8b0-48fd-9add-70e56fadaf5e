import Cookies from 'js-cookie';
import S from '@/libs/util';
import socket from '@/plugins/socketio';

const LoginCookieName = 'BACKEND_PASSPORT_V001';

import { debounce } from 'lodash';
import config from '@/config';
import { log } from 'mathjs';

const { storageNamePrefix, hisStorageNamePrefix, socketStorageNamePrefix } = config;
export const isLogin = () => {
  const hisInfo = getHisLoginInfo();
  // console.log('%c=>(runtime.js:8) hisInfo', 'font-size: 18px;color: #FF7043 ;', hisInfo);
  let uid = getUid();
  let hisUid = hisInfo.uid;
  return uid > 0 && hisUid > 0;
};

export const getUid = () => {
  let user = getUser();
  return Number(user.uid) || 0;
};

export const getUname = () => {
  let user = getUser();
  return user.name;
};

export const getClinicid = () => {
  let user = getUser();
  return user.clinicid || '';
};

export const getUser = () => {
  return getLoginInfo();
};

// 是否是数字化诊所
export const isRstClinic = () => {
  let user = getUser();
  return user.is_rst === '1';
};
// 是否是数字化诊所
export const isOpcClinic = () => {
  let user = getUser();
  return user.is_opc === '1';
};
// 是否是榕树堂数字化诊所
export const isRstOpcClinic = () => {
  let user = getUser();
  return user.is_rst_opc === '1';
};
// 是否是普通诊所转数字化诊所
export const isClinicToOpc = () => {
  let user = getUser();
  return user.transform_type_flag === 'CLINIC_TO_RST';
};
// 是否已登录，针对踢下线功能上线时，未登录过的用户
export const isLogined = () => {
  let user = getUser();
  return user.isLogin || user.isLogin === 'true';
};

// 是否开通聚合支付
export const isOpenjhPay = () => {
  let user = getUser();
  return user.pay_platform_ap_status === '1';
};

export const getAuthId = () => {
  let user = getUser();
  return user.auth_id || '';
};

export const getAuthSeq = () => {
  let user = getUser();
  return user.auth_seq || '';
};

export const authExpiredTime = () => {
  let user = getUser();
  return user.auth_expired_time || '';
};

// 获取当前诊所是否开通医保：1：开通中，2：已开通，3：未开通
export const getInsureStatus = () => {
  let user = getUser();
  return user.insure_status === '2';
};
export const getRootDomain = () => {
  let host = window.location.hostname;
  let domain = host.replace(/(.*\.)?([^.]+\.[^.]+)$/, '$2');
  return domain;
};
export const getLoginInfo = () => {
  let info = Cookies.get(S.generateStorageKey(storageNamePrefix)) || '{}';
  return JSON.parse(info);
};
export const getHisLoginInfo = () => {
  let info = Cookies.get(S.generateStorageKey(hisStorageNamePrefix)) || '{}';
  return JSON.parse(info);
};
export const getClinicName = () => {
  let user = getUser();
  return user.clinic_name;
};

export const writeLoginCookie = info => {
  //
  function isDate(val) {
    var d = new Date(val);
    return !isNaN(d.valueOf());
  }

  console.log(isDate(1));
  //
  let expires = info.expires;
  if (expires && typeof expires !== 'number') {
    if (isDate(expires)) {
      expires = new Date(expires);
    }
  }
  const options = {
    expires: expires || 0,
  };
  !isDevMode() && (options.domain = '.rsjxx.com');
  Cookies.set(S.generateStorageKey(storageNamePrefix), JSON.stringify(info), options);
  return true;
};

export const writeHisLoginCookie = info => {
  function isDate(val) {
    var d = new Date(val);
    return !isNaN(d.valueOf());
  }

  let expires = info.expires;

  if (expires && typeof expires !== 'number') {
    if (isDate(expires)) {
      expires = new Date(expires);
    }
  }
  const options = {
    expires: expires || 0,
  };
  !isDevMode() && (options.domain = '.rsjxx.com');
  Cookies.set(S.generateStorageKey(hisStorageNamePrefix), JSON.stringify(info), options);
  return true;
};

export const getEnv = () => {
  return process.env.VUE_APP_NODE_ENV;
};
export const isDevMode = () => {
  return process.env.VUE_APP_CMD === 'serve';
};
/**
 * @method prolongCookie
 * @param {number} timeDuration - 延长的时间  单位：分钟
 * @param {number} minimumPeriod - 有效期低于此时间才延长  单位：小时
 * @description: 延长 cookie 的有效期
 * @author: yangyi
 * @date: 2023/12/23
 */
export const prolongCookie = debounce(
  function (timeDuration = 24, minimumPeriodHours = 24) {
    return new Promise((resolve, reject) => {
      if (!isLogin()) {
        resolve(false);
        return;
      }
      try {
        let info = getLoginInfo();
        let hisInfo = getHisLoginInfo();
        // 获取过期时间的时间戳
        let time_stamp = info.time_stamp;
        // 获取当前时间戳
        let current_time_stamp = Math.floor(new Date().getTime() / 1000);
        // 获取剩余时间
        let surplus_time = Number(time_stamp) - Number(current_time_stamp);
        let effective_section_time = minimumPeriodHours * 60 * 60;
        let add_time_stamp = timeDuration * 60 * 60;
        if (0 < surplus_time && surplus_time <= effective_section_time) {
          // 说明有效时间小于两个小时了,此时延长cookie有效期
          info.time_stamp = info.time_stamp + add_time_stamp;
          hisInfo.time_stamp = info.time_stamp;

          const newExpiresTime = new Date(new Date(time_stamp * 1000).getTime() + add_time_stamp * 1000);
          info.expires = newExpiresTime;
          hisInfo.expires = newExpiresTime;
          writeHisLoginCookie(hisInfo);
          writeLoginCookie(info);
          resolve(true);
        }
      } catch (e) {
        reject(false);
      }
    });
  },
  1000,
  {
    leading: true,
    trailing: true,
  }
);
// 是否是直营诊所
export const isDirectClinic = () => {
  return getLoginInfo().is_direct === '1';
};
// 是否展示互医菜单
export const isEnableHYMenu = () => {
  return getLoginInfo().hy_menu_status === '1';
};

export const canUsePackageProd = () => {
  return getLoginInfo().use_prod_package === '1';
};

// 是否是己善渠道诊所
export const isJiShanChannel = () => {
  return getLoginInfo().is_jishan_channel === '1';
};
export const logout = () => {
  const options = {};
  !isDevMode() && (options.domain = '.rsjxx.com');
  Cookies.remove(S.generateStorageKey(storageNamePrefix), options);
  Cookies.remove(S.generateStorageKey(storageNamePrefix));

  Cookies.remove(S.generateStorageKey(hisStorageNamePrefix), options);
  Cookies.remove(S.generateStorageKey(hisStorageNamePrefix));
  localStorage.removeItem(S.generateStorageKey(storageNamePrefix));
  socket.socketManager.handleLogout();
  removeSocketInfo();
  try {
    sessionStorage.clear();
  } catch (e) {
    console.log(e);
  }
  return true;
};

export const writeSocketInfo = info => {
  localStorage.setItem(S.generateStorageKey(socketStorageNamePrefix), JSON.stringify(info));
};

export const getSocketInfo = () => {
  let info = localStorage.getItem(S.generateStorageKey(socketStorageNamePrefix)) || '{}';
  return JSON.parse(info);
};
export const removeSocketInfo = () => {
  localStorage.removeItem(S.generateStorageKey(socketStorageNamePrefix));
};
export const getUlid = () => {
  return Cookies.get('_sj_ulid') || '';
};
export const getAssetAppKey = () => {
  return getLoginInfo().asset_app_key;
};

// 获取理疗师/技师名称（榕树堂展示技师，其他展示理疗师）现统一叫理疗师
export const getPhysioName = () => {
  let user = getUser();
  return user.is_rst === '1' ? '理疗师' : '理疗师';
};

export const showAgent = () => {
  return localStorage.getItem('show_agent') === '1';
};
