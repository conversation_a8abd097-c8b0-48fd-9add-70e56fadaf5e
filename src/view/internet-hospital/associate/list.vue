<template>
  <div class="list-wrapper">
    <div v-if="is_enter === '1'">
      <standard-table
        :loading="tableLoading"
        :columns="tableCols"
        :data="list"
        :total="total"
        :page-size.sync="queryFormData.pageSize"
        :current.sync="queryFormData.page"
        @on-change="onPageChange"
      >
        <template #header>
          <Table class="option-table" :loading="tableLoading" :columns="optionTableCols" :data="optionList">
            <template slot-scope="{ row }" slot="face_url">
              <a v-if="row.invite_code" @click="seeInviteModal('面对面邀请', 'QrCode', row.invite_code)">查看二维码</a>
              <span v-else>-</span>
            </template>

            <template slot-scope="{ row }" slot="invite_code">
              <a v-if="row.invite_code" @click="seeInviteModal('邀请码邀请', 'invite_code', row.invite_code)">查看邀请码</a>
              <span v-else>-</span>
            </template>

            <template slot-scope="{ row }" slot="invite_h5_url">
              <a v-if="row.invite_h5_url" @click="seeInviteModal('链接邀请', 'invite_h5_url', row.invite_h5_url)">
                查看复制链接
              </a>
              <span v-else>-</span>
            </template>

            <template slot-scope="{ row }" slot="store_url">
              <a v-if="row.invite_qr?.qr_img" @click="seeInviteModal('门店小程序二维码', 'store_url', row.invite_qr?.qr_img)">查看二维码</a>
              <span v-else>-</span>
            </template>
          </Table>
          <Form
            inline
            :label-width="0"
            @submit.native.prevent
            @keyup.enter.native="onSearch"
            class="flex flex-item-between"
          >
            <Row :gutter="24">
              <Col span="24">
                <FormItem label="">
                  <Select v-model="queryFormData.source" placeholder="请选择邀请方式">
                    <Option
                      v-for="(source_item, source_index) in sourceDesc"
                      :key="source_index + 'user'"
                      :value="source_item.id"
                    >
                      {{ source_item.desc }}
                    </Option>
                  </Select>
                </FormItem>

                <FormItem label="">
                  <Input v-model.trim="queryFormData.mobile" placeholder="手机号" />
                </FormItem>

                <FormItem>
                  <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                  <span class="list-reset-btn" @click="onResetSearch">
                    <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                    <span>清除条件</span>
                  </span>
                </FormItem>
              </Col>
            </Row>
          </Form>
        </template>
        <template slot-scope="{ row }" slot="register_time">
          <div>{{ row.register_time | data_format('YYYY-MM-DD HH:mm:ss') }}</div>
        </template>

        <template slot-scope="{ row }" slot="invite_type">
          <div>{{ row.invite_type === '1' ? row.doctor_name : row.clinic_name }}</div>
        </template>
      </standard-table>
      <invite-modal v-model="inviteModalVisible" :inviteInfo="inviteInfo"></invite-modal>
    </div>

    <el-empty description="当前诊所尚未入驻" v-else></el-empty>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import search from '@/mixins/search'; // Http request
import inviteModal from './components/inviteModal.vue';
import renderHeader from '@/mixins/renderHeader';
import StandardTable from "@/components/StandardTable/index.vue";
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  source: '',
  mobile: '',
  r: '',
};

export default {
  name: 'list',
  components: {
    StandardTable,
    inviteModal,
  },
  mixins: [search, renderHeader],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getInviteList',
      sourceDesc: [], // 邀请方式枚举

      tableCols: [
        { title: '邀请方式', key: 'source_text' },
        { title: '手机号', key: 'mobile' },
        { title: '注册时间', slot: 'register_time' },
        { title: '邀请人', slot: 'invite_type' },
      ],
      tableLoading: false,
      list: [],
      total: 0,

      optionTableCols: [
        { title: '邀请方式', key: 'source_text' },
        { title: '面对面邀请', slot: 'face_url' },
        { title: '邀请码邀请', slot: 'invite_code' },
        { title: '链接邀请', slot: 'invite_h5_url' },
        { title: '门店小程序二维码', slot: 'store_url', renderHeader: (h, params) => this._renderHeader(h, params, '用户扫码后可以直接进入诊所门店') },
      ],
      optionList: [],

      inviteModalVisible: false,
      inviteInfo: {},
      is_enter: '2', // 1:入驻，2:未入驻
    };
  },

  created() {
    this.getInviteOption();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  methods: {
    seeInviteModal(title = '', type = '', text = '') {
      this.inviteInfo = {
        title,
        type,
        text,
      };
      this.inviteModalVisible = true;
    },
    handlerListData(data) {
      this.optionList = [
        {
          source_text: '邀请方式',
          face_url: data.invite_code,
          invite_code: data.invite_code,
          invite_h5_url: data.invite_h5_url,
          invite_qr: data.invite_qr
        },
      ];
      this.is_enter = data.is_enter;
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    // api-枚举选项
    getInviteOption() {
      this.$api.getInviteOption().then(res => {
        this.sourceDesc = S.descToArrHandle(res.sourceDesc);
      });
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
};
</script>

<style lang="less">
.option-table {
  margin-bottom: 40px;
}
p {
  margin: 0;
}
.ivu-tooltip-transfer {
  min-width: 200px;
}
</style>
