<template>
  <div class="audit-wrapper">
    <!--订单基础信息-->
    <order-base-info :consignee-info="consigneeInfo" :order-info="orderInfo" />
    <!--物流信息-->
    <logistics-info
      v-if="orderInfo.shipping_method !=2 && !(this.logistic_status === 'W_SHIP' && this.auditStatus === 'A_PASS')"
      @addExpress="addExpress"
      @updateLogistics="updateLogistics"
      :order-id="order_id"
      :logistic-status="logistic_status"
      :audit-status="auditStatus"
      :express-info="expressInfo"
    />
    <!--    用户基础信息-->
    <user-base-info :user-info="userInfo" :patient-info="patientInfo" />
    <div class="pres-info" style='margin-bottom: 20px;'>
      <prescription-info :pres_id="pres_id" :pres_code="pres_code" ref="info" :belong_type="belong_type" :pres_record_list="pres_record_list"/>
    </div>
    <dispensing-details :order-id="order_id" :logistic-status="logistic_status" />
    <div class="record-box">
      <audit-record :recordList="auditRecords" />
    </div>

    <!-- </div> -->
    <div class="fixed-bottom-wrapper">
      <back-button class="mr10"></back-button>
      <Button type="primary" @click="print('deliveryNote')">打印随货单</Button>
      <Button type="primary" class="ml10" @click="print('prescription')">打印处方笺</Button>
      <Button type="primary" class="ml10" @click="print('records')">打印电子病历</Button>
      <Button type="primary" class="ml10" @click="printDoseLabel">打印中药制剂标贴</Button>
    </div>
    <create-logistics v-model="addExpressVisible" @createLogistics="handleDelivery"></create-logistics>
    <div v-show="false">
      <print-pages
        :pres_id="pres_id"
        :pres_code="pres_code"
        ref="printPages"
        :printCheckbox="printCheckbox"
      ></print-pages>
    </div>

    <chinese-medicine-modal v-model="chineseMedicineVisible" :medicineInfo="medicineInfo"></chinese-medicine-modal>

    <create-delivery-modal
      v-model="deliveryVisible"
      :order-id="order_id"
      :order-code="order_code"
      :pres-id="pres_id"
      :is_update_logistics="true"
      :echo-info="ship_info"
      @refreshList="updateSuccess"
    ></create-delivery-modal>
  </div>
</template>
<script>
import cloneDeep from 'lodash.clonedeep'
import CreateLogistics from './components/CreateLogistics.vue';
import AuditRecord from '../components/AuditRecord.vue';
import PrescriptionInfo from '../components/PrescriptionInfo/index.vue';
import OrderBaseInfo from '../components/OrderBaseInfo.vue';
import UserBaseInfo from '../components/UserBaseInfo.vue';
import LogisticsInfo from './components/LogisticsInfo.vue';
import DispensingDetails from './components/DispensingDetails.vue';
import PrintPages from '../components/PrintPages/index.vue';
import createDeliveryModal from './components/CreateDeliveryModal.vue';
import ChineseMedicineModal from './components/chineseMedicineModal.vue';
import { $operator } from '../../../libs/operation';
const init_query_form_data = {
  name: '',
};
export default {
  name: 'OrderDetail',
  components: {
    DispensingDetails,
    UserBaseInfo,
    CreateLogistics,
    PrescriptionInfo,
    OrderBaseInfo,
    AuditRecord,
    LogisticsInfo,
    PrintPages,
    ChineseMedicineModal,
    createDeliveryModal
  },
  mixins: [],
  props: {},
  data() {
    return {
      tab: 'pharInfo',
      queryFormData: { ...init_query_form_data },
      presOptionList: [
        { label: '处方笺', value: 'wait' },
        { label: '电子病历', value: 'none' },
      ],
      activeName: '1',
      dispensingDrugs: [],
      originDrugs: [],
      auditRecords: [],
      order_code: '',
      order_id: '',
      pres_id: '',
      orderInfo: {
        order_code: '', //订单编号
        order_status: '', //订单状态
        info_status_text: '', //订单状态文本
        order_type: '', //订单类型
        pay_type_text: '', //支付方式文本
      },
      consigneeInfo: {
        name: '', //收货人姓名
        mobile: '', //收货人电话
        address: '', //收货人地址
      },
      patientInfo: {
        name: '', //患者姓名
        mobile: '', //患者电话
        age: '', //患者地址
        gender_text: '', //患者性别文本
        id_card: '', //患者身份证号
      },
      userInfo: {
        name: '',
        nick_name: '',
        uid: '',
        mobile: '',
      },
      presInfo: {
        pres_code: '',
      },
      auditStatus: '',
      logistic_status: '',
      overStockList: [], //超库存药品列表
      addExpressVisible: false, //添加物流弹窗
      pt_id: '',
      doc_id: '',
      pres_code: '',
      expressInfo: {
        express_company: '',
        express_no: '',
      },
      printCheckbox: [],

      chineseMedicineVisible: false,
      medicineInfo: {},
      belong_type: '',

      // 处方明细
      pres_record_list: [],

      deliveryVisible: false,
      ship_info: {},
    };
  },
  computed: {
    isDispenseAudit() {
      return (this.auditStatus === 'A_WAIT' || this.auditStatus === 'C_REJECT') && this.$route.query.is_audit;
    },
    confirmContent() {
      return this.auditStatus === 'A_WAIT' || this.auditStatus === 'C_REJECT'
        ? '确认出库药品并审核通过？'
        : '确认审核通过？';
    },
  },
  watch: {},
  created() {
    this.$router.onReady(() => {
      this.order_code = this.$route.query.order_code;
      this.getDetail().then( resolve => {
        if ( this.$route.query.updateLogistics ) {
          this.updateLogistics()
          let query = cloneDeep(this.$route.query)
          delete query['updateLogistics']
          this.$router.replace({
            path: this.$route.path,
            query: query
          })
        }
      } )
    });
  },
  mounted() {},
  methods: {
    handleShipInfo () {
      const express_info = this.orderInfo?.express_info || {}
      this.$set(this.ship_info, 'shipping_method', this.orderInfo.shipping_method || '')
      this.$set(this.ship_info, 'express_no', express_info.express_no || '')
      this.$set(this.ship_info, 'express_code', express_info.express_code || '')
    },
    // 打印中药制剂标签
    printDoseLabel() {
      this.medicineInfo = {
        name: this.patientInfo.name,
        pres_code: this.pres_code,
        order_code: this.order_code,
      };
      this.chineseMedicineVisible = true;
    },
    // 随货单，处方笺，电子病历打印
    print(type = '') {
      this.printCheckbox = [type];
      setTimeout(() => {
        this.$refs.printPages.printPages();
      }, 100);
    },
    confirmPrint() {
      this.$refs.printPages.printPages();
      this.hideDropdown();
    },
    cancelPrint() {
      this.printCheckbox = [];
      this.hideDropdown();
    },
    hideDropdown() {
      this.$refs.printDropdown.hide();
    },
    // 添加物流
    addExpress() {
      this.addExpressVisible = true;
    },
    // 修改物流
    updateLogistics () {
      this.deliveryVisible = true
      this.handleShipInfo()
    },
    updateSuccess () {
      this.initDetail();
    },
    // 发货
    handleDelivery(data) {
      console.log('-> %c data  ===    %o', 'font-size: 15px;color: #fa8c16 ;', data);
      const params = {
        ...data,
        order_code: this.order_code,
      };
      this.$api
        .deliveryPresOrder(params)
        .then(res => {
          this.$Message.success('发货成功');
          this.addExpressVisible = false;
          this.initDetail();
        })
        .catch(err => {
          console.log('-> %c err  ===    %o', 'font-size: 15px;color: #F56C6C ;', err);
          this.$Message.error(err.message);
        });
    },
    initDetail() {
      this.getDetail();
    },
    getDetail() {
      return new Promise( resolve => {
        this.$api.getHospitalOrderInfo({ order_code: this.order_code }).then(res => {
          this.consigneeInfo = res.consignee_info;
          this.userInfo = res.user_info;
          this.pres_record_list = res.pres_record_list
          this.order_id = res.order_id;
          this.handleOrderInfo(res);
          this.getHospitalAuditRecord();
          resolve(res)
        });
      } )
    },
    handleOrderInfo(orderInfo) {
      this.orderInfo = orderInfo;
      this.auditStatus = orderInfo.status;
      this.logistic_status = orderInfo.logistic_status;
      this.presInfo = orderInfo.pres_info;
      this.pres_id = orderInfo.pres_info.pres_id;
      this.pres_code = orderInfo.pres_info.pres_code;
      this.patientInfo = orderInfo.patient_info;
      this.expressInfo.express_no = orderInfo.express_info.express_no;
      this.expressInfo.express_company = orderInfo.express_info.express_company;
      this.belong_type = orderInfo.pres_info.belong_type;
    },
    getHospitalAuditRecord() {
      this.$api.getHospitalAuditRecord({ pres_id: this.presInfo.pres_id, statuses: '' }).then(res => {
        this.auditRecords = res.list;
      });
    },
    printFunc(name) {
      this.$refs.info.printFunc(name);
    },
  },
};
</script>
<style lang="less" scoped>
.ml10 {
  margin-left: 10px;
}
.print-checkbox {
  display: flex;
  flex-direction: column;
  padding: 10px 20px;

  ::v-deep .el-checkbox {
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 10px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
</style>
<style lang="less" scoped>
.audit-wrapper {
  padding-bottom: 40px;

  ::v-deep .p-title {
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    line-height: 20px;
    margin-bottom: 20px;
  }

  ::v-deep .info-box {
    display: flex;
    flex-wrap: wrap;
    padding: 16px;
    background-color: #f9fbfb;
    margin-bottom: 20px;

    .info-item {
      width: 33%;
      margin-bottom: 14px;
      font-size: 13px;
      line-height: 17px;

      .info-label {
        width: 76px;
        min-width: fit-content;
        text-align: right;
        color: #666666;
        margin-right: 10px;
      }

      .info-value {
        flex: 1;
        color: #333333;
      }

      &:nth-last-child(1),
      &:nth-last-child(2),
      &:nth-last-child(3) {
        margin-bottom: 0;
      }
    }
  }
}
</style>
