<template>
  <Modal
    title="打印中药制剂标贴"
    :value="value"
    width="600px"
    :mask-closable="false"
    :before-close="closeModal"
    @on-visible-change="visibleChange"
  >
    <div class="content">
      <Form ref="form" :model="formData" :label-width="80" label-suffix=":">
        <FormItem label="处方单号">
          {{ medicineInfo.pres_code }}
        </FormItem>
        <FormItem label="姓名">
          {{ medicineInfo.name }}
        </FormItem>

<!--        <FormItem label="包装量">-->
<!--          <div class="dose-box">-->
<!--            <InputNumber v-model="formData.dose" style="width: 100%" :controls="false"></InputNumber>-->
<!--            <div class="unit">ml</div>-->
<!--          </div>-->
<!--        </FormItem>-->

        <FormItem label="打印数量">
          <InputNumber v-model="formData.num" :min="1" style="width: 100%" controls-position="right"></InputNumber>
        </FormItem>
      </Form>

      <div class="errorInfo" v-if="!LODOP">
        <p v-if="!LODOP">
          <svg-icon iconClass="warn"></svg-icon>
          检测到未安装打印插件
          <a href="https://static.rsjxx.com/file/printer_drive/CLodop_Setup_for_Win64NT_6.579EN.zip" target="_blank"
            >【点击下载插件】</a
          ><br />安装完成后刷新页面打印
        </p>
        <p v-if="lodopDefaultEqu">
          <svg-icon iconClass="warn"></svg-icon>
          未检测到小票打印机
          <a
            href="https://static.rsjxx.com/file/printer_drive/GP-C200%20%E7%B3%BB%E5%88%97%E9%A9%B1%E5%8A%A8%20V1.1.zip"
            target="_blank"
            >【点击下载驱动】</a
          ><br />请先检查设备连接及驱动安装情况
        </p>
      </div>
    </div>

    <div slot="footer">
      <Button @click="closeModal">取 消</Button>
      <Button type="primary" @click="printFunc" :disabled="!LODOP && !lodopDefaultEqu" :loading="loading">打印</Button>
    </div>

    <div v-show="false">
      <lodopPrintDiv :labelInfo="labelInfo" :quantity="formData.dose" ref="lodopPrintDiv" />
    </div>
  </Modal>
</template>

<script>
import { definCss } from '@/components/lodopPrint/lodop.js';
import lodopPrintDiv from '@/components/lodopPrint/lodopPrintDiv.vue';
import '@/libs/LodopFuncs';
export default {
  name: 'inviteUserModal',
  mixins: [],
  components: {
    lodopPrintDiv,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    medicineInfo: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      LODOP: null,
      lodopDefaultEqu: false,
      formData: {
        // dose: null,
        num: 1,
      },
      loading: false,
      labelInfo: {},
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    visibleChange(val) {
      if (val) {
        this.validLodop();
      } else {
        this.closeModal();
      }
    },
    closeModal() {
      // this.formData.dose = null;
      this.formData.num = 1;
      this.$emit('input', false);
    },
    confirm() {
      this.closeModal();
    },

    // 校验表单数据
    validFormData() {
      // if (this.formData.dose <= 0 || this.formData.dose === undefined) {
      //   this.$Message.error('包装量不能为0');
      //   return false;
      // }

      if (this.formData.num <= 0) {
        this.$Message.error('打印数量最小为1');
        return false;
      }
      return true;
    },

    // 打印相关逻辑===============
    printFunc() {
      if (this.validFormData()) {
        this.printPrepLabeling().then(res => {
          if (this.validLodop()) {
            this.definePrint(() => {
              // 控制打印的份数量
              this.LODOP.SET_PRINT_COPIES(this.formData.num);
              // 预览
              // this.LODOP.PREVIEW();
              //  静默打印
              this.LODOP.PRINT();
              this.closeModal();
              this.$Message.success('正在打印中...');
            });
          }
        });
      }
    },

    definePrint(cb) {
      let strStyleCSS = definCss;
      let strFormHtml = strStyleCSS + '<body>' + this.domToString(this.$refs.lodopPrintDiv.$el) + '</body>';
      this.LODOP.ADD_PRINT_HTM(0, 0, '100%', '100%', strFormHtml);
      // 这里1表示纵向固定；600表示纸宽60mm；400表示纸高40mm
      this.LODOP.SET_PRINT_PAGESIZE(2, 400, 600, '');
      this.LODOP.SET_PRINT_STYLEA(0, 'AngleOfPageInside', 180);
      cb();
    },
    domToString(node) {
      return new XMLSerializer().serializeToString(node);
    },
    validLodop() {
      let LODOP_OBJ = (window?.getCLodop && window.getCLodop()) || null;
      if (LODOP_OBJ == null) {
        // this.$Message.error('未添加打印服务');
        return false;
      } else {
        this.LODOP = LODOP_OBJ;
        // this.LODOP.SELECT_PRINTER();
        let printerName = this.LODOP.GET_PRINTER_NAME(-1);
        // const defaultPrinterName = 'GP-C200 Series';
        const defaultPrinterName = 'TSC TE4503';
        if (printerName !== defaultPrinterName) {
          // this.$Message.error('请设置默认打印机为：' + defaultPrinterName);
          this.lodopDefaultEqu = true;
          return false;
        } else {
          this.lodopDefaultEqu = false;
        }
        return true;
      }
    },
    //==========================

    printPrepLabeling() {
      return new Promise(resolve => {
        let params = {
          order_code: this.medicineInfo.order_code,
        };
        this.loading = true;
        this.$api
          .printPrepLabeling(params)
          .then(
            res => {
              this.labelInfo = res;
              this.loading = true;
              resolve();
            },
            err => {}
          )
          .finally(() => (this.loading = false));
      });
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  padding: 30px 50px;

  .dose-box {
    display: flex;
    .unit {
      background: #f5f7fa;
      color: #606266;
      width: 35px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}

.errorInfo {
  text-align: center;
  background-color: #f7f7f7;
  color: #666;
  padding: 10px;
  font-size: 13px;
  line-height: 20px;

  p {
    font-size: 12px;
    text-align: center;
  }
}
</style>

<style lang="less" scoped>
// common modal style
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 150px;
  overflow-y: auto;
}
</style>
