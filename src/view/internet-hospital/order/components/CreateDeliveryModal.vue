<template>
  <Modal
    ref="delivery"
    :value="visible"
    :title="is_update_logistics ? '修改物流' : '发药详情'"
    :width="is_update_logistics ? 500 : 1100"
    @on-visible-change="initShipData"
    :mask-closable="false"
    :class-name="is_update_logistics ? 'custom-deliver-modal' : ''"
  >
    <div>
      <Form :model="formData" inline :rules="rules" ref="dispensingDrugsForm" :label-width="80">
        <Row>
          <FormItem label="发货方式" prop="shipping_method">
            <RadioGroup v-model="formData.shipping_method">
              <Radio disabled v-for="(item, index) in shipping_method_list" :key="+index" :label="item.val.kw">{{
                item.val.desc
              }}</Radio>
            </RadioGroup>
          </FormItem>
        </Row>

        <Row v-if="formData.shipping_method == 1">
          <Col :span="is_update_logistics ? 24 : 10">
            <FormItem label="配送方式" :prop="formData.shipping_method == 1 ? 'express_code' : ''" class="form-item">
              <Select v-model="formData.express_code" transfer style="width: 100%" placeholder="请选择配送方式">
                <Option
                  v-for="express in expressCompany"
                  :key="express.kuai_di_100_code"
                  :label="express.name"
                  :value="express.kuai_di_100_code"
                ></Option>
              </Select>
            </FormItem>
          </Col>
          <Col :span="is_update_logistics ? 24 : 10">
            <FormItem label="物流单号" :prop="formData.shipping_method == 1 ? 'express_no' : ''" class="form-item">
              <Input v-model="formData.express_no" placeholder="请输入快递单号" style="width: 100%"></Input>
            </FormItem>
          </Col>
        </Row>
      </Form>
      <Table v-if="!is_update_logistics" :data="deliveryData" row-key="id" :columns="tableCols" style="width: 100%; margin-top: 5px" height="450">
        <template v-slot:expire_time="{ row }">
          {{ row.expire_time | data_format('YYYY-MM-DD') }}
        </template>
        <template v-slot:supplier="{ row }">
          {{ row.supplier || '-' }}
        </template>
        <template v-slot:stock_num="{ row }">
          {{ row.stock_num || '-' }}
        </template>
        <template v-slot:wait_quantity="{ row }">
          {{ row.wait_quantity || '-' }}
        </template>
        <template v-slot:buy_quantity="{ row }">
          {{ row.parent_stock_detail_id ? '' : row.buy_quantity }}
        </template>
        <template v-slot:batch_code="{ row, index }">
          {{ row.batch_code || '-' }}
        </template>
      </Table>
    </div>
    <template #footer>
      <div class="footer-btn">
        <Button class="form-button" :size="is_update_logistics ? 'mini' : 'large'" type="default" @click="closeDialog">取消</Button>
        <Button
          class="form-button ml-20"
          :size="is_update_logistics ? 'mini' : 'large'"
          type="primary"
          :loading="submitLoading"
          @click="submitForm('dispensingDrugsForm')"
          >{{ is_update_logistics ? '修改物流' : '确认发药' }}</Button
        >
      </div>
    </template>
  </Modal>
</template>

<script>
// import { $operator } from '@/libs/operation';
import cloneDeep from 'lodash.clonedeep';

export default {
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    visible: {
      type: Boolean,
      default: () => false,
    },
    drugs: {
      type: Array,
      default: () => [],
    },
    orderId: {
      type: String,
      default: '',
    },
    orderCode: {
      type: String,
      default: '',
    },
    presId: {
      type: String,
      default: '',
    },
    echoInfo: {
      type: Object,
      default: () => {},
    },
    is_update_logistics: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      deliveryData: [],
      tableCols: [
        { title: '商品名称', key: 'prod_name', align: 'left', tree: true },
        { title: '有效期', slot: 'expire_time', align: 'left', width: 160 },
        { title: '供应商', slot: 'supplier', align: 'left' },
        { title: '现库存', slot: 'stock_num', align: 'left' },
        { title: '商品单位', key: 'prod_unit', align: 'left', width: 80 },
        { title: '待发数量', slot: 'wait_quantity', align: 'left' },
        { title: '选择批号', slot: 'batch_code', align: 'left', width: 130 },
        { title: '发药数量', key: 'num', align: 'left' },
      ],

      rules: {
        shipping_method: [
          {
            required: true,
            message: '请选择发货方式',
            trigger: 'change',
          },
        ],
        express_no: [
          {
            required: true,
            message: '请输入物流单号',
            trigger: 'blur',
          },
        ],
        express_code: [
          {
            required: true,
            message: '请选择物流公司',
            trigger: 'change',
          },
        ],
      },
      expressCompany: [],
      formData: {
        express_no: '',
        express_code: '',
        shipping_method: '',
      },
      originDrugs: [],
      submitLoading: false,
      shipping_method_list: [],
    };
  },
  mounted() {},
  methods: {
    handleEchoInfo() {
      let ship_info = cloneDeep(this.echoInfo || {});
      this.formData.shipping_method = ship_info.shipping_method;
      this.formData.express_code = ship_info.express_code;
      this.formData.express_no = ship_info.express_no;
    },
    initShipData(visible) {
      if (visible) {
        this.getExpressCompanies();
        this.getOptions();
        this.handleEchoInfo();
      } else {
        this.closeDialog();
      }
    },
    getExpressCompanies() {
      this.$api.getHospitalExpressList().then(res => {
        this.expressCompany = res.list;
        if ( !this.is_update_logistics ) {
          this.getHospitalOrderDeliveryDetail();
        }
      });
    },

    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          if (this.is_update_logistics) {
            this.updateOrderdeliveryexpressedit();
          } else {
            this.dispensingMedicine();
          }
        } else {
          this.$Message.error('请完善物流信息');
        }
      });
    },
    // 修改物流
    updateOrderdeliveryexpressedit() {
      this.submitLoading = true;
      let params = {
        express_code: this.formData.express_code,
        express_no: this.formData.express_no,
        order_code: this.orderCode,
        order_id: this.orderId,
        pres_id: this.presId,
      };
      this.$api
        .updateOrderdeliveryexpressedit(params)
        .then(
          res => {
            this.$Message.success('物流修改成功');
            this.closeDialog();
            this.$emit('refreshList');
          },
          err => this.$Message.error(err.errmsg)
        )
        .finally(() => (this.submitLoading = false));
    },
    // 确认发药
    dispensingMedicine() {
      this.submitLoading = true;
      let formData = cloneDeep(this.formData);
      this.$delete(formData, 'shipping_method');
      if (this.formData.shipping_method == 2) {
        formData.express_code = 'ZTCK';
        formData.express_no = '';
      }
      let params = {
        ...formData,
        order_code: this.orderCode,
        pres_id: this.presId,
      };
      this.$api
        .postHospitalOrderDelivery(params)
        .then(
          data => {
            this.$Message.success('发药成功');
            this.closeDialog();
            this.$emit('refreshList');
          },
          error => {}
        )
        .finally(() => (this.submitLoading = false));
    },
    getHospitalOrderDeliveryDetail() {
      let params = { order_id: this.orderId, pres_id: this.presId };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getHospitalOrderDeliveryDetail(params)
        .then(res => {
          this.deliveryData = res.list;
        })
        .catch(err => {
          {
          }
        });
    },

    closeDialog() {
      this.$store.commit('global/SET_HOS_ORDER', null);
      this.$emit('changeVisible', false);
      this.$refs.dispensingDrugsForm.resetFields();
      let overflowY = this.$el.getElementsByClassName('ivu-modal-body')[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },
    getOptions() {
      this.$api.getHospitalOrderOption().then(res => {
        this.shipping_method_list = res.shipping_method;
      });
    },
  },
  watch: {
    // visible(val) {
    //   if (val) {
    //     this.visible = val;
    //     this.handlerList();
    //   }
    // },
  },
};
</script>
<style lang="less" scoped>
.footer-btn {
  .form-button {
    width: 168px;
  }
}

.form-item {
  width: 100%;
}

::v-deep .ivu-table-body {
  overflow-y: auto;
  overflow-x: hidden;
}

::v-deep .ivu-icon {
  margin-left: 1px;
}
::v-deep .ivu-modal-body {
  padding: 20px 30px;
  height: calc(~'100% - 110px');
  overflow-y: auto;
}
::v-deep .ivu-modal {
  height: calc(~'100% - 100px') !important;
}
::v-deep .ivu-modal-content {
  height: calc(~'100% - 100px');
}
</style>

<style lang="less">
.custom-deliver-modal {
  .ivu-modal-content {
    height: 400px;
  }
  .footer-btn {
    .form-button {
      width: 100px;
    }
  }
}
</style>
