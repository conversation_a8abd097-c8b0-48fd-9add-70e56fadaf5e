<template>
  <el-dialog
    :visible="visible"
    title="添加物流"
    width="430px"
    :closable="false"
    append-to-body
    @close="closeDialog"
    @open="getExpressCompanies"
    class-name="confirm-modal"
    transfer
    :close-on-click-modal="false"
  >
    <div class="logistics-form">
      <el-form :model="formData" :rules="rules" ref="logisticsForm" label-width="100px">
        <el-form-item label="配送方式" prop="express_code">
          <el-select v-model="formData.express_code" style="width: 100%" placeholder="请选择配送方式">
            <el-option
              v-for="express in expressCompanys"
              :key="express.kuai_di_100_code"
              :label="express.name"
              :value="express.kuai_di_100_code"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="物流单号" prop="express_no">
          <el-input v-model="formData.express_no" placeholder="请输入快递单号"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('logisticsForm')">立即创建</el-button>
          <el-button @click="closeDialog">取消</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'CreateLogistics',
  mixins: [],
  model: {
    prop: 'visible',
    event: 'update:visible',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formData: {
        express_no: '',
        express_code: '',
      },
      rules: {
        express_no: [
          {
            required: true,
            message: '请输入物流单号',
            trigger: 'blur',
          },
        ],
        express_code: [
          {
            required: true,
            message: '请选择物流公司',
            trigger: 'change',
          },
        ],
      },
      expressCompanys: [],
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    closeDialog() {
      this.$refs.logisticsForm.resetFields();

      this.$emit('update:visible', false);
    },
    getExpressCompanies() {
      this.$api.getExpressList().then(res => {
        console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);
        this.expressCompanys = res.list;
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.$emit('createLogistics', this.formData);
        } else {
          this.$Message.error('请完善物流信息');
        }
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
