<template>
  <div>
    <standard-table
      :data="list"
      :columns="tableCols"
      :loading="tableLoading"
      stripe
      :total="total"
      :page-size.sync="queryFormData.page_size"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
          <div class="flex list-fn-mb-distance">
            <Button type="primary" class="mb18" @click="goCreate">新增药师</Button>
          </div>
          <Row>
            <FormItem>
              <Input v-model="queryFormData.keyword" clearable placeholder="药师姓名/ID" />
            </FormItem>
            <FormItem>
              <Input v-model="queryFormData.mobile" clearable placeholder="请输入手机号" />
            </FormItem>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="onResetSearch">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
            </FormItem>
          </Row>
        </Form>
        <div class="flex flex-item-between flex-item-align">
          <div class="panel-nav">
            <a class="nav" :class="{ active: !queryFormData.audit_status }" @click.prevent.capture="onStatusChange('')">
              全部
            </a>
            <a
              class="nav"
              v-for="item in auditStatusDesc"
              :key="item.id"
              :class="{ active: $route.query.audit_status === item.id }"
              @click.prevent.capture="onStatusChange(item.id)"
            >
              {{ item.desc }}
              <Tag :color="getTagColor(item.id)">{{ list_count[item.id] }}</Tag>
            </a>
          </div>
        </div>
      </template>

      <template v-slot:create_time="{ row }">
        {{ row.create_time | data_format }}
      </template>
      <template v-slot:audit_desc="{ row }">
        <mark-status :type="getMarkStatusType(row.audit_status)">{{ row.audit_desc || '' }}</mark-status>
      </template>
      <template v-slot:action="{ row }">
        <a class="mr10" @click="toDetail(row)">{{ row.audit_status === '3' ? '查看原因' : '查看信息' }}</a>
        <Poptip
          v-if="row.audit_status === '2'"
          confirm
          transfer
          :title="`确认${row.is_pres === '1' ? '禁用' : '启用'}该药师？`"
          @on-ok="setPresPermission(row)"
        >
          <a>
            {{ row.is_pres === '1' ? '禁用' : '启用' }}
          </a>
        </Poptip>
      </template>
    </standard-table>
  </div>
</template>
<script>
import search from '@/mixins/search';
import S from 'libs/util';
import StandardTable from '@/components/StandardTable/index.vue';

const init_query_form_data = {
  page: 1,
  page_size: 20,
  keyword: '',
  supplier_id: '',
  status: '',
  prod_type: '',
  company_id: '',
  r: '',
};
export default {
  name: 'PharmacistList',
  components: { StandardTable },
  mixins: [search],
  props: {},
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getPharmacistList',
      tableCols: [
        { title: 'ID', key: 'id', align: 'center', width: 100 },
        { title: '药师姓名', key: 'name', align: 'center', width: 100 },
        { title: '手机号', key: 'mobile', align: 'center', width: 100 },
        { title: '职称', key: 'professional_title_desc', align: 'center', width: 100 },
        { title: '处方权限', key: 'pres_desc', align: 'center', width: 100 },
        { title: '创建时间', slot: 'create_time', align: 'center', width: 140 },
        { title: '审核状态', slot: 'audit_desc', align: 'center', width: 100 },
        { title: '操作', slot: 'action', align: 'center', width: 100 },
      ],
      professional_title: [],
      auditStatusDesc: [],
      list_count: {},
    };
  },
  computed: {
    getTagColor() {
      return type => {
        switch (type) {
          case '1': // 待审核
            return 'warning';
          case '2': // 已认证
            return 'success';
          case '3': // 已驳回
            return 'error';
          default:
            // 已取消
            return 'default';
        }
      };
    },
    getMarkStatusType() {
      return status => {
        switch (status) {
          case '1': // 待审核
            return 'warn';
          case '2': // 已认证
            return 'success';
          case '3': // 已驳回
            return 'reject';
          default:
            // 已取消
            return 'default';
        }
      };
    },
  },
  watch: {},
  created() {
    this.getPharmacistOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    setPresPermission(row) {
      const params = {
        id: row.id,
        is_pres: row.is_pres === '1' ? 2 : 1,
      };
      this.$api.setPresPermission(params).then(() => {
        this.$Message.success('操作成功');
        this.submitQueryForm();
      });
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    goCreate() {
      this.$router.push('/internet-hospital/pharmacist/detail');
    },
    // disablePharmacist(row) {},
    toDetail(row) {
      this.$router.push({ path: '/internet-hospital/pharmacist/detail', query: { id: row.id } });
    },
    onStatusChange(tab) {
      this.queryFormData.page = 1;
      this.queryFormData.audit_status = tab;
      this.submitQueryForm();
    },
    getPharmacistOptions() {
      this.$api
        .getPharmacistOptions()
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.auditStatusDesc = S.descToArrHandle(res.auditStatusDesc);
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    handlerListData(data) {
      this.list_count = data.list_count;
      console.log('=>(list.vue:177) this.list_count', this.list_count);
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>
<style lang="less" scoped></style>
