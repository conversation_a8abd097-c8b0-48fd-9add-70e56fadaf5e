<template>
  <div style="padding-bottom: 20px">
    <div style="width: 80%">
      <Form ref="pharmacistForm" :model="formData" :label-width="150" :rules="rules" :disabled="isQueryDetail">
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="诊所员工：" prop="member_id">
              <Select v-model="formData.member_id" placeholder="请选择诊所员工" clearable @on-change="selectMember">
                <Option disabled value="" v-if="memberList.length">
                  <div class="flex">
                    <div style="width: 100px">姓名</div>
                    <div>手机号</div>
                  </div>
                </Option>
                <Option v-for="(option, index) in memberList" :value="option.id" :label="option.name" :key="index">
                  <div class="flex">
                    <div style="width: 100px" class="ecs">{{ option.name }}</div>
                    <div>{{ option.mobile }}</div>
                  </div>
                </Option>
              </Select>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.name" class="error-reason">上次驳回原因：{{ reject_data.name }}</div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="手机号：" prop="mobile">
              <Input v-model="formData.mobile" placeholder="请输入手机号" disabled></Input>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.mobile" class="error-reason">上次驳回原因：{{ reject_data.mobile }}</div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="邮箱：" prop="email">
              <Input v-model="formData.email" placeholder="请输入邮箱"></Input>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.email" class="error-reason">上次驳回原因：{{ reject_data.email }}</div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="头像：" prop="avatar">
              <MaterialPicture
                v-model="formData.avatar"
                :disabled="isQueryDetail"
                @input="validPicture('avatar')"
                style="line-height: 0"
                :limit="1"
              ></MaterialPicture>
              <div class="upload-note">图片比例：60x60px</div>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.avatar" class="error-reason mt20">上次驳回原因：{{ reject_data.avatar }}</div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="职称：" prop="professional_title">
              <Select v-model="formData.professional_title" placeholder="请选择职称" style="width: 100%">
                <Option v-for="item in professionList" :key="item.id" :label="item.desc" :value="item.id"></Option>
              </Select>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.professional_title" class="error-reason">
              上次驳回原因：{{ reject_data.professional_title }}
            </div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="身份证：" prop="id_card">
              <Input v-model="formData.id_card" class="mb10" placeholder="请输入身份证"></Input>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.id_card" class="error-reason">上次驳回原因：{{ reject_data.id_card }}</div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="身份证人像面照片" class="mt-30" prop="id_card_front">
              <MaterialPicture
                v-model="formData.id_card_front"
                :limit="1"
                :disabled="isQueryDetail"
                @input="validPicture('id_card_front')"
              >
              </MaterialPicture>
              <div class="upload-note">
                1.请上传彩色照片，要求正面拍摄，露出证件四角且清晰、完整，所有字符清晰可识别，不得反光或遮挡。不得翻拍、截图、镜像、PS。
                <br />
                2.图片只支持JPG、BMP、PNG格式，文件大小不能超过3M。
              </div>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.id_card_front" class="error-reason mt40">
              上次驳回原因：{{ reject_data.id_card_front }}
            </div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem prop="id_card_reverse" class="mt-30" label="身份证国徽面照片">
              <MaterialPicture
                v-model="formData.id_card_reverse"
                :limit="1"
                :disabled="isQueryDetail"
                @input="validPicture('id_card_reverse')"
              >
              </MaterialPicture>
              <div class="upload-note">
                1.请上传彩色照片，要求正面拍摄，露出证件四角且清晰、完整，所有字符清晰可识别，不得反光或遮挡。不得翻拍、截图、镜像、PS。
                <br />
                2.图片只支持JPG、BMP、PNG格式，文件大小不能超过3M。
              </div>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.id_card_reverse" class="error-reason mt40">
              上次驳回原因：{{ reject_data.id_card_reverse }}
            </div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="资格证管理号：" prop="manage_number">
              <Input v-model="formData.manage_number" placeholder="请输入资格证管理号"></Input>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.manage_number" class="error-reason">
              上次驳回原因：{{ reject_data.manage_number }}
            </div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="资格证发证日期：" prop="date_of_issue">
              <DatePicker
                :value="formData.date_of_issue"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="请选择资格证发证日期"
                style="width: 100%"
                :options="pickerOptions"
                @on-change="date => (formData.date_of_issue = date)"
              >
              </DatePicker>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.date_of_issue" class="error-reason">
              上次驳回原因：{{ reject_data.date_of_issue }}
            </div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="执业资格证照片：" prop="certification_images">
              <MaterialPicture
                class="mt10"
                v-model="formData.certification_images"
                :disabled="isQueryDetail"
                :limit="isQueryDetail ? 1 : 9"
              ></MaterialPicture>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.certification_images" class="error-reason mt20">
              上次驳回原因：{{ reject_data.certification_images }}
            </div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="职称证照片：" prop="professional_title_images">
              <MaterialPicture
                v-if="!(isQueryDetail && formData.professional_title_images.length === 0)"
                v-model="formData.professional_title_images"
                :disabled="isQueryDetail"
                :limit="isQueryDetail ? 1 : 9"
                @input="validPicture('professional_title_images')"
              ></MaterialPicture>
              <div v-else>暂未上传</div>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.professional_title_images" class="error-reason mt20">
              上次驳回原因：{{ reject_data.professional_title_images }}
            </div>
          </Col>
        </Row>
        <Row :gutter="20">
          <Col :span="14">
            <FormItem label="电子签名：" prop="e_signature">
              <Button v-show="!formData.e_signature" @click="showESignModal">电子签名</Button>
              <Picture
                v-show="formData.e_signature"
                ref="eSign"
                v-model="formData.e_signature"
                :disabled="isQueryDetail"
                :limit="1"
                @input="validPicture('e_signature')"
              ></Picture>
            </FormItem>
          </Col>
          <Col :span="10">
            <div v-if="reject_data.e_signature" class="error-reason">上次驳回原因：{{ reject_data.e_signature }}</div>
          </Col>
        </Row>
      </Form>
    </div>
    <div class="fixed-bottom-wrapper">
      <back-button class="mr10"></back-button>
      <Button v-if="!isQueryDetail" type="primary" @click="submitForm">提交</Button>
    </div>
    <e-signature :visible.sync="eSignVisible" @getESignature="getESignature"></e-signature>
  </div>
</template>
<script>
import S from 'libs/util';
import eSignature from './components/eSignatureModal.vue';
import Picture from '@/components/upload/picture/picture.vue';
export default {
  name: 'PharmacistDetail',
  components: { eSignature, Picture },
  mixins: [],
  props: {},
  data() {
    return {
      formData: {
        member_id: '',
        name: '',
        mobile: '',
        email: '',
        id_card: '', // 身份证号
        professional_title: '', // 职称id
        is_pres: '', // 是否开启处方
        avatar: '', // 头像
        id_card_front: '', // 身份证正面
        id_card_reverse: '', // 身份证背面
        manage_number: '', // 资格证管理号
        date_of_issue: '', // 发证日期
        certification_images: [],
        professional_title_images: [], // 职称证图片
        e_signature: '',
      },
      rules: {
        member_id: [{ required: true, message: '请选择诊所员工', trigger: 'change' }],
        name: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        mobile: [{ required: true, message: '请输入手机号', trigger: 'change' }],
        email: [{ required: true, message: '请输入邮箱', trigger: 'change' }],
        avatar: [{ required: true, message: '请上传用户头像', trigger: 'change' }],
        id_card: [
          {
            required: true,
            message: '请输入身份证号',
            trigger: 'change',
            pattern:
              /^(1[1-5]|2[1-3]|3[1-7]|4[1-6]|5[0-4]|6[1-5])\d{4}(19\d{2}|20(0[0-9]|1[0-8]))((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
          },
        ],
        id_card_front: [{ required: true, message: '请上传身份证人像面照片', trigger: 'change' }],
        id_card_reverse: [{ required: true, message: '请上传身份证国徽面照片', trigger: 'change' }],
        professional_title: [{ required: true, message: '请选择用户职称', trigger: 'change' }],
        is_pres: [{ required: true, message: '请输入姓名', trigger: 'change' }],
        manage_number: [{ required: true, message: '请输入资格证管理号', trigger: 'change' }],
        date_of_issue: [{ required: true, message: '请选择资格证发证日期', trigger: 'change' }],
        certification_images: [{ required: true, message: '请选择执业资格证照片', type: 'array', trigger: 'change' }],
        // professional_title_images: [{ required: true, message: '请上传职称证图片', type: 'array', trigger: 'change' }],
        e_signature: [{ required: true, message: '请上传电子签名', trigger: 'change' }],
      },
      professionList: [],
      pickerOptions: {
        disabledDate(date) {
          return date.getTime() > Date.now();
        },
      },
      memberList: [],
      eSignVisible: false,
      reject_data: {},
    };
  },
  computed: {
    isQueryDetail() {
      return !!this.$route.query.id && this.formData.audit_status !== '3';
    },
  },
  watch: {},
  created() {
    this.getHospitalOptions();
    this.getHospitalMemberSearch();
  },
  mounted() {},
  methods: {
    getHospitalOptions() {
      this.$api
        .getHospitalOptions()
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.professionList = S.descToArrHandle(res.getProfessionTitleDesc);
          const id = this.$route.query.id;
          id && this.getPharmacistDetail(id);
        })
        .catch(err => {});
    },
    submitForm() {
      this.$refs.pharmacistForm.validate(valid => {
        if (valid) {
          this.$route.query.id ? this.editPharmacist() : this.createPharmacist();
        }
      });
    },
    createPharmacist() {
      this.$api
        .createPharmacist(this.formData)
        .then(res => {
          this.$Message.success('创建成功');
          this.$router.back();
        })
        .catch(err => {});
    },
    editPharmacist() {
      let params = {
        id: this.$route.query.id,
        ...this.formData,
      };
      this.$api
        .editPharmacist(params)
        .then(res => {
          this.$Message.success('编辑成功');
          this.$router.back();
        })
        .catch(err => {});
    },
    getPharmacistDetail(id) {
      this.$api.getPharmacistDetail({ id }).then(res => {
        this.formData = res;
        this.reject_data = res.reject_data || {};
        let flag = this.memberList.findIndex(item => item.id === this.formData.member_id);
        if (flag === -1) {
          let current = { id: this.formData.member_id, name: this.formData.name, mobile: this.formData.mobile };
          this.memberList.push(current);
        }
      });
    },
    validPicture(key) {
      this.$refs['pharmacistForm'].validateField(key);
    },
    getHospitalMemberSearch() {
      this.$api.getHospitalMemberSearch().then(res => {
        this.memberList = res.list;
      });
    },
    selectMember(val) {
      if (val) {
        let selected = this.memberList.find(item => item.id === val);
        this.formData.name = selected.name;
        this.formData.mobile = selected.mobile;
      } else {
        this.formData.name = '';
        this.formData.mobile = '';
      }
    },
    showESignModal() {
      this.eSignVisible = true;
    },
    getESignature(file) {
      this.$refs.eSign.$refs['qn-upload']?.onBeforeUpload(file).then(() => {
        this.$refs.eSign?.$refs['qn-upload']?.$children[0]?.post(file);
      });
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
.phar-name {
  font-size: 30px;
  font-weight: 500;
}

.phar-info {
  line-height: 14px;
}

.upload-note {
  width: 480px;
  color: #aaaaaa;
  line-height: 20px;
  margin-top: -12px;
}
.error-reason {
  //padding-left: 120px;
  color: #ed4014;
  font-size: 12px;
}
.mt40 {
  margin-top: 40px;
}
</style>
