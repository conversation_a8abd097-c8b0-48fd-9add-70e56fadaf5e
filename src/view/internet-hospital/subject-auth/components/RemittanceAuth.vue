<template>
  <div class="subject-form">
    <div class="content-wrap" v-if="pageLoad">
      <img src="@/assets/image/auth/remmit-img.png" width="134" height="134" alt="" />
      <div class="remmit-text">汇款验证</div>
      <div class="remmit-tips">
        请在账户({{ maskBankCard }})收到打款验证信息后，并在
        {{ validate_form.deadline }} 之前确认完成打款验证，超时需要重新提交认证信息。
      </div>
      <div class="input-wrap flex flex-item-align">
        <div class="ipt-title">
          <span>汇款 验证</span>
        </div>
        <div class="divider"></div>
        <InputNumber
          v-model="formData.amount"
          class="remmit-ipt"
          size="large"
          :min="0"
          :precision="2"
          :active-change="false"
          :max="10"
          placeholder="请输入汇款金额、单位为元"
        />
        <span class="ml-6">元</span>
      </div>
      <!--      <div class="error-text" v-if="Number(validate_form.frequency) < 3">-->
      <!--        金额验证错误，还剩{{ validate_form.frequency }}次机会。-->
      <!--      </div>-->
    </div>
    <div v-else>
      <Spin>加载中...</Spin>
    </div>
    <div class="fixed-bottom-wrapper">
      <back-button class="mr-12">返回</back-button>
      <Button type="primary" @click="handleSubmit" :loading="submitLoading">提交</Button>
    </div>
    <remit-status-dialog
      v-model="statusDialogVisible"
      @on-close="closeDialog"
      :reason="reason"
      :isSuccess="isSuccess"
      :remmitStatus="remmitStatus"
    ></remit-status-dialog>
  </div>
</template>

<script>
import RemitStatusDialog from './RemitStatusDialog.vue';
// 主体认证
export default {
  name: 'RemittanceAuthComponent',
  components: {
    RemitStatusDialog,
  },
  data() {
    return {
      remitStatus: '', //
      statusDialogVisible: false,
      formData: {
        amount: null,
        out_request_no: '',
      },
      validate_form: { bank_acct_no: '', deadline: '', frequency: 0 },
      reason: '',
      isSuccess: false,
      submitLoading: false,
      pageLoad: false,
      remmitStatus: '',
    };
  },
  computed: {
    maskBankCard() {
      // 取前4位和后4位
      let cardNumber = this.validate_form.bank_acct_no;
      if (!cardNumber) return '';
      const firstFour = cardNumber.slice(0, 4);
      const lastFour = cardNumber.slice(-4);
      // 中间的数字用 "*" 替代
      const middleDigits = cardNumber.slice(4, -4).replace(/\d/g, '*');
      // 将部分隐藏后的银行卡号组合起来
      return `${firstFour}${middleDigits}${lastFour}`;
    },
  },
  created() {
    this.$router.onReady(() => {
      this.remitStatus = this.$route.query.status;
      this.formData.out_request_no = this.$route.query.out_request_no;
      this.getDetail();
    });
  },
  methods: {
    handleSubmit() {
      if (!this.formData.amount) {
        this.$Message.error('请输入汇款金额');
        return;
      }
      this.submitLoading = true;
      this.$api
        .remitAuthentication({ ...this.formData, sys_code: 'CDHY' })
        .then(
          res => {
            console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);
            this.isSuccess = res.status === 'succeed';
            this.remmitStatus = res.status;
            this.$nextTick(() => {
              this.statusDialogVisible = true;
            });
            this.validate_form.frequency = +res.frequency;
            this.reason = res.message;
          },
          err => {
            {};
          }
        )
        .finally(() => {
          this.submitLoading = false;
        });
    },
    closeDialog() {
      this.statusDialogVisible = false;
      this.$router.back();
    },
    getDetail() {
      this.$api
        .getAuthenticationInfo({
          out_request_no: this.$route.query.out_request_no,
          sys_code: 'CDHY'
        })
        .then(
          res => {
            this.formData.settle_acct_id = res.detail.validate_form.settle_acct_id;
            this.formData.out_request_no = res.detail.out_request_no;
            this.validate_form.bank_acct_no = res.detail.settle_acct.bank_acct_no;
            this.validate_form.deadline = res.detail.validate_form.deadline;
            this.validate_form.frequency = +res.detail.frequency;
          },
          err => {
            {};
          }
        )
        .finally(() => {
          this.pageLoad = true;
        });
    },
  },
};
</script>

<style scoped lang="less">
.subject-form {
}

.content-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  margin: 0 auto;
  width: 470px;
  padding-top: 160px;

  .remmit-text {
    font-size: 18px;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
    margin: 16px 0 8px;
  }

  .remmit-tips {
    color: #999999;
    line-height: 20px;
    text-align: center;
    margin-bottom: 30px;
  }

  .input-wrap {
    width: 100%;
    padding: 15px 26px;
    background: #fafafa;

    .remmit-ipt {
      border: none;
      flex: 1;
    }

    .ipt-title {
      width: 28px;
      font-size: 14px;
      color: #333333;
      line-height: 22px;
    }

    .divider {
      width: 1px;
      height: 40px;
      background: #e8e8e8;
      margin: 0 16px 0 26px;
    }
  }

  .error-text {
    color: #e83b3b;
    line-height: 16px;
    margin-left: 97px;
    margin-top: 16px;
    align-self: flex-start;
  }
}
</style>
