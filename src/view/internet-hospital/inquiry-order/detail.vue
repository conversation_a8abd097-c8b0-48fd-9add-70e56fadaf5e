<template>
  <div class="audit-wrapper">
    <p>订单详情</p>
    <div class="custom">
      <el-descriptions>
        <el-descriptions-item label="订单号">{{ detail.code || '-' }}</el-descriptions-item>
        <el-descriptions-item label="交易订单号">{{ detail.transaction_no || '-' }}</el-descriptions-item>
        <el-descriptions-item label="订单状态">{{ detail.list_status_text || '-' }}</el-descriptions-item>
        <el-descriptions-item label="服务类型">{{ detail.type_text || '-' }}</el-descriptions-item>
        <el-descriptions-item label="服务状态">{{ detail.service_type_text || '-' }}</el-descriptions-item>
        <el-descriptions-item label="支付状态">{{ detail.pay_status_text || '-' }}</el-descriptions-item>
        <!-- 若干展示逻辑 -->
        <el-descriptions-item
          v-for="(item, index) in getTimeArr(detail.list_status, detail)"
          :key="index"
          :label="item.label"
        >
          {{ detail[item.value] || '-' }}
        </el-descriptions-item>

        <!-- 若干展示逻辑 -->
        <!-- detail.total_fee -->
        <el-descriptions-item label="订单金额">{{ getTotalFee(detail) }}</el-descriptions-item>
        <el-descriptions-item label="优惠金额">
          <div v-text-format.percent="detail.discount_fee"></div
        ></el-descriptions-item>
        <el-descriptions-item label="实际支付">
          <div v-text-format.percent="detail.payment_fee"></div
        ></el-descriptions-item>
        <el-descriptions-item label="医生姓名">
          <div>
            <div v-if="detail.type === 3">
              <span v-if="detail?.doctor_team?.name">
                <div v-if="detail?.doctor?.name">{{ detail?.doctor?.name }}</div>
                <el-tag>{{ detail?.doctor_team.name }}</el-tag>
              </span>
            </div>
            <div v-else>
              <span v-if="detail?.doctor?.name">{{ detail?.doctor?.name }}</span>
              <span v-else>-</span>
            </div>
          </div>
          <!-- {{ detail?.doctor?.name || '-' }} -->
        </el-descriptions-item>
        <el-descriptions-item label="服务时长">
          <span v-if="detail.service_time">{{ detail.service_time }}{{ detail.service_time_unit_text }}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="优惠渠道">{{ detail.discount_channel || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <p>用户信息</p>
    <div class="custom">
      <el-descriptions>
        <el-descriptions-item label="用户姓名">{{ detail?.user?.nickname || '-' }}</el-descriptions-item>
        <el-descriptions-item label="手机号">{{ detail?.user?.mobile || '-' }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <p>患者信息</p>
    <div class="custom">
      <el-descriptions>
        <el-descriptions-item label="患者姓名">{{ detail?.patient?.name || '-' }}</el-descriptions-item>
        <el-descriptions-item label="年龄">{{ detail?.patient?.age || '-' }}</el-descriptions-item>
        <el-descriptions-item label="身份证">{{ detail?.patient?.id_card || '-' }}</el-descriptions-item>
        <el-descriptions-item label="性别">{{ detail?.patient?.gender_text || '-' }}</el-descriptions-item>
        <el-descriptions-item label="现病史">{{ detail?.patient?.current || '-' }}</el-descriptions-item>
        <el-descriptions-item label="既往史">{{ detail?.patient?.past_allergy || '-' }}</el-descriptions-item>
        <!-- <el-descriptions-item label="联系电话">{{ detail?.patient?.mobile || '-' }}</el-descriptions-item> -->
        <!-- <el-descriptions-item label="服务状态">{{ detail?.info_status_text || '-' }}</el-descriptions-item> -->
      </el-descriptions>
    </div>

    <!-- 赠送不展示问诊单 -->
    <p v-if="!isEmpty(guided_record_list)">问诊单</p>
    <el-table
      v-if="!isEmpty(guided_record_list)"
      :data="guided_record_list"
      header-cell-class-name="tableDefaultColor"
      style="width: 100%; margin-bottom: 20px"
    >
      <el-table-column prop="type_text" label="问诊单类型" />
      <el-table-column prop="title" label="问诊单名称" />
      <el-table-column prop="pt_name" label="填写人" />
      <el-table-column prop="created_at" label="提交时间" />
      <el-table-column prop="action" label="操作">
        <template slot-scope="{ row }">
          <el-button type="text" @click="viewInquiryDrawer(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    <inquiry-modal v-model="inquiryModalVisible" :inquiryInfo="inquiryInfo" />
  </div>
</template>

<script>
import { getTimeArr, getTotalFee } from './util';
import inquiryModal from './components/inquiryModal.vue';
import { isEmpty } from '../../../utils/helper';
export default {
  name: 'InquiryOrderDetail',
  components: { inquiryModal },
  data() {
    return {
      getTimeArr,
      getTotalFee,
      id: null,
      detail: {},
      detailAi: {},
      tabList: [
        { label: '患者数据', key: 'data' },
        { label: 'AI检测报告', key: 'ai-port' },
      ],
      activeName: 'data',
      aiInquiryUrl: '',

      // 抽屉weburl
      inquiryModalVisible: false,
      guided_record_list: [],
      inquiryInfo: {},
    };
  },
  computed: {
    getSex() {
      return function (val) {
        switch (val) {
          case 1:
            return '男';
          case 2:
            return '女';
          default:
            return '未知';
        }
      };
    },
  },
  created() {
    this.id = this.$route.query.id;
  },
  mounted() {
    this.getInquiryDetail();
    // this.getInquiryDetailAi();
  },
  methods: {
    isEmpty,
    // 查看问诊单
    viewInquiryDrawer(row) {
      this.inquiryInfo = row;
      this.inquiryModalVisible = true;
    },
    getInquiryDetail() {
      this.$api.getInquiryOrderDetail({ id: this.id }).then(res => {
        // console.log(res);
        this.detail = res;
        this.aiInquiryUrl = res.ai_inquiry_url;
        this.guided_record_list = res.guided_record_list;
      });
    },
    getInquiryDetailAi() {
      this.$api.getInquiryDetailAi({ id: this.id }).then(res => {
        this.detailAi = res;
      });
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .el-descriptions__body {
  background-color: transparent;
}

::v-deep .el-descriptions :not(.is-bordered) .el-descriptions-item__cell {
  padding: 10px 0 !important;
}
::v-deep .el-descriptions--small:not(.is-bordered) .el-descriptions-item__cell {
  padding-top: 10px;
  padding-bottom: 15px;
}

p {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 20px;
}

.custom {
  padding: 10px 20px;
  background-color: #f9fbfb;

  &:not(:last-child) {
    margin-bottom: 20px;
  }
}
</style>
