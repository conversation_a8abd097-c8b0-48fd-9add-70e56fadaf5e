<template>
  <div>
    <standard-table
      :data="list"
      :columns="tableCols"
      :loading="tableLoading"
      stripe
      :total="total"
      :page-size.sync="queryFormData.page_size"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form ref="searchForm" inline @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <FormItem>
              <Input v-model="queryFormData.code" clearable placeholder="处方单号/交易单号" />
            </FormItem>
            <FormItem>
              <Input v-model="queryFormData.doc_keyword" placeholder="医生姓名/手机号"> </Input>
            </FormItem>
            <FormItem>
              <Input v-model="queryFormData.pt_keyword" clearable placeholder="患者名字/手机号" />
            </FormItem>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="onResetSearch">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
            </FormItem>
          </Row>
        </Form>
        <div class="table-wrap">
          <div class="flex flex-item-between flex-item-align">
            <div class="panel-nav">
              <a
                class="nav"
                :class="{ active: !queryFormData.list_status }"
                @click.prevent.capture="onStatusChange('')"
              >
                全部
              </a>
              <a
                class="nav"
                v-for="item in inquiry_list_status"
                :key="item.key"
                :class="{ active: $route.query.list_status == item.key }"
                @click.prevent.capture="onStatusChange(item.key)"
              >
                {{ item.val.desc }}
                <Tag effect="dark" :color="getTagColor(item.key)">{{ status_count[item.key] }}</Tag>
              </a>
            </div>
          </div>
        </div>
      </template>

      <template v-slot:service_time="{ row }">
        <div>
          <span v-if="row.service_time">{{ row.service_time }}{{ row.service_time_unit_text }}</span>
          <span v-else>-</span>
        </div>
      </template>
      <template v-slot:trans_code="{ row }">
        <div>
          <span v-if="row.trans_code">{{ row.trans_code }}</span>
          <span v-else>-</span>
        </div>
      </template>
      <template v-slot:total_fee="{ row }">
        <div>
          {{ getTotalFee(row) }}
        </div>
      </template>
      <template v-slot:discount_fee="{ row }">
        <div v-text-format.percent="row.discount_fee"></div>
      </template>
      <template v-slot:payment_fee="{ row }">
        <div v-text-format.percent="row.payment_fee"></div>
      </template>
      <template v-slot:doctor_name="{ row }"
        ><div>
          <div v-if="row.type === 3">
            <div v-if="row?.doctor_name">{{ row.doctor_name }}</div>
            <div v-if="row?.doctor_team_name">
              <el-tag>{{ row.doctor_team_name }}</el-tag>
            </div>
          </div>
          <div v-else>
            <span v-if="row?.doctor_name">{{ row.doctor_name }}</span>
            <span v-else>-</span>
          </div>
        </div>
      </template>
      <template v-slot:created_at="{ row }">
        <div style="text-align: left" v-for="(item, index) in getTimeArr(row.list_status, row)" :key="index">
          {{ item.label }}：{{ row[item.value] || '-' }}
        </div>
      </template>
      <template v-slot:action="{ row }">
        <a @click="toDetail(row)">详情</a>
      </template>
    </standard-table>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from '@/libs/util';
import { tableCols } from './constant';
import { getTotalFee, getTimeArr } from './util';
import StandardTable from '@/components/StandardTable/index.vue';

const init_query_form_data = {
  page: 1,
  page_size: 20,
  list_status: '',
  doc_keyword: '',
  pt_keyword: '',
  code: '',
  kif_debug: '',
  clinic_id: '',
  uid: '',
  clinic_code: '',
};
export default {
  name: 'InquiryOrderList',
  components: { StandardTable },
  mixins: [search],
  data() {
    return {
      tableCols,
      getTotalFee,
      getTimeArr,
      queryFormData: { ...init_query_form_data },
      apiName: 'getInquiryOrderList',
      inquiry_list_status: [],
      status_count: {},
    };
  },
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getTabOption();
  },
  methods: {
    refreshList() {
      this.getsList();
    },
    onStatusChange(tab) {
      this.queryFormData.page = 1;
      this.queryFormData.list_status = tab;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    getTabOption() {
      this.$api.getInquiryOrderTabList().then(res => {
        this.inquiry_list_status = res?.inquiry_list_status;
      });
    },
    getOrderList() {
      this.$api.getInquiryOrderList().then(res => {
        this.list = res?.list;
      });
    },
    handlerListData(data) {
      this.status_count = data.status_count;
    },
    getTagColor(type) {
      switch (type) {
        case '1': // 待审核
        case '2': // 待审核
          return 'warning';
        case '3': // 已完成
          return 'success';
        case '4': // 已完成
        case '5': // 已完成
          return 'default';
        case '6': // 已完成
          return 'primary';
        default:
          // 已取消
          return 'default';
      }
    },
    toDetail(row, isShip) {
      let query = {
        code: row.code,
        id: row.id,
      };
      isShip && (query.is_ship = 1);
      this.$router.push({
        path: '/internet-hospital/inquiry-order/detail',
        query,
      });
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style scoped lang="less">
.print-checkbox {
  display: flex;
  flex-direction: column;
  padding: 10px 20px;

  ::v-deep .el-checkbox {
    margin-left: 0 !important;
    margin-right: 0 !important;
    margin-bottom: 10px;

    &:last-of-type {
      margin-bottom: 0;
    }
  }
}
.box-lv {
  display: flex;
  .box-label {
    text-align: right;
    min-width: 66px;
  }
}
</style>
