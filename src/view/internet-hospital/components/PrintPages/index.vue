<template>
  <div class="print-wrapper" ref="printPages">
    <PrintMedicalRecord v-if="shouldPrint('records')" ref="records" is-print :pres_id="pres_id"></PrintMedicalRecord>
    <prescription-note
      v-if="shouldPrint('prescription')"
      ref="prescription"
      :presCode="pres_code"
      :belong_type="belong_type"
    ></prescription-note>
    <print-delivery-note v-if="shouldPrint('deliveryNote')" ref="deliveryNote" :pres_id="pres_id"></print-delivery-note>
  </div>
</template>

<script>
import PrintMedicalRecord from '../PrintMedicalRecord';
import PrescriptionNote from '../PrescriptionNote/index.vue';
import PrintDeliveryNote from '../PrintDeliveryNote/index.vue';

export default {
  name: 'PrintAll',

  components: {
    PrintDeliveryNote,
    PrescriptionNote,
    PrintMedicalRecord,
  },
  mixins: [],

  props: {
    printCheckbox: {
      type: Array,
      default: () => [],
    },
    pres_id: {
      type: [String, Number],
      default: '',
    },
    pres_code: {
      type: String,
      default: '',
    },
    belong_type: {
      type: String,
      default: '',
    },
  },

  data() {
    return {};
  },

  computed: {
    shouldPrint() {
      return function (component) {
        return this.printCheckbox.includes(component);
      };
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    printPages() {
      let printList = [];
      this.printCheckbox.forEach(item => {
        console.log('-> %c item  ===    %o', 'font-size: 15px;color: #fa8c16 ;', item);
        console.log(this.$refs[item]);
        printList.push(this.$refs[item].getPrintInfo());
      });
      console.log(printList);
      Promise.all(printList)
        .then(res => {
          console.log(res);
          this.$print(
            this.$refs.printPages,
            {},
            () => {},
            () => {}
          );
        })
        .catch(err => {
        });
      //    this.$print(this.$refs.printPages)
    },
  },
};
</script>

<style scoped lang="less"></style>
