<template>
  <div>
    <div class="p-title">审核记录</div>
    <Table :data="recordList" :columns="tableCols" stripe style="width: 100%">
      <template v-slot:doctor="{ row }">
        <div>{{ row.doctor.name || '-' }}</div>
        <div>{{ row.doctor.operator_at || '-' }}</div>
      </template>
      <template v-slot:pharmacist="{ row }">
        <div>{{ row.pharmacist.name || '-' }}</div>
        <div>{{ row.pharmacist.operator_at || '-' }}</div>
      </template>
      <template v-slot:disp_phar="{ row }">
        <div>{{ row.disp_phar.name || '-' }}</div>
        <div>{{ row.disp_phar.operator_at || '-' }}</div>
      </template>
      <template v-slot:recheck_phar="{ row }">
        <div>{{ row.recheck_phar.name || '-' }}</div>
        <div>{{ row.recheck_phar.operator_at || '-' }}</div>
      </template>
      <template v-slot:reject_reason="{ row }">
        {{ row.reject_reason || '-' }}
      </template>
      <template v-slot:note="{ row }">
        <a @click="checkPresNote(row)">查看处方笺</a>
      </template>
    </Table>
    <Modal
      v-model="dialogVisible"
      width="1000px"
      footer-hide
      title="查看处方笺"
      class="custom-modal"
      :mask-closable="false"
      @on-cancel="dialogVisible = false"
    >
      <PrescriptionNote v-if="dialogVisible" ref="prescription" :pres-code="checkedPresCode" />
    </Modal>
  </div>
</template>

<script>
import PrescriptionNote from './PrescriptionNote';
export default {
  name: 'AuditRecord',
  components: { PrescriptionNote },
  props: {
    recordList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      checkedPresCode: '',
      dialogVisible: false,
      tableCols: [
        { title: '处方ID', key: 'pres_id', align: 'left', width: 100 },
        { title: '处方状态', key: 'status_text', align: 'left', width: 100 },
        { title: '开方医生/开方时间', slot: 'doctor', align: 'left', minWidth: 140 },
        { title: '审方药师/审核时间', slot: 'pharmacist', align: 'left', minWidth: 140 },
        { title: '调配药师/审核时间', slot: 'disp_phar', align: 'left', minWidth: 140 },
        { title: '核对药师/审核时间', slot: 'recheck_phar', align: 'left', minWidth: 140 },
        { title: '失败原因', slot: 'reject_reason', align: 'left', minWidth: 120 },
        { title: '处方笺', slot: 'note', align: 'left', minWidth: 120 },
      ],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    checkPresNote(row) {
      this.checkedPresCode = row.pres_code;
      this.dialogVisible = true;
    },
  },
};
</script>

<style lang="less" scoped>
.custom-modal {
  ::v-deep .ivu-modal-body {
    padding: 20px 30px;
    height: calc(~'100% - 110px');
    overflow-y: auto;
  }
  ::v-deep .ivu-modal {
    height: calc(~'100% - 100px') !important;
  }
  ::v-deep .ivu-modal-content {
    height: calc(~'100% - 100px');
  }
}
</style>
