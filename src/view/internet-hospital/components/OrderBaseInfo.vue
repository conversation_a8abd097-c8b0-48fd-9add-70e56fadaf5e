<template>
  <div class="base-box">
    <div class="p-title">订单详情</div>
    <div class="info-box">
      <div class="info-item flex">
        <div class="info-label">订单号</div>
        <div class="info-value">{{ orderInfo.order_code }}</div>
      </div>
      <div class="info-item flex">
        <div class="info-label">{{ isAudit ? '审核状态' : '订单状态' }}</div>
        <div class="info-value">{{ orderInfo.status_text }}</div>
      </div>
      <div class="info-item flex">
        <div class="info-label">支付状态</div>
        <div class="info-value">{{ orderInfo.pay_status_text }}</div>
      </div>
      <div class="info-item flex">
        <div class="info-label">下单时间</div>
        <div class="info-value">{{ orderInfo.create_time }}</div>
      </div>
      <div class="info-item flex">
        <div class="info-label">订单金额</div>
        <div class="info-value" v-text-format.percent="orderInfo?.total_fee"></div>
      </div>
      <div class="info-item flex">
        <div class="info-label">药品金额</div>
        <div class="info-value" v-text-format.percent="orderInfo?.medicine_fee"></div>
      </div>
      <div class="info-item flex">
        <div class="info-label">支付时间</div>
        <div class="info-value">{{ orderInfo.paid_at }}</div>
      </div>
      <div class="info-item flex">
        <div class="info-label">配送费</div>
        <div class="info-value" v-text-format.percent="orderInfo.post_fee"></div>
      </div>
      <div class="info-item flex">
        <div class="info-label">加工费</div>
        <div class="info-value" v-text-format.percent="orderInfo.decoct_fee"></div>
      </div>
      <div class="info-item flex">
        <div class="info-label">治疗服务费</div>
        <div class="info-value" v-text-format.percent="orderInfo.treatment_fee"></div>
      </div>
      <div class="info-item flex">
        <div class="info-label">实付金额</div>
        <div class="info-value" v-text-format.percent="orderInfo.payment_fee"></div>
      </div>
    </div>

    <template v-if="isShowConsigneeBox">
      <div class="p-title flex flex-item-align">
        <div>收货信息</div>
      </div>
      <div class="info-box" v-if="orderInfo.shipping_method === '1'">
        <div class="info-item flex" style="flex: 0 0 100%">
          <div class="info-label">配送方式</div>
          <div class="info-value">{{ orderInfo.shipping_method_text || '-' }}</div>
        </div>
        <div class="info-item flex">
          <div class="info-label">收货人</div>
          <div class="info-value">{{ consigneeInfo.name || '-' }}</div>
        </div>
        <div class="info-item flex">
          <div class="info-label">收货人电话</div>
          <div class="info-value">{{ consigneeInfo.mobile || '-' }}</div>
        </div>
        <div class="info-item flex">
          <div class="info-label">收货人地址</div>
          <div class="info-value">{{ consigneeInfo.address || '-' }}</div>
        </div>
      </div>
      <div class="info-box" v-if="orderInfo.shipping_method === '2'">
        <div class="info-item flex">
          <div class="info-label">提货人</div>
          <div class="info-value">{{ consigneeInfo.name || '-' }}</div>
        </div>
        <div class="info-item flex" style="width: 66%">
          <div class="info-label">手机号</div>
          <div class="info-value">{{ consigneeInfo.mobile || '-' }}</div>
        </div>
        <div class="info-item flex">
          <div class="info-label">发货时间</div>
          <div class="info-value">{{ orderInfo.shipping_at || '未发货' }}</div>
        </div>
        <div class="info-item flex">
          <div class="info-label">门店电话</div>
          <div class="info-value">{{ orderInfo.clinic_phone || '-' }}</div>
        </div>
        <div class="info-item flex">
          <div class="info-label">门店地址</div>
          <div class="info-value">{{ orderInfo.clinic_address || '-' }}</div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    orderInfo: {
      type: Object,
      default: () => ({}),
    },
    consigneeInfo: {
      type: Object,
      default: () => ({}),
    },
    isAudit: {
      type: Boolean,
      default: false,
    },
    isShowConsigneeBox: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    useToPrecision() {
      return (value, precision = 2, decimalOffset = 2) => {
        if (isNaN(value) || (!value && value !== 0)) {
          return value;
        } else {
          let formatter = new Intl.NumberFormat('en-US', {
            minimumFractionDigits: precision,
            maximumFractionDigits: precision,
          });
          return formatter.format(value / Math.pow(10, decimalOffset));
        }
      };
    },
  },
};
</script>

<style lang="less" scoped>
.shipping_tip {
  margin-left: 10px;
  font-weight: normal;
  font-size: 13px;
}
</style>
