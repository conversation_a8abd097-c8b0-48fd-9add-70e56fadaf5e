<template>
  <div>
    <standard-table
      :data="list"
      :columns="tableCols"
      :loading="tableLoading"
      stripe
      :total="total"
      :page-size.sync="queryFormData.page_size"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <FormItem>
              <Input v-model="queryFormData.pres_code" clearable placeholder="处方单号" />
            </FormItem>
            <FormItem>
              <Input v-model="queryFormData.phar_keyword" clearable placeholder="药师姓名" />
            </FormItem>
            <FormItem>
              <Input v-model="queryFormData.doc_keyword" clearable placeholder="开方医生" />
            </FormItem>
            <FormItem>
              <Input v-model="queryFormData.pt_keyword" clearable placeholder="患者姓名" />
            </FormItem>
            <FormItem>
              <Input v-model="queryFormData.user_keyword" clearable placeholder="用户/用户手机号" />
            </FormItem>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="onResetSearch">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
            </FormItem>
          </Row>
        </Form>
        <div class="table-wrap">
          <div class="flex flex-item-between flex-item-align">
            <div class="panel-nav">
              <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')">
                全部
              </a>
              <a
                class="nav"
                v-for="item in order_audit_status"
                :key="item.key"
                :class="{ active: $route.query.status == item.key }"
                @click.prevent.capture="onStatusChange(item.key)"
              >
                {{ item.val.desc }}
                <Tag :color="getTagColor(item.key)">{{ status_count[item.key] }}</Tag>
              </a>
            </div>
          </div>
        </div>
      </template>
      <template v-slot:status_text="{ row }">
        <mark-status :type="getStatusType(row.status)"
          ><span>{{ row.status_text }}</span></mark-status
        >
      </template>

      <template v-slot:user_real_name="{ row }">
        <div class="box-lv">
          <p class="box-label">用户姓名：</p>
          <p class="box-value">{{ row?.user_real_name || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">手机号：</p>
          <p class="box-value">{{ row?.user_mobile || '-' }}</p>
        </div>
      </template>

      <template v-slot:doctor="{ row }">
        <div v-if="row.doctor && row.doctor.name">
          <div>{{ row.doctor.name }}</div>
          <div>{{ row.doctor.operator_at }}</div>
        </div>
        <span v-else>-</span>
      </template>
      <template v-slot:pharmacist="{ row }">
        <div v-if="row.pharmacist && row.pharmacist.name">
          <div>{{ row.pharmacist.name }}</div>
          <div>{{ row.pharmacist.operator_at }}</div>
        </div>
        <span v-else>-</span>
      </template>
      <template v-slot:disp_phar="{ row }">
        <div v-if="row.disp_phar && row.disp_phar.name">
          <div>{{ row.disp_phar.name }}</div>
          <div>{{ row.disp_phar.operator_at }}</div>
        </div>
        <span v-else>-</span>
      </template>
      <template v-slot:recheck_phar="{ row }">
        <div v-if="row.recheck_phar && row.recheck_phar.name">
          <div>{{ row.recheck_phar.name }}</div>
          <div>{{ row.recheck_phar.operator_at }}</div>
        </div>
        <span v-else>-</span>
      </template>
      <template v-slot:action="{ row }">
        <a
          @click="toDetail(row, true)"
          v-if="hasPresPermission && (row.status === 'A_WAIT' || row.status === 'A_PHAR' || row.status === 'C_REJECT')"
          class="mr10"
        >
          {{ row.status === 'C_REJECT' ? '重新审核' : '审核' }}
        </a>
        <a @click="toDetail(row)">详情</a>
      </template>
    </standard-table>
  </div>
</template>
<script>
import search from '@/mixins/search';
import S from '@/libs/util';
import StandardTable from '@/components/StandardTable/index.vue';

const init_query_form_data = {
  page: 1,
  page_size: 20,
  keyword: '',
  pres_code: '',
  status: '',
  phar_keyword: '', //药师
  doc_keyword: '', //开方医生
  pt_keyword: '', //患者
  user_keyword: '',
  r: '',
};
export default {
  name: 'PresList',
  components: { StandardTable },
  mixins: [search],
  props: {},
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      activeName: '',
      order_audit_status: [],
      apiName: 'getHospitalAuditList',
      tableCols: [
        { title: '处方单号', key: 'pres_code', align: 'left', minWidth: 120 },
        { title: '处方来源', key: 'belong_type_text', align: 'left', minWidth: 120 },
        { title: '审核状态', slot: 'status_text', align: 'left', width: 80 },
        { title: '处方类型', key: 'pres_type_text', align: 'left', width: 80 },
        { title: '代煎方式', key: 'decoct_type_text', align: 'left', width: 80 },
        { title: '患者姓名', key: 'patient_name', align: 'left', width: 80, tooltip: true },
        { title: '用户姓名/手机号', slot: 'user_real_name', align: 'left', minWidth: 180 },
        // { title: '用户手机号', key: 'user_mobile', align: 'left', width: 120 },
        { title: '诊断', key: 'diagnosis', align: 'left', width: 130, tooltip: true },
        { title: '开方医生/开方时间', slot: 'doctor', align: 'left', minWidth: 170 },
        { title: '审方药师/审核时间', slot: 'pharmacist', align: 'left', minWidth: 170 },
        { title: '调配药师/审核时间', slot: 'disp_phar', align: 'left', minWidth: 170 },
        { title: '核对药师/审核时间', slot: 'recheck_phar', align: 'left', minWidth: 170 },
        { title: '操作', slot: 'action', align: 'center', width: 100, fixed: 'right' },
      ],
      status_count: {
        A_PASS: 0,
        A_PHAR: 0,
        A_WAIT: 0,
        CLOSE: 0,
        C_REJECT: 0,
      },
      hasPresPermission: false, // 是否存在药师审核权限
    };
  },
  computed: {
    getTagColor() {
      return type => {
        switch (type) {
          case 'A_WAIT': // 调配审核
          case 'A_PHAR': // 核对审核
            // case 'COM_W_PAY': // 省公司待付款
            return 'warning';
          // case 'WAIT_SHIP': // 待发货
          //   return 'primary';
          case 'CLOSE': // 调配驳回
          case 'C_REJECT': // 核对驳回
            return 'default';
          case 'HAS_SHIP': // 已发货
          case 'A_PASS': // 通过
            return 'success';
          default:
            // 已取消
            return 'default';
        }
      };
    },
    getStatusType() {
      return type => {
        switch (type) {
          case 'A_WAIT':
          case 'A_PHAR':
            return 'warn';
          case 'A_PASS':
            return 'success';
          case 'C_REJECT':
          case 'CLOSE':
            return 'reject';
          default:
            return 'default';
        }
      };
    },
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getHospitalOrderOption();
    this.getMemberPresPermission();
  },
  mounted() {},
  methods: {
    onStatusChange(tab) {
      this.queryFormData.page = 1;
      this.queryFormData.status = tab;
      this.submitQueryForm();
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    getHospitalOrderOption() {
      this.$api.getHospitalOrderOption().then(res => {
        this.order_audit_status = res.order_audit_status;
      });
    },
    handlerListData(data) {
      this.status_count = data.status_count;
    },
    toDetail(row, isAudit) {
      console.log('-> %c row  ===    %o', 'font-size: 15px;color: #F56C6C ;', row);
      const query = {
        order_code: row.order_code,
        order_id: row.id,
      };
      isAudit && (query.is_audit = 1);
      this.$router.push({
        path: '/internet-hospital/prescription/detail',
        query,
      });
    },
    getMemberPresPermission() {
      this.$api
        .getMemberPresPermission()
        .then(res => {
          this.hasPresPermission = res.audit_permission === '1';
          console.log('=>(detail.vue:242) res', res);
        })
        .catch(() => {});
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>
<style lang="less" scoped>
.box-lv {
  display: flex;
  .box-label {
    text-align: right;
    min-width: 66px;
  }
}
</style>
