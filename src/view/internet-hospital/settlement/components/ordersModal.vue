<template>
  <Modal
    :value="value"
    :title="title"
    width="1000px"
    @on-visible-change="changeVisible"
    footer-hide
    class="modal-wrapper"
  >
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <div class="no-wrap">
        <FormItem label="">
          <Input v-model="queryFormData.relate_code" placeholder="订单号" />
        </FormItem>

        <FormItem label="">
          <DatePicker
            type="daterange"
            clearable
            format="yyyy-MM-dd"
            placeholder="交易时间"
            v-model="times"
            @on-change="times => handleTimeChange(times)"
          ></DatePicker>
        </FormItem>

        <FormItem style="text-align: left">
          <Button type="primary" @click="onSearch" style="margin-right: 10px">筛选</Button>
          <span class="list-reset-btn" @click="onResetSearch"
          ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
          >
        </FormItem>
      </div>

    </Form>

    <Table :data="list" :columns="modalColumns" :loading="tableLoading" :height="360">
      <!-- 付款金额 -->
      <template slot-scope="{ row }" slot="payed_amount">
        <div>{{ row.payed_amount ? `￥${row.payed_amount}` : '0.00' }}</div>
      </template>

      <!-- 分账金额 -->
      <template slot-scope="{ row }" slot="sa_amount">
        <div>{{ row.sa_amount ? `+${row.sa_amount}` : '0.00' }}</div>
      </template>
    </Table>

    <div class="block_20"></div>

    <div class="page-wrapper">
      <KPage
        :total="total"
        :pageSizeOpts="[10, 20, 50]"
        :page-size.sync="+queryFormData.pageSize"
        :current.sync="+queryFormData.page"
        @on-change="onPageChange"
        style="text-align: center"
      />
      <div class="totalDesc">总金额<span style="color: red">￥{{ total_sa_amount || 0.00 }}</span> 元</div>
    </div>

  </Modal>
</template>

<script>
let init_query_form_data = {
  page: 1,
  pageSize: 10,
  st: '',
  et: '',
  relate_code: '', // 结算流水号
};

export default {
  name: 'modal',
  mixins: [],

  components: {},

  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '关联订单'
    },
    id: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      queryFormData: { ...init_query_form_data },

      modalColumns: [
        { title: '结算流水号', key: 'w_code', align: 'center' },
        { title: '订单号', key: 'relate_code', align: 'center' },
        { title: '交易流水号', key: 'trans_code', align: 'center' },
        { title: '交易时间', key: 'order_create_at', align: 'center' },
        { title: '付款金额(元)', slot: 'payed_amount', align: 'center' },
        { title: '分账金额（元)', slot: 'sa_amount', align: 'center' },
      ],

      // 如果当前需求页面有时间组件，并且需要限制选择范围
      // disabledTime: {
      //   disabledDate(date) {
      //     return date && date.valueOf() > Date.now();
      //   }
      // },
      times: [], // 时间

      list: [],
      tableLoading: false,
      total: 0,
      total_sa_amount: 0, // 分账总金额
    };
  },

  computed: {
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    init () {
      this.getSettleDetailList()
    },

    /**
     * 搜索
     * */
    onSearch: function () {
      this.queryFormData.page = 1;
      this.getSettleDetailList();
    },

    /**
     * 重置
     * */
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.times = [];
      this.getSettleDetailList();
    },

    /**
     * 分页
     * */
    onPageChange(page, pageSize) {
      console.log("=>(moneyDetailModal.vue:206) 分页");
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getSettleDetailList();
      this.scrollTop();
    },

    /**
     * 表格回归顶部
     * */
    scrollTop() {
      let overflowY = this.$el.getElementsByClassName('ivu-table-body')[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },

    /**
     * 关闭弹窗
     * */
    closeModal() {
      this.queryFormData = {...init_query_form_data}
      this.times = []
      this.scrollTop();
      this.$emit('input', false);
    },

    /**
     * 弹窗变化
     * */
    changeVisible(flag) {
      if (flag) {
        this.onSearch();
      } else {
        this.closeModal();
      }
    },

    /**
     * 当前需求如果有时间选项，用当前方法处理同步formData数据
     * */
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      console.log("=>(ordersModal.vue:206) times", times);
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },

    /**
     * @description: 获取列表
     * */
    getSettleDetailList() {
      this.tableLoading = true;
      let params = {
        id: this.id,
        ...this.queryFormData,
      };
      this.$api
        .getSettleDetailList(params)
        .then(res => {
          this.list = res.list;
          this.total = res.total;
          this.total_sa_amount = res.total_sa_amount
        })
        .finally(() => (this.tableLoading = false));
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
}
.cursor {
  cursor: pointer;
}
.mb10 {
  margin-bottom: 10px;
}
.no-wrap {
  display: flex;
  flex-wrap: wrap;
}
::v-deep .ivu-form-inline .ivu-input-wrapper, .ivu-form-inline .ivu-select {
  width: 200px;
}
.page-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;

  .totalDesc {
    margin-left: 20px;
  }
}
</style>
