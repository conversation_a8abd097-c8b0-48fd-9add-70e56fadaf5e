<template>
  <div class="f_order-wrapper global-list-box">
    <standard-table
      :loading="tableLoading"
      ref="warehouseTable"
      :columns="tableCols"
      :data="list"
      @on-select="handleSelect"
      @on-select-cancel="handleSelectCancel"
      @on-select-all="handleSelectAll"
      @on-select-all-cancel="handleCancelAll"
    >
      <template #header>
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <Col>
              <FormItem label="">
                <Input v-model="queryFormData.batch_divide_code" placeholder="结算流水号" clearable />
              </FormItem>
            </Col>

            <Col>
              <FormItem>
                <DatePicker
                  type="daterange"
                  clearable
                  format="yyyy-MM-dd"
                  placeholder="申请时间"
                  v-model="timeRange"
                  @on-change="times => handleTimeChange(times)"
                ></DatePicker>
              </FormItem>
            </Col>

            <!--        <Col>-->
            <!--          <FormItem label="">-->
            <!--            <Select v-model="queryFormData.status" clearable placeholder="结算状态">-->
            <!--              <Option v-for="item in statusDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>-->
            <!--            </Select>-->
            <!--          </FormItem>-->
            <!--        </Col>-->

            <Col>
              <FormItem label="">
                <Input v-model="queryFormData.applicant_name" placeholder="申请人" clearable />
              </FormItem>
            </Col>

            <Col>
              <FormItem style="text-align: left" class="flex">
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
                <Button type="default" class="mr10" @click="exportEvent" :loading="downloadLoading">导出</Button>
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="table-wrapper">
          <div class="panel-nav">
            <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')"> 全部 </a>
            <a
              v-for="item in withdrawalStatusDesc"
              :key="item.id"
              class="nav"
              :class="{ active: $route.query.status == item.id }"
              @click.prevent.capture="onStatusChange(item.id)"
            >
              {{ item.desc }}
            </a>
          </div>
        </div>
      </template>

      <!-- 结算流水号 -->
      <template slot-scope="{ row }" slot="code">
        <span>{{ row.code || '-' }}</span>
      </template>

      <!-- 结算金额 -->
      <template slot-scope="{ row }" slot="amount">
        <span>¥{{ row.amount || '0.00' }}</span>
      </template>

      <!-- 结算时间 -->
      <template slot-scope="{ row }" slot="settle_at">
        <span>{{ row.settle_at || '-' }}</span>
      </template>

      <!-- 审核人 -->
      <template slot-scope="{ row }" slot="operator_name">
        <span>{{ row.operator_name || '-' }}</span>
      </template>

      <!-- 操作 -->
      <template slot-scope="{ row }" slot="operate">
        <a class="mr10" @click="openDetail(row)" v-if="row.status == 2">审核</a>
        <a @click="openDetail(row, 'detail')">详情</a>
      </template>
      <template #footer>
        <div class="flex" style="width: 100%; justify-content: space-between">
          <div class="batch-box flex-1">
            <span class="ml10 batch-check"
              >已选中 <span class="num-color">{{ selectLen }}</span> 个记录</span
            >
            <div class="action-box flex">
              <Button class="ml4" type="primary" @click="SelectAll">全选</Button>
              <Button class="ml4" type="primary" @click="cancelSelectAll">重置</Button>
              <Button
                class="ml4"
                type="primary"
                :disabled="selectLen === 0"
                @click="confirmVisible = true"
                :loading="passLoading"
              >
                批量通过
              </Button>
              <Button
                class="ml4"
                type="primary"
                :disabled="selectLen === 0"
                :loading="rejectLoading"
                @click="refuseModalVisible = true"
              >
                批量驳回
              </Button>
            </div>
          </div>

          <div class="global-list-page" style="margin-left: auto">
            <KPage
              :current="+queryFormData.page"
              :page-size="+queryFormData.pageSize"
              :total="total"
              @on-change="onPageChange"
            />
          </div>
        </div>
      </template>
    </standard-table>

    <!-- 侧边栏详情 -->
    <detail-drawer v-model="detailDrawerVisible" :row="currentRow" @refresh="onSearch"></detail-drawer>

    <!-- 审核驳回 -->
    <examine-reject-modal v-model="refuseModalVisible" @success="examineReject"></examine-reject-modal>

    <!-- 审核通过 -->
    <confirm-modal
      content="通过审核"
      contentText="确认审核通过？"
      :confirmVisible.sync="confirmVisible"
      @ok="examinePass"
    ></confirm-modal>
  </div>
</template>

<script>
import S from '@/libs/util';
import search from '@/mixins/search';
import downloadExcel from '@/mixins/downloadExcel';
import detailDrawer from './components/detailDrawer.vue';
import confirmModal from '@/components/confirmModal/confirmModal';
import examineRejectModal from './components/examineRejectModal.vue';
import StandardTable from "@/components/StandardTable/index.vue";
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  batch_divide_code: '',
  st: '',
  et: '',
  applicant_name: '',
  status: '',
  r: '',
};
export default {
  name: 'list',
  mixins: [search, downloadExcel],
  components: {
    StandardTable,
    detailDrawer,
    confirmModal,
    examineRejectModal,
  },
  data() {
    return {
      apiName: 'getSettleList',
      downloadApiName: 'getWithdrawalexcelurl', // 导出
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { type: 'selection', width: 40 },
        { title: '结算流水号', slot: 'code' },
        { title: '审核状态', key: 'status_text' },
        { title: '申请人', key: 'applicant_name' },
        { title: '申请人手机号', key: 'applicant_mobile', minWidth: 50 },
        { title: '结算金额(元)', slot: 'amount', minWidth: 20 },
        { title: '结算账户', key: 'bank_card' },
        { title: '账户所属银行', key: 'bank_name', minWidth: 50 },
        { title: '申请时间', key: 'created_at' },
        { title: '审核时间', slot: 'settle_at' },
        { title: '审核人', slot: 'operator_name' },
        { title: '操作', slot: 'operate', align: 'center', width: 100 },
      ],
      tableLoading: false,
      list: [],
      total: 0,
      withdrawalStatusDesc: [],
      timeRange: [],

      // 轮询数据
      selectionList: [], // 选中的所有ids数组合集
      selectObj: {}, // 选中的所有id的对象合集
      chunkItemsLength: 0, // 切割后的轮询数组 eg: [[],[],[]]
      chunkIndex: 0, // 轮询的索引，用于判断轮询是否结束

      detailDrawerVisible: false,
      currentRow: {}, // 当前选中的row

      passLoading: false, // 审核通过
      rejectLoading: false, // 审核驳回
      confirmVisible: false,
      refuseModalVisible: false,
    };
  },
  computed: {
    selectLen() {
      return Object.keys(this.selectObj).length;
    },
  },
  watch: {},
  created() {
    this.getBalanceOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    /**
     * 获取下拉枚举
     * */
    getBalanceOptions() {
      this.$api.getSettleOptions().then(res => {
        // this.statusDesc = S.descToArrHandle(res.statusDesc);
        this.withdrawalStatusDesc = S.descToArrHandle(res.withdrawalStatusDesc);
      });
    },

    openDetail(row, type = '') {
      this.currentRow = {
        ...row,
        _clinic_type: type,
      };
      this.detailDrawerVisible = true;
    },
    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    // 导出
    exportEvent() {
      this.downloadExcel(this.queryFormData);
    },

    /************/
    /**
     * 获取列表，因为此处需要对返回数据处理，所以在使用混入后，在页面需要重构getList方法
     * */
    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(
          data => {
            this.total = +data.total;
            this.list = this.handler(data.list);
          },
          error => {
            {};
          }
        )
        .finally(() => {
          this.tableLoading = false;
        });
    },
    /**
     * 处理返回的list数据，如果有初始化需要禁用的选项，在此处就开始处理
     * */
    handler(list) {
      // 初始化禁止勾选项
      list.forEach(item => {
        if (item.status != 2) {
          item._disabled = true;
        } else {
          item._disabled = false;
        }
      });

      // 每次请求回显已经勾选的数据
      for (let k in list) {
        for (let j in this.selectObj) {
          if (list[k].id == this.selectObj[j].id) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }
      }
      return list;
    },

    /***
     * ============= start 组件事件的勾选操作 start ==============
     * */
    //选中项发生变化时就会触发
    handleSelect(selection, row) {
      this.$set(this.selectObj, row.id, row);
    },
    //选中项发生变化时就会触发
    handleSelectCancel(selection, row) {
      this.$delete(this.selectObj, row.id);
    },

    //点击全选时触发
    handleSelectAll(selection) {
      selection.forEach(item => {
        if (item._disabled === false) {
          this.$set(this.selectObj, item.id, item);
        }
      });
    },
    // 返选时触发
    handleCancelAll(selection) {
      for (let k in this.list) {
        if (this.list[k].id in this.selectObj) {
          this.$delete(this.selectObj, this.list[k].id);
        }
      }
    },

    /***
     * ============= end 组件事件的勾选操作 end ==============
     * */

    /**
     * 手动全部勾选
     * */
    SelectAll() {
      this.$refs.warehouseTable.selectAll(true);
      this.handleSelectAll(this.list);
    },

    /**
     * 手动重置勾选
     * */
    cancelSelectAll() {
      this.$refs.warehouseTable.selectAll(false);
      this.selectObj = {};
    },

    /**
     * 批量轮询
     * 传入 row 对单个数据进行处理，不传数据，则从selectObj开启批量轮询
     * */
    examineBatch(row, status = '', reject_reason = '') {
      this.selectionList = this.getAuditOrderIds(row, status, reject_reason);
      // 轮询开始,loading开始
      if (status == '1') {
        this.passLoading = true;
      }
      if (status == '3') {
        this.rejectLoading = true;
      }
      // 切割数据
      let chunkItems = this.$lodash.chunk(this.selectionList, 20);
      this.chunkItemsLength = chunkItems.length;
      if (this.chunkIndex < +this.chunkItemsLength) {
        this.getSettleBatchaudit(chunkItems[this.chunkIndex], status, reject_reason);
      }
    },

    /**
     * 过滤选中的ids
     * */
    getAuditOrderIds(row) {
      if (row) {
        return [row.id];
      } else {
        return Object.values(this.selectObj).map(item => item.id);
      }
    },

    /**
     * 初始化轮询参数
     * */
    initBatch() {
      this.selectObj = {};
      this.chunkIndex = 0;
      this.chunkItemsLength = 0;
      this.selectionList = [];
      this.passLoading = false;
      this.rejectLoading = false;
    },

    /**
     * api -- 开始轮询接口
     * */
    getSettleBatchaudit(ids, status = '', reject_reason = '') {
      let params = {
        id: ids,
        status,
        reject_reason,
      };
      this.$api
        .getSettleBatchaudit(params)
        .then(res => {
          this.chunkIndex++;
          if (this.chunkIndex === this.chunkItemsLength) {
            this.getsList();
            let tip_text = status == 1 ? '审核通过' : '驳回成功';
            this.$Message.success(tip_text);
            // 所有轮询结束，初始化轮询数据
            this.initBatch();
          } else {
            // 轮询未结束，继续轮询api
            this.examineBatch(undefined, (status = ''), reject_reason);
          }
        })
        .catch(error => {
          // 处理异常终止
          this.passLoading = true;
          this.rejectLoading = true;
          {};
        });
    },

    /**
     * 审核驳回
     * */
    examineReject(reason = '') {
      this.examineBatch(undefined, '3', reason);
    },

    /**
     * 审核通过
     * */
    examinePass() {
      this.examineBatch(undefined, '1');
      this.confirmVisible = false;
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
  filters: {},
};
</script>
<style lang="less" scoped>
.ml4 {
  margin-left: 4px;
}
.batch-box {
  display: flex;
  align-items: center;
}
::v-deep .ivu-form-inline .ivu-input-wrapper,
.ivu-form-inline .ivu-select {
  width: 200px;
}
:deep(.ivu-form-item-content) {
  display: flex;
}
</style>
