<template>
  <div>
    <div class="flex">
      <div class="block-card flex-1 pb40">
        <div class="block-title">
          分账待结算(元)
          <Tooltip content="分账交易未完成(进行中/待结算)的订单总金额">
            <Icon type="ios-help-circle" class="ml10 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
        </div>
        <div class="block-money mt25 ml-7" v-text-format.number="account.divide_wait_settle_balance"></div>
      </div>
      <div class="block-card flex-1 ml20 pb40">
        <div class="block-title">
          分账可结算(元)
          <Tooltip content="申请结算需向平台提供不低于申请金额的发票">
            <Icon type="ios-help-circle" class="ml10 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
        </div>
        <div class="flex flex-item-align mt25 ml-7">
          <div class="block-money" v-text-format.number="account.divide_prepare_settle_balance"></div>
          <Button type="primary" class="ml20" @click="showRelatedModal">申请结算</Button>
        </div>
      </div>
      <div class="block-card flex-1 ml20 pb40">
        <div class="block-title">
          分账结算中(元)
          <Tooltip content="等待平台审核通过后，T+1后会将申请金额流转到互医资产可用余额">
            <Icon type="ios-help-circle" class="ml10 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
        </div>
        <div class="block-money mt25 ml-7" v-text-format.number="account.divide_during_settle_balance"></div>
      </div>
      <div class="block-card flex-1 ml20 pb40">
        <div class="block-title">
          分账已结算(元)
          <Tooltip content="在互医资产可用余额中申请提现到诊所结算账户中">
            <Icon type="ios-help-circle" class="ml10 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
        </div>
        <div class="flex flex-item-align mt25 ml-7">
          <div class="block-money" v-text-format.number="account.divide_finish_amount"></div>
          <Button type="primary" class="ml20" @click="checkFinishedRecord('')">申请记录</Button>
        </div>
      </div>
    </div>
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch" class="mt20">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.order_code" placeholder="订单号" style="width: 180px"></Input>
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.batch_divide_code" placeholder="结算流水号" style="width: 180px"></Input>
        </FormItem>
        <FormItem>
          <DatePicker
            type="daterange"
            :options="dateOptions"
            v-model="dealTimeRange"
            clearable
            @on-change="times => handleTimeChange(times)"
            placeholder="交易时间"
          ></DatePicker>
        </FormItem>
        <FormItem>
          <DatePicker
            type="daterange"
            :options="dateOptions"
            v-model="settleTimeRange"
            clearable
            @on-change="times => handleTimeChange(times, 'settle_st', 'settle_et')"
            placeholder="结算时间"
          ></DatePicker>
        </FormItem>
        <FormItem>
          <Button type="primary" @click="onSearch">筛选</Button>
          <Dvd />
          <Dvd />
          <Button type="default" @click="exportEvent" :loading="downloadLoading">导出</Button>
          <Dvd />
          <Dvd />
          <span class="list-reset-btn" @click="onResetSearch">
            <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
            <span>清除条件</span>
          </span>
        </FormItem>
      </Row>
    </Form>
    <div class="flex flex-item-between flex-item-align mt20">
      <div class="panel-nav">
        <a class="nav" :class="{ active: !queryFormData.divide_status }" @click.prevent.capture="onStatusChange('')">
          全部
        </a>
        <a
          class="nav"
          v-for="item in divideStatusDesc"
          :key="item.id"
          :class="{ active: queryFormData.divide_status == item.id }"
          @click.prevent.capture="onStatusChange(item.id)"
        >
          {{ item.desc }}
        </a>
      </div>
    </div>
    <Table :loading="tableLoading" :columns="tableCols" :data="list">
      <template v-slot:mch_order_code="{ row }">
        <KLink
          v-if="row.order_detail?.is_clinic === '1'"
          :to="{
            path: '/internet-hospital/order/detail',
            query: { order_code: row.order_detail?.relate_code, order_id: row.order_detail?.relate_id },
          }"
          target="_blank"
          >{{ row.mch_order_code }}</KLink
        >
        <div v-else>{{ row.mch_order_code }}</div>
      </template>
      <template v-slot:order_amount="{ row }"> ￥{{ row.order_amount }} </template>
      <template v-slot:amount="{ row }">
        ￥{{ row.amount }}
        <Tooltip v-if="row.wallet_bill_type === '1'" max-width="500" theme="light">
          <div slot="content">
            <Table :columns="relatedTableCols" :data="row.sa_detail">
              <template slot-scope="{ row }" slot="ratio"> {{ row.ratio }}%</template>
              <template slot-scope="{ row }" slot="amount"> ￥{{ row.amount }}</template>
            </Table>
          </div>
          <Icon type="md-alert" class="cursor" color="#9FA6C1" size="18" />
        </Tooltip>
      </template>
      <template v-slot:batch_divide_code="{ row }">
        <a v-if="row.batch_divide_code" @click="checkFinishedRecord(row.batch_divide_code)">{{
          row.batch_divide_code
        }}</a>
        <div v-else>-</div>
      </template>
      <template v-slot:divide_status_desc="{ row }">
        {{ row.divide_status_desc }}
        <Tooltip
          v-if="row.divide_status === '3' || row.divide_status === '6'"
          content="等待平台审核通过后，D+1个工作日会将申请金额流转到互医资产可用余额。"
        >
          <Icon type="md-alert" class="cursor" color="#9FA6C1" size="18" />
        </Tooltip>
      </template>
      <template v-slot:settle_at="{ row }">
        {{ row.settle_at || '-' }}
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: right"
    />
    <select-related-order-modal
      v-model="relatedVisible"
      :is-check-detail="isCheckDetail"
      @refresh="refresh"
    ></select-related-order-modal>
    <submit-settle-modal
      isCheckRecord
      v-model="submitVisible"
      :batchDivideCode="currentBatchDivideCode"
    ></submit-settle-modal>
  </div>
</template>

<script>
import S from '@/libs/util';
import moment from 'moment/moment';
import selectRelatedOrderModal from './components/selectRelatedOrderModal.vue';
import submitSettleModal from './components/submitSettleModal.vue';
import downloadExcel from '@/mixins/downloadExcel';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  st: '', // 交易时间开始
  et: '', // 交易时间截止
  settle_st: '', // 结算时间开始
  settle_et: '', // 结算时间截止
  batch_divide_code: '', // 结算流水号
  order_code: '', // 订单号
  divide_status: '', // 结算状态
};
export default {
  name: 'account-detail',
  mixins: [downloadExcel],
  components: { selectRelatedOrderModal, submitSettleModal },
  props: {},
  data() {
    return {
      submitVisible: false,
      account: {},
      queryFormData: { ...init_query_form_data },
      divideStatusDesc: [],
      tableCols: [
        { title: '订单号', slot: 'mch_order_code', align: 'center' },
        { title: '交易流水号', key: 'code', align: 'center' },
        { title: '交易时间', key: 'created_at', align: 'center', width: 180 },
        { title: '付款金额（元）', slot: 'order_amount', align: 'center' },
        { title: '分账金额（元）', slot: 'amount', align: 'center' },
        { title: '结算状态', slot: 'divide_status_desc', align: 'center', width: 150 },
        { title: '结算流水号', slot: 'batch_divide_code', align: 'center' },
        { title: '结算时间', slot: 'settle_at', align: 'center', width: 180 },
      ],
      total: 0,
      list: [],
      tableLoading: false,
      downloadLoading: false,
      dateOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: '今天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              return [start, end];
            },
          },
          {
            text: '昨天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return [start, start];
            },
          },
          {
            text: '近七天',
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [start, end];
            },
          },
          {
            text: '近三十天',
            value() {
              const start = moment().subtract(30, 'days').toDate();
              const end = moment().toDate();
              // start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [start, end];
            },
          },
        ],
      },
      dealTimeRange: [],
      settleTimeRange: [],
      symbolDesc: [],
      bizTypeDesc: [],
      relatedTableCols: [
        { title: '分账角色', key: 'payee_type_text', align: 'center', width: 100 },
        { title: '分账名称', key: 'payee_name', align: 'center', width: 100 },
        { title: '分账比例', slot: 'ratio', align: 'center', width: 100 },
        { title: '分账金额', slot: 'amount', align: 'center', width: 100 },
      ],
      relatedVisible: false,
      isCheckDetail: false,
      allowShowRelatedModal: false,
      downloadApiName: 'exportHospitalBalance', // 导出
      currentBatchDivideCode: '',
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getBlanceBlance();
    this.getBlanceOptions();
    this.getHospitalBalanceList();
  },
  methods: {
    // api-账户余额
    getBlanceBlance() {
      this.$api.getBlanceBlance({ sys_code: 'CDHY' }).then(
        res => {
          this.account = res.account;
          if (this.$route.query.apply && this.allowShowRelatedModal) {
            this.showRelatedModal();
            this.allowShowRelatedModal = false;
          }
        },
      );
    },
    //  api-获取枚举
    getBlanceOptions() {
      this.$api
        .getBlanceOptions({ sys_code: 'CDHY' })
        .then(
          res => {
            this.divideStatusDesc = S.descToArrHandle(res.divideStatusDesc);
            this.symbolDesc = S.descToArrHandle(res.walletBillTypeDesc);
            this.bizTypeDesc = S.descToArrHandle(res.bizTypeDesc);
          },
        )
        .catch(() => {});
    },

    // api-获取分账明细列表
    getHospitalBalanceList() {
      this.tableLoading = true;
      let params = this.queryFormData;
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getHospitalBalanceList(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.list = res.list;
          this.total = res.total;
        })
        .catch(err => {})
        .finally(() => (this.tableLoading = false));
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.dealTimeRange = [];
      this.settleTimeRange = [];
      this.getHospitalBalanceList();
    },

    exportEvent() {
      this.downloadExcel(this.queryFormData);
    },

    onSearch() {
      this.queryFormData.page = 1;
      this.getHospitalBalanceList();
    },

    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getHospitalBalanceList();
    },

    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },

    onStatusChange(status) {
      this.queryFormData.divide_status = status;
      this.queryFormData.page = 1;
      this.getHospitalBalanceList();
    },

    showRelatedModal() {
      this.isCheckDetail = false;
      this.relatedVisible = true;
    },
    checkFinishedRecord(batch_divide_code) {
      this.currentBatchDivideCode = batch_divide_code || '';
      this.submitVisible = true;
    },
    refresh() {
      this.getBlanceBlance();
      this.getHospitalBalanceList();
    },
  },
  beforeRouteEnter(to, from, next) {
    console.log('=>(account-detail.vue:337) to, from, next', from);
    next(vm => {
      if (from.path === '/internet-hospital/property/balance') {
        vm.allowShowRelatedModal = true;
      }
    });
  },
};
</script>
<style scoped lang="less">
.block-card {
  position: relative;
  background: #f7f8fa;
  border-radius: 4px;
  padding: 16px 24px;

  .block-title {
    font-size: 16px;
    font-weight: 400;
    color: #767c96;
    line-height: 22px;
  }

  .block-money {
    position: relative;
    font-size: 38px;
    font-weight: normal;
    color: #757c98;
    line-height: 42px;
  }

  .block-button {
    font-size: 16px;
    //position: absolute;
    //right: 0;
    //bottom: 0;
  }

  .block-other {
    position: absolute;
    bottom: 0;
    font-size: 16px;
    font-weight: normal;
    color: #757c98;
    line-height: 42px;
  }

  .num-record {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 15px;
  }
}

.block-title-tip {
  font-size: 12px !important;
  font-weight: 300 !important;
  color: #999999;
  line-height: 17px;
  margin-top: 1px;
}

.line {
  width: 1px;
  height: 100px;
  background: #eeeeee;
  margin-right: 30px;
}

.ml10 {
  margin-left: 10px;
}

.ml20 {
  margin-left: 20px;
}

.ml-7 {
  margin-left: -7px;
}

.mt12 {
  margin-top: 12px;
}

.mt20 {
  margin-top: 20px;
}

.mt25 {
  margin-top: 25px;
}

.pb40 {
  padding-bottom: 40px !important;
}

.cursor {
  cursor: pointer;
}
</style>
