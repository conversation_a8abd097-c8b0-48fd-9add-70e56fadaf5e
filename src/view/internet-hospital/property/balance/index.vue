<template>
  <div>
    <Alert type="warning" v-if="isyztopen === '0'">
      为了保障资金安全，需要先通过主体认证，方可使用。
      <Button size="small" type="primary" @click="$router.push('/internet-hospital/subject-auth/index')">去认证</Button>
      <!-- <template slot="desc">Content of prompt. Content of prompt. Content of prompt. Content of prompt. </template> -->
    </Alert>
    <div class="flex">
      <div class="block-card flex-1 pb40">
        <div class="block-title">
          可用余额(元)
          <Tooltip content="可提现金额">
            <Icon type="ios-help-circle" class="ml10 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
        </div>
        <div class="block-money mt25 ml-7" v-text-format.number="account.available_balance"></div>
        <div class="block-other" v-if="+account.settle_pay_fee_balance > 0" style="color: #e83b3b">
          <span>待支出通道费：</span>
          <span v-text-format.number="account.settle_pay_fee_balance" />
        </div>
      </div>

      <div class="block-card flex-1 ml20 pb40">
        <div class="block-title">
          待结算金额(元)
          <Tooltip max-width="320">
            <div slot="content">
              <p>交易未完成(进行中/待结算)的订单</p>
              <p>作为发药诊所的订单和诊所商品订单在确认收货后且同时满足T+1后会自动转成可用余额；</p>
              <p>
                待结算会包含作为诊所商品订单、售卖药品的订单和分账未结算的订单；分账未结算包括分账待结算、分账可结算、分账结算中的资金
              </p>
            </div>
            <Icon type="ios-help-circle" class="ml10 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
        </div>
        <div class="flex flex-item-between flex-item-align mt25 ml-7">
          <div class="block-money" v-text-format.number="account.settle_balance"></div>
          <a class="block-button" @click="jumpToAccountDetail">分账明细</a>
        </div>
        <div class="block-other">
          其中有{{ account.divide_prepare_settle_balance }}元需要<a @click="toSettle">手动申请结算</a>
        </div>
      </div>

      <div class="block-card flex-1 ml20 pb40">
        <div class="block-title">
          不可用余额(元)
          <Tooltip content="冻结中、提现中或退款中的冻结金额">
            <Icon type="ios-help-circle" class="ml10 cursor" color="#9FA6C1" size="18" />
          </Tooltip>
          <!--          <Tooltip content="未定">-->
          <!--            <Icon type="ios-help-circle" class="ml10 cursor" color="#9FA6C1" size="18" />-->
          <!--          </Tooltip>-->
        </div>
        <div class="block-money mt25 ml-7" v-text-format.number="account.disable_balance"></div>
      </div>
    </div>

    <div class="block-header flex flex-item-align">
      <span>交易记录</span>
      <span class="block-title-tip">订单记录可能存在5-15分钟延迟情况</span>
    </div>

    <Form :label-width="10" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <Col>
          <FormItem>
            <DatePicker
              type="daterange"
              :options="dateOptions"
              v-model="timeRange"
              clearable
              @on-change="times => handleTimeChange(times)"
              placeholder="交易时间"
              style="width: 200px"
            ></DatePicker>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Input v-model="queryFormData.code" placeholder="交易流水号" style="width: 180px"></Input>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Select v-model="queryFormData.wallet_bill_type" placeholder="收支类型" clearable style="width: 180px">
              <Option v-for="(desc, status) in symbolDesc" :key="desc.kw" :value="desc.id">{{ desc.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Select v-model="queryFormData.mch_bill_type" placeholder="业务类型" clearable style="width: 180px">
              <Option v-for="(desc, status) in bizTypeDesc" :key="desc.kw" :value="desc.id">{{ desc.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col>
          <FormItem>
            <Button type="primary" @click="onSearch">筛选</Button>
            <Dvd />
            <Dvd />
            <Button type="default" @click="exportEvent" :loading="downloadLoading">导出</Button>
            <Dvd />
            <Dvd />
            <span class="list-reset-btn" @click="onResetSearch">
              <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
              <span>清除条件</span>
            </span>
          </FormItem>
        </Col>
      </Row>
    </Form>

    <div class="block-card flex">
      <div class="flex-1">
        <div class="block-title">收入项</div>
        <div class="block-money ml-7 mt12" v-text-format.number="statistics.incr_amount_string"></div>
        <div class="num-record mt12">交易笔数：{{ statistics.incr_count || 0 }}</div>
      </div>

      <p class="line"></p>

      <div class="flex-1">
        <div class="block-title">支出项</div>
        <div class="block-money ml-7 mt12" v-text-format.number="statistics.decr_amount_string"></div>
        <div class="num-record mt12">交易笔数：{{ statistics.decr_count || 0 }}</div>
      </div>
    </div>

    <div class="flex flex-item-between flex-item-align mt20">
      <div class="panel-nav">
        <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')"> 全部 </a>
        <a
          class="nav"
          v-for="item in statusDesc"
          :key="item.id"
          :class="{ active: $route.query.status == item.id }"
          @click.prevent.capture="onStatusChange(item.id)"
        >
          {{ item.desc }}
        </a>
      </div>
    </div>
    <Table :loading="tableLoading" :columns="tableCols" :data="list">
      <template slot-scope="{ row }" slot="mch_order_code">
        <KLink v-if="row.order_detail?.is_clinic === '1'" :to="getToOrderDetailRoute(row)" target="_blank"
          >{{ row.mch_order_code }}
        </KLink>
        <div v-else>{{ row.mch_order_code }}</div>
      </template>
      <template slot-scope="{ row }" slot="created_at">
        <!-- {{ row.created_at | data_format }} -->
        {{ row.created_at || '-' }}
      </template>
      <template slot-scope="{ row }" slot="order_amount">
        ￥{{ row.order_amount || '-' }}
        <Tooltip v-if="row.wallet_bill_type === '1' && row.sa_detail.length > 0" max-width="500" theme="light">
          <div slot="content">
            <Table :columns="relatedTableCols" :data="row.sa_detail">
              <template slot-scope="{ row }" slot="ratio"> {{ row.ratio }}%</template>
              <template slot-scope="{ row }" slot="amount"> ￥{{ row.amount }}</template>
            </Table>
          </div>
          <Icon type="md-alert" class="cursor" color="#9FA6C1" size="18" />
        </Tooltip>
      </template>

      <template slot-scope="{ row }" slot="amount">
        <div v-if="row.amount">
          <div v-if="row.wallet_bill_type === '2'">
            <span
              ><span style="color: red">-{{ Number(row.amount).toFixed(2) }}</span></span
            >
          </div>
          <div v-else>
            <span
              ><span style="color: green">+{{ Number(row.amount).toFixed(2) }}</span></span
            >
          </div>
        </div>
        <div v-else>-</div>
        <!-- {{ row.amount ? `￥${row.wallet_bill_type === '2' ? '-' : '+'}${row.amount}` : '-' }} -->
      </template>

      <template slot-scope="{ row }" slot="to_mch_name">
        {{ row.to_mch_name || '-' }}
      </template>

      <template v-slot:status_desc="{ row }">
        <!-- <status-text status="10">{{ row.status_desc }}</status-text> -->
        <mark-status :type="getStatusTextColor(row.status)">{{ row.status_desc || '-' }}</mark-status>
      </template>

      <!-- 操作 -->
      <template slot-scope="{ row }" slot="action">
        <a @click="toDetail(row.code)">详情</a>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: right"
    />
    <select-related-order-modal v-model="relatedVisible"></select-related-order-modal>
  </div>
</template>

<script>
import search from '@/mixins/search';
import downloadExcel from '@/mixins/downloadExcel';
import moment from 'moment';
import S from '@/libs/util';
import SelectRelatedOrderModal from './components/selectRelatedOrderModal.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  st: moment().subtract(30, 'days').format('YYYY-MM-DD'),
  et: moment().format('YYYY-MM-DD'),
  trade_code: '',
  wallet_bill_type: '',
  biz_type: '',
  status: '',
  sys_code: 'CDHY',
};
export default {
  name: 'list',
  components: { SelectRelatedOrderModal },
  mixins: [search, downloadExcel],
  props: {},
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getBlanceList',
      downloadApiName: 'getBlanceExport', // 导出
      symbolDesc: [],
      bizTypeDesc: [],
      statusDesc: [],
      relatedVisible: false,
      dateOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: '今天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              return [start, end];
            },
          },
          {
            text: '昨天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return [start, start];
            },
          },
          {
            text: '近七天',
            value() {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [start, end];
            },
          },
          {
            text: '近三十天',
            value() {
              const start = moment().subtract(30, 'days').toDate();
              const end = moment().toDate();
              // start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [start, end];
            },
          },
        ],
      },
      timeRange: [],
      tableCols: [
        {
          title: '订单号',
          slot: 'mch_order_code',
          align: 'center',
        },
        {
          title: '流水号',
          key: 'mch_bill_code',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.mch_bill_code || '-'),
        },
        {
          title: '交易流水号',
          key: 'code',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.code || '-'),
        },
        { title: '交易时间', slot: 'created_at', align: 'center' },
        {
          title: '业务类型',
          key: 'mch_bill_type_text',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.mch_bill_type_text || '-'),
        },
        { title: '收支类型', key: 'wallet_bill_type_desc', align: 'center' },
        { title: '交易金额（元）', slot: 'order_amount', align: 'center', width: 120 },
        // { title: '交易手续费（元）', key: 'fee_amount', align: 'center' },
        { title: '收支金额（元）', slot: 'amount', align: 'center' },
        { title: '对方名称', slot: 'to_mch_name', align: 'center' },
        { title: '处理状态', slot: 'status_desc', align: 'center' },
      ],
      list: [],
      tableLoading: false,

      account: {
        balance: '',
        freeze: '',
      },

      statistics: {
        incr_amount: '',
        incr_count: '',
        decr_amount: '',
        decr_count: '',
      },
      isyztopen: '',
      relatedTableCols: [
        { title: '分账角色', key: 'payee_type_text', align: 'center', width: 100 },
        { title: '分账名称', key: 'payee_name', align: 'center', width: 100 },
        { title: '分账比例', slot: 'ratio', align: 'center', width: 100 },
        { title: '分账金额', slot: 'amount', align: 'center', width: 100 },
      ],
    };
  },
  computed: {
    getStatusTextColor() {
      return status => {
        switch (status) {
          case '3':
            return 'success';
          case '1':
            return 'warn';
          case '2':
            return 'gray';
          case '4':
            return 'reject';
        }
      };
    },
  },
  watch: {},
  created() {},

  activated() {
    this.init();
  },
  mounted() {
    const isDev = process.env.VUE_APP_CMD === 'serve';
    isDev && this.init();
  },
  methods: {
    init() {
      this.getBlanceOptions();
      this.getBlanceBlance();
      this.getIsyztopen();
      this.queryFormData.st = moment().subtract(30, 'days').format('YYYY-MM-DD');
      this.queryFormData.et = moment().format('YYYY-MM-DD');
    },

    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    // 详情
    toDetail(code) {
      this.$router.push({
        path: '/property/balance/detail',
        query: { code },
      });
    },

    // 导出
    exportEvent() {
      this.downloadExcel(this.queryFormData);
    },

    onStatusChange(status) {
      this.queryFormData.status = status;
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },

    handleTimeChange(times) {
      this.queryFormData.st = (times && times[0]) || '';
      this.queryFormData.et = (times && times[1]) || '';
    },

    // api-账户余额
    getBlanceBlance() {
      this.$api.getBlanceBlance({ sys_code: 'CDHY' }).then(
        res => {
          this.account = res.account;
        },
      );
    },

    getIsyztopen() {
      this.$api.getIsyztopen({ sys_code: 'CDHY' }).then(res => {
        this.isyztopen = res.is_yzt_open;
      });
    },

    //  api-获取枚举
    getBlanceOptions() {
      this.$api
        .getBlanceOptions({ sys_code: 'CDHY' })
        .then(
          res => {
            this.statusDesc = S.descToArrHandle(res.statusDesc);
            this.symbolDesc = S.descToArrHandle(res.walletBillTypeDesc);
            this.bizTypeDesc = S.descToArrHandle(res.bizTypeDesc);
            this.queryFormData = S.merge(this.queryFormData, this.$route.query);
            this.submitQueryForm(true);
          },
        )
        .catch(() => {});
    },

    jumpToAccountDetail() {
      this.$router.push({
        path: '/internet-hospital/property/balance/account-detail',
      });
    },
    toSettle() {
      this.$router.push({
        path: '/internet-hospital/property/balance/account-detail',
        query: { apply: true },
      });
    },
    getToOrderDetailRoute(row) {
      if (row.mch_bill_type === 'HOSP_P_INQUIRY') {
        return {
          path: '/internet-hospital/inquiry-order/detail',
          query: { code: row.order_detail?.relate_code, id: row.order_detail?.relate_id },
        };
      }
      if (row.mch_bill_type === 'HOSP_P_PRES_ORDER') {
        return {
          path: '/internet-hospital/order/detail',
          query: { order_code: row.order_detail?.relate_code, order_id: row.order_detail?.relate_id },
        };
      }
    },
  },
  filters: {},
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.block-card {
  position: relative;
  background: #f7f8fa;
  border-radius: 4px;
  padding: 16px 24px;

  .block-title {
    font-size: 16px;
    font-weight: 400;
    color: #767c96;
    line-height: 22px;
  }

  .block-money {
    position: relative;
    font-size: 38px;
    font-weight: normal;
    color: #757c98;
    line-height: 42px;
  }

  .block-button {
    font-size: 16px;
    //position: absolute;
    //right: 0;
    //bottom: 0;
  }

  .block-other {
    position: absolute;
    bottom: 0;
    font-size: 16px;
    font-weight: normal;
    color: #757c98;
    line-height: 42px;
  }

  .num-record {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 15px;
  }
}

.block-title-tip {
  font-size: 12px !important;
  font-weight: 300 !important;
  color: #999999;
  line-height: 17px;
  margin-top: 1px;
}

.line {
  width: 1px;
  height: 100px;
  background: #eeeeee;
  margin-right: 30px;
}

.ml10 {
  margin-left: 10px;
}

.ml20 {
  margin-left: 20px;
}

.ml-7 {
  margin-left: -7px;
}

.mt12 {
  margin-top: 12px;
}

.mt20 {
  margin-top: 20px;
}

.mt25 {
  margin-top: 25px;
}

.pb40 {
  padding-bottom: 40px !important;
}

.cursor {
  cursor: pointer;
}
</style>
