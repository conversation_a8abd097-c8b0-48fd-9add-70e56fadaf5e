<template>
  <div>
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.batch_divide_code" placeholder="结算流水号" />
        </FormItem>
        <FormItem>
          <DatePicker
            type="daterange"
            clearable
            :options="disabledTime"
            format="yyyy-MM-dd"
            placeholder="申请时间"
            v-model="times"
            @on-change="times => handleTimeChange(times, 'st', 'et')"
          ></DatePicker>
        </FormItem>
        <FormItem style="text-align: left">
          <Button type="primary" @click="onSearch" style="margin-right: 10px">筛选</Button>
          <span class="list-reset-btn" @click="onResetSearch">
            <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
            <span>清除条件</span>
          </span>
        </FormItem>
      </Row>
    </Form>

    <Table :data="list" :columns="tableColumns" :loading="tableLoading" :height="350">
      <template slot-scope="{ row }" slot="created_at"> {{ row.created_at || '-' }}</template>
      <template slot-scope="{ row }" slot="amount"> ￥{{ row.amount }}</template>
      <!--  结算状态    -->
      <template slot-scope="{ row }" slot="divide_status_desc">
        {{ row.divide_status_desc }}
      </template>
      <!--  审核状态    -->
      <template slot-scope="{ row }" slot="status_text">
        {{ row.status_text }}
        <Tooltip
          v-if="row.status === '2'"
          content="等待平台审核通过后，D+1个工作日会将申请金额流转到互医资产可用余额。"
        >
          <Icon type="md-alert" class="cursor" color="#9FA6C1" size="18" />
        </Tooltip>
      </template>
      <!--  审核时间    -->
      <template slot-scope="{ row }" slot="settle_at"> {{ row.settle_at || '-' }}</template>
      <!--  结算时间    -->
      <template slot-scope="{ row }" slot="divide_at"> {{ row.divide_at || '-' }}</template>
      <template slot-scope="{ row }" slot="note">
        <div v-if="row.note">
          <Tooltip placement="top-end" :content="row.note">
            <div class="ecs">{{ row.note }}</div>
          </Tooltip>
        </div>
        <div v-else>-</div>
      </template>
      <template slot-scope="{ row }" slot="action">
        <a class="mr-12" @click="checkRelatedOrder(row)">关联订单</a>
        <a @click="checkApplyDetail(row)">申请详情</a>
      </template>
    </Table>
    <div class="block_20"></div>
    <KPage
      :total="total"
      :pageSizeOpts="[10, 20, 50]"
      :page-size.sync="+queryFormData.pageSize"
      :current.sync="+queryFormData.page"
      @on-change="onPageChange"
      style="text-align: center"
    />
    <related-orders-modal v-model="relatedVisible" :id="recordId"></related-orders-modal>
    <apply-detail :expressCompanys="expressCompanys" v-model="applyVisible" :apply-id="applyId"></apply-detail>
  </div>
</template>

<script>
import RelatedOrdersModal from './relatedOrdersModal.vue';
import ApplyDetail from './applyDetail.vue';
const init_query_form_data = {
  page: 1,
  pageSize: 10,
  name: '',
  st: '',
  et: '',
  bill_code: '',
  applicant_name: '',
  status: '',
  clinic_id: '',
};
export default {
  name: 'ApplyRecord',
  props: {
    expressCompanys: {
      type: Array,
      default: () => [],
    },
    batchDivideCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      key: 'value',
      list: [],
      queryFormData: {
        ...init_query_form_data,
      },
      relatedVisible: false,
      applyVisible: false,
      recordId: '',
      applyId: '',
      tableLoading: false,
      // 申请结算记录
      tableColumns: [
        { title: '结算流水号', key: 'code', align: 'center', width: 220 },
        { title: '申请时间', slot: 'created_at', align: 'center', width: 180 },
        { title: '结算金额（元）', slot: 'amount', align: 'center', width: 150 },
        { title: '结算状态', slot: 'divide_status_desc', align: 'center', width: 80 },
        { title: '审核状态', slot: 'status_text', align: 'center', width: 150 },
        { title: '审核时间', slot: 'settle_at', align: 'center', width: 180 },
        { title: '结算时间', slot: 'divide_at', align: 'center', width: 180 },
        { title: '备注', slot: 'note', align: 'center', width: 100 },
        { title: '操作', slot: 'action', align: 'center', width: 150, fixed: 'right' },
      ],
      total: 0,
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      times: [],
    };
  },
  components: {
    RelatedOrdersModal,
    ApplyDetail,
  },
  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.getApplyList();
    },
    getApplyList() {
      this.tableLoading = true;
      this.$api
        .getApplySettleList(this.queryFormData)
        .then(res => {
          this.list = res.list;
          this.total = res.total;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getApplyList();
    },
    /**
     * 重置
     * */
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.times = [];
      this.getApplyList();
    },
    checkRelatedOrder(row) {
      this.relatedVisible = true;
      this.recordId = row.id;
    },
    checkApplyDetail(row) {
      this.applyId = row.id;
      this.applyVisible = true;
    },
    /**
     * 当前需求如果有时间选项，用当前方法处理同步formData数据
     * */
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
