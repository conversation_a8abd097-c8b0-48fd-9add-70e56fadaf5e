<template>
  <div>
    <Modal
      :value="value"
      title="关联订单"
      width="1000"
      @on-visible-change="changeVisible"
      footer-hide
      class="modal-wrapper"
      :mask-closable="false"
    >
      <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
        <Row>
          <FormItem>
            <Input v-model="queryFormData.order_code" placeholder="订单号" />
          </FormItem>

          <FormItem>
            <DatePicker
              type="daterange"
              clearable
              :options="disabledTime"
              format="yyyy-MM-dd"
              placeholder="交易时间"
              v-model="times"
              @on-change="times => handleTimeChange(times, 'st', 'et')"
            ></DatePicker>
          </FormItem>
          <FormItem style="text-align: left">
            <div class="flex">
              <Button type="primary" @click="onSearch" style="margin-right: 10px">筛选</Button>
              <span class="list-reset-btn" @click="onResetSearch">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
              <!--              <Button type="default" @click="onResetSearch" style="margin-right: 10px">清除</Button>-->
            </div>
          </FormItem>
        </Row>
        <Row>
          <FormItem>
            <Button type="primary" v-if="!isCheckDetail" @click="submitToSettle" style="margin-right: 10px"
              >提交结算</Button
            >
            <Button type="default" v-if="!isCheckDetail" @click="checkRecord" style="margin-right: 10px"
              >申请记录</Button
            >
          </FormItem>
        </Row>
      </Form>

      <Table
        :data="list"
        :columns="tableColumns"
        :loading="tableLoading"
        :height="300"
        @on-select="handleSelect"
        @on-select-cancel="handleSelectCancel"
        @on-select-all="handleSelectAll"
        @on-select-all-cancel="handleCancelAll"
      >
        <template slot-scope="{ row }" slot="order_amount"> ￥{{ row.order_amount }}</template>
        <template slot-scope="{ row }" slot="amount"> +{{ row.amount }}</template>
      </Table>

      <div class="block_20"></div>
      <div class="flex flex-item-align flex-item-between">
        <KPage
          :total="total"
          :pageSizeOpts="[10, 20, 50]"
          :page-size.sync="+queryFormData.pageSize"
          :current.sync="+queryFormData.page"
          @on-change="onPageChange"
          style="text-align: center"
        />
        <div>
          共<span style="color: red">{{ selectLen }}</span
          >条, 总金额<span v-text-format.money="selectTotal" style="color: red"></span>元
        </div>
      </div>
    </Modal>
    <submit-settle-modal
      v-model="submitVisible"
      @success="settleSuccess"
      :selectedObj="selectObj"
      :is-check-record="isCheckRecord"
    ></submit-settle-modal>
  </div>
</template>

<script>
import { $operator } from '@/libs/operation';
import submitSettleModal from './submitSettleModal.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 10,
  st: '', // 交易时间开始
  et: '', // 交易时间截止
  settle_st: '', // 结算时间开始
  settle_et: '', // 结算时间截止
  divide_status: '2',
  order_code: '', // 订单号
};

export default {
  name: 'modal',
  mixins: [],

  components: { submitSettleModal },

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    isCheckDetail: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      queryFormData: { ...init_query_form_data },
      originTableColumns: [
        { type: 'selection', width: 60, align: 'center' },
        { title: '订单号', key: 'mch_order_code', align: 'center', width: 200 },
        { title: '交易流水号', key: 'code', align: 'center', width: 200 },
        { title: '交易时间', key: 'created_at', align: 'center', width: 180 },
        { title: '付款金额（元）', slot: 'order_amount', align: 'center' },
        { title: '分账金额（元）', slot: 'amount', align: 'center' },
      ],
      tableColumns: [],

      optionsList: [],
      // 如果当前需求页面有时间组件，并且需要限制选择范围
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      times: [], // 时间

      list: [],
      tableLoading: false,
      total: 0,
      selectObj: {},
      submitVisible: false,
      isCheckRecord: false,
    };
  },

  computed: {
    selectLen() {
      return Object.keys(this.selectObj).length;
    },
    selectTotal() {
      return Object.values(this.selectObj).reduce((prev, curr) => {
        return $operator.add(prev, Number(curr.amount));
      }, 0);
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    checkRecord() {
      this.isCheckRecord = true;
      this.submitVisible = true;
    },
    // 结算成功
    settleSuccess() {
      this.onSearch();
      this.selectObj = {};
      this.$emit('refresh');
    },
    /**
     * 搜索
     * */
    onSearch: function () {
      this.queryFormData.page = 1;
      this.getHospitalBalanceList();
    },

    /**
     * 重置
     * */
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.times = [];
      this.getHospitalBalanceList();
    },

    /**
     * 状态切换
     * */
    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.tabType = status;
      this.getHospitalBalanceList();
    },

    /**
     * 分页
     * */
    onPageChange(page, pageSize) {
      console.log('=>(moneyDetailModal.vue:206) 分页');
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getHospitalBalanceList();
      this.scrollTop();
    },

    /**
     * 表格回归顶部
     * */
    scrollTop() {
      let overflowY = this.$el.getElementsByClassName('ivu-table-body')[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },

    /**
     * 关闭弹窗
     * */
    closeModal() {
      this.queryFormData = { ...init_query_form_data };
      this.times = [];
      this.selectObj = {};
      this.scrollTop();
      this.$emit('input', false);
    },

    /**
     * 弹窗变化
     * */
    changeVisible(flag) {
      if (flag) {
        if (this.isCheckDetail) {
          this.tableColumns = this.originTableColumns.slice(1);
        } else {
          this.tableColumns = this.originTableColumns;
        }
        this.onSearch();
      } else {
        this.closeModal();
      }
    },

    /**
     * 当前需求如果有时间选项，用当前方法处理同步formData数据
     * */
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.queryFormData[startTime] = times[0];
        this.queryFormData[endTime] = times[1];
      } else {
        this.queryFormData[startTime] = '';
        this.queryFormData[endTime] = '';
      }
    },

    // api-获取分账明细列表
    getHospitalBalanceList() {
      this.tableLoading = true;
      let params = this.queryFormData;
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getHospitalBalanceList(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.total = res.total;
          this.list = this.handler(res.list);
        })
        .catch(err => {})
        .finally(() => (this.tableLoading = false));
    },

    /**
     * 处理返回的list数据，如果有初始化需要禁用的选项，在此处就开始处理
     * */
    handler(list) {
      // 每次请求回显已经勾选的数据
      for (let k in list) {
        for (let j in this.selectObj) {
          if (list[k].code === this.selectObj[j].code) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }
      }
      return list;
    },
    /**
     * 过滤选中的ids
     * */
    getAuditOrderIds(row) {
      if (row) {
        return [row.code];
      } else {
        return Object.values(this.selectObj).map(item => item.code);
      }
    },

    /***
     * ============= start 组件事件的勾选操作 start ==============
     * */
    //选中项发生变化时就会触发
    handleSelect(selection, row) {
      this.$set(this.selectObj, row.code, row);
    },
    //选中项发生变化时就会触发
    handleSelectCancel(selection, row) {
      this.$delete(this.selectObj, row.code);
    },

    //点击全选时触发
    handleSelectAll(selection) {
      selection.forEach(item => {
        this.$set(this.selectObj, item.code, item);
      });
    },
    // 返选时触发
    handleCancelAll(selection) {
      for (let k in this.list) {
        if (this.list[k].code in this.selectObj) {
          this.$delete(this.selectObj, this.list[k].code);
        }
      }
    },

    submitToSettle() {
      this.isCheckRecord = false;
      console.log('=>(relatedOrderModal.vue:285) getAuditOrderIds', this.getAuditOrderIds());
      if (!this.getAuditOrderIds().length) {
        this.$Message.error('请选择订单');
        return;
      }
      if (this.getAuditOrderIds().length > 50) {
        this.$Message.error('选择订单数量不得超过50条');
        return;
      }
      this.submitVisible = true;
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
}

.cursor {
  cursor: pointer;
}

.mb10 {
  margin-bottom: 10px;
}

.no-wrap {
  display: flex;
  flex-wrap: wrap;
}
</style>
