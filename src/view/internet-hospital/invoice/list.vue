<template>
  <div>
    <standard-table
      :data="list"
      :columns="tableCols"
      :loading="tableLoading"
      stripe
      :total="total"
      :page-size.sync="queryFormData.page_size"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form v-model="queryFormData" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <Col>
              <FormItem>
                <Input v-model="queryFormData.relate_code" clearable placeholder="订单编号" />
              </FormItem>
            </Col>
            <Col>
              <FormItem>
                <Input v-model="queryFormData.code" clearable placeholder="申请单号" />
              </FormItem>
            </Col>
            <Col>
              <FormItem>
                <DatePicker
                  type="daterange"
                  clearable
                  format="yyyy-MM-dd"
                  placeholder="申请时间"
                  v-model="timeRange"
                  :options="timeRangeOption"
                  @on-change="times => handleTimeChange(times)"
                ></DatePicker>
              </FormItem>
            </Col>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn mr10" @click="onResetSearch">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
            </FormItem>
          </Row>
        </Form>
        <div class="table-wrap">
          <div class="flex flex-item-between flex-item-align">
            <div class="panel-nav">
              <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')">
                全部
              </a>
              <a
                class="nav"
                v-for="item in status_enum"
                :key="item.key"
                :class="{ active: $route.query.status == item.key }"
                @click.prevent.capture="onStatusChange(item.key)"
              >
                {{ item.name }}
                <Tag effect="dark" :color="getTagColor(item.key)">{{ status_count[item.key] }}</Tag>
              </a>
            </div>
            <div class="table-btn flex">
              <Button type="primary" @click="applyInvoice">申请开票</Button>
            </div>
          </div>
        </div>
      </template>
      <template v-slot:applicant="{ row }">
        <div class="box-lv">
          <p class="box-label">申请方：</p>
          <p class="box-value">{{ row?.applicant || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">申请人：</p>
          <p class="box-value">{{ row?.buyer_name || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">手机号：</p>
          <p class="box-value">{{ row?.buyer_phone || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">接收邮箱：</p>
          <p class="box-value">{{ row?.buyer_email || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">注册地址：</p>
          <p class="box-value">{{ row?.buyer_address || '-' }}</p>
        </div>
      </template>
      <template v-slot:type="{ row }">
        <div class="box-lv">
          <p class="box-label">发票类型：</p>
          <p class="box-value">{{ row?.line_text || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">服务费金额：</p>
          <p class="box-value">{{ formatYuan(row?.service_fee_cent) }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">通道费金额：</p>
          <p class="box-value">{{ formatYuan(row?.channel_fee_cent) }}</p>
        </div>
      </template>
      <template v-slot:invoicing_title="{ row }">
        <div class="box-lv">
          <p class="box-label">发票抬头：</p>
          <p class="box-value">{{ row?.invoicing_title || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">公司税号：</p>
          <p class="box-value">{{ row?.invoicing_tax_num || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">注册地址：</p>
          <p class="box-value">{{ row?.invoicing_address || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">注册电话：</p>
          <p class="box-value">{{ row?.invoicing_tel || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">开户银行：</p>
          <p class="box-value">{{ row?.invoicing_bank || '-' }}</p>
        </div>
        <div class="box-lv">
          <p class="box-label">银行账号：</p>
          <p class="box-value">{{ row?.invoicing_bank_account || '-' }}</p>
        </div>
      </template>
      <template v-slot:bailing_at="{ row }">
        {{ row.bailing_at || '-' }}
      </template>
      <template v-slot:action="{ row }">
        <a v-if="row.status === '4'" class="mr10" @click="toDetail(row, 'edit')">编辑</a>
        <a @click="toDetail(row)">详情</a>
      </template>
    </standard-table>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from '@/libs/util';
import { formatOptions, formatYuan } from '../../../utils/helper';
import TipModel from '../../../components/confirmModal';
import moment from 'moment';
import StandardTable from '@/components/StandardTable/index.vue';

const init_query_form_data = {
  page: 1,
  page_size: 20,
  status: '',
  type: '',
  code: '',
  relate_code: '',
  line: '',
  st: '',
  et: '',
  r: '',
};

export default {
  name: 'InvoiceList',
  components: { StandardTable },
  mixins: [search],
  data() {
    return {
      formatYuan,
      queryFormData: { ...init_query_form_data },
      apiName: 'getHospitalInvoiceList',
      tableCols: [
        { title: '申请单号', key: 'code', minWidth: 180 },
        { title: '申请方', slot: 'applicant', minWidth: 260 },
        { title: '开票方', key: 'invoicing_party', minWidth: 100 },
        { title: '发票信息', slot: 'type', minWidth: 150 },
        { title: '开票信息', slot: 'invoicing_title', minWidth: 260 },
        { title: '蓝票状态', key: 'status_text', minWidth: 100 },
        { title: '申请时间', key: 'apply_at', minWidth: 130 },
        { title: '开票时间', slot: 'bailing_at', minWidth: 130 },
        { title: '操作', slot: 'action', align: 'center', fixed: 'right', minWidth: 60 },
      ],
      status_enum: [],
      line_enum: [],
      relate_type_enum: [],
      type_enum: [],
      status_count: {},
      timeRange: [],
      timeRangeOption: {
        disabledDate(date) {
          return moment(date).isAfter(moment());
        },
      },
    };
  },
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getHospitalInvoiceOptions();
  },
  computed: {
    getTagColor() {
      return type => {
        switch (type) {
          case '3': // 待发货
            return 'warning';
          case '1': // 已完成
            return 'success';
          default:
            // 已取消
            return 'default';
        }
      };
    },
  },
  methods: {
    getHospitalInvoiceOptions() {
      this.$api.getHospitalInvoiceOptions().then(res => {
        this.status_enum = formatOptions(res?.status_enum || []);
        this.line_enum = formatOptions(res?.line_enum || []);
        this.relate_type_enum = formatOptions(res?.relate_type_enum || []);
        this.type_enum = formatOptions(res?.type_enum || []);
      });
    },
    onStatusChange(tab) {
      this.queryFormData.page = 1;
      this.queryFormData.status = tab;
      this.submitQueryForm();
    },
    handlerListData(data) {
      this.status_count = data.status_count;
    },
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },
    handleTimeChange(val) {
      this.queryFormData.st = val?.[0];
      this.queryFormData.et = val?.[1];
    },
    applyInvoice() {
      this.$api.getHospitalCheckInvoiceStatus().then(res => {
        if (+res?.not_pass === 0) {
          this.$router.push({
            path: `/internet-hospital/invoice/detail`,
            query: {
              type: 'add',
            },
          });
          return;
        }
        const action = {
          1: '去填写',
          2: '去修改',
          3: '好的',
          4: '好的',
        };
        TipModel({
          content: '提示',
          showOk: false,
          cancelText: action?.[res?.type],
          contentText: res?.msg,
        }).catch(() => {
          if (+res?.type === 1 || +res?.type === 2) {
            this.$router.push({
              path: `/setting/Invoice`,
            });
          }
        });
      });
    },
    toDetail(row, type = 'view') {
      this.$router.push({
        path: '/internet-hospital/invoice/detail',
        query: {
          id: row.code,
          type,
        },
      });
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style scoped lang="less">
.box-lv {
  display: flex;
  .box-label {
    text-align: right;
    min-width: 66px;
  }
}
.mr6 {
  margin-right: 6px;
}
// 清除条件
.list-reset-btn {
  display: inline-block;
  width: unset;
  cursor: pointer;
  color: #666;
  &:hover {
    color: blue;
    .reset-icon {
      color: blue !important;
    }
  }
  .reset-icon {
    color: #666 !important;
    margin-right: 2px;
  }
}
::v-deep .ivu-form-inline .ivu-input-wrapper,
.ivu-form-inline .ivu-select {
  width: 200px;
}
</style>
