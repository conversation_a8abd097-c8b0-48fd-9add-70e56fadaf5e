<template>
  <div>
    <standard-table
      :data="list"
      :columns="tableCols"
      :loading="tableLoading"
      stripe
      :total="total"
      :page-size.sync="queryFormData.page_size"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <!-- 待结算金额 -->
      <template v-slot:divide_wait_settle_balance="{ row }">
        <a @click="seeMoneyModal(row, '1')">¥{{ row.divide_wait_settle_balance || '0.00' }}</a>
      </template>
      <!-- 可结算金额 -->
      <template v-slot:divide_prepare_settle_balance="{ row }">
        <a @click="seeMoneyModal(row, '2')">¥{{ row.divide_prepare_settle_balance || '0.00' }}</a>
      </template>

      <!-- 结算中金额 -->
      <template v-slot:divide_during_settle_balance="{ row }">
        <a @click="seeMoneyModal(row, '3')">¥{{ row.divide_during_settle_balance || '0.00' }}</a>
      </template>

      <!-- 已结算金额 -->
      <template v-slot:divide_finish_amount="{ row }">
        <a @click="seeMoneyModal(row, '4')">¥{{ row.divide_finish_amount || '0.00' }}</a>
      </template>

      <!-- 累计收益 -->
      <template v-slot:divide_all_amount="{ row }">
        {{ row.divide_all_amount || '0.00' }}
      </template>
    </standard-table>

    <!-- 查看金额的详情弹窗 -->
    <money-detail-modal
      v-model="moneyDetailModalVisible"
      :row="currentRow"
      :currentModalType="currentModalType"
    ></money-detail-modal>
  </div>
</template>
<script>
import search from '@/mixins/search';
import S from 'libs/util';
import moneyDetailModal from './components/moneyDetailModal.vue';
import StandardTable from '@/components/StandardTable/index.vue';

const init_query_form_data = {
  page: 1,
  page_size: 20,
  r: '',
};
export default {
  name: 'PharmacistList',
  components: {
    StandardTable,
    moneyDetailModal,
  },
  mixins: [search],
  props: {},
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getBalanceDoctorlist',
      tableCols: [
        { title: '医生ID', key: 'id', align: 'center' },
        { title: '医生姓名', key: 'name', align: 'center' },
        { title: '待结算金额(元)', slot: 'divide_wait_settle_balance', align: 'center' },
        { title: '可结算金额(元)', slot: 'divide_prepare_settle_balance', align: 'center' },
        { title: '结算中金额(元)', slot: 'divide_during_settle_balance', align: 'center' },
        { title: '已结算金额(元)', slot: 'divide_finish_amount', align: 'center' },
        { title: '累计收益(元)', slot: 'divide_all_amount', align: 'center' },
      ],
      moneyDetailModalVisible: false,
      currentRow: {},
      currentModalType: '',
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    /**
     * 查看金额详情
     * */
    seeMoneyModal(row, modalType = '') {
      this.currentRow = row;
      this.currentModalType = modalType;
      this.moneyDetailModalVisible = true;
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>
<style lang="less" scoped></style>
