<template>
  <modal
    :value.sync="visible"
    title="修改记录"
    width="1000px"
    @on-visible-change="changeVisible"
    class="modal-wrapper"
    footer-hide
  >
    <Table
      :loading="tableLoading"
      ref="warehouseTable"
      :columns="tableCols"
      :data="list"
      :height="$store.state.app.clientHeight - 290"
    >
      <template v-slot:reject_reason="{ row }">
        <template v-if="row.reject_reason instanceof Array && row.reject_reason.length > 0">
          <div v-for="text in row.reject_reason" :key="text">{{ text }}</div>
        </template>
        <template v-else>-</template>
      </template>
      <template v-slot:action="{ row }">
        <a class="ml10" @click="handleOpenEditLogModel(row)">查看内容</a>
      </template>
    </Table>
    <div v-if="visible" style="width: 100%; display: flex; justify-content: center">
      <Page
        :total="page.total"
        :page-size="page.pageSize"
        :current="page.page"
        :page-size-opts="[10, 20, 50, 80, 100]"
        @on-change="onPageChange"
        @on-page-size-change="onPageSizeChange"
        show-sizer
        show-elevator
        show-total
        transfer
        style="margin-top: 10px"
      >
      </Page>
    </div>
    <edit-log :visible.sync="editLogVisible" :selectedRow.sync="selectedRow" @closeModel="handleCloseEditLogModel" />
  </modal>
</template>
<script>
import EditLog from './edit-log.vue';

export default {
  name: 'edit-record',
  components: { EditLog },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    clinicCode: {
      type: String,
      default: '',
    },
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.page.page = 1;
          this.page.page_size = 10;
          this.page.total = 0;
          this.form.clinic_code = this.clinicCode;
          this.handleQueryEditRecordList();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  data() {
    return {
      tableLoading: false,
      editLogVisible: false,
      selectedRow: {},
      tableCols: [
        { title: '提交时间', key: 'created_at' },
        { title: '操作人', key: 'operator_name' },
        { title: '审核状态', key: 'audit_status_desc' },
        { title: '驳回原因', slot: 'reject_reason', minWidth: 200 },
        { title: '操作', slot: 'action', width: 100 },
      ],
      list: [],
      page: {
        page: 1,
        page_size: 10,
        total: 0,
      },
      form: {
        clinic_id: '',
        clinic_code: '',
      },
    };
  },
  methods: {
    changeVisible(val) {
      if (!val) {
        this.$emit('closeModel');
      }
    },
    handleQueryEditRecordList() {
      const params = {
        ...this.page,
        ...this.form,
      };
      if (!this.form.clinic_code || this.form.clinic_code === '') return;
      this.$api.getClinicSettledEditRecord(params).then(
        res => {
          console.log(res, ' res');
          this.list = res?.list || [];
          this.page.total = res.total * 1 || 0;
        },
        error => {
          console.log(error, 'error');
          this.$Message.error(error.message || error.errmsg);
        }
      );
    },
    onPageChange(page) {
      this.page.page = page;
      this.handleQueryEditRecordList();
    },
    onPageSizeChange(pageSize) {
      this.page.page_size = pageSize;
      this.handleQueryEditRecordList();
    },
    handleOpenEditLogModel(row) {
      this.editLogVisible = true;
      this.selectedRow = row;
    },
    handleCloseEditLogModel() {
      this.editLogVisible = false;
      this.selectedRow = {};
    },
  },
};
</script>
<style scoped lang="less"></style>
