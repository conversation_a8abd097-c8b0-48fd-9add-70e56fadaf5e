<template>
  <Modal :value="value" title="填写退货物流" width="800px" :mask-closable="false" @on-visible-change="changeVisible">
    <div class="modal-content">
      <Form ref="formData" :label-width="80" :model="formData">
        <div class="flex" v-for="(logistics_item, logistics_index) in formData.return_logistics" :key="logistics_index">
          <FormItem
            :label="logistics_index == 0 ? '退货物流' : ''"
            :required="ifVerify !== 'NOT'"
            prop="return_logistics"
          >
            <Select
              v-model="logistics_item.express_code"
              placeholder="请选择快递公司"
              style="width: 200px"
              @on-change="e => optionChange(e, logistics_index)"
              label-in-value
            >
              <Option
                v-for="item in companyList"
                :value="item.code"
                :key="String(JSON.stringify(item))"
                :label="item.name"
              ></Option>
              <!-- <Option value="shanghai">London</Option>
              <Option value="shenzhen">Sydney</Option> -->
            </Select>
          </FormItem>

          <FormItem :label-width="20" prop="express_no">
            <Input v-model="logistics_item.express_no" placeholder="请输入物流单号" style="width: 340px" />
          </FormItem>

          <div class="ml10 flex flex-item-align symbol-wrapper">
            <p class="symbol" @click="addLogistics" v-show="formData.return_logistics.length < 5">
              <svg-icon shape="circle" iconClass="plus" class="symbol-icon"></svg-icon>
            </p>
            <p
              class="symbol delete-symbol ml10"
              @click="deleteLogistics(logistics_index)"
              v-show="formData.return_logistics.length > 1"
            >
              <svg-icon iconClass="remove" class="symbol-icon" style="color: red"></svg-icon>
            </p>
          </div>
        </div>

        <FormItem label="备注" prop="remark">
          <Input
            v-model="formData.remark"
            type="textarea"
            :maxlength="200"
            :show-word-limit="true"
            :autosize="{ minRows: 3, maxRows: 5 }"
            placeholder="选填，最多200字"
          ></Input>
        </FormItem>

        <FormItem label="凭证" prop="images">
          <MaterialPicture v-model="formData.images" :limit="9" />
          <div>支持上传照片，单张图片大小不超过3.0M，最多9张</div>
        </FormItem>
      </Form>
    </div>

    <div slot="footer" class="edit-footer">
      <Button type="default" @click="closeModal">取消</Button>
      <Button type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from '@/libs/util';
import io from '@/libs/io'; // Http request

import { cloneDeep } from 'lodash';

let init_formData = {
  return_logistics: [{ express_code: '', express_no: '' }],
  remark: '',
  images: [],
  apply_code: '',
};
export default {
  name: 'editOrder',
  components: {},
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false,
    },
    apply_code: {
      type: String,
      default: '',
    },
    packs: {
      type: Array,
      default: () => [],
    },
    ifVerify: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      formData: cloneDeep(init_formData),
      companyList: [],
    };
  },
  computed: {},
  watch: {
    value(val) {
      if (val) {
        this.handlePacks();
      }
    },
  },
  created() {},
  mounted() {
    this.initData();
  },
  methods: {
    initData() {
      // this.companyList = [
      //   { value: "beijing", label: "New York" },
      //   { value: "shanghai", label: "London" },
      //   { value: "shenzhen", label: "Sydney" }
      // ];
      io.get('/clinic/common.getexpresslist')
        .then(
          res => {
            console.log(res, 2323223);
            if (res) {
              this.companyList = res.list;
            }
          },
          err => {
            {
            }
          }
        )
        .catch(error => {});
    },
    addLogistics() {
      this.formData.return_logistics.push({ express_code: '', express_no: '' });
    },
    deleteLogistics(index) {
      this.formData.return_logistics.splice(index, 1);
    },
    // 校验
    validateFormData() {
      // let flag = true;
      let { return_logistics } = this.formData;
      for (const returnLogistic of return_logistics) {
        if (!returnLogistic.express_code || !returnLogistic.express_no) {
          this.$Message.error('请填写完整退货物流信息');
          return false;
        }
      }
      return true;
      // return_logistics &&
      //   return_logistics.some(item => {
      //     if (!item.express_code || !item.express_no) {
      //       this.$Message.error("请填写完整退货物流信息");
      //       flag = false;
      //       return true;
      //     }
      //   });
      // return flag;
    },
    ifSubmit() {
      const sendData = this.getSubmitData(this.formData);
      console.log(sendData);
      this.$api
        .inputReturnLogistics(sendData)
        .then(
          res => {
            this.$Message.success('提交成功');
            this.$emit('refreshDetail');
            this.closeModal();
          },
          err => {
            {
            }
          }
        )
        .catch(error => {
          {
          }
        });
    },
    confirm() {
      // 未收到货时不需要校验
      if (this.ifVerify !== 'NOT') {
        this.validateFormData() && this.ifSubmit();
      } else {
        this.ifSubmit();
      }
    },
    handlePacks() {
      console.log(this.packs);
      this.formData = cloneDeep({ ...init_formData });
      if (this.packs.length) {
        const packInfo = cloneDeep(this.packs[0]);
        let descList = [];
        if (packInfo.express_desc.length) {
          packInfo.express_desc.forEach(v => {
            if (v.express_code && v.express_no) {
              descList.push({
                ...v,
              });
            }
          });
        }

        if (!descList.length) {
          descList.push({
            express_code: '',
            express_no: '',
          });
        }

        this.formData.return_logistics = descList;
        this.formData.images = packInfo.prove_images;
        this.formData.remark = packInfo.prove_remark;
        console.log('-> %c this.formData.remark  ===    %o', 'font-size: 15px;color: #F56C6C ;', this.formData.remark);
      } else {
        this.formData = cloneDeep(init_formData);
      }
    },
    /*
     * @Description: 设置提交数据
     * return: {Object} 提交数据
     */
    getSubmitData(formData) {
      let subData = cloneDeep(formData);
      if (subData.return_logistics?.length) {
        subData.express_detail = [];
        subData.return_logistics?.forEach(item => {
          if (item.express_code !== '' && item.express_no !== '') {
            subData.express_detail.push({
              ...item,
            });
          }
        });
      }

      subData.apply_code = this.apply_code;

      delete subData.return_logistics;

      return subData;
    },

    changeVisible(visible) {
      !visible && this.closeModal();
    },
    // 关闭弹窗
    closeModal() {
      this.$emit('input', false);
    },
    optionChange(val, index) {
      // if(val){
      // }
      console.log(val, 'vallllllll');
      this.formData.return_logistics[index].express_name = val.label;
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  max-height: 400px;
  min-height: 400px;
  overflow-y: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  .ivu-form {
    width: 640px;
  }

  .symbol-wrapper {
    height: 45px;

    .delete-symbol {
      border-color: red !important;
    }

    .symbol {
      border: 1px solid #1157e5;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 2px;
      width: 18px;
      height: 18px;
      box-sizing: border-box;
      cursor: pointer;

      &:hover {
        opacity: 0.6;
      }

      .symbol-icon {
        font-size: 20px;
      }
    }
  }
}
</style>
