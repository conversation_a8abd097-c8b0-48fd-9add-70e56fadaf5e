<template>
  <div class="COVID-wrapepr">
    <div class="warning-tip flex flex-item-align">*该新冠抗疫处方需以打包的形式进行倍数采购</div>

    <!-- 表格-->
    <Table
      :columns="tableCols"
      :data="list"
      :height="$store.state.app.clientHeight - 265"
      :loading="tableLoading"
      stripe
      class="mt20"
    >
      <!-- 采购单价 -->
      <template slot="clinic_price" slot-scope="{ row }">
        <span v-if="row.min_clinic_price == row.max_clinic_price">￥{{ row.min_clinic_price }}</span>
        <span v-else>￥{{ row.min_clinic_price }}-￥{{ row.max_clinic_price }}</span>
      </template>

      <template slot="stock_num" slot-scope="{ row }">
        {{ row.stock_num_text }}
      </template>

      <template slot="img" slot-scope="{ row }">
        <div class="flex flex-item-center">
          <viewer
            v-if="row.desc_imgs.length"
            :images="row.desc_imgs"
            class="flex flex-item-center"
            style="margin: 6px 0"
          >
            <img
              v-for="(img, index) in row.desc_imgs"
              v-show="index === 0"
              :key="img"
              :src="img | imageStyle"
              class="image"
              style="width: 60px; height: auto; max-height: 70px"
            />
          </viewer>
          <span v-else style="vertical-align: middle">-</span>
        </div>
      </template>

      <template slot="purchase_info" slot-scope="{ row }">
        {{ row.skus[0] && row.skus[0].purchase_info && row.skus[0].purchase_info.text }}
      </template>
    </Table>

    <!-- 采购模块 -->
    <div class="purchase-module flex flex-item-align">
      <div class="flex flex-item-end">
        <div>
          采购件数：<InputNumber
            v-model="purchaseNum"
            :precision="0"
            :min="0"
            style="width: 120px"
            placeholder="采购件数"
          ></InputNumber>
        </div>
        <div class="purchase-tip" v-show="peopleNum > 0">
          注：当前填写的采购量，在单疗程每人开5剂的情况，可开给{{ peopleNum }}个人
        </div>
      </div>
    </div>

    <!-- 底部固定 -->
    <div class="fixed-bottom-wrapper">
      <div class="flex flex-item-align" style="justify-content: flex-end; height: 40px">
        <div class="goods-item">
          <span>采购商品种类：</span>
          <span>{{ select_goods_count }}</span>
        </div>
        <div class="goods-item">
          <span>采购商品数量：</span>
          <span>{{ total_num }}</span>
        </div>
        <div class="goods-item">
          <span>待支付总额：</span>
          <span class="money">¥ {{ total_price }}</span>
        </div>
        <div class="goods-item">
          <Button type="primary" @click="toSettlement">去结算</Button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapMutations } from 'vuex';
import { $operator } from '@/libs/operation';
import renderHeader from '@/mixins/renderHeader';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  pur_list_type: 'covid', // covid:新冠
};
export default {
  name: 'coList',
  components: {},
  mixins: [renderHeader],
  props: {},
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      tableLoading: false,
      list: [],
      tableCols: [
        { title: '编号', key: 'code', minWidth: 120 },
        { title: '介绍图', slot: 'img', align: 'center', minWidth: 80 },
        {
          title: '商品名',
          key: 'generic_name',
          tooltip: true,
          minWidth: 90,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '添加多规格商品时，会将规格名作为采购单中的商品名'),
        },
        { title: '类型', key: 'prod_type_desc', minWidth: 70, tooltip: true },
        { title: '规格	', key: 'prod_spec_text', minWidth: 100, tooltip: true },
        { title: '单位	', key: 'prod_unit_text', minWidth: 40, tooltip: true },
        { title: '供应商', key: 'supplier_name', minWidth: 120 },
        {
          title: '销售商',
          key: 'seller',
          minWidth: 120,
          renderHeader: (h, params) => this._renderHeader(h, params, '诊所采购时货品金额部分，发票由销售商开具'),
        },
        {
          title: '采购单价',
          slot: 'clinic_price',
          minWidth: 120,
          renderHeader: (h, params) => this._renderHeader(h, params, '采购单价 = 货品金额 + 平台使用费'),
        },
        {
          title: '当前库存',
          slot: 'stock_num',
          minWidth: 90,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '当库存状态为“先下后审”时，需要供应商审核后才能知道库存是否足够'),
        },
        { title: '起购倍数', slot: 'purchase_info', minWidth: 120 },
      ],

      purchaseNum: null, // 采购件数
    };
  },
  computed: {
    // 可开给服用的人数
    peopleNum() {
      return $operator.multiply(Number(this.purchaseNum), 50, 0);
    },
    // 采购种类
    select_goods_count() {
      return this.list.length;
    },

    // 采购商品数量
    total_num() {
      let totalNum = 0;
      this.list &&
        this.list.forEach(item => {
          let multipleNum = (item.skus[0] && item.skus[0].purchase_info && item.skus[0].purchase_info.num) || 1;
          totalNum = $operator.add(Number(totalNum), Number(multipleNum));
        });
      totalNum = $operator.multiply(Number(totalNum), Number(this.purchaseNum));
      return totalNum;
    },

    // 待支付总额
    total_price() {
      let totalMoney = 0;
      this.list &&
        this.list.forEach(item => {
          let multipleNum = (item.skus[0] && item.skus[0].purchase_info && item.skus[0].purchase_info.num) || 1;
          let itemMultipleMoney = $operator.multiply(Number(item.max_clinic_price), Number(multipleNum));
          let itemTotalMoney = $operator.multiply(Number(itemMultipleMoney), Number(this.purchaseNum));
          totalMoney = $operator.add(Number(itemTotalMoney), Number(totalMoney));
        });
      return totalMoney;
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getGoodsskuList();
    },
    ...mapMutations('purchase', ['SET_COVID_LISTOF']),

    getListof() {
      let listof = {};
      this.list &&
        this.list.forEach(item => {
          if (item.skus[0]) {
            let num = (item.skus[0].purchase_info && item.skus[0].purchase_info.num) || 0;
            listof[item.skus[0].id] = $operator.multiply(Number(this.purchaseNum), Number(num));
          }
        });
      return listof;
    },
    // 去结算
    toSettlement() {
      if (!this.purchaseNum) {
        this.$Message.error('请先设置采购件数再进行结算');
        return false;
      }
      console.log('-> this.getListof()', this.getListof());
      this.SET_COVID_LISTOF(this.getListof());
      // 新增校验接口
      this.$api
        .checkStockGoodsSku({ listof: this.getListof() })
        .then(() => {
          this.$router.push({
            path: '/purchase/stock/billingDetail',
            query: {
              type: 'covid',
            },
          });
        })
        .catch(err => {});
    },

    // 获取新冠药物处方列表
    getGoodsskuList() {
      this.$api.getGoodsskuList(this.queryFormData).then(res => {
        this.list = res.list;
      });
    },
  },
  filters: {},
};
</script>

<style lang="less" scoped>
.COVID-wrapepr {
  .warning-tip {
    color: red;
    font-size: 14px;
    font-weight: bold;
  }

  // 采购件数
  .purchase-module {
    margin-top: 10px;
    height: 60px;

    .purchase-tip {
      font-size: 12px;
      color: #666;
      margin-left: 20px;
    }
  }

  .goods-item {
    margin-right: 40px;
  }

  .money {
    font-size: 16px;
    font-weight: 600;
    color: #ed4014;
  }
}
.mt20 {
  margin-top: 20px;
}
</style>
