<template>
  <Modal
    ref="selectReceiveModal"
    :value="visible"
    title="选择收货地址"
    :mask-closable="false"
    width="675px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <Alert show-icon class="mb20">
        <div>
          应有关部门合规性要求，在进行药品采购时，<span style="color: #1157e5"
            >收件地址必须与备案证地址一致，收件人信息必须与法人委托人一致。</span
          >您的收件地址、法人委托人信息若发生改变，请及时联系常繁进行变更
        </div>
      </Alert>
      <div class="address-item mb20">
        <Radio disabled></Radio>
        <div>
          <div class="custom-info">
            <div class="mr10">{{ defaultAddress.consignee }}</div>
            <div>{{ defaultAddress.mobile }}</div>
          </div>
          <div class="custom-address">{{ defaultAddress.address_text }}</div>
        </div>
        <div class="common-tag">常用</div>
      </div>
      <div class="address-item mb10">
        <Radio v-model="registerRadio"></Radio>
        <div>
          <div class="custom-info">
            <div class="mr10">
              {{ registerAddress.consignee }}
            </div>
            <div>{{ registerAddress.mobile }}</div>
          </div>
          <div class="custom-address" :style="{ color: !registerAddress.address_text ? '#d63232' : '' }">
            {{ registerAddress.address_text ? registerAddress.address_text : '地址获取失败，请联系客服' }}
          </div>
        </div>
        <div class="info-tag">备案信息</div>
      </div>
      <div class="warn-tip">
        <Icon type="ios-information-circle-outline" size="16" />
        此收件信息是您提交给常繁药业的备案信息和法人委托信息
      </div>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="submitForm" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'libs/util';
const initFormData = {
  code: '', //销售单编号；如果不填写，创建时系统会自动生成
};

export default {
  name: 'selectReceiveModal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    defaultAddress: {
      type: Object,
      default: () => {},
    },
    registerAddress: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      formData: { ...initFormData },
      submitLoading: false,
      registerRadio: true,
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    changeVisible(val) {
      if (val) {
      } else {
        this.formData = { ...initFormData }; // 重置数据
      }
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {
      this.$emit('onOk', '1');
      this.cancel();
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
.content {
  padding: 0 15px;
  ::v-deep .ivu-alert.ivu-alert-with-icon {
    padding: 8px 10px 8px 30px;
  }
}
.address-item {
  position: relative;
  display: flex;
  padding: 18px 12px;
  background: #f7f7f7;
  border-radius: 2px;
  .custom-info {
    display: flex;
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    margin-bottom: 10px;
  }
  .custom-address {
    font-weight: 400;
    font-size: 14px;
    color: #333333;
  }
  .common-tag {
    position: absolute;
    top: 0;
    right: 0;
    background: #cbcbcb;
    color: #ffffff;
    border-radius: 0px 2px 0px 2px;
    padding: 0 6px;
  }
  .info-tag {
    position: absolute;
    top: 0;
    right: 0;
    background: #1157e5;
    color: #ffffff;
    border-radius: 0px 2px 0px 2px;
    padding: 0 6px;
  }
}
.warn-tip {
  color: #d63232;
}
::v-deep .ivu-modal-header {
  border-bottom: 1px solid #e8eaec !important;
}
</style>
