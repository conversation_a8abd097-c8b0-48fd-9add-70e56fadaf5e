<template>
  <div style="position: relative">
    <!--    <div class="pur-btn-box flex list-fn-mb-distance">-->
    <!--      &lt;!&ndash; 新冠抗疫采购专区 &ndash;&gt;-->
    <!--&lt;!&ndash;      <Tooltip content="新冠采购专区正在升级中，请稍后再试">&ndash;&gt;-->
    <!--&lt;!&ndash;        <Button type="error" @click="jumpCovid" disabled>新冠抗疫采购专区</Button>&ndash;&gt;-->
    <!--&lt;!&ndash;      </Tooltip>&ndash;&gt;-->
    <!--      <img class="limit-discount" src="@/assets/image/limited-time-discount.png" alt="">-->
    <!--      <Button type="primary" @click="jumpYSHomology">药食同源预售专区</Button>-->
    <!--    </div>-->
    <div class="form-warpper flex flex-item-between" style="margin-bottom: 20px">
      <Form :label-width="0" class="form-warpper_left" inline @submit.native.prevent @keyup.enter.native="onSearch">
        <FormItem>
          <Input v-model="queryFormData.name" placeholder="商品名称" type="text" />
        </FormItem>
        <FormItem>
          <!-- <Input v-model="queryFormData.supplier_name" placeholder="供应商" type="text"/> -->
          <Select v-model="queryFormData.supplier_id" clearable placeholder="请选择供应商">
            <Option v-for="item in supplierList" :key="item.id" :value="item.pms_supplier_id">{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.prod_type" placeholder="全部类型">
            <Option value="">全部类型</Option>
            <Option v-for="(item, index) in prodTypes" :key="item.id" :value="item.id">{{ item.name }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
          <span class="list-reset-btn" @click="initSearch"
            ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
          >
        </FormItem>
        <FormItem>
          <!-- 三周年庆典banner -->
          <Poptip
            placement="bottom-end"
            v-model="activityVisible"
            v-if="cf_is_activity_on === '1'"
            width="300"
            word-wrap
            style="position: absolute; top: -12px; right: -300px"
          >
            <div style="position: relative">
              <img
                src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0715/111802_61181.jpg"
                alt=""
                width="300"
                height="70"
                style="cursor: pointer"
              />
            </div>

            <div slot="content" class="activity-box">
              <div class="activity-top">
                <div>活动规则</div>
                <img
                  src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0709/140529_24474.png"
                  alt=""
                  class="title-icon"
                />
                <img
                  src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0709/140529_44783.png"
                  alt=""
                  class="close-icon"
                  @click="closePop"
                />
              </div>
              <div class="rule-box">
                <div style="margin-bottom: 5px">1.活动时间2024年7月15日-7月31日。</div>
                <div>
                  2.活动期间，诊所银联采购常繁全线产品（套餐除外）的订单一次性下单金额大于等于1000，且小于等于2000元，提交订单时将自动加购一袋「2公斤装新疆精选红枣」仅需支付0.1元。
                </div>
                <div style="margin-bottom: 5px">
                  3.订单金额大于2000元以外的，每2000元，可多加购一袋「2公斤装新疆精选红枣」。
                </div>
                <div style="margin-bottom: 5px">4.单笔订单最多加购8袋「2公斤装新疆精选红枣」。</div>
                <div style="margin-bottom: 5px">5.每家诊所活动期间下单满足条件，可以参与两次本活动（至多16袋）。</div>
                <div style="margin-bottom: 5px">6.加购产品「2公斤装新疆精选红枣」数量有限，卖完为止。</div>
              </div>
            </div>
          </Poptip>
        </FormItem>
      </Form>
    </div>

    <Poptip trigger="hover" placement="bottom-end" style="position: absolute; top: -4px; right: -16px">
      <div class="wechat-box cursor">
        <svg-icon iconClass="wechat" class="wechat"></svg-icon>
        <div>
          <!--          <div>常繁药业</div>-->
          <div>在线客服</div>
        </div>
      </div>
      <div class="custom-box" slot="content">
        <div class="qr-code-box">
          <img :src="cf_customer_qr_code" width="90px" height="90px" style="margin-bottom: 8px" />
          <div>微信扫码添加</div>
          <div style="margin-bottom: 10px"><span class="bold">常繁</span>在线客服</div>
          <div class="note-box">
            <div>问题咨询/采购下单</div>
            <div>9:00-20:00 周一至周六</div>
          </div>
        </div>

        <div class="qr-code-box" v-if="sj_customer_qr_code">
          <img :src="sj_customer_qr_code" width="90px" height="90px" style="margin-bottom: 8px" />
          <div>微信扫码添加</div>
          <div style="margin-bottom: 10px"><span class="bold">上海树家</span>在线客服</div>
          <div class="note-box">
            <div>问题咨询/采购下单</div>
            <div>9:30-18:30 周一至周五</div>
          </div>
        </div>
      </div>
    </Poptip>

    <Table
      :columns="tableCols"
      :data="list"
      :height="$store.state.app.clientHeight - 283"
      :loading="tableLoading"
      stripe
    >
      <!-- <template slot="prod_date" slot-scope="{row}">
				{{ row.prod_date|data_format('YYYY-MM-DD') }}
			</template>
			<template slot="expire_date" slot-scope="{row}">
				{{ row.expire_date|data_format('YYYY-MM-DD') }}
			</template> -->

      <!--商品名-->
      <template slot="generic_name" slot-scope="{ row }">
        <Tooltip max-width="200" :content="row.generic_name">
          <div class="ecs"><span v-if="row.is_taocan_goods == 1" class="tc-tag">套餐</span>{{ row.generic_name }}</div>
        </Tooltip>
      </template>
      <!--规格-->
      <template slot="prod_spec_text" slot-scope="{ row }">
        <div v-if="row.is_taocan_goods == 1">-</div>
        <div v-else>{{ row.prod_spec_text || '-' }}</div>
      </template>

      <!-- 采购单价 -->
      <template slot="clinic_price" slot-scope="{ row }">
        <span v-if="row.min_clinic_price == row.max_clinic_price">￥{{ row.min_clinic_price }}</span>
        <span v-else>￥{{ row.min_clinic_price }}-￥{{ row.max_clinic_price }}</span>
      </template>

      <template slot="stock_num" slot-scope="{ row }">
        {{ row.stock_num_text }}
      </template>

      <template slot="quantity" slot-scope="{ row, index }">
        <div v-if="!isHasMoreSKU(row)">
          <InputNumber
            v-model="list[index].quantity"
            :precision="0"
            placeholder="进货数量"
            @on-blur.capture="val => changeUnit(index)"
          ></InputNumber>
          <span class="tip" v-if="row.skus[0] && +row.skus[0].purchase_info.num > 1">{{
            row.skus[0] && row.skus[0].purchase_info.text
          }}</span>
        </div>
        <div v-else>-</div>
      </template>
      <template slot="img" slot-scope="{ row }">
        <div class="flex flex-item-center">
          <viewer
            v-if="row.desc_imgs.length"
            :images="row.desc_imgs"
            class="flex flex-item-center"
            style="margin: 6px 0"
          >
            <img
              v-for="(img, index) in row.desc_imgs"
              v-show="index === 0"
              :key="img"
              :src="img"
              class="image"
              style="width: 60px; height: auto; max-height: 70px"
            />
          </viewer>
          <span v-else style="vertical-align: middle">-</span>
        </div>
      </template>
      <template slot="operate" slot-scope="{ row, index }">
        <a @click="addPurchaseOrder(index)">+加入采购单</a>
      </template>
    </Table>
    <div class="block_20"></div>

    <KPage
      :current.sync="queryFormData.page"
      :page-size.sync="queryFormData.pageSize"
      :total="total"
      style="text-align: right"
      @on-change="onPageChange"
    />
    <div class="block_20"></div>

    <div class="fixed-bottom-wrapper">
      <div class="flex flex-item-align" style="justify-content: flex-end; height: 40px">
        <div class="goods-item">
          <span>采购商品种类：</span>
          <span>{{ total_type }}</span>
        </div>
        <div class="goods-item">
          <span>采购商品数量：</span>
          <span>{{ total_num }}</span>
        </div>
        <div class="goods-item">
          <span>待支付总额：</span>
          <span class="money">¥ {{ total_price }}</span>
        </div>
        <div class="goods-item">
          <Badge :count="total_type" type="error">
            <Button @click="showPurchaseModal">采购单明细</Button>
          </Badge>
        </div>
        <div class="goods-item">
          <Button type="primary" @click="toSettlement" :loading="settleLoading">去结算</Button>
        </div>
      </div>
    </div>
    <purchase-modal ref="purchaseModal"></purchase-modal>
    <Modal
      :value="settleVisible"
      :mask-closable="false"
      class-name="vertical-center-modal"
      title="温馨提示"
      width="400px"
      @on-visible-change="closeSettleModal"
    >
      <div class="flex-c flex-item-center">
        <h3 class="modal-title">您的购物车存在已失效的商品！</h3>
        <p class="align-center">
          <span v-for="(item, index) in lose_effect_list" :key="index"
            >商品 <a style="margin-right: 6px">{{ item.generic_name }} </a>
            <span style="color: #ed4014">已失效</span></span
          >
        </p>
        <p class="align-center" v-if="up_stock_list.length">
          <span v-for="(item, index) in up_stock_list" :key="index"
            >商品 <a style="margin-right: 6px">{{ item.generic_name }} </a>
            <span style="color: #ed4014">库存不足</span>
          </span>
        </p>
        <p class="align-center">请移除上述商品后再进行结算操作!</p>
      </div>

      <div slot="footer">
        <div>
          <Button @click="closeSettleModal(false)">取消</Button>
          <Button type="primary" @click="toRemoveOffGoods">去移除</Button>
        </div>
      </div>
    </Modal>
    <!-- 一个spu对于多个sku选择 -->
    <multiple-specs></multiple-specs>

    <Modal v-model="covidTipVisible" :footer-hide="true" :closable="false">
      <div class="covid-content">
        <p class="covid-tip">
          <Icon type="ios-help-circle" color="orange" size="28" class="tip-icon" />
          <span>{{ covidButtonInfo.msg }}</span>
        </p>
        <div class="covid-tip-btn flex flex-item-end">
          <Button type="primary" @click="covidTipVisible = false">确定</Button>
        </div>
      </div>
    </Modal>
    <shipping-notice
      :visible.sync="shippingVisible"
      :changfanGoods="changfanGoods"
      :cf_shipping_money="cf_shipping_money"
      :cf_total="cf_total"
      :rsj-goods="rsjGoods"
      :rsj_prod_types="rsj_prod_types"
      :rsj_total="rsj_total"
      :zx-goods="zxGoods"
      :zx_shipping_money="zx_shipping_money"
      :zx_total="zx_total"
      @ok="$router.push('/purchase/stock/billingDetail')"
    ></shipping-notice>
    <auth-remind-modal v-model="remindVisible" :show-close="showCloseBtn" @cancelAuth="cancelAuth"></auth-remind-modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'libs/util'; // Some commonly used tools
import io from 'libs/io'; // Http request
import cloneDeep from 'lodash.clonedeep';
/* eslint-disable */
import renderHeader from '@/mixins/renderHeader';
import search from '@/mixins/search';
import { mapGetters, mapMutations, mapState } from 'vuex';
import { $operator } from '@/libs/operation';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  supplier_id: '',
};

export default {
  name: 'list',
  mixins: [renderHeader, search],
  components: {
    purchaseModal: () => import('./components/purchaseModal'),
    multipleSpecs: () => import('./components/multipleSpecs'),
    shippingNotice: () => import('./components/shippingNotice'),
    AuthRemindModal: () => import('./components/AuthRemindModal'),
  },
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getGoodsskuList',
      tableCols: [
        { title: '编号', key: 'code', minWidth: 120 },
        { title: '介绍图', slot: 'img', align: 'center', minWidth: 80 },
        {
          title: '商品名',
          slot: 'generic_name',
          tooltip: true,
          minWidth: 120,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '添加多规格商品时，会将规格名作为采购单中的商品名'),
        },
        { title: '类型', key: 'prod_type_desc', minWidth: 70, tooltip: true },
        { title: '规格	', slot: 'prod_spec_text', minWidth: 100, tooltip: true },
        { title: '单位	', key: 'prod_unit_text', minWidth: 50, tooltip: true },
        { title: '供应商', key: 'supplier_name', minWidth: 120 },
        {
          title: '销售商',
          key: 'seller',
          minWidth: 120,
          renderHeader: (h, params) => this._renderHeader(h, params, '诊所采购时货品金额部分，发票由销售商开具'),
        },
        // {title: '批号', key: 'batch_code', minWidth: 140},
        // {title: '生产日期', slot: 'prod_date', minWidth: 140},
        // {title: '过期时间', slot: 'expire_date', minWidth: 140},
        {
          title: '采购单价',
          slot: 'clinic_price',
          minWidth: 120,
          renderHeader: (h, params) => this._renderHeader(h, params, '采购单价 = 货品金额 + 平台使用费'),
        },
        {
          title: '当前库存',
          slot: 'stock_num',
          minWidth: 90,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '当库存状态为“先下后审”时，需要供应商审核后才能知道库存是否足够'),
        },
        // {title: '最低起购', slot: 'start_purchase', minWidth: 80},
        { title: '采购数量', slot: 'quantity', width: 100 },
        { title: '操作', slot: 'operate', width: 90 },
      ],
      tableLoading: true,
      list: [],
      total: 0,
      prodTypes: [],
      prodStatusDesc: {},
      supplierList: [], // 供应商列表
      is_goods_off: false,
      up_stock_list: [],
      lose_effect_list: [],

      covidTipVisible: false, // 新冠拦截提示弹窗
      covidButtonInfo: {
        msg: '新冠抗疫专方采购正在升级中，明天正式开售', // 升级文案
        status: '', // upgrade:升级中， normal:正常
      }, // 是否升级中
      shippingVisible: false,
      shippingInfo: {},
      changfanGoods: [], // 常繁商品
      cf_shipping_money: 0, // 常繁包邮价格
      cf_supplier_id: '', // 常繁供应商id
      cf_total: 0, // 常繁商品总价
      rsj_supplier_id: '', // 上海树家供应商id
      rsj_shipping_money: 0, // 树家包邮价格
      rsj_total: 0, // 树家商品总价
      rsj_prod_types: [], // 树家产品类型
      rsjGoods: [],
      zx_supplier_id: '', // 至信供应商id
      zx_shipping_money: 0, // 至信包邮价格
      zxGoods: [],
      zx_total: 0, // 至信商品总价
      settleLoading: false,
      cf_customer_qr_code: '', // 常繁供应商客服二维码
      sj_customer_qr_code: '', // 上海树家客服二维码
      remindVisible: false,
      showCloseBtn: false,
      shouldShowRemind: true,
      cf_is_activity_on: '0', // 三周年庆典
      activityVisible: false,
    };
  },
  deactivated() {
    this.$destroy('PurchaseModal');
    this.$destroy(true);
    console.log(this.$store.state.purchase, 'this.$store.state.purchase');
  },
  computed: {
    // 当前spu下是否有多个sku
    isHasMoreSKU() {
      return row => {
        return row.skus.length == 1 ? false : true;
      };
    },

    ...mapState('purchase', {
      // select_goods: state => state.selectGoodsList,
      settleVisible: state => state.settleVisible,
    }),
    ...mapGetters('purchase', ['total_price', 'total_num', 'total_type', 'selectGoodsList']),
  },
  created() {
    // this.checkGoods()
  },
  mounted() {
    console.log(this.$store.state.purchase);
    this.getStatusList().then(() => {
      this.queryFormData = S.merge(this.queryFormData, this.$route.query);
      this.submitQueryForm(true);
    });
    this.getSupplierList();
    this.getCovidbutton();
    this.getClinicCustomerServiceList();
    this.getCFActivityInfo();
    console.log(this.$store.state.purchase, 'this.$store.state.purchase');
  },
  methods: {
    // 跳转新冠抗疫采购专区
    // jumpCovid() {
    // jumpCovid() {
    //   if (this.covidButtonInfo.status == 'upgrade') {
    //     this.covidTipVisible = true;
    //   } else {
    //     this.$router.push('/purchase/stock/COVIDList');
    //   }
    // },
    // 跳转药食同源采购专区
    // jumpYSHomology(){
    //   this.$router.push('/purchase/stock/ys-homology')
    // },

    cancelAuth() {
      this.remindVisible = false;
      this.shouldShowRemind = false;
    },
    closeSettleModal(visible) {
      console.log('-> visible', visible);
      if (!visible) {
        this.CHANGE_SETTLE_VISIBLE(visible);
      }
    },
    toRemoveOffGoods() {
      this.CHANGE_SETTLE_VISIBLE(false);
      this.SHOW_PURCHASE_MODAL(true);
    },
    //去结算
    toSettlement() {
      if (!this.total_num) {
        this.$Message.error('请先选择商品再进行结算');
        return;
      }
      for (const goods of this.selectGoodsList) {
        console.log('-> %c goods  === %o ', 'font-size: 15px', goods);
        // if(goods.quantity>Number(goods.stock_num)&&goods.stock_mod=='1'){
        //   this.$Message.error(`商品${goods.generic_name}采购数量不能大于当前库存数量`)
        //   return
        // }
        if (
          (goods.purchase_info.type === 'MUl' && goods.quantity % goods.purchase_info.num !== 0) ||
          Number(goods.quantity) < Number(goods.purchase_info.num)
        ) {
          this.$Message.error(`商品${goods.name || goods.generic_name}采购数量不符合最低起购要求`);
          return;
        }
      }
      this.settleLoading = true;
      this.$store
        .dispatch('purchase/getCartList', {
          isOpenModal: false,
          isMerge: false,
        })
        .then(res => {
          console.log(this.selectGoodsList);
          this.up_stock_list = this.selectGoodsList.filter(item => item.isUpStock);
          console.log(
            '-> this.up_stock_list.length||this.lose_effect_list.length',
            this.up_stock_list.length || this.lose_effect_list.length
          );
          this.lose_effect_list = this.selectGoodsList.filter(item => item.is_effective === '0');
          if (this.up_stock_list.length || this.lose_effect_list.length) {
            this.CHANGE_SETTLE_VISIBLE(true);
            this.settleLoading = false;
          } else {
            const listof = {};
            const tc_listof = {};
            this.selectGoodsList.map(item => {
              if (item.is_taocan_goods == 1) {
                tc_listof[item.id] = item.quantity;
              } else {
                listof[item.id] = item.quantity;
              }
            });
            // 校验常繁库存 + 供应商对应的包邮价
            this.$api
              .checkStockGoodsSku({ listof, tc_listof })
              .then(res => {
                this.checkGoodSkuFreeInfo(res);
              })
              .catch(err => {})
              .finally(() => (this.settleLoading = false));
          }
        })
        .catch(err => {
          this.settleLoading = false;
          {
          }
        });
      // ! 如果加入购物车的商品下架 ，所以在结算前check
    },
    ...mapMutations('purchase', [
      'SHOW_PURCHASE_MODAL',
      'CHANGE_SELECT_GOODS',
      'CHANGE_SETTLE_VISIBLE',
      'SHOW_MUTIPLE_SPECS_MODAL', // sku弹窗
      'SET_MODAL_SKULIST',
    ]),
    /**
     * @description：加入采购单
     *  1:当前spu下只有一个sku时,加入采购单，
     *  2:当前spu下有多个sku时,走弹窗添加
     */
    addPurchaseOrder(index) {
      if (this.list[index].skus.length == 1) {
        let purchase_info = this.list[index].skus[0] && this.list[index].skus[0].purchase_info;
        let sku_item = this.list[index].skus[0];

        if (this.list[index].quantity < Number(purchase_info.num)) {
          this.list[index].quantity = Number(purchase_info.num);
        }
        if (this.list[index].quantity > Number(sku_item.stock_num) && sku_item.stock_mod == '1') {
          this.$Message.error(`商品${sku_item._name}采购数量不能大于当前库存`);
          return;
        }
        let copy_good = cloneDeep(this.list[index]);
        this.CHANGE_SELECT_GOODS(this.mergeSku(copy_good, this.list[index].skus));
      } else {
        this.SET_MODAL_SKULIST(this.mergeSku(this.list[index], this.list[index].skus));
        this.SHOW_MUTIPLE_SPECS_MODAL(true);
      }
    },
    /**
     * @description:将sku的合并成一条数据
     *  1:如果只有一个sku的话，将暴露出该sku的对象
     *  2:如果是多个sku的话,将暴露出整合spu-sku的数组数据，于sku弹窗中展示
     * */
    mergeSku(spuItem, list) {
      let mergeList = [];
      // 套餐逻辑处理
      if (spuItem.is_taocan_goods == 1) {
        let taocan_id = cloneDeep(spuItem.id);
        mergeList =
          list &&
          list.map(item => {
            return { quantity: spuItem.quantity, ...item, id: taocan_id, is_taocan_goods: spuItem.is_taocan_goods };
          });
      } else {
        mergeList =
          list &&
          list.map(item => {
            return { quantity: spuItem.quantity, ...item };
          });
      }
      return list.length == 1 ? mergeList[0] : mergeList;
    },
    /**
     * @description:改变采购数量
     *  1: unit_type: MUL(倍数起购) NUM(件数起购)
     */
    changeUnit(index) {
      let purchase_info = this.list[index].skus[0] && this.list[index].skus[0].purchase_info;
      let quantity = this.list[index].quantity;
      const unit_type = purchase_info.type;
      if (quantity < Number(purchase_info.num) || !quantity) {
        console.log('你笑了');
        this.list[index].quantity = Number(purchase_info.num);
        return;
      }
      if (unit_type === 'MUL') {
        if (quantity % purchase_info.num !== 0) {
          this.list[index].quantity = this.list[index].quantity - (quantity % purchase_info.num);
        }
      } else {
        this.list[index].quantity = quantity;
      }
    },
    // 获取供应商列表
    getSupplierList() {
      let res = io.get('/clinic/supplier.list', {
        data: { pms: 1, status: 'ON' },
      });
      res.then(res => {
        this.supplierList = res.suppliers;
      });
    },
    showPurchaseModal() {
      if (this.total_type) {
        this.$store.dispatch('purchase/getCartList', {
          isOpenModal: true,
          isMerge: false,
        });
      } else {
        this.$Message.error('请先选择采购商品');
      }
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    getStatusList() {
      let res = io.get('/clinic/product.product.options', {
        data: {
          source: 'purchase_list',
        },
      });
      res
        .then(data => {
          // console.log(data,"data");
          this.prodTypes = S.descToArrHandle(data.prodTypes).sort((a, b) => a.sort - b.sort);
          this.prodStatusDesc = data.prodStatusDesc;
        })
        .catch(error => {
          {
          }
        });
      return res;
    },
    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.total = Number(data.total);
          // this.handleInitUnit(data.list)
          this.list = this.handleInitUnit(data.list);
          if (data.need_into_license !== '0' && this.shouldShowRemind) {
            this.remindVisible = true;
          }
          this.showCloseBtn = data.need_into_license === '2';
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },
    handleInitUnit(list) {
      list.map(item => {
        // 只有当当前spu下挂载一个sku时,设置相应的unit，否则默认设置一键起购
        if (item.skus.length == 1) {
          item.quantity = Number(item.skus[0].purchase_info.num);
          // 设置采购价
          item.clinic_price = item.min_clinic_price;
        } else {
          item.skus.forEach(sku_item => (sku_item.quantity = 0));
          item.quantity = 1;
        }
      });
      return list;
    },
    initSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    getCovidbutton() {
      this.$api.getCovidbutton().then(res => {
        this.covidButtonInfo = res;
      });
    },

    checkGoodSkuFreeInfo(res) {
      console.log('-> res', res);
      this.cf_supplier_id = res.cf_info.cf_supplier_id;
      this.cf_shipping_money = Number(res.cf_info.max_money) || 0;
      this.rsj_supplier_id = res.rsj_info.rsj_supplier_id;
      this.rsj_shipping_money = Number(res.rsj_info.max_money) || 0;
      this.rsj_prod_types = S.descToArrHandle(res.rsj_info.tooltip_prod_types);
      this.zx_supplier_id = res.zx_info.zx_supplier_id;
      console.log('-> this.zx_supplier_id', this.zx_supplier_id);
      this.zx_shipping_money = Number(res.zx_info.max_money) || 0;
      console.log('-> this.zx_shipping_money', this.zx_shipping_money);
      this.checkShipping(res);
    },
    // 套餐商品id，类似:"TC-2858"，为后端给的假数据，如业务有需要用到的业务，同后端商量
    checkShipping(res) {
      let changfanGoods = res.list.filter(item => item.supplier_id === this.cf_supplier_id); // 常繁商品
      let rsjGoods = res.list.filter(item => item.supplier_id === this.rsj_supplier_id);
      let zxGoods = res.list.filter(item => item.supplier_id === this.zx_supplier_id);
      if ((res.cf_info.free_ship_status === '0' || !changfanGoods.length) && !rsjGoods.length && !zxGoods.length) {
        // 常繁包邮未设置或商品列表中没有常繁商品时不执行 && 没有树家供应商货物 && 没有至信商品
        this.$router.push('/purchase/stock/billingDetail');
      } else {
        // 计算对应供应商总价
        // 计算常繁商品总价
        let cf_total = 0;
        changfanGoods.forEach(item => {
          item.prod_total = $operator.multiply(Number(item.num), Number(item.clinic_price));
          cf_total = $operator.add(cf_total, item.prod_total);
        });
        // 当选择到可直接包邮的商品时（不在8种产品类型中的商品），默认全部包邮
        let isShipping = false; // 是否包邮
        isShipping = !rsjGoods.every(rsj => {
          return this.rsj_prod_types.some(rsj_type => {
            return rsj_type.id === rsj.prod_type;
          });
        });

        // 计算树家商品总价
        let rsj_total = 0;
        if (!isShipping) {
          rsjGoods.forEach(item => {
            item.prod_total = $operator.multiply(Number(item.num), Number(item.clinic_price));
            rsj_total = $operator.add(rsj_total, item.prod_total);
          });
        }

        let zx_total = 0;
        zxGoods.forEach(item => {
          item.prod_total = $operator.multiply(Number(item.num), Number(item.clinic_price));
          zx_total = $operator.add(zx_total, item.prod_total);
        });

        if (cf_total < this.cf_shipping_money) {
          this.changfanGoods = changfanGoods;
          this.cf_total = cf_total;
        } else {
          this.changfanGoods = [];
        }

        if (!isShipping && rsj_total < this.rsj_shipping_money) {
          this.rsjGoods = rsjGoods;
          this.rsj_total = rsj_total;
        } else {
          this.rsjGoods = [];
        }

        if (zx_total < this.zx_shipping_money) {
          this.zxGoods = zxGoods;
          this.zx_total = zx_total;
        } else {
          this.zxGoods = [];
        }

        if (this.changfanGoods.length > 0 || this.rsjGoods.length > 0 || this.zxGoods.length > 0) {
          this.shippingVisible = true;
        } else {
          this.$router.push('/purchase/stock/billingDetail');
        }
      }
    },
    getClinicCustomerServiceList() {
      let params = {};
      this.$api
        .getClinicCustomerServiceList(params)
        .then(res => {
          this.cf_customer_qr_code = res.list.find(item => item.supplier_code === 'CF').qr_code;
          this.sj_customer_qr_code = res.list.find(item => item.supplier_code === 'RSJ').qr_code;
        })
        .catch(err => {});
    },
    getCFActivityInfo() {
      let params = {};
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getCFActivityInfo(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.cf_is_activity_on = res.is_activity_on;
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    closePop() {
      this.activityVisible = false;
    },
  },
  beforeDestroy() {
    this.remindVisible = false;
    this.CHANGE_SETTLE_VISIBLE(false);
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
  // beforeDestroy(){
  // 	console.log(3213)
  // 	this.CHANGE_SETTLE_VISIBLE(false)
  // },
  beforeRouteEnter(to, from, next) {
    // this.
    next(vm => {
      vm.CHANGE_SETTLE_VISIBLE(false);
    });
  },
};
</script>

<style lang="less" scoped>
.goods-item {
  margin-right: 14px;
}

.money {
  font-size: 16px;
  font-weight: 600;
  color: #ed4014;
}

.modal-title {
  text-align: center;
  margin-bottom: 15px;
  font-size: 16px;
}

.align-center {
  //text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex-wrap: wrap;
}

.covid-content {
  padding: 20px;

  .covid-tip {
    text-align: center;
    font-size: 14px;
    display: flex;
    align-items: center;

    .tip-icon {
      margin-right: 10px;
    }
  }

  .covid-tip-btn {
    margin-top: 20px;
  }
}

.tip {
  display: inline-block;
  margin-top: 4px;
  font-size: 12px;
  font-weight: 300;
  color: #e5634b;
  line-height: 17px;
}

.wechat-box {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  width: 116px;
  height: 46px;
  background: #f6f6f9;
  border-radius: 100px 0 0 100px;
  padding: 7px;
  box-sizing: border-box;
  color: #60607d;
}

.wechat {
  font-size: 30px;
  color: #60607d;
}

.qr-code-box {
  width: 220px;
  text-align: center;
  padding: 10px;
  box-sizing: border-box;
  border-radius: 8px;
  font-size: 14px;

  .note-box {
    background: #f9f9fb;
    border-radius: 4px;
    padding: 4px;
    box-sizing: border-box;
    font-size: 12px;
    color: #9599ac;
    line-height: 16px;
  }
}

.pur-btn-box {
  position: relative;
  display: inline-block;

  .limit-discount {
    position: absolute;
    display: block;
    top: -10px;
    width: 88px;
    height: 20px;
    left: 50%;
    margin-left: -44px;
  }
}

.custom-box {
  display: flex;
  background: #ffffff;
  box-shadow: 0px 0px 8px 0px #b1bbce;
  border-radius: 8px;
  padding-top: 20px;

  .bold {
    font-weight: bold;
  }
}
.activity-box {
  //width: 168px;
  //height: 156px;
  background-color: #be202f;
  border-radius: 4px;
  padding: 0 3px 3px;
  .activity-top {
    position: relative;
    padding-top: 6px;
    font-weight: 600;
    font-size: 12px;
    color: #fff7d4;
    text-align: center;
    margin-bottom: 6px;
    .title-icon {
      position: absolute;
      width: 72px;
      height: 16px;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }
    .close-icon {
      position: absolute;
      top: 10px;
      right: 10px;
      width: 8px;
      height: 8px;
      cursor: pointer;
    }
  }
  .rule-box {
    background: #f8d5a2;
    border-radius: 4px;
    font-size: 12px;
    color: #be202f;
    padding: 9px;
    white-space: normal;
  }
}
</style>
