<template>
  <Modal :value="value" :title="title" width="960" @on-visible-change="visibleChange">
    <div class="content">
      <!-- 审核驳回 -->
      <div class="mb20 error flex flex-item-align" v-if="status === 'REJECTED' && custom_status !== 'edit'">
        审核驳回：{{ rejected_desc }}
      </div>

      <!-- 发票明细 -->
      <div>
        <div>
          <p class="title" v-if="status !== 'FINISHED'">发票明细</p>
          <Table :columns="columns" :data="invoiceList" max-height="450">
            <template slot-scope="{ row, index }" slot="amount"> ￥{{ row.amount }} </template>

            <!-- 发票抬头 -->
            <template slot-scope="{ row, index }" slot="organization_name">
              {{ row.organization_name || '-' }}
            </template>

            <!-- 税号 -->
            <template slot-scope="{ row, index }" slot="organization_code">
              {{ row.organization_code || '-' }}
            </template>

            <!-- 状态 -->
            <template slot-scope="{ row, index }" slot="status_text">
              <span :class="{ red: row.status !== 'FINISHED' }">{{ row.status_text }}</span>
            </template>
          </Table>
          <p class="mt20" v-if="status == 'NORMAL' || custom_status === 'edit'">
            {{ tip }}
            <a @click="toInvoice">修改正确信息 ></a>
          </p>
        </div>
      </div>

      <!-- 联系人信息 -->
      <div class="mt20" v-if="status !== 'FINISHED'">
        <p class="title">联系人信息</p>
        <Row>
          <Col span="10">
            <Form ref="formValidate" :model="formValidate" :rules="ruleValidate" :label-width="100" label-colon>
              <FormItem label="联系人姓名" prop="name">
                <Input v-model.trim="formValidate.name" placeholder="请输入联系人姓名" :disabled="disabled"></Input>
              </FormItem>

              <FormItem label="联系人手机号" prop="mobile">
                <Input v-model.trim="formValidate.mobile" placeholder="请输入联系人手机号" :disabled="disabled"></Input>
              </FormItem>

              <FormItem label="联系人邮箱" prop="email">
                <Input v-model.trim="formValidate.email" placeholder="请输入联系人邮箱" :disabled="disabled"></Input>
              </FormItem>
            </Form>
          </Col>
        </Row>
      </div>

      <p v-if="status === 'FINISHED'" class="mt20">
        针对于已开具的发票，系统已经处理并发送到邮箱{{ email }}中，请注意查收；如果邮箱信息错误或者未收到，请联系平台
      </p>
    </div>
    <div slot="footer" class="footer">
      <Button type="default" @click="closeable" v-if="status === 'NORMAL' || custom_status === 'edit'">取消</Button>
      <Button
        type="primary"
        @click="applyEvent"
        :loading="applyLoading"
        v-if="status === 'NORMAL' || custom_status === 'edit'"
        >立即申请</Button
      >
      <Button type="primary" @click="immediateEdit" v-if="status === 'REJECTED' && custom_status !== 'edit'"
        >立即修改</Button
      >
      <Button type="primary" @click="closeable" v-if="status === 'FINISHED'">我知道了</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'applyInvoic',
  components: {},
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    status: {
      type: String,
      default: () => ''
    },
    order_code: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      columns_unStatus: [
        { title: '申请单号', key: 'apply_code', align: 'center' },
        { title: '开票方', key: 'drawer', align: 'center' },
        { title: '发票类型', key: 'type', align: 'center' },
        { title: '发票内容', key: 'content', align: 'center' },
        { title: '发票金额', slot: 'amount', align: 'center' },
        { title: '发票抬头', slot: 'organization_name', align: 'center' },
        { title: '税号', slot: 'organization_code', align: 'center' }
      ],
      columns_status: [
        { title: '申请单号', key: 'apply_code', align: 'center' },
        { title: '开票方', key: 'drawer', align: 'center' },
        { title: '发票类型', key: 'type', align: 'center' },
        { title: '发票内容', key: 'content', align: 'center' },
        { title: '发票金额', slot: 'amount', align: 'center' },
        { title: '发票抬头', slot: 'organization_name', align: 'center' },
        { title: '税号', slot: 'organization_code', align: 'center' },
        { title: '状态', slot: 'status_text', align: 'center' }
      ],
      invoiceList: [], // 发表列表数据

      /* 联系人信息 */
      formValidate: {
        name: '',
        mobile: '',
        email: ''
      },
      ruleValidate: {
        name: [{ required: true, message: '请输入联系人姓名', trigger: 'change' }],
        mobile: [
          { required: true, message: '请输入联系人手机', trigger: 'change' },
          {
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!this.regRole(value)) {
                callback(new Error('请输入正确的手机号'));
              } else {
                callback();
              }
            }
          }
        ],
        email: [
          { required: true, message: '请输入联系人邮箱', trigger: 'change' },
          {
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!this.regEmail(value)) {
                callback(new Error('请输入正确的邮箱'));
              } else {
                callback();
              }
            }
          }
        ]
      },

      rejected_desc: '', // 驳回原因
      tip: '', // 提示
      email: '', // 已经开票成功得邮箱

      // loading
      applyLoading: false, // 立即申请loading
      custom_status: ''
    };
  },
  computed: {
    title() {
      return this.status === 'FINISHED' ? '发票明细' : '申请开票';
    },
    disabled() {
      const { status, custom_status } = this;
      return status == 'REJECTED' && custom_status == '' ? true : false;
    },
    // 动态生产表格的表头
    columns() {
      const { status } = this;
      if (status === 'FINISHED') {
        return this.columns_status;
      } else {
        return this.columns_unStatus;
      }
    }
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 获取最后申请开票的收票人信息
    getInvoiceApplicant() {
      this.$api.getInvoiceApplicant().then(
        res => {
          this.formValidate.name = res.applicant.name;
          this.formValidate.mobile = res.applicant.mobile;
          this.formValidate.email = res.applicant.email;
        },
        err => {}
      );
    },
    // 关闭弹窗
    closeable() {
      this.$emit('input', false);
    },
    // 弹窗属性值发生变更
    visibleChange(val) {
      if (val) {
        this.getInvoiceApplicant();
        if (this.status == 'FINISHED' || this.status == 'REJECTED') {
          this.getInvoiceList();
        } else {
          this.getInvoiceDetail();
        }
      } else {
        this.closeable();
        this.handleReset();
        this.custom_status = '';
        this.email = '';
      }
    },
    // 点击立即申请按钮
    applyEvent() {
      const { status } = this;
      if (status === 'NORMAL') {
        this.immediateApply();
      } else {
        this.immediateUpdate();
      }
    },
    // 立即申请
    immediateApply() {
      this.$refs.formValidate.validate(valid => {
        if (valid) {
          this.applyLoading = true;
          let params = {
            order_code: this.order_code,
            ...this.formValidate
          };
          this.$api
            .invoiceCommit(params)
            .then(res => {
              this.$Message.success('申请成功');
              this.closeable();
              this.$emit('success', true);
            })
            .catch(err => {
              {};
              this.$emit('success', true);
              this.closeable();
            })
            .finally(() => {
              this.applyLoading = false;
            });
        }
      });
    },
    // 修改发票
    immediateUpdate() {
      this.$refs.formValidate.validate(valid => {
        console.log('invoiceList', this.invoiceList);
        if (valid) {
          this.applyLoading = true;
          let params = {
            apply_codes: this.handleApplyCode(),
            ...this.formValidate
          };
          this.$api
            .invoiceUpdate(params)
            .then(res => {
              this.$Message.success('申请成功');
              this.closeable();
              this.$emit('success', true);
            })
            .catch(err => {
              {};
              this.$emit('success', true);
              this.closeable();
            })
            .finally(() => {
              this.applyLoading = false;
            });
        }
      });
    },
    // 立即修改
    immediateEdit() {
      this.custom_status = 'edit';
    },

    // 清空校验
    handleReset() {
      if (this.$refs['formValidate']) {
        this.$refs['formValidate'].resetFields();
      } else {
        this.formValidate.name = '';
        this.formValidate.mobile = '';
        this.formValidate.email = '';
      }
    },

    // 跳转发票页面
    toInvoice() {
      this.closeable();
      this.$router.push('/setting/Invoice');
    },

    // 手机号校验
    regRole(tel) {
      let flag;
      let reg = /^1[3456789]\d{9}$/;
      flag = reg.test(tel);
      return flag;
    },
    // 邮箱校验
    regEmail(value) {
      return /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(value);
    },

    /* api */
    getInvoiceDetail() {
      let params = {
        order_code: this.order_code
      };
      this.$api
        .getInvoiceDetail(params)
        .then(res => {
          this.invoiceList = res.list;
          this.tip = res.tip;
        })
        .catch(err => {
          {};
        });
    },

    // 获取当前发票列表的applyCode的集合,目前大多只有一个,后面可能会有多个,所以转成数组传递
    handleApplyCode() {
      let apply_codes = [];
      this.invoiceList &&
        this.invoiceList.forEach(invoice_item => {
          apply_codes.push(invoice_item.apply_code);
        });
      return apply_codes || [];
    },

    /* api 获取已经提交的发票列表*/
    getInvoiceList() {
      const { status } = this;
      let params = {
        status: status == 'REJECTED' ? 'REJECTED' : '',
        order_code: this.order_code
      };
      this.$api
        .getInvoiceList(params)
        .then(res => {
          this.invoiceList = res.list;
          this.tip = res.tip;
          this.email = res.contact_info.email;
          this.rejected_desc = res.rejected_desc;
          this.formValidate = res.contact_info;
        })
        .catch(err => {
          {};
        });
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
.content {
  .error {
    background: rgb(240, 121, 121);
    padding: 4px 10px;
    color: #fff;
  }
}

.footer {
}
</style>

<style lang="less" scoped>
// common less
.mt20 {
  margin-top: 20px;
}
.mb20 {
  margin-bottom: 20px;
}
.title {
  font-size: 14px;
  color: #666;
  font-weight: 600;
}
.red {
  color: red;
}
</style>
