<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      stripe
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template slot="header">

        <div class="auto-box">
          <div>
            <Tooltip max-width="240" style="margin-right: 5px">
              <div slot="content">点击{{ isAutoPay === 'ON' ? '关闭' : '开启' }}“客户一件代发单”的一键支付</div>
              <i-switch v-model="isAutoPay" true-value="ON" false-value="OFF" size="small" :before-change="changeAutoPay" />
            </Tooltip>
            已{{ isAutoPay === 'ON' ? '开启' : '关闭' }}“客户一件代发单”的一键支付
            <Tooltip
              content="开启一键支付后，在银联余额足额的情况下，将进行自动抵扣“客户一件代发订单”的支付"
              max-width="200"
              style="cursor: pointer"
            >
              <Icon type="md-help-circle" size="16" style="margin-right: 5px" />
            </Tooltip>
          </div>
          <!--      <Checkbox v-model="isAutoPay" true-value="ON" false-value="OFF" style="border-radius: 50%;" @click.prevent.native="changeAutoPay"></Checkbox>-->
        </div>
        <Divider style="width: calc(100% + 32px); background: #f0f0f0; margin-left: -16px; height: 10px" />
        <div class="form-warpper" style="margin-top: 10px">
          <Form class="form-warpper_left" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <Row>
              <Col>
                <FormItem>
                  <Input type="text" v-model="queryFormData.order_code" placeholder="订单编号/交易号" />
                </FormItem>
              </Col>
              <Col>
                <FormItem>
                  <Select v-model="queryFormData.list_status" style="" placeholder="请选择状态" clearable>
                    <Option :value="item.key" v-for="(item, index) in statusDesc" :key="index">{{ item.desc }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col>
                <FormItem>
                  <Select v-model="queryFormData.store_status" placeholder="请选择入库状态" clearable>
                    <Option :value="item.key" v-for="(item, index) in storeStatusDesc" :key="index">{{ item.desc }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col>
                <FormItem>
                  <Select v-model="queryFormData.type" placeholder="请选择订单类型" clearable>
                    <Option :value="item.key" v-for="(item, index) in typeDesc" :key="index">{{ item.desc }}</Option>
                  </Select>
                </FormItem>
              </Col>
              <Col>
                <FormItem>
                  <Select v-model="queryFormData.sign_status" placeholder="请选择签收状态" clearable>
                    <Option :value="item.key" v-for="(item, index) in signStatusDesc" :key="index">{{ item.desc }}</Option>
                  </Select>
                </FormItem>
              </Col>
            </Row>
            <Row>
              <Col>
                <FormItem>
                  <unionpay-supplier-search
                    ref="supplier-search"
                    v-model="queryFormData.supplier_id"
                    :isClearable="true"
                  ></unionpay-supplier-search>
                </FormItem>
              </Col>
              <Col>
                <FormItem>
                  <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                  <Dropdown trigger="click" class="mr-10" @on-click="exportExcel">
                    <Button :loading="exportLoading">
                      导出
                      <Icon type="ios-arrow-down" />
                    </Button>
                    <DropdownMenu slot="list">
                      <DropdownItem name="order">导出订单明细</DropdownItem>
                      <DropdownItem name="goods">导出商品明细</DropdownItem>
                    </DropdownMenu>
                  </Dropdown>
                  <span class="list-reset-btn" @click="initSearch"
                  ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
                  >
                </FormItem>
              </Col>
            </Row>
          </Form>
        </div>

        <div class="table-wrapper">
          <div class="panel-nav">
            <a class="nav" :class="{ active: !queryFormData.list_status }" @click.prevent.capture="onStatusChange('')">
              全部
            </a>
            <a
              class="nav"
              v-for="(item, index) in statusDesc"
              :class="{ active: $route.query.list_status == item.key }"
              :key="index"
              @click.prevent.capture="onStatusChange(item.key)"
            >
              {{ item.desc }}
              <Tag :color="getTagColor(item.key)">{{ status_count[item.key] }}</Tag>
            </a>
          </div>
        </div>
      </template>

      <template slot-scope="{ row, index }" slot="total_fee">
        <div>￥{{ row.total_fee }}</div>
      </template>

      <template slot="store_status_text" slot-scope="{ row }">
        <!-- <p>{{ row.store_status === 'WAIT' ?span '-' : row.store_status_text }}</p> -->
        <span>{{ row.store_status_text }}</span>
      </template>

      <!-- <template slot="sign_status_desc" slot-scope="{ row }"> -->
      <!--   <div> -->
      <!--     <div> -->
      <!--       {{ row.sign_status_desc || '-' }} -->
      <!--     </div> -->
      <!--   </div> -->
      <!-- </template> -->

      <template slot-scope="{ row }" slot="action">
        <a class="mr10" @click="jump(row, 'sendGoods')" v-if="Number(row.can_put_stock) === 1">入库</a>
        <a class="mr10" @click="jump(row)" v-if="row.status === 'WAIT_PAY'">去支付</a>
        <a v-if="row.invoice_status === 'NORMAL'" class="mr10" @click="checkInvoiceStatus(row)">申请开票</a>
        <span v-if="row.invoice_status === 'PROCESSING'" class="mr10 readonly">开票处理中</span>
        <a v-if="row.invoice_status === 'FINISHED'" class="mr10" @click="applyInvoicing(row)">发票明细</a>
        <a v-if="row.invoice_status === 'REJECTED'" class="mr10" @click="applyInvoicing(row)">开票驳回</a>
        <a v-if="row.can_sign === '1'" class="mr10" @click="jump(row, 'receipt')">确认收货</a>
        <!-- <a class="mr10" @click="jump(row)">确认收货</a> -->
        <a @click="jump(row)">详情</a>
      </template>
    </standard-table>

    <!-- 申请开票 -->
    <applyInvoicModal
      v-model="applyVisible"
      :order_code="order_code"
      :status="invoice_status"
      @success="submitQueryForm"
    ></applyInvoicModal>

    <!-- 发票信息未完善提示 -->
    <invoice-info-tip
      v-model="invoiceInfoVisible"
      :tips-content="invoiceTipsContent"
      :tipsType="invoiceTipsType"
    ></invoice-info-tip>

    <!-- 常繁发票提示 -->
    <cf-invoice-tip v-model="cfInvoiceInfoVisible"></cf-invoice-tip>
  </div>
</template>

<script>
/* eslint-disable */
import S from 'libs/util'; // Some commonly used tools
import io from 'libs/io'; // Http request
import * as runtime from 'libs/runtime'; // Runtime information
/* eslint-disable */
import applyInvoicModal from './components/apply-invoic-modal';
import renderHeader from '@/mixins/renderHeader';
import search from '@/mixins/search';
import invoiceInfoTip from '@/components/InvoiceModal/invoice-info-tip.vue';
import cfInvoiceTip from './components/cf-invoice-tip.vue';
import unionpaySupplierSearch from '@/components/unionpay-supplier-search/unionpay-supplier-search';
import StandardTable from "@/components/StandardTable/index.vue";

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  order_code: '', // 订单编号
  list_status: '', // 状态
  sign_status: '',
  store_status: '', // 收货状态
  type: '', // 订单类型
  supplier_id: '',
};

export default {
  name: 'list',
  components: {
    StandardTable,
    applyInvoicModal,
    invoiceInfoTip,
    cfInvoiceTip,
    unionpaySupplierSearch,
  },
  mixins: [renderHeader, search],

  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getPmsOrderList',
      tableCols: [
        { title: '采购单号', key: 'order_code', align: 'center' },
        { title: '交易号', key: 'trade_code', align: 'center' },
        {
          title: '进货商品',
          key: 'goods_names',
          align: 'center',
          tooltip: true,
        },
        { title: '供应商', key: 'supplier', align: 'center' },
        {
          title: '销售商',
          key: 'seller',
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '诊所采购时货品金额部分，发票由销售商开具'),
        },
        { title: '下单时间', key: 'create_time', align: 'center' },
        { title: '采购数量', key: 'num', align: 'center' },
        { title: '订单金额', slot: 'total_fee', align: 'center' },
        { title: '订单类型', key: 'type_text', align: 'center' },
        { title: '订单状态', key: 'status_text', align: 'center' },
        { title: '入库状态', slot: 'store_status_text', align: 'center' },
        {
          title: '签收状态',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.sign_status_desc || '-'),
        },
        { title: '操作', slot: 'action', align: 'center', width: '140' },
      ],
      tableLoading: false,
      test: '123',
      list: [], // 采购订单列表
      total: 0,
      statusDesc: [], // 发货状态
      storeStatusDesc: [], // 入库状态
      signStatusDesc: [],
      status_count: {
        WAIT_AUDIT: 0,
        WAIT_EDIT: 0,
        WAIT_PAY: 0,
        WAIT_SHIP: 0,
        HAS_SHIP: 0,
        FINISHED: 0,
        CLOSED: 0,
      },
      typeDesc: [], // 订单类型
      /* 发票数据 */
      applyVisible: false, // 申请发票
      order_code: '', // 单号
      invoice_status: '', // 发票状态
      isAutoPay: 'OFF',
      invoiceInfoVisible: false,
      cfInvoiceInfoVisible: false, // 常繁发票提示弹窗
      exportLoading: false,
      invoiceTipsContent: '',
      invoiceTipsType: '',
    };
  },
  computed: {
    getTagColor(type) {
      return type => {
        switch (type) {
          case 'WAIT_AUDIT': // 待审核
          case 'CLI_W_PAY': // 诊所待付款
          case 'COM_W_PAY': // 省公司待付款
          case 'WAIT_PAY': // 省公司待付款
            return 'warning';
          case 'WAIT_SHIP': // 待发货
            return 'primary';
          case 'WAIT_EDIT': // 待发货
            return 'error';
          case 'HAS_SHIP': // 已发货
          case 'FINISHED': // 已完成
            return 'success';
          default:
            // 已取消
            return 'default';
        }
      };
    },
  },
  created() {
    this.getStatusList().then(() => {
      this.queryFormData = S.merge(this.queryFormData, this.$route.query);
      this.submitQueryForm(true);
    });
  },

  methods: {
    exportExcel(name) {
      const downloadMethod = name === 'goods' ? 'getPurchaseGoodsUrl' : 'getPurchaseOrderUrl';
      this.exportLoading = true;
      let params = {
        ...this.queryFormData,
      };
      this.$api[downloadMethod](params).then(
        res => {
          this.action = res.url;
          this.download(res.url);
          this.exportLoading = false;
        },
        err => {
          {
          }
          this.exportLoading = false;
        }
      );
    },
    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
    /* 发票 */
    // 申请开票
    applyInvoicing({ order_code, invoice_status }) {
      this.order_code = order_code;
      this.invoice_status = invoice_status;
      this.applyVisible = true;
    },

    checkInvoiceStatus(row) {
      if (row.is_cf === '1') {
        this.cfInvoiceInfoVisible = true;
        return;
      }

      this.$api.checkInvoiceStatus().then(res => {
        // if (res.not_pass !== '1') {
        //   this.applyInvoicing(row);
        // } else {
        //   this.invoiceInfoVisible = true;
        // }
        if (res.not_pass !== '1') {
          this.applyInvoicing(row);
        } else {
          this.invoiceTipsContent = res.msg;
          this.invoiceTipsType = res.type;
          this.invoiceInfoVisible = true;
        }
      });
    },

    // 进入详情
    jump(row, openType) {
      let query = {
        status: row.status,
        order_code: row.order_code,
        openType,
      };
      this.$router.push({
        path: '/purchase/order/detail',
        query,
      });
    },
    onStatusChange(status) {
      console.log('-> status', status);
      this.queryFormData.page = 1;
      this.queryFormData.list_status = status;
      this.submitQueryForm();
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    // 处理键值
    handlerList(obj = {}) {
      let arr = Object.keys(obj);
      let resultArr = [];
      arr.map(item => {
        resultArr.push({
          key: item,
          kw: obj[item].kw,
          desc: obj[item].desc,
          sort: obj[item].sort || '',
        });
      });
      console.log('-> %c resultArr  === %o ', 'font-size: 15px', resultArr);
      return resultArr.sort((a, b) => a.sort - b.sort);
    },
    // *api 获取枚举信息
    getStatusList() {
      let res = io.get('/clinic/pms.order.options');
      res
        .then(data => {
          console.log(data, 'data');
          this.statusDesc = this.handlerList(data.listStatusDesc);
          this.storeStatusDesc = this.handlerList(data.storeStatusDesc);
          this.typeDesc = this.handlerList(data.typeDesc);
          this.signStatusDesc = this.handlerList(data.signStatusDesc);
        })
        .catch(error => {
          {
          }
        });
      return res;
    },
    // *api 获取采购单列表
    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.total = data.total;
          this.list = data.list;
          this.status_count = data.status_count;
          this.isAutoPay = data.self_auto_pay;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },
    handleList(list) {
      return list;
    },
    initSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.$refs['supplier-search'].clear();
      this.submitQueryForm();
    },
    changeAutoPay() {
      return new Promise(resolve => {
        this.$Modal.confirm({
          title: `确认${this.isAutoPay === 'ON' ? '关闭' : '开启'}`,
          content: `<p>是否确认${this.isAutoPay === 'ON' ? '关闭' : '开启'}“客户一件代发单”的一键支付</p>`,
          onOk: () => {
            this.changeSelfAutoPay();
          },
        });
      });
    },
    changeSelfAutoPay() {
      let act = this.isAutoPay === 'ON' ? 'OFF' : 'ON';
      let params = { act };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .changeSelfAutoPay(params)
        .then(res => {
          this.$Message.success(`${act === 'ON' ? '开启' : '关闭'}成功`);
          this.getsList();
        })
        .catch(err => {});
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.mr10 {
  margin-right: 10px;
}

.readonly {
  color: #ccc;
}

.auto-box {
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-sizing: border-box;
  height: 25px;
  line-height: 32px;
  margin-bottom: 7px;
  margin-top: -10px;

  ::v-deep .ivu-checkbox-inner {
    border-radius: 50%;
  }
}
</style>
