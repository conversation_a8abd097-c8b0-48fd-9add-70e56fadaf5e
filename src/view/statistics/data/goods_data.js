export const overviewOptions_MIL = [
  {
    value: 'paid_goods_num',
    label: '支付件数',
  },
  {
    value: 'paid_amount',
    label: '支付金额',
    isMoney: true
  },
  {
    value: 'goods_num',
    label: '在售商品数',
  }
  , {
    value: 'refunded_amount',
    label: '退款金额',
    isMoney: true
  }, {
    value: 'hot_goods',
    label: '销量最佳',
  }, {
    value: 'reserve_num',
    label: '发起预约数',
  }, {
    value: 'confirmed_reserve_num',
    label: '确认预约数',
  }];
export const overviewOptions_HIS = [
  {
    value: 'pres_num',
    label: '已开具处方数',
  },
  {
    value: 'paid_pres_num',
    label: '已支付处方数',
  },
  {
    value: 'assigned_pres_num',
    label: '已取药处方数',
  }
  , {
    value: 'order_cv',
    label: '已服务用户数',
  }, {
    value: 'turnover',
    label: '营业额',
    isMoney: true,
  }, {
    value: 'price_per_pres',
    label: '诊疗均价（元）',
    isMoney: true,

  }, {
    value: 'refunded_amount',
    label: '成功退款金额（元）',
    isMoney: true
  }, {
    value: 'hot_pres',
    label: '常用方剂',
  }, {
    value: 'hot_medicine',
    label: '常用中药饮片',
  }]


  /**
 * @description: 商品分析卡片枚举数据
 * isPercent: { 当前为百分比展示 }
 * isThousandth: { 当前为千分位展示 }
 * isMoney: { 当前为金额展示 }
 *
*/
export const goods_card_options = [
  {
    label: '中药饮片销售额',
    value: 'herbs_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '中药饮片销售数量(kg)',
    value: 'herbs_quantity',
    current: '',
    last: '',
  },
  {
    label: '中药饮片代煎次数(次)',
    value: 'decoct_num',
    current: '',
    last: '',
  },
  {
    label: '中药饮片代煎金额',
    value: 'decoct_turnover',
    current: '',
    last: '',
  },
  {
    label: '膏方销售额',
    value: 'plaster_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '膏方销售件数(件)',
    value: 'plaster_num',
    current: '',
    last: '',
  },
  // {
  //   label: '食品销售额',
  //   value: 'food_turnover',
  //   current: '',
  //   last: '',
  //   isMoney: true,
  // },
  // {
  //   label: '食品销售件数(件)',
  //   value: 'food_num',
  //   current: '',
  //   last: '',
  // },
  {
    label: '微生态销售额',
    value: 'microecolog_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '微生态数量(件)',
    value: 'microecolog_num',
    current: '',
    last: '',
  },
  {
    label: '代茶销售额',
    value: 'replace_tea_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '代茶数量(件)',
    value: 'replace_tea_num',
    current: '',
    last: '',
  },{
    label: '食养销售额',
    value: 'chronic_disease_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '食养数量(件)',
    value: 'chronic_disease_num',
    current: '',
    last: '',
  },
  {
    label: '理疗服务销售额',
    value: 'treat_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '理疗服务销售件数(件)',
    value: 'treat_num',
    current: '',
    last: '',
  },
  {
    label: '挂号费',
    value: 'reg_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '问诊费',
    value: 'con_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
  {
    label: '通兑券销售额',
    value: 'exchange_turnover',
    current: '',
    last: '',
    isMoney: true,
  },
]

export const goods_tag_sales = [
  {
    label: '中药饮片',
    kw: 'herbs',
  },
  {
    label: '膏方',
    kw: 'plaster',
  },
  {
    label: '微生态',
    kw: 'wst',
  },
  {
    label: '食养',
    kw: 'chronic_disease',
  },{
    label: '代茶',
    kw: 'replace_tea',
  },
  // {
  //   label: '中药古方(十二通等)',
  //   kw: 'tcm_old',
  // },
  {
    label: '其他(实物商品)',
    kw: 'entity_other',
  },
  {
    label: '淋漓祛湿',
    kw: 'qs',
  },
  {
    label: '艾灸调理',
    kw: 'aj',
  },
  {
    label: '通经络',
    kw: 'tjl',
  },
  {
    label: '膏膜保健',
    kw: 'bjg',
  },
  {
    label: '局部调理',
    kw: 'jb',
  },
  {
    label: '针灸/拔罐/正骨',
    kw: 'zj_bg_zg',
  },
  {
    label: '其他(理疗服务)',
    kw: 'serv_other',
  },
  {
    label: '菌群移植',
    kw: 'jqyz',
  },
]

/* 商品his卡片枚举值 */
export const goods_card_his_options = [
  {
    label: '销售金额',
    value: 'turnover',
    current: '',
    isMoney: true,
  },
  {
    label: '支付订单数',
    value: 'paid_order_num',
    current: '',
  },
  {
    label: '已开具处方数',
    value: 'pres_num',
    current: '',
  },
  {
    label: '已支付处方数',
    value: 'paid_pres_num',
    current: '',
  },
  {
    label: '已发药处方数',
    value: 'assigned_pres_num',
    current: '',
  },
  {
    label: '已支付中药饮片处方数',
    value: 'pres_herbs_num',
    current: '',
  },
  {
    label: '已支付膏方处方数',
    value: 'pres_plaster_num',
    current: '',
  },
  {
    label: '已支付治疗-祛湿处方数',
    value: 'pres_treat_qs_num',
    current: '',
  },
  {
    label: '已支付治疗-通经络处方数',
    value: 'pres_treat_tjl_num',
    current: '',
  },
  {
    label: '已支付治疗-其他处方数',
    value: 'pres_treat_num',
    current: '',
  },
  {
    label: '单次诊疗均价',
    value: 'his_order_price_pre',
    current: '',
    isMoney: true,
    hideBottomLine: true,
  },
  {
    label: '成功退药处方数',
    value: 'return_goods_pres_num',
    current: '',
    hideBottomLine: true,
  },
  {
    label: '成功退款金额',
    value: 'refunded_amount',
    current: '',
    isMoney: true,
    hideBottomLine: true,
  },
  {
    label: '累计服务用户数',
    value: 'pres_cv',
    current: '',
    hideBottomLine: true,
  },
  {
    label: '已支付-调理产品处方数',
    value: 'physical_num',
    current: '',
    hideBottomLine: true,
  }
]

/* 商品his得诊疗品tag */
export const goods_his_treat_tag = [
  {
    label: '中药饮片',
    subTitle: '药名',
    kw: 'HERBS',
  },
  {
    label: '膏方',
    subTitle: '膏方名',
    kw: 'PLASTER',
  },
  {
    label: '治疗',
    subTitle: '治疗项',
    kw: 'TREAT_ALL',
  },
]

/* 商品shop卡片枚举值 */
export const goods_card_shop_options = [
  {
    label: '销售金额',
    value: 'turnover',
    current: '',
    isMoney: true,
  },
  {
    label: '支付订单数',
    value: 'paid_order_num',
    current: '',
  },
  {
    label: '已售商品件数',
    value: 'paid_goods_num',
    current: '',
    isPercent: true
  },
  {
    label: '已售实物商品件数',
    value: 'paid_shiwu_goods_num',
    current: '',
  },
  {
    label: '已售实物商品金额',
    value: 'paid_shiwu_turnover',
    current: '',
    isMoney: true,
  },
  {
    label: '已售虚拟商品件数',
    value: 'paid_xuni_goods_num',
    current: '',
  },
  {
    label: '已售虚拟商品金额',
    value: 'paid_xuni_turnover',
    current: '',
    isMoney: true,
  },
  {
    label: '成功退款商品数',
    value: 'refund_goods_num',
    current: '',
  },
  {
    label: '成功退款金额',
    value: 'refund_goods_money',
    current: '',
    isMoney: true,
  },
  {
    label: '单均价',
    value: 'order_price_pre',
    current: '',
    isMoney: true,
  },
  {
    label: '上架中商品数',
    value: 'on_sale_good_num',
    current: '',
    hideBottomLine: true,
  },
  {
    label: '零售可售商品数',
    value: 'retail_sale_goods_num',
    current: '',
    hideBottomLine: true,
  },
  {
    label: '问诊治疗关联商品数',
    value: 'pres_sale_goods_num',
    current: '',
    hideBottomLine: true,
  },
  {
    label: '小程序售卖商品数',
    value: 'shop_sale_goods_num',
    current: '',
    hideBottomLine: true,
  },
]
