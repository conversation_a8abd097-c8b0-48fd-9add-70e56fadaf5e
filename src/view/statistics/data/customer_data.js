export const overviewOptions_MIL = [
  {
    value: 'order_cv',
    label: '下单人数',
  },
  {
    value: 'paid_cv',
    label: '支付人数',
  },
  {
    value: 'customer_incr',
    label: '新增用户数',
  },
  {
    value: 'repurchase_cv',
    label: '复购用户数',
  },
  {
    value: 'customer_total',
    label: '用户总数',
  },
]
export const overviewOptions_HIS = [
  {
    value: 'order_cv',
    label: '下单人数',
  },
  {
    value: 'paid_cv',
    label: '支付人数',
  },
  {
    value: 'customer_incr',
    label: '新增用户数',
  },
  {
    value: 'repurchase_cv',
    label: '复购用户数',
  },
  {
    value: 'customer_total',
    label: '用户总数',
  },
]
export const allOverviewOptions = [
  {
    value: 'order_cv',
    label: '下单人数',
  },
  {
    value: 'paid_cv',
    label: '支付人数',
  },
  {
    value: 'customer_incr',
    label: '新增用户数',
    isPercent: true
  },
  {
    value: 'repurchase_cv',
    label: '复购用户数',
  },
  {
    value: 'customer_total',
    label: '用户总数',
  },
]

/**
 * @description: 用户分析卡片枚举数据
 * isPercent: { 当前为百分比展示 }
 * isThousandth: { 当前为千分位展示 }
 * isMoney: { 当前为金额展示 }
 *
*/
export const customer_card_options = [
  {
    label: '新增用户数(人)',
    value: 'customer_incr',
    current: '',
    last: '',
  },
  {
    label: '到店用户数(人)',
    value: 'arrival_cv',
    current: '',
    last: '',
  },
  {
    label: '到店次数(次)',
    value: 'arrival_num',
    current: '',
    last: '',
  },
  // {
  //   label: '下单用户数(人)',
  //   value: 'order_cv',
  //   current: '',
  //   last: '',
  // },
  {
    label: '支付用户数(人)',
    value: 'total_paid_cv',
    current: '',
    last: '',
  },
  {
    label: '消费用户数(人)',
    value: 'paid_cv',
    current: '',
    last: '',
  },
  {
    label: '储值用户数(人)',
    value: 'recharge_cv',
    current: '',
    last: '',
  },
  {
    label: '实操用户数(人)',
    value: 'serv_used_cv',
    current: '',
    last: '',
  },
  {
    label: '消费退款用户数(人)',
    value: 'refunded_finish_cv',
    current: '',
    last: '',
  },
  {
    label: '储值退款用户数(人)',
    value: 'recharge_refund_cv',
    current: '',
    last: '',
  },
  {
    label: '复购用户数(人)',
    value: 'total_repurchase_cv',
    current: '',
    last: '',
    readOnly: true,
    hideCompare: true,
  },
  {
    label: '总用户数(人)',
    value: 'customer_total',
    current: '',
    last: '',
    readOnly: true,
    hideCompare: true,
  },
  {
    label: '累计储值用户数(人)',
    value: 'recharge_grand_cv',
    current: '',
    last: '',
    readOnly: true,
    hideCompare: true,
  },
  {
    label: '待服务用户数(人)',
    value: 'serv_not_used_cv',
    current: '',
    last: '',
    readOnly: true,
    hideCompare: true,
  },
]

export const customer_tag_consumptions = [
  {
    label: '消费次数',
    kw: 'paid_num',
  },
  {
    label: '消费金额',
    kw: 'paid_money',
  },
  {
    label: '消耗次数',
    kw: 'serv_used_num',
  },
  {
    label: '消耗金额',
    kw: 'serv_used_money',
  },
  {
    label: '笔单价',
    kw: 'order_unit_price',
  },
  {
    label: '退款次数',
    kw: 'refunded_order_num',
  },
  {
    label: '成功退款金额',
    kw: 'refunded_finish_amount',
  },
]

// export const situation_tooltip_list = [
//   "新增用户数：获取到手机号的用户人数",
//   "到店用户数：到店的用户人数(基于日常模块录入的到店数据)",
//   "下单用户数：已成功下单的用户人数",
//   "支付用户数：已成功支付的用户人数",
//   "支付人数：完成订单支付的人数",
//   "实操用户数：为用户操作理疗服务，并成功完成卡券核销的用户人数",
//   "成功退款用户数：为用户成功操作订单退款的用户人数",
//   "复购用户数：统计周期内，发生过2次或以上支付行为的用户",
//   "累计用户数：截止统计区间结束时间，诊所全部用户数",
//   "累计已支付用户数：截止统计区间结束时间，诊所全部已成功支付的用户人数",
//   "复购用户数：待服务用户数，截止统计区间结束时间，诊所还需服务用户人数(有卡券待核销的用户)"
// ]

export const situation_tooltip_list = [
  "新增用户数：获取到手机号的用户人数",
  "到店用户数：到店的用户人数(基于日常模块录入的到店人数+消费下单人数+已完成储值订单充值人数+理疗消耗人数 根据用户去重)",
  "到店次数：客户到店的次数(基于日常模块录入的到店次数+消费下单订单数+已完成储值订单数+理疗消耗次数，1天内同一客户有多次行为记1次)",
  "支付用户数：消费支付或储值支付的用户人数",
  "消费用户数：完成消费订单支付的人数",
  "储值用户数：完成储值订单支付的人数",
  "实操用户数：为用户操作理疗服务，并成功完成卡券核销的用户人数",
  "消费退款用户数：完成消费订单退款的用户人数",
  "储值退款用户数：完成储值退款的用户人数",
  "复购用户数：统计累计发生过2次或以上支付行为的用户(支付指消费或充值)",
  "总用户数：诊所累计的用户总数(不随选择周期变化)",
  "累计储值用户数：不随时间周期变动，诊所累计成功储值的用户数，不考虑储值后退款的情况",
  "待服务用户数：诊所还需服务用户人数(有卡券待核销的用户)，不随选择周期变化",
]
