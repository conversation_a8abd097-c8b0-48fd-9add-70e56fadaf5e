export const overviewOptions = [
  {
    value: 'consume_turnover_amount',
    label: '今日消费营收',
    isMoney: true,
  },
  {
    value: 'recharge_turnover',
    label: '今日储值营收',
    isMoney: true,
  },
  {
    value: 'paid_cv',
    label: '今日消费用户数',
    isMoney: false,
  },
  {
    value: 'recharge_cv',
    label: '今日储值用户数',
    isMoney: false,
  },
  {
    value: 'serv_used_cv',
    label: '今日实操用户数',
  },
  {
    value: 'serv_used_money',
    label: '今日理疗消耗金额(元)',
    isMoney: true,
  },
]

// 曲线options
export const customer_analysis = [
  {
    before: '',
    after: '',
    text: '访问支付转化率',
    id: 'cv_pay_conv_rate',
    color: ['#1662FF', '#5990FF'],
    tooltip_lists: [
      "访问支付转化率：支付用户数 / 访客数*100%",
      "支付用户数：消费支付或储值支付的用户人数",
    ],
  },
  {
    before: '',
    after: '',
    text: '下单支付转化率',
    id: 'order_pay_conv_rate',
    color: ['#955AA4', '#A864B2'],
    tooltip_lists: [
      "消费下单支付转化率：消费订单中，已支付订单数 / 总订单数*100%"
    ],
  },
  {
    before: '',
    after: '',
    text: '退款订单占比',
    id: 'refund_rate',
    color: ['#BE3667', '#EB4882'],
    tooltip_lists: [
      "消费退款订单占比：消费订单中，已退款订单数 / 支付订单数*100%"
    ],
  },
  {
    before: '',
    after: '',
    text: '用户复购率',
    id: 'repurchase_rate',
    color: ['#449C42', '#86C762'],
    tooltip_lists: [
      "用户复购率：复购用户数/支付用户数",
      "复购用户数：成功支付≥2次的用户人数",
      "支付用户数：消费支付或储值支付的用户人数",
    ],
  },
]

// 数据趋势
export const trendItems = [
  {
    id: 'gross_turnover',
    desc: '总营收金额',
    isChecked: true,
    isMoney: true,
  },
  {
    id: 'consume_turnover_amount',
    desc: '消费营收',
    isChecked: false,
    isMoney: true,
  },
  {
    id: 'recharge_turnover',
    desc: '储值营收',
    isChecked: false,
    isMoney: true,
  },
  {
    id: 'serv_used_money',
    desc: '理疗消耗金额(元)',
    isChecked: false,
    isMoney: true,
  },
  // {
  //   id: 'paid_order_num',
  //   desc: '支付订单数',
  //   isChecked: false,
  //   isMoney: false,
  // },
  // {
  //   id: 'refunded_amount',
  //   desc: '成功退款金额',
  //   isChecked: false,
  //   isMoney: true,
  // },
  {
    id: 'arrival_cv',
    desc: '到店用户数',
    isChecked: false,
    isMoney: false,
  },
  {
    id: 'customer_incr',
    desc: '新增用户数',
    isChecked: false,
  },
  {
    id: 'paid_cv',
    desc: '消费用户数',
    isChecked: false,
    isMoney: false,
  },
  {
    id: 'recharge_cv',
    desc: '储值用户数',
    isChecked: false,
    isMoney: false,
  },
  {
    id: 'serv_used_cv',
    desc: '实操用户数',
    isChecked: false,
    isMoney: false,
  },
]

export const overview_tooltip_list = [
  "今日营收：今日消费营收+今日储值营收",
  "今日消费营收：今日实收金额-储值消耗金额-现金/微信退款金额",
  "今日储值营收：今日储值订单实充金额-储值退款金额",
  "今日消费用户数：今日下消费订单人数(HIS诊疗订单或商城订单)",
  "今日储值用户数：今日已完成储值的用户数(储值订单中已完成状态下关联人数)",
  "今日实操用户数：今日为多少用户提供了理疗服务(核销卡券人数)",
  "今日理疗消耗金额：今日提供的理疗服务价值金额(已核销卡券的价值合计)",
]

export const collection_tooltip_list = [
  "账户收款包含，消费订单收款和储值订单收款",
  "微信支付账户收入=微信账户收款-退款",
  "现金账户收入=现金账户收款-退款",
  "金额合计等于同时间段的总营收金额",
]
