<template>
  <Modal
    :value="value"
    :mask-closable="false"
    class-name="vertical-center-modal prodCostModal"
    :title="title"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="text">导出诊所货品成本明细报表前，请先调整并完成诊所货品成本初始化</div>
    </div>
    <div slot="footer">
      <Button @click="cancel">我知道了</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">立即调整</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'ProdCostTip',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    }
  },

  data() {
    return {
      confirmLoading: false
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.$emit('input', false);
    },

    confirm() {
      this.$emit('success')
      this.cancel();
    },

    changeVisible(visible) {
      if (!visible) {
        this.cancel();
      }
    }
  }
};
</script>

<style scoped lang="less">
.text {
  font-size: 14px;
  margin-top: 30px;
}
::v-deep .prodCostModal {
  .ivu-modal-body {
    height: 10px !important;
  }
}
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 150px;
  overflow-y: auto;
}
</style>
