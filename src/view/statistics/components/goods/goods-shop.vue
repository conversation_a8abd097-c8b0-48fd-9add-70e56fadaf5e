<template>
  <div class="wrapper">
    <!-- content -->
    <div class="block pt0">

      <!-- card -->
      <div class="mt16 flex flex-warp">
        <div
          class="solid-card-block mb16"
          v-for="(card_item, card_index) in shop_card_options"
          :key="'card'+card_index"
          :class="{'solid-bottom--none': card_item.hideBottomLine }"
          >
          <!-- @click="cardChange(card_item, card_index)" -->
          <p class="card-title flex">{{ card_item.label }}</p>
          <p class="card-content mt12 solid-right-line" v-if="card_item.isMoney">￥{{ card_item.current | number_format }}</p>
          <p class="card-content mt12 solid-right-line" v-else>{{ card_item.current }}</p>
        </div>
      </div>

      <p class="transverse-line"></p>

      <div class="mt12">
        <p class="block-title flex">
          商品售卖排行
          <Tooltip max-width="300" content="统计已支付订单中，商品成交的数量及金额排行" theme="light" placement="right" class="custom-tooltip tooltip-left-arrow ml6">
            <p class="flex flex-item-center cursor">
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </p>
          </Tooltip>
        </p>

        <div>
          <chart-view :chart-option="saleOptions" v-if="isShowLineEcharts(saleOptions, 'line')"></chart-view>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>
      </div>

      <div class="mt12">
        <p class="block-title flex">
          商品退款排行
          <Tooltip max-width="300" content="统计已成功退款的商品数量及金额排行" theme="light" placement="right" class="custom-tooltip tooltip-left-arrow ml6">
            <p class="flex flex-item-center cursor">
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            </p>
          </Tooltip>
        </p>

        <div>
          <chart-view :chart-option="refundOptions" v-if="isShowLineEcharts(refundOptions, 'line')"></chart-view>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script>
import { goods_card_shop_options } from '../../data/goods_data';
import { number_format } from '@/libs/filters';
import showEcharts from "@/mixins/showEcharts";
  export default {
    name: "goodsShop",
    components: {
    },
    mixins: [showEcharts],
    props: {
      formDate: {
        type: Array,
        default: []
      },
      date_type: {
        type: String,
        default: ''
      }
    },
    data () {
      return {
        shop_card_options: goods_card_shop_options, // shop卡片数据展示

        // 商品售卖排行
        saleOptions : {},

        // 商品退款排行
        refundOptions: {},
      }
    },
    computed: {
      // 底部bottom隐藏
      isBottomNone () {
        return (card_index) => {
          if ( card_index == 10 || card_index == 11 || card_index == 12 || card_index == 13 ) {
            return true
          }else {
            return false
          }
        }
      }
    },
    watch: {
      'formDate': {
        handler (val) {
          if ( val && val[0] ) {
            this.initData()
          }
        }
      }
    },
    created() {

    },
    mounted() {
      this.initData()
    },
    methods: {
      // 初始化数据
      initData () {
        this.getGoodsShopOrderanalysis()
      },
      // 处理横向柱状图数据
      handleRank (dataSource, list, xName, yName, labelName = '', color = '#9CA4FF') {
        let xData = []
        let yData = []
        let numData = []

        list && list.forEach( item => {
          xData.push(item[xName])
          yData.push(item[yName])
          numData.push(item.num)
        } )

        xData = xData.reverse()
        yData = yData.reverse()
        numData = numData.reverse()

        this[dataSource] = this.$eChartFn.horizontalBar( {
          grid: {
            right: '218px',
          },
          xAxis: {
            name: '单位:元',
            data: yData,
          },
          yAxis: {
            data: yData,
          },
          tooltip: {
            formatter:(a)=>{
              let numIndex = yData.indexOf(a.name)
              let val = `${labelName}:${numData[numIndex]}件  ￥${number_format(a.value,2)}`
              return val
            }
          },
          series: [{
            data: xData,
            label: {
              formatter:(a)=>{
                let numIndex = yData.indexOf(a.name)
                let val = `${labelName}:${numData[numIndex]}件   ￥${number_format(a.value,2)}`
                return val
              },
            },
            itemStyle: {
              color,
            }
          }]
        })
      },
      /* api */
      getGoodsShopOrderanalysis () {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
        }
        this.$api.getGoodsShopOrderanalysis(params).then( res => {
          this.shop_card_options.forEach( item => {
            item.current = res.current[item.value]
          } )
          this.handleRank('saleOptions',res.shop_goods_rank, 'money', 'name', '售卖件数')
          this.handleRank('refundOptions',res.shop_refunded_rank, 'money', 'goods_name', '已退件数', '#E6C69C')
        } )
      }
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
@import url('../../style/common.less');
</style>
