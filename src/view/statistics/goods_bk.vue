<template>
  <div class="goods-wrapper">
    <section class="g-h flex flex-item-between flex-item-center ceiling">
      <div class="g-h-l flex flex-item-align">
        <span class="label">统计时间：</span>
        <DatePicker type="daterange" placeholder="请选择日期区间" :clearable="false" style="width: 200px" :options="timeOptions" v-model="statisticsTime"
                    @on-change="changeTrendTime"></DatePicker>
      </div>
      <div class="g-h-r">
        <Button :type="dataType==='HIS'?'primary':'default'" style="margin-right: 8px;height:30px;"
                @click="changeDataType('HIS')">HIS诊疗
        </Button>
        <Button :type="dataType==='MIL'?'primary':'default'" @click="changeDataType('MIL')">商城购物</Button>
      </div>
    </section>
    <!--    实时概况-->
    <section class="data-h common-bg">
      <div class="ow-head flex">
        <h3 class="h-title">实时概况</h3>
<!--        <span class="h-time">更新时间：{{ update_time }}</span>-->
      </div>
      <div class="flex flex-warp data-sta ">
        <div class="sta-item flex flex-c " v-for="(item,index) in overviewOptions" :key="item.value"
             :class="[index<5?'has-line':'']">
          <h4 class="sta-t">{{ item.label }}</h4>
          <div class="data-v">
          <span v-if="item.isMoney">{{ overview[item.value] && overview[item.value]|number_format }}</span>
          <span v-else :class="{'is-text': item.label == '常用方剂' || item.label == '常用中药饮片' || item.label == '销量最佳'}">{{  overview[item.value] && overview[item.value] ||'-'}}</span>
          <span v-show="item.isPercent">%</span>
         </div>
        </div>
      </div>
    </section>
    <section class="trans-wrapper common-bg" style="padding-bottom: 37px;">
      <h3 class="h-title">商品分析</h3>
      <div class="ranking-wrapper flex" v-if="dataType==='HIS'">
        <div class="rank-l rank-item">
          <h3 class="h-title">HIS诊疗方案应用排行</h3>
          <div class="rank-table rank-table-l">
            <Table :columns="appletsCols_pres"
                   style="width: 100%;"
                   :data="pres_rank" v-if="pres_rank.length">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="content">
                {{ row.name }}
              </template>
              <template slot-scope="{row}" slot="num">
                {{ row.num }}
              </template>
            </Table>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>
        <div class="rank-r rank-item">
          <h3 class="h-title">中药饮片常用排行</h3>
          <div class="rank-table ">
            <Table style="width:100%;" :columns="appletsCols"
                   :data="herbs_rank" v-if="herbs_rank.length">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="content">
                {{ row.name }}
              </template>
              <template slot-scope="{row}" slot="num">
                {{ row.num }}
              </template>
            </Table>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>
      </div>
      <div class="ranking-wrapper flex" v-else>
        <div class="rank-l rank-item">
          <h3 class="h-title">商城商品成交排行</h3>
          <div class="rank-table rank-table-l">
            <Table style="width:100%;" :columns="appletsCols"
                   :data="goods_rank" v-if="goods_rank.length">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="content">
                {{ row.name }}
              </template>
              <template slot-scope="{row}" slot="num">
                {{ row.num }}
              </template>
            </Table>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>
        <div class="rank-r rank-item">
          <h3 class="h-title">商城预约服务排行</h3>
          <div class="rank-table ">
            <Table style="width:100%;" :columns="appletsCols"
                   :data="reserve_rank" v-if="reserve_rank.length">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="content">
                {{ row.name }}
              </template>
              <template slot-scope="{row}" slot="num">
                {{ row.num }}
              </template>
            </Table>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>
      </div>
    </section>
    <section class="trans-wrapper common-bg card-top-line" style="margin-top: -18px;" v-show="dataType==='HIS'">
      <div class="ranking-wrapper flex">
        <div class="rank-l rank-item">
          <h3 class="h-title">西药/中成药常用排行</h3>
          <div class="rank-table rank-table-l">
            <Table style="width:100%;" :columns="appletsCols"
                   :data="medicines_rank" v-if="medicines_rank.length">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="content">
                {{ row.name }}
              </template>
              <template slot-scope="{row}" slot="num">
                {{ row.num }}
              </template>
            </Table>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>
        <div class="rank-r rank-item">
          <h3 class="h-title">治疗单排行</h3>
          <div class="rank-table ">
            <Table style="width:100%;" :columns="appletsCols"
                   :data="treat_rank" v-if="treat_rank.length">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="content">
                {{ row.name }}
              </template>
              <template slot-scope="{row}" slot="num">
                {{ row.num }}件
              </template>
            </Table>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 体验偏好 -->
    <div class="flex"  v-if="dataType==='MIL'" >
<!--      <div class="card margin-right20 refund-statistics">-->
<!--        <div class="title">小程序自助体验偏好统计</div>-->
<!--        <div>-->
<!--          <K-Pie-Chart :id="Date.parse(new Date()).toString()+'pie1'" :options="hobby_options" key="pie1"></K-Pie-Chart>-->
<!--        </div>-->
<!--        -->
<!--      </div>-->
<!--      <div class="card refund-details">-->
<!--        <div class="title">小程序自助体验偏好明细</div>-->
<!--        <Table class="margin-top8" :columns="exeCols" :data="exe_list" border>-->
<!--          <template slot-scope="{row}" slot="rate">-->
<!--            {{row.rate}}%-->
<!--          </template>-->
<!--        </Table>-->
<!--      </div>-->
      <div class="statistics-wrapper ">
        <h3 class="h-title s-title-margin">小程序自助体验偏好统计</h3>
        <div class="flex-item-center flex">
          <div class="echarts-wrapper flex " style="width: 60%;margin-left: -20%;">
            <!-- left bar -->
            <div class="line-echarts flex " style=";flex:1;">
              <div style="flex:1;">
                <K-Pie-Chart :id="Date.parse(new Date()).toString()+'pie5'" key="optionsPie_like" :options="hobby_options"></K-Pie-Chart>
              </div>
              <div class="bar-table" style="width: 50%">
                <Table :columns="exeCols" :data="exe_list" style="width: 400px;margin-top: 40px;margin-left: -30px;">
                  <template slot-scope="{row,index}" slot="text">
                    <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                    <span>{{ row.text }}</span>
                  </template>
                  <template slot-scope="{row}" slot="num">
                    {{ row.num }}
                  </template>
                  <template slot-scope="{row}" slot="rate">
                    {{ row.rate }}%
                  </template>
                </Table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <section class="sales-wrapper common-bg">
      <h3 class="h-title" style="margin-bottom: 10px;">商品销量明细</h3>
      <div class="sales-btn" v-if="dataType === 'HIS'">
        <Button :type="salesType==='HERBS'?'primary':'default'" style="margin-right: 10px;"
                @click="changeSaleType('HERBS')">中药饮片
        </Button>
        <Button :type="salesType==='HERBS'?'default':'primary'" @click="changeSaleType('MEDICINE')">西药/中成药</Button>
      </div>
      <div class="sales-table">
        <Table :columns="salesCols" :data="goods_list"></Table>
        <div class="block_20"></div>
        <KPage :total="total"
               :page-size.sync="queryFormData.pageSize"
               :current.sync="queryFormData.page"
               @on-change="onPageChange"
               style="text-align:right"
        />
      </div>
    </section>
  </div>
</template>

<script>
import {overviewOptions_HIS, overviewOptions_MIL} from './data/goods_data'
import KAnnularPie from '_c/k-echarts/k-annular-pie';
import KPieChart from '_c/k-echarts/k-pie-chart';
import {color_enum} from "@/view/statistics/data/color";
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  type: 'HERBS',
  st: '',
  et: ''
}
export default {
  name: "goods",
  components: {
    KAnnularPie,
    KPieChart
  },
  data() {
    return {
      update_time: '',
      timeData: {
        st: '',
        et: ''
      },
      statisticsTime: [],//统计时间
      timeOptions: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now() - 86400000
        }
      },
      hobby_options: {
        seriesData: [],
        color: color_enum,
        legendData: [],
        name: '小程序自助体验偏好占比',
      },
      appletsCols_pres: [
        {title: '排名', slot: 'rank', width: 40, align: 'center', className: 'rank'},
        {title: '处方名称', slot: 'content', align: 'left', minWidth: 200, tooltip: true},
        {title: '数量', slot: 'num', align: 'right', width: 80},
      ],
      appletsCols: [
        {title: '排名', slot: 'rank', width: 40, align: 'center', className: 'rank'},
        {title: '名称', slot: 'content', align: 'left', minWidth: 200, tooltip: true},
        {title: '数量', slot: 'num', align: 'right', width: 80},
      ],
      salesCols: [],
      herbsCols: [
        {title: '药品名', key: 'name', align: 'center'},
        {title: '销售数量（g）', key: 'sales_num', align: 'center', tooltip: true},
        {title: '销售金额（元）', key: 'sales_amount', align: 'center'},
      ],
      total: 0,
      queryFormData: {...init_query_form_data},
      dataType: 'HIS',//MIL 商城购物
      salesType: 'HERBS',//销售明细类型 中成药MEDCINES
      overviewOptions: [],
      overview: {},
      medicines_rank: [],
      herbs_rank: [],
      pres_rank: [],
      treat_rank: [],
      color_table_enum: color_enum,//色值表
      goods_rank: [],//小程序商品排行
      reserve_rank: [],//小程序预约排行
      goods_list: [],//商品列表
      MIL_Cols: [
        {title: '商品名', key: 'name', align: 'center', tooltip: true},
        {title: '下单件数', key: 'order_stock', align: 'center'},
        {title: '支付件数', key: 'paid_stock', align: 'center'},
        {title: '支付金额', key: 'paid_amount', align: 'center'},
      ],
      exeCols: [
        {title: '类型', slot: 'text', align: 'left', tooltip: true},
        {title: '体验用户数', key: 'num', align: 'center'},
        {title: '体验次数', key: 'times', align: 'center'},
        {title: '占比', slot: 'rate', align: 'right'},
      ],
      exe_list: [],
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      this.salesCols = this.herbsCols
      this.getSalesCommon()
      this.getOverviewData()
      // this.changeDataType('HIS')
    },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page
      this.queryFormData.pageSize = pageSize
      this.getSalesCommon()

    },
    changeTrendTime(times) {
      console.log(times)
      if (times) {
        this.timeData.st = this.queryFormData.st = times[0]
        this.timeData.et = this.queryFormData.et = times[1]
      } else {
        this.timeData.st = ''
        this.timeData.et = ''
        this.queryFormData.st = ''
        this.queryFormData.et = ''
      }
      this.getOverviewData()
      this.getSalesCommon()
    },
    changeDataType(type) {
      this.dataType = type
      this.getOverviewData()
      this.getSalesCommon()
    },
    changeSaleType(type) {
      this.salesType = this.queryFormData.type = type
      this.queryFormData.page = 1
      this.getSalesCommon()
    },
    getOverviewData() {
      this.overviewOptions = overviewOptions_HIS
      const type = this.dataType
      if (type === 'HIS') {
        this.$api.getHISOverview(this.timeData).then(res => {
          console.log(res)
          this.overview = res.overview
          this.medicines_rank = res.goods_analysis.medicines_rank
          this.herbs_rank = res.goods_analysis.herbs_rank
          this.pres_rank = res.goods_analysis.pres_rank
          this.treat_rank = res.goods_analysis.treat_rank
          this.statisticsTime = [res.st,res.et]
          this.timeData.st = res.st
          this.timeData.et = res.et
        }, err => {})
      } else {
        this.overviewOptions = overviewOptions_MIL
        this.$api.getMILOverview(this.timeData).then(res => {
          console.log(res)
          this.overview = res.overview
          this.goods_rank = res.goods_analysis.goods_rank
          this.reserve_rank = res.goods_analysis.reserve_rank
          this.exe_list = res.self_test_perfect
          this.hobby_options.seriesData = this.handleHobbyE(res.self_test_perfect)
        console.log('exe_list', this.exe_list);
        }, err => {})
      }
    },
    // 处理饼图数据
    handleHobbyE (options) {
      let seriesData = options.map(item => {
          return {
            name: item.text,
            value: item.times,
            rate: item.rate&&(item.rate+ '%')
          }
      })
      return seriesData
    },
    getSalesCommon(){
      if(this.dataType === 'HIS'){
        this.getHisSalesData()
      }else {
        this.getShopSalesData()
      }
    },
    getHisSalesData() {
      this.$api.getGoodsDetails(this.queryFormData).then(res => {
      this.salesCols = this.herbsCols
      console.log(res)
      this.goods_list = res.list
      this.total = res.total
        }, err => {})
    },
    getShopSalesData(){
      const {st,et,page} = this.queryFormData
      this.$api.getShopDetails({st,et,page}).then(res => {
        this.salesCols = this.MIL_Cols
        console.log(res)
        this.goods_list = res.list
        this.total = res.total
      }, err => {})
    }
  },
}
</script>

<style scoped lang="less">
.goods-wrapper {
  .g-h {
    padding: 20px 0 16px;
  }

  .data-h {
    height: 254px;
    .ow-head {
      padding: 20px 20px 0;

      .h-time {
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 18px;
        align-self: flex-end;
        transform: scale(0.8)
      }
    }

    .data-sta {
      flex-wrap: wrap;
      padding: 0 9px;

      .has-line {
        border-bottom: 1px solid #EFEFEF;
      }

      .sta-item {
        width: 20%;
        padding: 20px 0 20px 60px;

        .sta-t {
          font-size: 12px;
          color: #444444;
          line-height: 17px;
          font-weight: 400;
        }

        .data-v {
          color: #000000;
          font-size: 20px;
          font-family: OPPOSans;
          font-weight: 500;
          line-height: 29px;
          -webkit-text-stroke: 0px #000000;
          text-stroke: 0px #000000;
          padding: 12px 0 6px;
          border-right: 1px solid #EFEFEF;
        }

        &:nth-child(5n) {
          .data-v {
            border-right: none;
          }
        }

        .data-t {
          font-weight: 400;
          color: #AAAAAA;
          line-height: 17px;
        }
      }
    }
  }

  .trans-wrapper {
    margin: 16px 0;
    padding: 16px 20px 22px;

    .ranking-wrapper {
      margin-top: 10px;
      .rank-table{
        flex:1;
        display: flex;
        justify-content: center;
      }
      .top-three {
        > img {
          width: auto;
          height: 20px;
        }
      }

      .rank-l {
        border-right: 1px solid #EFEFEF;

        .rank-table-l {
          margin-right: 50px;
        }
      }

      .rank-r {
        margin-left: 50px;
      }

      .rank-item {
        flex: 1;
        display:flex;
        flex-direction: column;
        ::v-deep .ivu-table {
          thead {
            th {
              background: #FFFFFF;
              color: #999999;
            }

            td {
              height: 40px;
            }
          }

          &::before {
            height: 0;
          }

          td {
            border-bottom: 1px solid rgba(204, 204, 204, 0.5);
            height: 40px;
          }

          th {
            border-bottom: 1px solid rgba(204, 204, 204, 0.5);
            height: 40px;
          }
        }
      }
    }
  }

  .sales-wrapper {
    padding: 20px 20px 30px;
    margin-top: 16px;
    .sales-btn {
      margin-top: 14px;
      margin-bottom: 10px;
    }
  }
  .refund {
    height: auto;
    width: 100%;
    margin-top: 20px;
    // 退款统计
    .refund-statistics {
      min-width: 200px;
      width: 25%;
    }

    // 退款明细
    .refund-details {
      width: 75%;
    }
  }
}
.bar-table {
  ::v-deep .ivu-table {
      thead {
        th {
          background: #FFFFFF;
          color: #999999;
        }

        td {
          height: 40px;
        }
      }

      &::before {
        height: 0;
      }

      td {
        border-bottom: none;
        height: 40px;
      }

      th {
        border-bottom: none;
        height: 40px;
      }
    }
}
.statistics-wrapper{
  background: #FFFFFF;
  padding: 20px;
  border-radius: 4px;
  width: 100%;
  margin-bottom: 16px;
}
.circle{
  border-radius: 50%;
  border: 2px solid red;
  width: 10px;
  height: 10px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 6px;
}
</style>
<style lang="less" scoped>
.h-title {

  font-weight: bolder;
  line-height: 25px;
}

.common-bg {
  background: #ffffff;
  border-radius: 4px;
}

::v-deep .ivu-btn {
  height: 30px;
}

::v-deep .ivu-input {
  height: 32px;
  line-height: 32px;
}
</style>
<style lang="less" scoped>
// common scss
.title {
  height: 20px;
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  line-height: 20px;
}
.card {
  padding: 20px;
  background: #FFFFFF;
  border-radius: 4px;
}
.page-wrapper {
  margin-top: 20px;
  text-align: right;
}
.margin-top8 {
  margin-top: 8px;
}
.margin-top20 {
  margin-top: 16px;
}
.margin-right20 {
  margin-right: 20px;
}
.card-top-line {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-top: 4px solid #EFEFEF;
}
.is-text {
  font-family: none !important;
}
</style>
