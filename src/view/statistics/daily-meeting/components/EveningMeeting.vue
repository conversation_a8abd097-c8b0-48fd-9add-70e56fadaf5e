<template>
  <div style="height: 100%">
    <div v-if="loading" style="width: 100%; height: 100%" class="flex flex-item-center">
      <Spin size="large"></Spin>
    </div>
    <div class="morning-wrap" v-else>
      <section class="overview">
        <div class="volume">
          <div class="volume-title">当日现金收入</div>
          <div class="volume-value">
            <span class="currency">￥</span>
            <span class="value">{{
              formatIndicatorsValue(daily_transaction_amount.today?.daily_transaction_amount, true)
            }}</span>
            <div
              class="volume-growth positive"
              :style="{ color: getObjDiffPercent(daily_transaction_amount, 'daily_transaction_amount').color }"
            >
              <svg-icon
                :name="getObjDiffPercent(daily_transaction_amount, 'daily_transaction_amount')?.icon"
              ></svg-icon>
              <!--              {{ getObjDiffPercent(daily_transaction_amount, 'daily_transaction_amount').value }}%-->
            </div>
          </div>
          <div class="target-box">
            <div class="target-title">
              <svg-icon name="meeting-target" class="mr-8" size="20"></svg-icon>
              目标完成率
            </div>
            <div class="target-progress">
              <span class="target-percent"
                >{{ daily_transaction_amount.today?.target_completion_rate }}<span class="percent">%</span></span
              >
            </div>
          </div>
        </div>
        <div class="indicators-box">
          <div class="indicators-item" v-for="(item, index) in indicators" :key="index + '-indicators'">
            <div class="indicators-title">{{ item.title }}</div>
            <div class="indicators-value">
              <span class="currency" v-if="item.isMoney">￥</span>{{ formatIndicatorsValue(item.value, item.isMoney) }}
            </div>
          </div>
        </div>
      </section>
      <section class="performance-box">
        <div class="transaction-items">
          <div class="tran-title">分项目成交</div>
          <div class="tran-item-box">
            <div class="tran-item" v-for="(item, index) in transactionItems" :key="index + '-transaction'">
              <div class="tran-item-title">{{ item.title }}</div>
              <div class="tran-item-value">
                <span class="currency">￥</span>{{ formatIndicatorsValue(item.value, true) }}
                <svg-icon :name="item.diffVal?.icon" size="12" class="ml-8 mr-3"></svg-icon>
                <!--                <span class="increase" :style="{ color: item.diffVal?.color }">{{ item.diffVal?.value }}%</span>-->
              </div>
            </div>
          </div>
        </div>
        <div class="performance-member">
          <div class="performance-doctor">
            <div class="tran-title">医生业绩</div>
            <div class="tran-item-box">
              <template v-if="staffPerformances.physical_therapist_performance.length === 0">
                <div class="common-empty" style="margin-top: 56px">暂无数据</div>
              </template>
              <div
                class="performance-wrap"
                v-for="(item, index) in staffPerformances.doctor_performance"
                :key="index + 'doctor-performance'"
              >
                <div class="performance-item">
                  <div class="tran-item-title">{{ item.name }}</div>
                  <div class="tran-item-value">
                    <span class="currency">￥</span>{{ formatIndicatorsValue(item.today, true) }}
                    <svg-icon
                      :name="getDirectDiff(item.today, item.before).icon"
                      size="12"
                      class="ml-8 mr-3"
                    ></svg-icon>
                    <!--                    <span class="increase" :style="{ color: getDirectDiff(item.today, item.before).color }"-->
                    <!--                      >{{ getDirectDiff(item.today, item.before).value }}%</span-->
                    <!--                    >-->
                  </div>
                </div>
                <Progress :percent="+item.percentage" :stroke-width="5" hide-info />
              </div>
            </div>
          </div>
          <div class="divide"></div>
          <div class="performance-physical">
            <div class="tran-title">理疗师业绩</div>
            <div class="tran-item-box physical-wrapper">
              <div
                class="performance-wrap"
                v-for="(item, index) in staffPerformances.physical_therapist_performance"
                :key="index + 'physical-therapist-performance'"
              >
                <div class="performance-item">
                  <div class="tran-item-title">{{ item.name }}</div>
                  <div class="tran-item-value">
                    <span class="currency">￥</span>{{ formatIndicatorsValue(item.today, item.isMoney) }}
                    <svg-icon
                      :name="getDirectDiff(item.today, item.before).icon"
                      size="12"
                      class="ml-8 mr-3"
                    ></svg-icon>
                    <span class="increase" :style="{ color: getDirectDiff(item.today, item.before).color }"
                      >{{ getDirectDiff(item.today, item.before).value }}%</span
                    >
                  </div>
                </div>
                <Progress :percent="+item.percentage" :stroke-width="5" hide-info />
              </div>
              <template v-if="staffPerformances.physical_therapist_performance.length === 0">
                <div class="common-empty">暂无数据</div>
              </template>
            </div>
          </div>
        </div>
      </section>
      <section class="bottom-box">
        <div class="case-item">
          <div class="tran-title">优秀案例分享：</div>
          <div class="case-input">
            {{ excellent_cases || '-' }}
          </div>
        </div>
        <div class="case-item">
          <div class="tran-title">院感检查：</div>
          <div class="case-input">
            <Icon type="md-checkmark-circle" size="14" color="#115bd4" v-if="nosocomial_inspection === '1'" />
            <Icon type="md-close-circle" size="14" v-else />
            {{ nosocomial_inspection === '1' ? '已检查' : '未检查' }}
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script>
import { $operator } from '@/libs/operation';
const init_daily_transaction = {
  daily_transaction_amount: 0, // 当日交易额
  target_completion_rate: 0, // 目标达成率
  transaction_user_count: 0, // 交易人数
  new_customer_transaction: 0, // 新客交易
  member_transaction: 0, // 会员交易
  recharge_user_count: 0, // 储值人数
  daily_revenue: 0, // 当日营收
  today_recharge_amount: 0, // 今日储值
  physiotherapy_transaction: 0, // 理疗成交
  physiotherapy_consumption: 0, // 理疗消耗
};
export default {
  name: 'MorningMeeting',
  props: {
    selectedDate: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      workPlan: '',
      daily_transaction_amount: {
        today: {
          ...init_daily_transaction,
        },
        before: {
          ...init_daily_transaction,
        },
      },
      indicators: [
        { title: '交易人数', value: '', key: 'transaction_user_count' },
        { title: '新客交易', value: '', key: 'new_customer_transaction' },
        { title: '会员交易', value: '', key: 'member_transaction' },
        { title: '储值人数', value: '', key: 'recharge_user_count' },
        { title: '当日营收', value: '', isMoney: true, key: 'daily_revenue' },
        { title: '今日储值', value: '', isMoney: true, key: 'today_recharge_amount' },
        { title: '理疗成交', value: '', key: 'physiotherapy_transaction' },
        { title: '理疗消耗', value: '', key: 'physiotherapy_consumption' },
      ],
      transactionItems: [
        { title: '医疗', value: '', isMoney: true, key: 'health_care_therapy' },
        { title: '理疗', value: '', isMoney: true, key: 'medical_treatment' },
        { title: '养疗', value: '', isMoney: true, key: 'physiotherapy' },
      ],
      increaseColors: {
        up: '#ee3838',
        down: '#25b830',
        default: '#115bd4',
      },
      staffPerformances: {
        doctor_performance: [],
        physical_therapist_performance: [],
      },
      excellent_cases: '', //案例分享
      nosocomial_inspection: '', //院内巡查
      project_deals: {
        today: [],
        before: [],
      },
      loading: true,
    };
  },
  computed: {
    getObjDiffPercent() {
      return (obj, key) => {
        if (!obj || !key) {
          return {};
        }
        const val = obj.today?.[key];
        const compareVal = obj.before?.[key];
        return this.getDiffPer(val, compareVal);
      };
    },
    getDirectDiff() {
      return (val, compareVal) => {
        return this.getDiffPer(val, compareVal);
      };
    },
  },
  watch: {
    selectedDate: {
      handler(val) {
        this.getMorningMeetingDetail();
      },
    },
  },
  created() {
    this.getMorningMeetingDetail();
  },
  methods: {
    getDiffPer(val, compareVal) {
      val = Number(val);
      compareVal = Number(compareVal);
      // if(val === 0 || compareVal === 0) {
      //   return {
      //     value: 0,
      //     icon: 'meeting-neutral',
      //     color: '#115bd4',
      //     diffType: '',
      //   };
      // }
      const statusMap = {
        up: {
          icon: 'meeting-rising',
          color: '#EE3838',
          diffType: '+',
        },
        down: {
          icon: 'meeting-volume',
          color: '',
          diffType: '-',
        },
        default: {
          icon: 'meeting-neutral',
          color: '#115bd4',
          diffType: '',
        },
      };
      let res = 0;
      const diffVal = $operator.subtract(val, compareVal, 2);
      // if (diffVal) {
      //   res = $operator.multiply($operator.divide(diffVal, compareVal, 2), 100, 2);
      // }
      res = Math.abs(res);
      const diffType = diffVal > 0 ? 'up' : diffVal < 0 ? 'down' : 'default';
      return {
        value: statusMap[diffType].diffType + res,
        icon: statusMap[diffType].icon,
        color: statusMap[diffType].color,
        diffType: statusMap[diffType].diffType,
      };
    },
    formatIndicatorsValue(value, isMoney) {
      // 金额展示千分位，补2个0
      value = Number(value);
      if (isMoney) {
        return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
      } else {
        return value;
      }
    },
    getMorningMeetingDetail() {
      const query = {
        date: this.selectedDate,
      };
      this.loading = true;
      this.$api
        .getEveningMeetingDetail(query)
        .then(res => {
          this.daily_transaction_amount.today = res.daily_transaction_amount.today;
          this.daily_transaction_amount.before = res.daily_transaction_amount.before;
          this.indicators.forEach(item => {
            item.value = this.daily_transaction_amount.today[item.key];
          });
          this.staffPerformances.doctor_performance = res.staff_performance.doctor_performance;
          console.log('this.staffPerformances: ', this.staffPerformances);
          this.staffPerformances.physical_therapist_performance = res.staff_performance.physical_therapist_performance;
          this.excellent_cases = res.excellent_cases;
          this.nosocomial_inspection = res.nosocomial_inspection;
          this.project_deals.today = res.project_deals.today;
          this.project_deals.before = res.project_deals.before;
          this.transactionItems.map(item => {
            item.value = this.project_deals.today[item.key];
            item.diffVal = this.getDirectDiff(this.project_deals.today[item.key], this.project_deals.before[item.key]);
          });
        })
        .finally(() => (this.loading = false));
    },
  },
};
</script>

<style lang="less" scoped>
.morning-wrap {
  color: #333333;
  .overview {
    display: flex;
    background: #f5f6f8;
    .volume {
      padding: 16px;
      background: #fff;
      margin-right: 16px;
      width: 30%;
      .volume-title {
        font-weight: 600;
        font-size: 16px;
        line-height: 24px;
      }
      .volume-value {
        margin: 32px auto;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        .value {
          font-weight: 600;
          font-size: 32px;
          line-height: 28px;
          margin-left: 4px;
          margin-right: 8px;
        }
        .currency {
          font-size: 20px;
        }
      }
      .volume-growth {
        font-size: 12px;
        color: #25b830;
        line-height: 18px;
      }
      .target-box {
        background: #f9fafb;
        border-radius: 4px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .target-title {
          font-size: 13px;
          line-height: 20px;
          text-align: left;
          font-style: normal;
          display: inline-flex;
          align-self: center;
        }
        .target-progress {
          .target-percent {
            font-weight: 500;
            font-size: 14px;
            line-height: 20px;
            .percent {
              font-size: 10px;
              line-height: 20px;
              margin-left: 2px;
            }
          }
        }
      }
    }

    .indicators-box {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
      .indicators-item {
        width: ~'calc(25% - 12px)';
        margin-right: 16px;
        margin-bottom: 16px;
        background: #fff;
        padding: 16px;

        &:nth-child(4n) {
          margin-right: 0;
        }
        &:nth-child(n + 5) {
          margin-bottom: 0;
        }
        .indicators-title {
          margin-bottom: 12px;
          font-size: 13px;
          color: #999999;
          line-height: 20px;
        }
        .indicators-value {
          font-weight: 600;
          font-size: 24px;
          line-height: 32px;
        }
      }
    }
  }

  .performance-box {
    margin-top: 16px;
    display: flex;
    .transaction-items {
      width: 30%;
      background: #fff;
      padding: 16px;
      .tran-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 16px;
      }
      .tran-item-box {
        .tran-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          padding: 14px 16px;
          background: linear-gradient(90deg, #f3f7ff 0%, rgba(245, 246, 248, 0) 100%);
          border-radius: 4px;
          .tran-item-title {
            font-size: 13px;
            line-height: 20px;
          }
          .tran-item-value {
            font-weight: 600;
            font-size: 18px;
            line-height: 18px;
            display: inline-flex;
            align-items: center;
            .currency {
              font-size: 13px;
            }
            .increase {
              font-size: 12px;
              color: #155bd4;
              line-height: 18px;
              &.up {
                color: #ee3838;
              }
              &.down {
                color: #25b830;
              }
            }
          }
        }
      }
    }
    .performance-member {
      flex: 1;
      margin-left: 16px;
      background: #fff;
      padding: 16px;
      display: flex;
      .performance-doctor {
        flex: 1;
      }
      .divide {
        width: 1px;
        height: 100%;
        background: #ebedf0;
        margin: 0 16px;
        align-self: flex-end;
        height: calc(100% - 12px);
      }
      .performance-physical {
        flex: 2;
        .physical-wrapper {
          display: flex;
          flex-wrap: wrap;
          .performance-wrap {
            width: 50%;
            padding-right: 24px;
          }
        }
      }
      .tran-title {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 16px;
      }
      .tran-item-box {
        .tran-item {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 16px;
          padding: 14px 16px;
          .tran-item-title {
            font-size: 13px;
            line-height: 20px;
          }
          .tran-item-value {
            font-weight: 600;
            font-size: 14px;
            line-height: 18px;
            display: inline-flex;
            align-items: center;
            .currency {
              font-size: 13px;
            }
            .increase {
              font-size: 12px;
              color: #155bd4;
              line-height: 18px;
              // &.up {
              //   color: #ee3838;
              // }
              // &.down {
              //   color: #25b830;
              // }
            }
          }
        }
      }
    }
    .performance-item {
      display: flex;
      justify-content: space-between;
    }
    .performance-doctor {
      padding-right: 24px;
    }
    .performance-wrap {
      margin-bottom: 16px;
    }
  }

  .bottom-box {
    margin-top: 16px;
    background-color: #fff;
    border-radius: 4px;
    padding: 16px;
    .case-item {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      font-size: 13px;
      color: #999;
      line-height: 20px;
      .case-input {
        color: #333333;
      }
    }
  }
}

.common-empty {
  padding: 10px 0;
  text-align: center;
  min-width: 100%;
  color: #999;
  margin-top: 40px;
}
</style>
