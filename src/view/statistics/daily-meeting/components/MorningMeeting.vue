<template>
  <div style="height: 100%">
    <div v-if="loading" style="width: 100%; height: 100%" class="flex flex-item-center">
      <Spin size="large"></Spin>
    </div>
    <div class="morning-meeting" v-else>
      <div class="main-box">
        <div class="left-box">
          <div class="overview-item" v-for="(item, index) in overviews" :key="index + '-overview'">
            <div class="overview-title">{{ item.title }}</div>
            <div class="overview-value">{{ item.value }}{{ item.unit }}</div>
            <svg-icon :name="item.icon" class="overview-icon"></svg-icon>
          </div>
        </div>
        <div class="right-box">
          <div class="right-top">
            <div class="right-main">
              <div class="appointment-users">
                <div class="appointment-title">当日预约用户信息</div>
                <div class="appointment-list">
                  <template v-if="reserveData.user_list?.length > 0">
                    <div class="appointment-item" v-for="(item, index) in reserveData.user_list" :key="item.uid">
                      <img
                        class="appointment-avatar"
                        :src="item.avatar || 'https://img-sn01.rsjxx.com/image/2025/0306/150751_28927.png-B.w300'"
                        alt="avatar"
                      />
                      <div class="appointment-name">{{ item.name }}</div>
                      <div class="user-divide" v-if="index !== reserveData.user_list.length - 1"></div>
                    </div>
                  </template>
                  <div class="common-empty" v-else>暂无数据</div>
                </div>
              </div>
              <div class="appointment-employee appointment-users">
                <div class="appointment-title">当日员工预约数量</div>
                <div class="appointment-list">
                  <template v-if="reserveData.staff_list?.length > 0">
                    <div
                      class="staff-item"
                      v-for="(item, index) in reserveData.staff_list"
                      :key="index + '-staff_list'"
                    >
                      {{ item.name }}：预约{{ item.num }}位
                    </div>
                  </template>

                  <div class="common-empty" v-else>暂无数据</div>
                </div>
              </div>
              <div class="appointment-doctor appointment-users">
                <div class="appointment-doctor-left">
                  <div class="appointment-title">预约医生</div>
                  <div
                    class="appointment-doctor-item"
                    v-for="(item, index) in reserveData.doctor_consult_list"
                    :key="index + '-doctor_list'"
                  >
                    <svg-icon :name="index ? 'meeting-initial' : 'meeting-fz'" size="26"></svg-icon>
                    <div class="appointment-value">当日{{ item.title }}：{{ item.num }}人</div>
                  </div>
                  <!-- <div class="appointment-item">
                  <svg-icon name="meeting-initial">当日复诊：{{ reserveData.doctor_consult_list.repeat }}人</svg-icon>
                </div> -->
                </div>
                <div class="appointment-doctor-right">
                  <div class="appointment-title">预约服务</div>
                  <div class="services-box">
                    <template v-if="reserveData.service_list?.length > 0">
                      <div
                        class="service-item"
                        v-for="(item, index) in reserveData.service_list"
                        :key="index + '-service_list'"
                      >
                        {{ item.title }}：{{ item.num }}
                      </div>
                    </template>
                    <div class="common-empty" v-else>暂无数据</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="right-card">
              <div class="card-title">目标现金收入</div>
              <div class="trade-value">
                <span class="value"
                  ><span class="currency">￥</span>{{ formatIndicatorsValue(target_performance.daily_target) }}</span
                >
              </div>
              <div class="trade-bottom">
                <div class="target-box">
                  <div class="target-title">
                    <svg-icon name="meeting-circle1" class="mr-8" size="20"></svg-icon>
                    本月目标进度：
                  </div>
                  <div class="target-percent">
                    {{ Number(target_performance.monthly_completion_rate).toFixed(2) }}<span class="percent">%</span>
                  </div>
                </div>
                <div class="target-box">
                  <div class="target-title">
                    <svg-icon name="meeting-circle2" class="mr-8" size="20"></svg-icon>
                    本月时间进度：
                  </div>
                  <div class="target-percent">
                    {{ Number(target_performance.monthly_time_progress).toFixed(2) }}<span class="percent">%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="summary-box">
        <div class="overview-item">
          <div class="overview-title">今日重点工作</div>
          <div class="overview-value">汇总</div>
          <svg-icon name="meeting-main" class="overview-icon"></svg-icon>
        </div>
        <div class="right-bottom">
          <div class="right-bottom-left common-card">
            <div class="normal-title">预约服务</div>
            <div class="follow-list">
              <div class="follow-item" v-for="(item, index) in priority_user_follow" :key="index + '-follow'">
                {{ item.name }}：{{ item.reserve_time }}
              </div>
            </div>
            <el-empty v-if="!priority_user_follow.length" :image-size="80"></el-empty>
          </div>
          <div class="right-bottom-right common-card">
            <div class="normal-title">计划/备注</div>
            <div class="remark-box">
              <div class="remark-item">
                <div class="label">学习计划：</div>
                <div class="content">{{ study_plan }}</div>
              </div>
              <div class="remark-item">
                <div class="label">备注：</div>
                <div class="content">{{ remark }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bottom-box">
        <div class="meeting-user">
          <div class="label">参会人：</div>
          <div class="meeting-user-wrap">
            <template>
              <div class="user" v-for="(item, index) in attendee_list" :key="index + '-attendee'">{{ item.name }}</div>
            </template>
            <div class="user" v-if="!attendee_list.length" style="color: #999">暂无参会人</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EveningMeeting',
  props: {
    selectedDate: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      caseContent: '',
      workPlan: '',
      overviews: [
        { title: '当日预约用户人数', key: 'user_num', value: '1,024', icon: 'meeting-user', unit: '人' },
        { title: '当日预约量', value: '1,024', key: 'num', icon: 'meeting-number', unit: '单' },
      ],

      reserveData: {
        user_num: 0, // 当日预约用户数
        num: 0, // 当日预约量
        user_list: [], // 用户列表
        staff_list: [], //员工列表
        doctor_consult_list: [], // 预约医生数量
        service_list: [], // 预约服务数量
      },
      target_performance: {
        daily_target: 0, // 当日目标
        daily_completion_target: 0, // 当日完成目标
        target_completion_rate: 0, // 目标完成率
        monthly_target: 0, // 月目标
        monthly_completion_rate: 0, // 月完成率
        monthly_completion_target: 0, // 月完成目标
        monthly_time_progress: 0, // 月时间进度
      },
      priority_user_follow: [], //重点用户跟进列表
      study_plan: '', // 学习计划列表
      remark: '', // 备注
      attendee_list: [], // 参会人列表
      loading: true,
    };
  },
  created() {
    this.getMorningMeetingDetail();
  },
  watch: {
    selectedDate() {
      this.getMorningMeetingDetail();
    },
  },
  methods: {
    formatIndicatorsValue(value, isMoney = true) {
      // 金额展示千分位，补2个0
      value = Number(value);
      if (isMoney) {
        return value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
      } else {
        return value;
      }
    },
    getMorningMeetingDetail() {
      const query = {
        date: this.selectedDate,
      };
      this.loading = true;
      this.$api
        .getMorningMeetingDetail(query)
        .then(res => {
          console.log('res: ', res);
          this.reserveData = res.reserve;
          this.target_performance = res.target_performance;
          this.priority_user_follow = res.priority_user_follow;
          this.study_plan = res.study_plan;
          this.remark = res.remark;
          this.attendee_list = res.attendee_list;
          this.overviews.forEach(item => {
            item.value = this.reserveData[item.key];
          });
        })
        .finally(() => (this.loading = false));
    },
  },
};
</script>

<style lang="less" scoped>
.morning-meeting {
  display: flex;
  flex-direction: column;
  .overview-item {
    width: 243px;
    min-height: 222px;
    background: #ffffff;
    padding: 20px;
    border-radius: 4px;
    margin-bottom: 16px;
    position: relative;
    &:last-child {
      margin-bottom: 0;
    }
    .overview-title {
      font-size: 20px;
      color: #666666;
      line-height: 30px;
    }
    .overview-value {
      font-size: 26px;
      color: #333333;
      line-height: 44px;
      margin: 16px 0;
    }
    .overview-icon {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 101px;
      height: 130px;
    }
  }
  .appointment-title,
  .normal-title {
    font-size: 16px;
    color: #666666;
    line-height: 22px;
    margin-bottom: 14px;
  }
  .main-box {
    flex: 1;
    display: flex;
    margin-bottom: 16px;
    .left-box {
      display: flex;
      flex-direction: column;
      .overview-item {
        flex: 1;
      }
    }
    .right-box {
      flex: 1;
      min-width: 0;

      .right-top {
        display: flex;

        .right-main {
          flex: 1;
          margin: 0 16px;
          flex-shrink: 0;
          width: 100%;
          min-width: 0;
          .appointment-users {
            background-color: #ffffff;
            padding: 14px;
            border-radius: 4px;
            padding-bottom: 6px;

            .appointment-list {
              display: flex;
              flex-wrap: nowrap;
              overflow-x: auto;
              min-width: 0;
              padding-right: 20px;
              padding-bottom: 8px;
              .appointment-item {
                display: flex;
                align-items: center;
                flex-shrink: 0;
                min-width: 0;
                .appointment-avatar {
                  width: 39px;
                  height: 39px;
                  border: 1px solid #eeeeee;
                  border-radius: 50%;
                }
                .appointment-name {
                  font-size: 14px;
                  color: #333333;
                  line-height: 20px;
                  margin-left: 10px;
                }
                .user-divide {
                  width: 1px;
                  height: 17px;
                  border: 1px solid #e3e3e3;
                  margin: 0 20px;
                }
              }
            }
          }
          .appointment-employee {
            margin-top: 16px;
            border-radius: 4px;
            .staff-item {
              padding: 8px 12px;
              font-size: 14px;
              color: #333333;
              line-height: 20px;
              background: #f5f6f8;
              margin-right: 14px;
              border-radius: 4px;
              flex-shrink: 0;
              &:last-child {
                margin-right: 0;
              }
            }
          }

          .appointment-doctor {
            display: flex;
            margin-top: 16px;
            .appointment-doctor-left {
              flex: 2;
              padding-right: 13px;
              .appointment-doctor-item {
                display: flex;
                align-items: center;
                padding: 23px 16px;
                margin-bottom: 14px;
                background: #f4f7fd;
                border-radius: 4px;
                .appointment-value {
                  font-size: 16px;
                  color: #75829e;
                  line-height: 22px;
                  margin-left: 16px;
                }
                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
            .appointment-doctor-right {
              flex: 3;
              padding-left: 13px;
              .services-box {
                display: flex;
                flex-wrap: wrap;
                gap: 14px;
                .service-item {
                  padding: 8px 17px;
                  border-radius: 4px;
                  border: 1px solid #dcdfe6;
                  opacity: 0.8;
                }
                .common-empty {
                  margin-top: 40px;
                }
              }
            }
          }
        }

        .right-card {
          padding: 20px;
          border-radius: 4px;
          background-color: #ffffff;
          display: flex;
          flex-direction: column;
          min-width: 260px;
          width: 260px;
          flex-shrink: 0;
          .card-title {
            font-weight: 600;
            font-size: 18px;
            color: #333333;
            line-height: 26px;
          }
          .trade-value {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            .currency {
              font-size: 14px;
              color: #333333;
              line-height: 20px;
            }
            .value {
              font-size: 26px;
              color: #333333;
              line-height: 44px;
            }
          }

          .trade-bottom {
            background: #f9fafb;
            border-radius: 5px;
            padding: 16px;

            .target-box {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 16px;
              &:last-child {
                margin-bottom: 0;
              }
              .target-title {
                display: inline-flex;
                align-items: center;
              }
            }
          }
        }
      }
    }
  }
  .summary-box {
    display: flex;
    margin-bottom: 16px;
    .overview-item {
      margin-bottom: 0;
    }
  }
  .right-bottom {
    display: flex;
    margin-left: 16px;
    flex: 1;
    max-height: 222px;

    .common-card {
      background: #fff;
      padding: 14px;
      border-radius: 4px;
    }
    .right-bottom-left {
      flex: 1;
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      .follow-list {
      }
    }
    .right-bottom-right {
      flex: 3;
      margin-left: 16px;
      .remark-box {
        display: flex;
        .remark-item {
          display: flex;
          flex: 1;
          .label {
            width: 80px;
            text-align: right;
            font-size: 14px;
            color: #999;
            line-height: 20px;
            margin-right: 10px;
          }
          .content {
            font-size: 14px;
            color: #333333;
            line-height: 20px;
          }
        }
      }
    }
  }
  .bottom-box {
    background: #ffffff;
    border-radius: 4px;
    padding: 12px 16px;
    .meeting-user {
      display: flex;
      align-items: center;
      .label {
        font-size: 16px;
        color: #333333;
        line-height: 22px;
      }
      .meeting-user-wrap {
        display: flex;
        flex-wrap: nowrap;
        gap: 12px;
        font-size: 16px;
        color: #333333;
      }
    }
  }
}
:deep(.el-empty) {
  padding: 10px 0;
}

.common-empty {
  padding: 10px 0;
  text-align: center;
  min-width: 100%;
  color: #999;
}
</style>
