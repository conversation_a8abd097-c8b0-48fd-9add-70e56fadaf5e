import customDatePicker from '../components/CustomDatePicker/CustomDatePicker';
import showEcharts from "@/mixins/showEcharts";

export default {
  mixins: [showEcharts],
  data () {
    return {
      formDate: [],
      date_type: '', // 获取数据后的确认的状态
      _date_type: '', // 实时状态
      custom_lastText: '',
    }
  },
  components: {
    customDatePicker
  },
  watch: {
    formDate: {
      handler (val) {
        if ( val && val[0] ) {
          this.setDateType()
          this.dateChange()
          this.custom_lastText = this.__lastText
        }
      }
    }
  },
  computed: {
    __lastText () {
      switch (this.date_type) {
        case 'day':
          return '前一日'
        case 'week':
          return '前一周'
        case 'month':
          return '前一月'
        default:
            return ''
      }
    }
  },
  methods: {
    // 统计时间
    getDateType (getDateType) {
      this._date_type = getDateType
    },
    setDateType () {
      if ( this._date_type !== 'daterange' ) {
        this.date_type = this._date_type
      }else {
        this.date_type = ''
      }
    },
    // 获取趋势图
    getTrend (cardList, currentIndex) {
      let params = {
        st: this.formDate[0],
        et: this.formDate[1],
        date_type: this.date_type,
        item: cardList[currentIndex].value
      }
      this.$api.getTrend(params).then( res => {
        const xData = res.list.map( item => item.x )
        const yData = res.list.map( item => item.y )

        this.line_options = this.$eChartFn.areaLineOptions( {
          xAxis: {
            data: xData
          },
          series: [{ data: yData }]
        }, cardList[currentIndex].label, cardList[currentIndex].isMoney || false)
      } )
    },

    // echarts配置
    /**
     * @description: 饼图配置
     * value: 配置函数模板
     * data: 数据源
     * title: 饼图title
     * isShow: 是否显示toolTip, 默认显示
     * */

    setPieConfigure (value, data, title, isShow = true) {
      data.map( item => {
        item.name = item.title||item.text||item.age_text
      })
      this[value] = this.$eChartFn.orderPie( {
        legend: {
          type:'scroll',
          show: true,
          bottom: '24px',
          left: 'center',
          name: 'consumption'
        },
        series: [{
          data,
          name: 'consumption'
        }],
        title: { text: title },
        tooltip: {
          formatter: ( a ) => {
            let content = ''
            a.data.tootipList && a.data.tootipList.forEach( (item, index) => {
              if ( index == 0 ) {
                content = content +
                `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${ item.key }</span>
                <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${ item.value }</span></br>`
              }else{
                content = content +
                `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${ item.key }</span>
                <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${ item.value}</span>`
              }
            } )
            return `<div class="flex-col">
                      ${content}
                    </div>
                    `
          },
          show: isShow
        },
      } )
    }
  }
}
