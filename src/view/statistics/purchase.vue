<template>
  <div class="purchase-wrapper">
    <section class="g-h flex flex-item-between flex-item-center ceiling">
      <div class="g-h-l flex flex-item-align">
        <span class="label">统计时间：</span>
        <DatePicker type="daterange" placeholder="请选择日期区间" style="width: 200px" :clearable="false" :options="timeOptions" v-model="purTimes"
                    @on-change="changePurTime"></DatePicker>
      </div>
    </section>
    <!--    实时概况-->
    <section class="pur-h common-bg">
      <div class="ow-head flex">
        <h3 class="h-title">整体情况</h3>
      </div>
      <div class="flex flex-warp data-sta ">
        <div class="sta-item flex flex-c " v-for="(item,index) in overviewOptions" :key="item.value"
             :class="[index<5?'has-line':'']">
          <h4 class="sta-t">{{ item.label }}</h4>
          <div class="data-v">
            <span v-if="item.isMoney">{{ overview[item.value] && overview[item.value]|number_format }}</span>
            <span v-else-if="item.isDate&&item.length"
                  style="margin-top: -6px;display:flex;flex-direction: column;">{{ overview[item.value] && (overview[item.value].slice(0, 10)) }}<br/>
              <span class="time">{{ overview[item.value] && overview[item.value].substr(-8) }}</span></span>
            <span v-else :class="{'is-text': item.label == '常购商品'}">{{ overview[item.value] && overview[item.value] }}</span>
          </div>
        </div>
      </div>
    </section>
    <section class="pur-wrapper common-bg">
      <div class="ranking-wrapper flex">
        <div class="rank-l rank-item">
          <h3 class="h-title">采购物资排行</h3>
          <div class="rank-table rank-table-l">
            <Table :columns="appletsCols"
                   :data="goods_rank" v-if="goods_rank.length">
              <template slot-scope="{row,index}" slot="rank">
                <div class="top-three" v-if="index<3">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank1" v-show="index === 0">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank2" v-show="index === 1">
                  <img src="@/assets/image/data/<EMAIL>" alt="rank3" v-show="index === 2">
                </div>
                <div style="text-align: center;color:#F0A522;" v-else>{{ index + 1 }}</div>
              </template>
              <template slot-scope="{row}" slot="content">
                {{ row.content }}
              </template>
              <template slot-scope="{row}" slot="rate">
                {{ row.rate }}%
              </template>
            </Table>
            <div class="empty" v-else>暂无数据</div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
export default {
  name: "purchase",
  data() {
    return {
      purTimes: [],
      timeData: {
        st: '',
        et: ''
      },
      appletsCols: [
        {title: '排名', slot: 'rank', width: 60, align:'center'},
        {title: '名称', key: 'name', align: 'left', minWidth: 200},
        {title: '采购件数', key: 'num', align: 'center', width: 80},
        {title: '采购金额', key: 'amount', align: 'center', width: 80},
        {title: '占比', slot: 'rate', align: 'center', width: 80},
      ],
      overviewOptions: [
        {
          value: 'purchase_num',
          label: '采购次数',
        },
        {
          value: 'purchase_amount',
          label: '采购金额（元）',
          isMoney: true,
        },
        {
          value: 'last_purchase_time',
          label: '最近一次采购时间',
          isDate: true
        },
        {
          value: 'hot_goods',
          label: '常购商品',
        }, {
          value: 'day_per_storage',
          label: '平均采购入库时长（天）',
        }],
      timeOptions: {
        disabledDate(date){
          return date && date.valueOf() > Date.now() - 86400000
        }
      },
      overview: {},
      goods_rank: []
    }
  },
  created() {
    this.getInitData()
  },
  methods: {
    changeDataType(type) {
      this.dataType = type
      this.getOverviewData(type)
    },
    getInitData() {
      this.$api.getPurchaseOverview(this.timeData).then((res) =>{
        console.log(res)
        this.overview = res.overview
        this.goods_rank = res.goods_rank

        this.purTimes = [res.st,res.et]
        this.timeData.st = res.st
        this.timeData.et = res.et
      })
    },
    changePurTime(times) {
      console.log(times)
      if (times) {
        this.timeData.st = times[0]
        this.timeData.et = times[1]
      } else {
        this.timeData.st = ''
        this.timeData.et = ''
      }
      this.getInitData()
    },
  },
}
</script>

<style scoped lang="less">
.purchase-wrapper {
  .g-h {
    padding: 20px 0 16px;
  }

  .pur-h {
    .ow-head {
      padding: 20px 20px 0;

      .h-time {
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #999999;
        line-height: 18px;
        align-self: flex-end;
        transform: scale(0.8)
      }
    }

    .data-sta {
      flex-wrap: wrap;
      padding: 0 9px;

      .sta-item {
        width: 20%;
        padding: 20px 0 20px 60px;

        .sta-t {
          font-size: 12px;
          color: #444444;
          line-height: 17px;
          font-weight: 400;
        }

        .data-v {
          color: #000000;
          font-size: 20px;
          font-family: OPPOSans;
          font-weight: 500;
          line-height: 29px;
          -webkit-text-stroke: 0px #000000;
          text-stroke: 0px #000000;
          padding: 12px 0 6px;
          border-right: 1px solid #EFEFEF;
          .time{
            font-size: 14px;
            line-height: 1;
            font-weight: 400;
          }
        }

        &:nth-child(5n) {
          .data-v {
            border-right: none;
          }
        }

        .data-t {
          font-weight: 400;
          color: #AAAAAA;
          line-height: 17px;
        }
      }
    }
  }

  .pur-wrapper {
    padding: 16px 20px 22px;
    margin-top: 16px;

    .ranking-wrapper {
      margin-top: 10px;

      .rank-l {
        .rank-table-l {
          margin-right: 50px;
        }
      }

      .rank-r {
        margin-left: 50px;
      }

      .rank-item {
        flex: 1;

        ::v-deep .ivu-table {
          thead {
            th {
              background: #FFFFFF;
              color: #999999;
            }

            td {
              height: 40px;
            }
          }

          &::before {
            height: 0;
          }
        }
      }
    }
  }
}
</style>
<style lang="less" scoped>
.h-title {

  font-weight: bolder;
  line-height: 25px;
}

.common-bg {
  background: #ffffff;
  border-radius: 4px;
}
.top-three {
  > img {
    width: auto;
    height: 20px;
    margin: 0px;
  }
}
.is-text {
  font-family: none !important;
}
</style>
