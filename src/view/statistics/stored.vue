<template>
  <div class="wrapper">
    <!-- Header -->
    <header class="head-block ceiling">
      <div class="flex flex-item-align">
        <p>统计时间：</p>
        <customDatePicker v-model="formDate" @getDateType="getDateType"></customDatePicker>
      </div>
    </header>

    <!-- 整体情况 -->
    <div class="block">
      <h4 class="block-title flex flex-align-center">
        <p>整体情况</p>
        <Tooltip max-width="300" theme="light" placement="bottom" :offset="100" class="custom-tooltip tooltip-left-arrow ml6">
          <div slot="content">
            <p v-for="(item, index) in stored_tooltip_list" :key="index+'tooltip'">{{ item }}</p>
          </div>
          <p class="flex flex-item-center cursor">
            <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            <!-- <span class="cursor tip-text"></span> -->
          </p>
        </Tooltip>
      </h4>
      <!-- 卡片数据 -->
      <div class="mt16 flex flex-warp">
        <div
          class="card-block mb16"
          v-for="(card_item, card_index) in stored_card_options"
          :key="'card'+card_index"
          :class="{
            'card-block--actived' : current_card_index === card_index ,
            'card-block--hover':  card_item.readOnly !== true,
            'cursor': card_item.readOnly !== true,
          }"
          @click="cardChange(card_item, card_index)"
          >
          <p class="card-title">{{ card_item.label }}</p>
          <p class="card-content mt12" v-if="card_item.isMoney">￥{{ card_item.current | number_format(2) }}</p>
          <p class="card-content mt12" v-else>{{ card_item.current || 0 }}</p>
          <div v-show="date_type&&!card_item.hideCompare">
            <p class="card-tip mt6" v-if="card_item.isMoney">{{ custom_lastText }}：￥{{ card_item.last | number_format(2) }}</p>
            <p class="card-tip mt6" v-else>{{ custom_lastText }}：{{ card_item.last || 0 }}</p>
          </div>
        </div>
      </div>

      <!-- 折线趋势图 -->
      <div class="mt30">
        <chart-view height="260px" :chart-option="line_options"></chart-view>
      </div>
    </div>

    <!-- 剩余储值金额分布 -->
    <div class="block mt16">
      <h4 class="block-title flex flex-align-center">
        <p>剩余储值金额分布</p>
        <Tooltip max-width="300" theme="light" placement="bottom" :offset="100" class="custom-tooltip tooltip-left-arrow ml6">
          <div slot="content">
            <p v-for="(item, index) in surplus_tooltip_list" :key="index+'tooltip'">{{ item }}</p>
          </div>
          <p class="flex flex-item-center cursor">
            <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            <!-- <span class="cursor tip-text"></span> -->
          </p>
        </Tooltip>
      </h4>
      <!-- content -->
      <div class="mt30 flex">
        <div class="flex-1 pr30">
          <chart-view :chart-option="surplus_data" v-if="isShowLineEcharts(surplus_data, 'line')"></chart-view>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>

        <div class="flex-1">
          <Table class="mt16 custom-table-line" :columns="surplusCols" :data="surplus_lists" v-if="surplus_lists.length">
            <template slot-scope="{row, index}" slot="title">
              <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
              <span>{{ row.title }}</span>
            </template>
            <template slot-scope="{row}" slot="rate">
              {{ row.rate }}%
            </template>
          </Table>
          <div class="empty" v-else >
            暂无数据
          </div>
        </div>
      </div>
    </div>

    <!-- 单次储值金额区间分布 -->
    <div class="block mt16">
      <h4 class="block-title flex flex-align-center">
        <p>单次储值金额区间分布</p>
        <Tooltip max-width="300" theme="light" placement="bottom" :offset="100" class="custom-tooltip tooltip-left-arrow ml6">
          <div slot="content">
            <p v-for="(item, index) in money_tooltip_list" :key="index+'tooltip'">{{ item }}</p>
          </div>
          <p class="flex flex-item-center cursor">
            <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
            <!-- <span class="cursor tip-text"></span> -->
          </p>
        </Tooltip>
      </h4>
      <!-- content -->
      <div class="mt30 flex">
        <div class="flex-1 pr30">
          <div class="mt12">
            <Table :columns="moneyCols" :data="money_lists" v-if="money_lists.length" class="custom-table-line">
              <template slot-scope="{row, index}" slot="title" class="flex">
                <!--              <i class="circle" :style="{borderColor:color_table_enum[index]}"></i>-->
                <span>{{ row.title }}</span>
              </template>
              <template slot-scope="{row}" slot="rate">
                {{ row.rate }}%
              </template>
            </Table>
            <div class="empty" v-else >
              暂无数据
            </div>
          </div>
        </div>

        <div class="flex-1 flex flex-item-align">
          <!-- right bar -->
          <chart-view height="240px" :chart-option="money_bar_options"></chart-view>
        </div>
      </div>
    </div>

    <!-- 储值场景分析/储值次数分析 -->
    <div class="block mt16 flex">
      <div class="flex-1 pr30">
        <h4 class="block-title flex flex-align-center">
          <p>储值场景分析</p>
          <Tooltip max-width="300" theme="light" placement="bottom" :offset="100" class="custom-tooltip tooltip-left-arrow ml6">
            <div slot="content">
              <p v-for="(item, index) in source_tooltip_list" :key="index+'tooltip'">{{ item }}</p>
            </div>
            <p class="flex flex-item-center cursor">
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
              <!-- <span class="cursor tip-text"></span> -->
            </p>
          </Tooltip>
        </h4>
        <!-- bar -->
        <div class="flex-1 flex flex-item-align right-line mt16 pr30">
          <chart-view height="240px" :chart-option="source_bar_options"></chart-view>
        </div>
      </div>

      <div class="flex-1">
        <h4 class="block-title flex flex-align-center">
          <p>储值次数分析</p>
          <Tooltip max-width="300" theme="light" placement="bottom" :offset="100" class="custom-tooltip tooltip-left-arrow ml6">
            <div slot="content">
              <p v-for="(item, index) in num_tooltip_list" :key="index+'tooltip'">{{ item }}</p>
            </div>
            <p class="flex flex-item-center cursor">
              <svg-icon iconClass="tip" class="helpIcon cursor"></svg-icon>
              <!-- <span class="cursor tip-text"></span> -->
            </p>
          </Tooltip>
        </h4>
        <!-- bar -->
        <div class="mt16 flex flex-item-align">
          <chart-view height="240px" :chart-option="num_bar_options"></chart-view>
        </div>
      </div>
    </div>

    <!-- 储值统计 -->
    <div class="block mt16">
      <div class="flex flex-item-between">
        <p class="block-title">储值统计</p>
        <a @click="exportStored">导出储值统计</a>
      </div>
      <Table class="mt9" :columns="storedCols" :data="stored_list" :loading="storedTabLoading">
        <!-- 日期 -->
        <template slot-scope="{row, index}" slot="date">
          <p>{{ row.date | data_format('YYYY-MM-DD') }}</p>
        </template>

        <!-- 已完成储值订单数 -->
        <template slot-scope="{row, index}" slot="paid_num">
          <p>{{ row.paid_num || 0 }}</p>
        </template>

        <!-- 全部储值订单数 -->
        <!-- <template slot-scope="{row, index}" slot="order_num">
          <p>{{ row.order_num || 0 }}</p>
        </template> -->

        <!-- 实充金额 -->
        <template slot-scope="{row, index}" slot="recharge_real_amount">
          <p v-if="row.recharge_real_amount" >￥{{ row.recharge_real_amount }}</p>
          <p v-else>-</p>
        </template>

        <!-- 赠送金额 -->
        <template slot-scope="{row, index}" slot="recharge_given_amount">
          <p v-if="row.recharge_given_amount" >￥{{ row.recharge_given_amount }}</p>
          <p v-else>-</p>
        </template>

        <!-- 总储值金额 -->
        <template slot-scope="{row, index}" slot="recharge_total_amount">
          <p v-if="row.recharge_total_amount" >￥{{ row.recharge_total_amount }}</p>
          <p v-else>-</p>
        </template>

        <!-- 实际退款金额 -->
        <template slot-scope="{row, index}" slot="recharge_refund_receive">
          <p v-if="row.recharge_refund_receive" >￥{{ row.recharge_refund_receive }}</p>
          <p v-else>-</p>
        </template>

        <!-- 实充退款金额 -->
        <template slot-scope="{row, index}" slot="recharge_refund_real">
          <p v-if="row.recharge_refund_real" >￥{{ row.recharge_refund_real }}</p>
          <p v-else>-</p>
        </template>

        <!-- 赠送撤回金额 -->
        <template slot-scope="{row, index}" slot="recharge_refund_given">
          <p v-if="row.recharge_refund_given" >￥{{ row.recharge_refund_given }}</p>
          <p v-else>-</p>
        </template>

        <!-- 储值营收 -->
        <template slot-scope="{row, index}" slot="recharge_revenue">
          <p v-if="row.recharge_revenue" >￥{{ row.recharge_revenue }}</p>
          <p v-else>-</p>
        </template>
      </Table>
      <div class="page-wrapper">
        <KPage
          :total="stored_total"
          :page-size.sync="stored_queryFormData.pageSize"
          :current.sync="stored_queryFormData.page"
          @on-change="OnPageChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { stored_card_options, stored_tooltip_list, surplus_tooltip_list, money_tooltip_list, source_tooltip_list, num_tooltip_list} from './data/stored_data';
import { color_enum } from "@/view/statistics/data/color";
import dateChange from './mixins/dateChange';
import download from '@/mixins/downloadExcel';
const init_query_form_data = {
  page: 1,
  pageSize: 10,
}
  export default {
    name: "stored",
    components: {
    },
    mixins: [download,dateChange],
    props: {

    },
    data () {
      return {
        downloadApiName: 'rechargeExporturl',
        color_table_enum: color_enum,//色值表
        /* 整体情况 */
        stored_card_options: stored_card_options, // 卡片数据枚举
        current_card_index: 0, // 当前选中的卡片索引
        line_options: {},
        stored_tooltip_list: stored_tooltip_list, // 整体情况字段说明

        /* 剩余储值金额分布 */
        surplus_tooltip_list: surplus_tooltip_list,
        surplus_data: {},
        surplusCols: [
          {title: '剩余储值金额', slot: 'title', tooltip: true,minWidth: 100},
          {title: '人数', key: 'num', align: 'center',width: 80},
          {title: '人数占比', slot: 'rate', align: 'right', width: 80},
        ],
        surplus_lists: [],

        /* 单次储值金额区间分布 */
        money_tooltip_list: money_tooltip_list,
        moneyCols: [
          {title: '单次储值金额区间', slot: 'title', align: 'center', minWidth: 200, tooltip: true},
          {title: '次数', key: 'num', align: 'center', width: 80},
          {title: '次数占比', slot: 'rate', align: 'right', width: 80},
        ],
        money_lists: [],
        money_bar_options: {}, // 单次储值金额区间分布饼图options

        source_bar_options: {}, // 储值场景分析饼图数据
        source_tooltip_list: source_tooltip_list,

        num_bar_options: {}, // 储值次数分析饼图数据
        num_tooltip_list: num_tooltip_list,

        /* 储值统计 */
        storedCols: [
          { title: '日期', key: 'date', align: 'center' },
          // { title: '全部储值订单数', slot: 'order_num', align: 'center' },
          { title: '已完成储值订单数', slot: 'paid_num', align: 'center' },
          { title: '实充金额', slot: 'recharge_real_amount', align: 'center' },
          { title: '赠送金额', slot: 'recharge_given_amount', align: 'center' },
          { title: '总储值金额', slot: 'recharge_total_amount', align: 'center' },
          { title: '实际退款金额', slot: 'recharge_refund_receive', align: 'center' },
          { title: '实充退款金额', slot: 'recharge_refund_real', align: 'center' },
          { title: '赠送撤回金额', slot: 'recharge_refund_given', align: 'center' },
          { title: '储值营收', slot: 'recharge_revenue', align: 'center' },
        ],
        stored_list: [], // 商品交易
        stored_queryFormData: {...init_query_form_data},
        stored_total: 0, // 表格总条数
        storedTabLoading: false,
      }
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {
    },
    methods: {
      // 当时间发生变化，进行得操作
      dateChange () {
        // 趋势图
        this.getTrend(this.stored_card_options, this.current_card_index)
        // 获取整体情况数据
        this.getStoredOverview()
        // 储值分布分析
        this.getRechargeAnalysis()
        // 获取储值订单记录
        this.getRechargeRecord()
      },

      /* 卡片切换事件 */
      cardChange ( card_item, card_index ) {
        if ( card_item.readOnly ) return
        if ( card_index == this.current_card_index ) return

        this.current_card_index = card_index
        this.getTrend(this.stored_card_options, this.current_card_index)
      },

      // 剩余储值金额分布柱状图
      handleFirst (list) {
        this.surplus_lists = list
        let xData = list.map( x_item => x_item.title )
        let yData = list.map( y_item => y_item.num )
        this.surplus_data = this.$eChartFn.verticalBar( {
          xAxis: {
            data: xData
          },
          series: [{
            data: yData
          }]
        })
      },

      // 处理单次储值金额区间分布饼图数据
      handleMoneyOptions (list, bar_options_name = '', listName =  '') {
        this[listName] = list
        let resultList = []

        let defaultTooltipList = []
        list && list.forEach( item => {
          if (bar_options_name == 'money_bar_options') {
            defaultTooltipList = [
              { key: '储值区间:', value: item.title },
              { key: '', value: `${item.num}次` },
              { key: '占比', value: `${item.rate}%` }
            ]
          }
          if (bar_options_name == 'source_bar_options') {
            defaultTooltipList = [
              { key: '', value: item.title },
              { key: '', value: `${item.num}单` },
              { key: '', value: `￥${item.money || 0}` },
              { key: '金额占比', value: `${item.rate}%` }
            ]
          }
          if (bar_options_name == 'num_bar_options') {
            defaultTooltipList = [
              { key: '储值次数:', value: item.title },
              { key: '', value: `${item.num}人` },
              { key: '占比', value: `${item.rate}%` }
            ]
          }
          resultList.push({
            ...item,
            value: item.num,
            tootipList: defaultTooltipList
          })
        } )
        this.setPieConfigure(bar_options_name, resultList, '')
      },

      // 导出储值明细
      exportStored () {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
        }
        this.downloadExcel(params)
      },

      // 表格分页
      OnPageChange (page, pageSize) {
        this.stored_queryFormData.page = page
        this.stored_queryFormData.pageSize = pageSize
        this.getRechargeRecord()
      },

      /* API */
      // api-获取整体情况
      getStoredOverview () {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          date_type: this.date_type,
        }
        this.$api.getRechargeOverview(params).then( ({overview}) => {
          this.stored_card_options.forEach( item => {
            if ( typeof overview[item.value] == 'string') {
              item.current = overview[item.value]
            }else{
              item.current = overview[item.value].current
              item.last = overview[item.value].before
            }
          } )
        } ).catch( error => {} )
      },

      // api-储值分布分析
      getRechargeAnalysis () {
        let params = {
          st: this.formDate[0],
          et: this.formDate[1],
          date_type: this.date_type,
        }
        this.$api.getRechargeAnalysis(params).then( (res) => {
          // 处理剩余储值金额分布
          this.handleFirst(res.recharge_balance_distribution)

          // 处理单次储值金额区间分布
          this.handleMoneyOptions(res.recharge_money_distribution, 'money_bar_options', 'money_lists')

          // 处理储值场景分析
          this.handleMoneyOptions(res.recharge_source_distribution, 'source_bar_options')

          // 处理储值次数分析
          this.handleMoneyOptions(res.recharge_num_distribution, 'num_bar_options')

        } ).catch( error => {} )
      },

      // api-获取储值记录
      getRechargeRecord () {
        this.storedTabLoading = true
        let params = {
          ...{
            st: this.formDate[0],
            et: this.formDate[1],
          },
          ...this.stored_queryFormData,
          date_type: this.date_type,
        }
        this.$api.getRechargeRecord(params).then( res => {
          this.stored_list = res.list
          this.stored_total = Number(res.total)
        } ).finally( () => this.storedTabLoading = false )
      },

    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
@import url('./style/common.less');
</style>
