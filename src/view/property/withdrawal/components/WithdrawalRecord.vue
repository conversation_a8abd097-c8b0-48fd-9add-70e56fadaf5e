<template>
  <div class="WithdrawalRecord-wrapper">
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      size="small"
      :extra-height="49"
      stripe
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form @submit.native.prevent @keyup.enter.native="onSearch" :label-width="0" inline>
          <Row>
            <Col>
              <FormItem label="">
                <DatePicker
                  type="daterange"
                  :options="dateOptions"
                  v-model="timeRange"
                  @on-change="times => handleTimeChange(times)"
                  placeholder="交易时间"
                  style="width: 180px"
                ></DatePicker>
              </FormItem>
            </Col>
            <Col>
              <FormItem label="">
                <Select v-model="queryFormData.status" placeholder="提现状态" style="width: 180px">
                  <Option v-for="(item, status) in statusDesc" :key="status" :value="status">{{ item.desc }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col>
              <FormItem>
                <Button type="primary" @click="initList">筛选</Button>
                <Dvd /><Dvd />
                <span class="list-reset-btn" @click="onResetSearch"
                  ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
                >
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="table-wrapper">
          <div class="tips" style="margin-top: -12px; padding-bottom: 4px; color: #999">
            提现记录可能存在5-15分钟延迟情况
          </div>
        </div>
      </template>

      <template v-slot:created_at="{ row }">
        {{ row.created_at || '-' }}
      </template>
      <template v-slot:amount_string="{ row }"> ￥{{ Number(row.amount_string).toFixed(2) }} </template>
      <template v-slot:status_desc="{ row }">
        <!-- <status-text status="10">{{ row.status_desc }}</status-text> -->
        <mark-status :type="getStatusTextColor(row.status)">{{ row.status_desc || '-' }}</mark-status>
      </template>
    </standard-table>
  </div>
</template>

<script>
import moment from 'moment';
import search from '@/mixins/search';
import S from '@/libs/util';
import StandardTable from '@/components/StandardTable/index.vue';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  trade_code: '',
  biz_type: '',
  type: '',
  status: '',
  symbol: '',
  st: moment().subtract(30, 'days').format('YYYY-MM-DD'),
  et: moment().format('YYYY-MM-DD'),
  sys_code: 'RSJYZT',
};
export default {
  name: 'WithdrawalRecord',
  mixins: [search],

  components: { StandardTable },

  props: {},

  data() {
    return {
      apiName: 'getWithdrawalRecordList',
      isMultipleTabs: true,
      timeRange: [],
      statusOptions: [],
      queryFormData: {
        ...init_query_form_data,
      },
      dateOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: '今天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              return [start, end];
            },
          },
          {
            text: '昨天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'weeks').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              start.setTime(start.getTime() - 3600 * 1000 * 24);
              return [start, start];
            },
          },
          {
            text: '近七天',
            value() {
              const end = new Date();
              const start = new Date();
              console.log('-> %c end  === %o', 'font-size: 15px;color: green;', end);
              // const start = new Date (moment().subtract(1, 'months').unix()*1000)
              // console.log("-> %c start  === %o", "font-size: 15px;color: green;", start)
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
              return [start, end];
            },
          },
          {
            text: '近三十天',
            value() {
              const start = moment().subtract(30, 'days').toDate();
              const end = moment().toDate();
              // start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
              return [start, end];
            },
          },
        ],
      },
      list: [],
      tableCols: [
        // {
        //   title: '商户订单号',
        //   key: 'mch_order_code',
        //   align: 'center',
        //   render: (h, { row }) => h('span', {}, row.mch_order_code || '-')
        // },
        // {
        //   title: '商户流水号',
        //   key: 'mch_bill_code',
        //   align: 'center',
        //   render: (h, { row }) => h('span', {}, row.mch_bill_code || '-')
        // },
        {
          title: '交易流水号',
          key: 'code',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.code || '-'),
        },
        // { title: '银行账户', key: 'to_account_no', align: 'center' },
        {
          title: '收款账号',
          slot: 'bank_no',
          align: 'center',
          render: (h, { row }) => h('span', {}, row?.extend?.withdraw_extend?.bank_no || '-'),
        },
        { title: '交易时间', slot: 'created_at', align: 'center' },
        { title: '金额（元）', slot: 'amount_string', align: 'center' },
        {
          title: '交易类型',
          key: 'mch_bill_type_text',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.mch_bill_type_text || '-'),
        },
        {
          title: '提现状态',
          slot: 'status_desc',
          align: 'center',
        },
        {
          title: '附言',
          key: 'bank_memo',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.extend?.withdraw_extend?.bank_memo || '-'),
        },
      ],
      tableLoading: false,
      symbolDesc: [],
      typeDesc: [],
      statusDesc: [],
    };
  },

  computed: {
    getStatusTextColor() {
      return status => {
        switch (status) {
          case '3':
            return 'success';
          case '1':
            return 'warn';
          case '2':
            return 'gray';
          case '4':
            return 'reject';
        }
      };
    },
  },

  watch: {},

  created() {
    this.getOptions();
  },

  mounted() {},

  methods: {
    handleTimeChange(times) {
      this.queryFormData.st = (times && times[0]) || '';
      this.queryFormData.et = (times && times[1]) || '';
    },
    onResetSearch() {
      this.queryFormData = {
        ...init_query_form_data,
      };
      this.timeRange = [];
      this.initList();
    },
    getOptions() {
      this.$api.getBalanceOptions({ sys_code: 'RSJYZT' }).then(data => {
        this.statusDesc = data.statusDesc;
        this.initList();
      });
    },
    initList() {
      this.queryFormData = S.merge(this.queryFormData, this.$route.query);
      if (this.queryFormData.st && this.queryFormData.et) {
        this.timeRange = [this.queryFormData.st, this.queryFormData.et];
      }
      this.getsList();
    },
  },

  destroyed() {
    console.log(213123);
  },
};
</script>

<style scoped lang="less"></style>
