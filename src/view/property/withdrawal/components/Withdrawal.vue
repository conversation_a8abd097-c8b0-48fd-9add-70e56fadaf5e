<template>
  <div class="Withdrawal-wrapper">
    <Alert type="warning" v-if="isyztopen === '0'">
      为了保障资金安全，需要先通过主体认证，方可使用。
      <Button size="small" type="primary" @click="$router.push('/setting/subject-auth/index')">去认证</Button>
      <!-- <template slot="desc">Content of prompt. Content of prompt. Content of prompt. Content of prompt. </template> -->
    </Alert>
    <Form :label-width="130" :model="formData" ref="formData" :rules="formDataRules" :label-colon="true">
      <FormItem label="出款账户">
        <RadioGroup value="10">
          <Radio label="10">余额</Radio>
        </RadioGroup>
      </FormItem>

      <FormItem label="到账银行卡">
        <div class="flex" v-if="isyztopen === '1'">
          <Input :value="hideBankCard(formData.bank_acct_no)" class="normal-input" readonly disabled />
          <!--          <p style="margin-left: 12px;color: #ff9900;">不可变更绑定的银行卡</p>-->
        </div>
        <a @click="$router.push('/setting/subject-auth/index')" v-else>去认证</a>
      </FormItem>

      <FormItem label="提现金额" required>
        <InputNumber
          v-model="formData.balance"
          :min="0"
          placeholder="请输入提现金额"
          :precision="2"
          :active-change="false"
          style="width: 150px"
        />
        <!-- @on-blur="getServiceCharge" -->
        <p>最多可提现 {{ formData.max_withdraw }} 元</p>
      </FormItem>

      <!-- <FormItem label="手续费"> -->
      <!--   <span v-if="Number(formData.charge)">{{ formData.charge }}元</span> -->
      <!--   <span v-else>-</span> -->
      <!-- </FormItem> -->

      <FormItem label="备注">
        <Input
          type="textarea"
          placeholder="请输入提现用途，选填"
          v-model="formData.remark"
          class="normal-input"
          maxlength="50"
          show-word-limit
        ></Input>
      </FormItem>

      <FormItem label="">
        <Button type="primary" @click="submitWithdrawal">提交</Button>
      </FormItem>
    </Form>

    <ConfirmModal
      content="确认提现"
      contentText="提款可能会有延迟，请耐心等待"
      :confirmVisible.sync="confirmVisible"
      @ok="confirmFunc"
    ></ConfirmModal>
  </div>
</template>

<script>
import ConfirmModal from '@/components/confirmModal/confirmModal';

const init_form_data = {
  bank_acct_no: '',
  max_withdraw: '',
  balance: null,
  remark: '',
  charge: '',
};
export default {
  name: 'Withdrawal',
  mixins: [],

  components: {
    ConfirmModal,
  },

  props: {},

  data() {
    return {
      formData: {
        ...init_form_data,
      },
      selectedAddress: [],
      confirmVisible: false,
      formDataRules: {
        name: [{ required: true, message: '请输入收件人姓名', trigger: 'change' }],
      }, // 表单校验
      isyztopen: '',
    };
  },

  computed: {
    hideBankCard() {
      return value => {
        if (value && value.length > 8) {
          let reg = /^(\d{4})(\d*)(\d{4})$/;
          let str = value.replace(reg, (a, b, c, d) => {
            return b + c.replace(/\d/g, '*') + d;
          });
          return str;
        } else {
          return value;
        }
      };
    },
  },

  watch: {},

  created() {
    this.getWithdrawalInfo();
    this.getIsyztopen();
  },

  mounted() {},

  methods: {
    getIsyztopen() {
      this.$api.getIsyztopen({ sys_code: 'RSJYZT' }).then(res => {
        this.isyztopen = res.is_yzt_open;
      });
    },
    submitWithdrawal() {
      if (this.isyztopen === '0') {
        this.$Message.error('当前因未认证，暂不支持余额提现');
        return;
      }
      if (!this.formData.balance) {
        this.$Message.error('请输入提现金额');
        return;
      }
      if (this.formData.balance > this.formData.max_withdraw) {
        this.$Message.error('提现金额不能大于最大可提现金额');
        return;
      }
      this.confirmVisible = true;
    },
    confirmFunc() {
      this.$api.submitWithdrawal({ ...this.formData, sys_code: 'RSJYZT' }).then(res => {
        this.$Message.success('提现成功');
        this.confirmVisible = false;
        this.getWithdrawalInfo();
      });
    },
    getServiceCharge() {
      this.$api.getWithdrawalCharge({ balance: this.formData.balance, sys_code: 'RSJYZT' }).then(res => {
        console.log(res);
        this.formData.charge = +res.charge;
      });
    },
    getWithdrawalInfo() {
      this.formData = {
        ...init_form_data,
      };
      this.$api
        .getWithdrawalInfo({ sys_code: 'RSJYZT' })
        .then(res => {
          console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
          if (res.withdraw?.bank_acct_no?.bank_no_list?.length) {
            this.formData.bank_acct_no = res.withdraw.bank_acct_no.bank_no_list[0].bank_acct_no;
          }
          this.formData.max_withdraw = res.withdraw.max_withdraw;
        })
        .catch(err => {
          {
          }
        });
    },
    // 银行卡保留前后四位 中间全部用*号代替 不包含空格
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
p {
  margin: 0;
}

.normal-input {
  width: 480px;
}
</style>
