<template>
  <div class="company-wrapper">
    <Form inline @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem>
          <Input v-model="queryFormData.name" placeholder="员工姓名" clearable />
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.mobile" placeholder="员工手机号" clearable />
        </FormItem>
        <FormItem>
          <Input v-model="queryFormData.wallet_code" placeholder="记账钱包" clearable />
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.bind_status" placeholder="结算信息">
            <Option v-for="item in bindOptionList" :value="item.id" :key="item.id">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem style="text-align: left">
          <Button class="mr10" type="primary" @click="onSearch">筛选</Button>
          <span class="list-reset-btn" @click="onResetSearch"
            ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
          >
        </FormItem>
      </Row>
    </Form>
    <div class="table-wrapper">
      <Table :columns="tableCols" :data="list" :loading="tableLoading" :height="$store.state.app.clientHeight - 228">
        <template v-slot:staff_info="{ row }">
          <div class="flex flex-item-align">
            <img
              :src="row.avatar || 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png'"
              class="avatar"
            />

            <div style="text-align: left">
              <div>姓名：{{ row.name }}</div>
              <div>手机号：{{ row.mobile || '-' }}</div>
              <div>身份证号：{{ row.idcard }}</div>
            </div>
          </div>
        </template>
        <!-- 待结算金额 -->
        <template v-slot:divide_wait_settle_balance="{ row }">
          <a @click="seeMoneyModal(row, '1')">¥{{ row.divide_wait_settle_balance || '0.00' }}</a>
        </template>
        <!-- 可结算金额 -->
        <template v-slot:divide_prepare_settle_balance="{ row }">
          <a @click="seeMoneyModal(row, '2')">¥{{ row.divide_prepare_settle_balance || '0.00' }}</a>
        </template>

        <!-- 结算中金额 -->
        <template v-slot:divide_during_settle_balance="{ row }">
          <a @click="seeMoneyModal(row, '3')">¥{{ row.divide_during_settle_balance || '0.00' }}</a>
        </template>

        <!-- 已结算金额 -->
        <template v-slot:divide_finish_amount="{ row }">
          <a @click="seeMoneyModal(row, '4')">¥{{ row.divide_finish_amount || '0.00' }}</a>
        </template>

        <!-- 累计收益 -->
        <template v-slot:divide_all_amount="{ row }"> ￥{{ row.divide_all_amount || '0.00' }}</template>

        <template v-slot:settle_info="{ row }">
          <a v-if="!row.bank_no" @click="showBindModal(row)">未绑定</a>
          <div v-else style="text-align: left">
            <div>开户银行：{{ row.bank_name }}</div>
            <div>银行卡号：{{ row.bank_no }}</div>
            <a @click="showBindModal(row)">更换银行卡</a>
          </div>
        </template>
      </Table>
      <div class="block_20"></div>
      <KPage
        :current="+queryFormData.page"
        :page-size="+queryFormData.pageSize"
        :total="total"
        style="text-align: center"
        @on-change="onPageChange"
      />
    </div>
    <staff-wallet-modal
      v-model="staffVisible"
      :row="currentRow"
      :currentModalType="currentModalType"
    ></staff-wallet-modal>
    <bind-bank-no-modal :visible.sync="bindVisible" :row="currentRow" @refresh="getsList"></bind-bank-no-modal>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'libs/util';
import staffWalletModal from './components/staffWalletModal.vue';
import bindBankNoModal from './components/bindBankNoModal.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  mobile: '',
  wallet_code: '',
  bind_status: '',
};
export default {
  name: 'list',
  mixins: [search],
  components: { staffWalletModal, bindBankNoModal },
  data() {
    return {
      apiName: 'getRstMemberWalletList',
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      queryFormData: {
        ...init_query_form_data,
      },
      tableCols: [
        { title: '员工信息', slot: 'staff_info', align: 'center', width: 280 },
        { title: '记账钱包', key: 'wallet_code', align: 'center' },
        { title: '待结算(元)', slot: 'divide_wait_settle_balance', align: 'center' },
        { title: '可结算(元)', slot: 'divide_prepare_settle_balance', align: 'center' },
        { title: '结算中(元)', slot: 'divide_during_settle_balance', align: 'center' },
        { title: '已结算(元)', slot: 'divide_finish_amount', align: 'center' },
        { title: '累计收益(元)', slot: 'divide_all_amount', align: 'center' },
        { title: '结算信息', slot: 'settle_info', align: 'center', width: 220 },
      ],
      statusDesc: [],
      staffVisible: false,
      bindOptionList: [
        { id: '1', desc: '绑定' },
        { id: '2', desc: '未绑定' },
      ],
      currentRow: {},
      currentModalType: '',
      bindVisible: false,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    /**
     * 查看金额详情
     * */
    seeMoneyModal(row, modalType = '') {
      this.currentRow = row;
      this.currentModalType = modalType;
      this.staffVisible = true;
    },
    showBindModal(row) {
      this.bindVisible = true;
      this.currentRow = row;
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.avatar {
  width: 29px;
  height: 29px;
  min-width: 29px;
  min-height: 29px;
  border-radius: 50%;
  margin-right: 10px;
}

::v-deep .ivu-form-item {
  margin-bottom: 16px;

  label {
    vertical-align: middle;
  }
}

.table-wrapper {
  .table-fun {
    //text-align: right;
    padding: 10px 0;
  }
}
</style>
