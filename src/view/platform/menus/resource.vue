<template>
  <div class="resource">
    <div class="ks-menus_container">
      <div v-for="menu in menus" :key="menu.id" class="ks-li dir">

        <div class="ks-li-parent">
          <div class="ks-li-left" @click="statemanager['menu-show-'+menu.id] = !statemanager['menu-show-'+menu.id]">
            <span>
              <i v-if="!statemanager['menu-show-'+menu.id] && menu.sub_menus.length > 0" class="fa fa-plus-square"></i>
              <i v-else class="fa fa-minus-square"></i>
              {{ menu.name }}
            </span>
          </div>
          <div class="ks-li-resource"><a href="javascript:;" @click="onShowResourceListModal(menu.id)">功能维护</a></div>
          <div class="ks-li-right">
            <a v-for="item in menu.resources" :key="item.id" href="javascript:;">{{ item.name }}</a>
          </div>
        </div>

        <div class="ks-li-sub" :class="{hide: !statemanager['menu-show-'+menu.id]}" v-if="menu.sub_menus.length > 0">
          <div v-for="sub_menu in menu.sub_menus" :key="sub_menu.id" class="ks-li">
            <div class="ks-li-parent">
              <div class="ks-li-left"><span><i class="fa fa-circle"></i> {{ sub_menu.name }}</span></div>
              <div class="ks-li-resource"><a href="javascript:;" @click="onShowResourceListModal(sub_menu.id)">功能维护</a></div>
              <div class="ks-li-right">
                <a
                  v-for="item in sub_menu.resources"
                  :key="item.id"
                  href="javascript:;"
                  @click="onShowAccessListModal(item)"
                >
                  {{ item.name }}
                  <span v-if="item.element_list?.length">
                    (<span v-for="(ele, index) in item.element_list" :key="index">
                      {{ ele.name }}
                      <Dvd v-if="index !== item.element_list?.length - 1"></Dvd> </span
                  >)
                  </span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>

    </div>

    <!-- Modal -->
    <Modal
      v-model="resourcelistModal"
      :mask-closable="false"
      :width="800"
      :styles="{top: '45px'}"
      footer-hide>
      <template slot="header">
        <Button type="primary" @click="onShowAddModal(currentEditMenuid, 'add')">新增</Button>
      </template>
      <div style="max-height:350px;overflow: auto">
        <Table :columns="resourcelistTableColumns"
               :data="resourcelistTableData"
        >
          <template slot-scope="{ row }" slot="operate">
            <a href="javascript:;" class="space4" @click="onShowAddModal(row.id, 'edit')">编辑</a>
            <Poptip confirm transfer title="确认删除?" @on-ok="onDelResource(row.id)">
              <a href="javascript:;" class="space4">删除</a>
            </Poptip>
          </template>
        </Table>
      </div>
    </Modal>

    <Modal
      v-model="addModal"
      :mask-closable="false"
      :width="480"
      :loading="addModalLoading"
      @on-ok="onAddResource">
      <p></p>
      <div style="width: 80%">
        <div class="widget-form-group">
          <div class="widget-form-label">功能名称:</div>
          <div class="widget-form-content">
            <Input type="text" v-model="formData.name"/>
          </div>
        </div>
        <div class="widget-form-group">
          <div class="widget-form-label">功能类型:</div>
          <div class="widget-form-content">
            <RadioGroup v-model="formData.type">
              <Radio label="page"><span>页面</span></Radio>
              <Radio label="api"><span>接口</span></Radio>
            </RadioGroup>
          </div>
        </div>
        <div class="widget-form-group">
          <div class="widget-form-label">URL:</div>
          <div class="widget-form-content">
            <Input type="text" v-model="formData.url"/>
          </div>
        </div>
      </div>
    </Modal>

    <!-- 权限弹窗 -->
    <access-modal v-model="accessVisible" :accessId.sync="accessId"></access-modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util' // Some commonly used tools
import io from "@/libs/io" // Http request
import * as runtime from '@/libs/runtime' // Runtime information
/* eslint-disable */
import '../menus.less'
import accessModal from './components/accessModal';

// 添加/编辑的表单数据
let init_formData = {
  menuid: 0, // 添加
  id: 0, // 编辑
  name: '',
  type: '',
  url: '',
}

export default {
  name: "resource",
  components: {
    accessModal
  },
  data() {
    return {
      menus: [],
      resourcelistModal: false,
      addModal: false,
      addModalLoading: true,
      isEditModal: false,
      statemanager: {},

      currentEditMenuid: 0,
      resourcelistTableColumns: [
        {title: '功能名称', key: 'name'},
        {title: '功能类型', key: 'type'},
        {title: 'url', key: 'url'},
        {title: '操作', slot: 'operate'},
      ],
      resourcelistTableData: [],

      formData: {...init_formData},

      accessVisible: false, // 权限弹窗
      accessId: ''
    }
  },
  created() {
    this.getsMenus()
  },

  methods: {
    /**
     * 设置元素相关功能
     * */
    onShowAccessListModal(item) {
      console.log({ item });
      this.accessId = item.id;
      this.accessVisible = true;
    },
    getsMenus: function (callback = function(){}) {
      io({
        url: 'clinic/permission.resource.list',
        mehtod: 'get',
      }).then(menus => {
        this.menus = this.handerMenus(menus)
        callback()
      }).catch(error => {
        {}
      })
    },

    handerMenus: function (menus) {
      let new_menus = []
      menus.forEach(item => {
        if (item.parent_id == 0) {
          item['sub_menus'] = []
          menus.forEach(item2 => {
            if (item2.parent_id == item.id) {
              item.sub_menus.push(item2);
            }
          })
          new_menus.push(item)
          // 对象新增属性，需要通过this.$set方法来获得响应式
          if (S.isUndefined(this.statemanager['menu-show-'+item.id])) {
            this.$set( this.statemanager, 'menu-show-'+item.id, true)
          }
        }
      })
      return new_menus
    },

    onShowResourceListModal: function (menuid) {
      io({
        url: 'clinic/permission.resource.resourcelist',
        mehtod: 'get',
        data: {menuid}
      }).then(data => {
        this.resourcelistTableData = data
        this.currentEditMenuid = menuid
      }).catch(error => {
        {}
      }).finally(() => {
        this.resourcelistModal = true
      })
    },

    onShowAddModal: function (id, action) {
      this.clearFormData()

      if (action == 'edit') {
        this.isEditModal = true
      } else {
        this.isEditModal = false
      }

      io.get('clinic/permission.resource.get', {
        data: {id},
      }).then(data => {
        if (action == 'add') {
          this.formData.menuid = id
        } else {
          this.formData.id = data.id
          this.formData.name = data.name
          this.formData.type = data.type
          this.formData.url = data.url
        }
        this.addModal = true
      }, reject => {
        this.$Message.error(reject.errmsg)
      })

    },

    onAddResource: function () {
      let formData = {...this.formData}

      if (!formData.name.trim()) {
        this.$Message.error('请填写菜单名称')
        this.addModalLoading = false
        return
      }

      if (!formData.type) {
        this.$Message.error('请选择菜单类型')
        this.addModalLoading = false
        return
      }

      if (!formData.url) {
        this.$Message.error('请填写url')
        this.addModalLoading = false
        return
      }

      let url = 'clinic/permission.resource.add'
      if (this.isEditModal) {
        url = 'clinic/permission.resource.edit'
      }

      io.post(url, formData).then(data => {
        this.addModal = false
        if (!this.isEditModal) {
          this.resourcelistTableData.push(data)
        } else {
          for (let k in Object.keys(this.resourcelistTableData)) {
            if (this.resourcelistTableData[k].id == data.id) {
              this.$set(this.resourcelistTableData, k, data)
            }
          }
        }
        this.getsMenus()
      }, reject => {
        this.addModalLoading = false
        this.$Message.error(reject.errmsg)
      })
    },

    clearFormData: function () {
      this.formData = {...init_formData}
    },

    onDelResource: function (id) {
      io.post('clinic/permission.resource.del', {id}).then(() => {
        let self = this
        this.resourcelistTableData.forEach((item, k) => {
          if (item.id == id) {
            this.resourcelistTableData.splice(k, 1)
          }
        })
        this.getsMenus(function () {
          self.$Message.success('删除成功')
        })
      }).catch(error => {
        {}
      })
    }
  },

  watch: {
    addModalLoading: function () {
      this.$nextTick(() => this.addModalLoading = true)
    }
  }

}
</script>

<style scoped>

</style>
