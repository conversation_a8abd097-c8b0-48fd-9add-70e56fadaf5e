<template>
  <div>
    <div class="ks-menus_container">
      <div v-for="menu in menus" :key="menu.id" class="ks-li dir">
        <div class="ks-li-parent">
          <div class="ks-li-left" @click="statemanager['menu-show-' + menu.id] = !statemanager['menu-show-' + menu.id]">
            <span>
              <i
                v-if="!statemanager['menu-show-' + menu.id] && menu.sub_menus.length > 0"
                class="fa fa-plus-square"
              ></i>
              <i v-else class="fa fa-minus-square"></i>
              {{ menu.name }}
            </span>
          </div>
          <div class="ks-li-right">
            <CheckboxGroup v-model="checkedmanger[menu.id]" style="display: inline-block">
              <Checkbox v-for="item in menu.resources" :key="item.id" :label="item.id">{{ item.name }}</Checkbox>
            </CheckboxGroup>
            <div style="display: inline-block">
              [<a @click="onCheckAll(menu.id, 'menu')">全选</a><a @click="onUnCheckAll(menu.id, 'menu')">取消</a>]
            </div>
          </div>
        </div>

        <div
          class="ks-li-sub"
          :class="{ hide: !statemanager['menu-show-' + menu.id] }"
          v-if="menu.sub_menus.length > 0"
        >
          <div v-for="sub_menu in menu.sub_menus" :key="sub_menu.id" class="ks-li">
            <div class="ks-li-parent">
              <div class="ks-li-left">
                <span><i class="fa fa-circle"></i> {{ sub_menu.name }}</span>
              </div>
              <div class="ks-li-right">
                <CheckboxGroup v-model="checkedmanger[sub_menu.id]" style="display: inline-block">
                  <div v-for="resource in sub_menu.resources" :key="resource.id" style="display: inline-block">
                    <Checkbox :key="resource.id" :label="resource.id">
                      {{ resource.name }}
                    </Checkbox>

                    <div v-if="resource.element_list.length" class="ele-item" @click.stop>
                      <!--                    <el-checkbox-group @click.native.stop v-model="checkedmanger['resource_'+resource.id]" style="display: block;">-->
                      <!--                      <el-checkbox @click.native.stop v-for="element in resource.element_list" :key="element.id" :label="element.id">{{ element.name }}-->
                      <!--                      </el-checkbox>-->
                      <!--                    </el-checkbox-group>-->
                      <el-checkbox-group
                        @click.native.stop
                        v-model="checkedmanger['resource_' + resource.id]"
                        style="display: block"
                      >
                        <el-checkbox
                          @click.native.stop
                          v-for="element in resource.element_list"
                          :key="element.id"
                          :label="element.id"
                        >{{ element.name }}
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                  </div>
                </CheckboxGroup>
                <div style="display: inline-block">
                  [<a @click="onCheckAll(sub_menu.id, 'sub_menu')">全选</a
                ><a @click="onUnCheckAll(sub_menu.id, 'sub_menu')">取消</a>]
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="block_10"></div>
      <div style="text-align: center" class="fixed-bottom-wrapper">
        <Button type="primary" :loading="statemanager.saveLoading" @click="onSave">提交</Button>
      </div>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
/* eslint-disable */
import '../menus.less';

export default {
  name: 'permission',
  data() {
    return {
      roleid: this.$route.query.roleid,
      menus: [],
      statemanager: {
        saveLoading: false
      },
      allMenuIds: {},
      allResourceIds: {},
      checkedmanger: {}
    };
  },
  created() {
    this.getsMenus();
  },
  methods: {
    getsMenus: function (callback = function () {}) {
      let roleid = this.roleid;
      io.get('clinic/permission.role.resource', { data: { roleid } })
        .then(data => {
          let menus = this.handerMenus(data.menus);
          this.handerResources(menus);
          this.handerCheckedManger(menus);
          this.menus = menus;
          // callback()
        })
        .catch(error => {
          {};
        });
    },

    handerMenus: function (menus) {
      let new_menus = [];
      menus.forEach(item => {
        if (item.parent_id == 0) {
          item['sub_menus'] = [];
          menus.forEach(item2 => {
            if (item2.parent_id == item.id) {
              item.sub_menus.push(item2);
            }
          });
          new_menus.push(item);
          // 对象新增属性，需要通过this.$set方法来获得响应式
          if (S.isUndefined(this.statemanager['menu-show-' + item.id])) {
            this.$set(this.statemanager, 'menu-show-' + item.id, true);
          }
        }
      });

      return new_menus;
    },

    handerResources: function (menus) {
      menus.forEach(menu => {
        this.allResourceIds[menu.id] = [];
        this.allMenuIds[menu.id] = [];
        menu.resources.forEach(item => {
          this.allResourceIds[menu.id].push(item.id);
        });
        menu.sub_menus.forEach(sub_menu => {
          this.allResourceIds[sub_menu.id] = [];
          sub_menu.resources.forEach(resource => {
            this.allResourceIds[sub_menu.id].push(resource.id);
            this.allResourceIds['resource_' + resource.id] = [];
            if (resource.element_list.length) {
              resource.element_list.forEach(element => {
                this.allResourceIds['resource_' + resource.id].push(element.id);
              });
            }
          });
          this.allMenuIds[menu.id].push(sub_menu.id);
        });
      });
    },

    handerCheckedManger: function (menus) {
      menus.forEach(menu => {
        let checkedRids = [];
        menu.resources.forEach(item => {
          if (item.isGet > 0) {
            checkedRids.push(item.id);
          }
        });
        this.$set(this.checkedmanger, menu.id, checkedRids);

        menu.sub_menus.forEach(sub_menu => {
          let checkedRids = [];
          sub_menu.resources.forEach(resource => {
            if (resource.isGet > 0) {
              checkedRids.push(resource.id);
            }
            if (resource.element_list.length) {
              let elementCheckedRids = [];
              resource.element_list.forEach(element => {
                if (element.isGet > 0) {
                  elementCheckedRids.push(element.id);
                }
              });
              this.$set(this.checkedmanger, 'resource_' + resource.id, elementCheckedRids);
            }
          });
          this.$set(this.checkedmanger, sub_menu.id, checkedRids);
        });
      });
    },

    onCheckAll: function (id, type) {
      if (type == 'sub_menu') {
        this.checkedmanger[id] = this.allResourceIds[id];
        this.allResourceIds[id].forEach(resource => {
          this.checkedmanger['resource_' + resource] = this.allResourceIds['resource_' + resource];
        });
      }
      if (type == 'menu') {
        this.checkedmanger[id] = this.allResourceIds[id];

        this.allMenuIds[id].forEach(sub_menu_id => {
          this.checkedmanger[sub_menu_id] = this.allResourceIds[sub_menu_id];
          this.allResourceIds[sub_menu_id].forEach(resource => {
            this.checkedmanger['resource_' + resource] = this.allResourceIds['resource_' + resource];
          });
        });
      }
    },

    onUnCheckAll: function (id, type) {
      if (type == 'sub_menu') {
        this.checkedmanger[id] = [];
        this.allResourceIds[id].forEach(resource => {
          this.checkedmanger['resource_' + resource] = [];
        });
      }
      if (type == 'menu') {
        this.checkedmanger[id] = [];
        this.allMenuIds[id].forEach(sub_menu_id => {
          this.checkedmanger[sub_menu_id] = [];
          this.allResourceIds[sub_menu_id].forEach(resource => {
            this.checkedmanger['resource_' + resource] = [];
          });
        });
      }
    },

    onSave: function () {
      this.statemanager.saveLoading = true;

      let resids = [];
      for (let k in this.checkedmanger) {
        resids = resids.concat(this.checkedmanger[k]);
      }

      io.post('clinic/permission.role.setresource', {
        roleid: this.roleid,
        resids: JSON.stringify(resids)
      })
        .then(
          () => {
            this.$Message.success('保存成功');
          },
          reject => {
            this.$Message.error(reject.errmsg);
          }
        )
        .finally(() => {
          this.statemanager.saveLoading = false;
        });
    }
  },
  watch: {}
};
</script>

<style scoped lang="less">
::v-deep .el-checkbox {
  margin-right: 6px;

  .el-checkbox__label {
    color: #333333;
  }
}

::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #1157e5;
  border-color: #1157e5;
}
.ele-item {
  display: inline-block;
  background: #eeeeee;
  border: 1px solid #e9e9e9;
  border-radius: 2px;
  margin: 0px 6px 6px 6px;
  padding: 6px 6px 2px;
}
</style>
