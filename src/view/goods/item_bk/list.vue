<template>
  <div>
    <div class="flex list-fn-mb-distance">
      <Button type="primary" :to="{ path: '/goods/item/edit' }">发布商品</Button>
    </div>
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <FormItem label="">
        <Input type="text" v-model="queryFormData.name" placeholder="商品名称" />
      </FormItem>
      <FormItem label="">
        <Select v-model="queryFormData.goods_type" placeholder="商品类型" clearable>
          <Option value="">全部</Option>
          <Option v-for="(type, typeKey) in typeDesc" :value="typeKey" :key="typeKey">{{ type.desc }}</Option>
        </Select>
      </FormItem>
      <FormItem label="">
        <Select v-model="queryFormData.status" placeholder="商品状态" clearable>
          <Option value="">全部</Option>
          <Option v-for="(status, statusKey) in statusDesc" :value="statusKey" :key="statusKey">{{
            status.desc
          }}</Option>
        </Select>
      </FormItem>
      <FormItem label="">
        <Select v-model="queryFormData.source_platform" placeholder="商品来源">
          <Option value="">全部</Option>
          <Option v-for="(status, statusKey) in sourcePlatformDesc" :value="statusKey" :key="statusKey">{{
            status.desc
          }}</Option>
        </Select>
      </FormItem>
      <FormItem>
        <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
        <span class="list-reset-btn" @click="onResetSearch">
          <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
          <span>清除条件</span>
        </span>
      </FormItem>
    </Form>
    <div class="table-action flex flex-item-between">
      <div class="panel-nav" v-if="Object.keys(statusDesc).length">
        <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')"> 全部 </a>
        <a class="nav" :class="{ active: $route.query.status == '200' }" @click.prevent.capture="onStatusChange('200')">
          上架中
        </a>
        <a class="nav" :class="{ active: $route.query.status == '209' }" @click.prevent.capture="onStatusChange('209')">
          已售罄
        </a>
        <a class="nav" :class="{ active: $route.query.status == '800' }" @click.prevent.capture="onStatusChange('800')">
          下架中
        </a>
      </div>
    </div>
    <div class="goods-table-wrapper">
      <Table
        class="goods-table"
        :no-data-text="''"
        :loading="tableLoading"
        :columns="tableCols"
        :data="list"
        :height="$store.state.app.clientHeight - 312"
      >
        <template slot-scope="{ row }" slot="info">
          <div class="media-left media-middle">
            <img :src="row.main_img | imageStyle" style="width: 35px; margin-right: 5px" class="img-rounded" />
          </div>
          <div class="media-body media-middle">
            <div class="_3U0W">
              <Tooltip
                max-width="200"
                v-if="row.is_valid_icon === '0'"
                content="商品已从总部下架，无法采用总部一件代发的销售方式，请修改销售方式或下架商品"
              >
                <svg-icon iconClass="jurassic" class="helpIcon"></svg-icon>
              </Tooltip>
              <KLink :to="{ path: '/goods/item/edit', query: { id: row.id } }">{{ row.name }}</KLink>
            </div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="price_section">
          <p v-if="!row.price_section.max">￥{{ row.price_section.min }}</p>
          <p v-else>￥{{ row.price_section.min }} - ￥{{ row.price_section.max }}</p>
        </template>
        <!-- 储值价 -->
        <template slot-scope="{ row }" slot="stored_price_section">
          <p v-if="!row.stored_price || row.is_recharge_buy === 'no'">-</p>
          <template v-else>
            <p v-if="!row.stored_price_section.max">￥{{ row.stored_price_section.min }}</p>
            <p v-else>￥{{ row.stored_price_section.min }} - ￥{{ row.stored_price_section.max }}</p>
          </template>
        </template>
        <template slot-scope="{ row }" slot="stock">
          {{ row.stock }}
        </template>
        <template slot-scope="{ row }" slot="sales">
          {{ row.sales }}
        </template>
        <template slot-scope="{ row }" slot="create_time">
          {{ row.create_time | data_format }}
        </template>
        <template slot-scope="{ row }" slot="status">
          {{ statusDesc[row.status].desc }}
        </template>
        <template slot-scope="{ row }" slot="operate">
          <KLink :to="{ path: '/goods/item/edit', query: { id: row.id } }">编辑</KLink>
          <Poptip
            confirm
            transfer
            title="确定下架？"
            v-if="isCanUndercarriage(row)"
            @on-ok="onChangeStatus('DOWN', row.id)"
          >
            <Divider type="vertical" />
            <a>下架</a>
          </Poptip>
          <Poptip
            confirm
            transfer
            title="确定上架？"
            v-if="statusDesc[row.status].kw == 'STATUS_SHELVES_DOWN'"
            @on-ok="onChangeStatus('UP', row.id)"
          >
            <Divider type="vertical" />
            <a>上架</a>
          </Poptip>
          <Poptip
            confirm
            transfer
            title="确定删除？"
            v-if="
              statusDesc[row.status].kw == 'STATUS_SALE_OUT' &&
              row.source_platform != 'PLAT' &&
              row.source_platform != 'COM'
            "
            @on-ok="onChangeStatus('DEL', row.id)"
          >
            <Divider type="vertical" />
            <a>删除</a>
          </Poptip>
          <Poptip confirm transfer title="确定复制？" @on-ok="onCopy(row.id)">
            <Divider type="vertical" />
            <a>复制</a>
          </Poptip>
        </template>
      </Table>
      <div class="no-text-box" v-show="!tableLoading && list.length === 0">
        <img src="@/assets/image/base/empty.png" alt="暂无数据" />
        <p class="nodata-text">暂无商品，快去商品库选择商品添加进来吧~</p>
        <Button @click="$router.push('/goods/warehouse/list')">去商品库</Button>
      </div>
    </div>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */
import renderHeader from '@/mixins/renderHeader';
import search from '@/mixins/search';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  id: '',
  name: '',
  goods_type: '',
  r: '',
  source_platform: '',
};

export default {
  name: 'list',
  mixins: [renderHeader, search],

  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getCureRecordList',
      tableCols: [
        { title: 'ID', key: 'id', minWidth: 50 },
        { title: '商品', slot: 'info', minWidth: 100 },
        { title: '商品类型', key: 'goods_type_text', width: 80 },
        { title: '商品来源', key: 'source_platform_text', width: 80 },
        { title: '价格', slot: 'price_section', minWidth: 80 },
        {
          title: '储值价',
          slot: 'stored_price_section',
          align: 'center',
          minWidth: 80,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '商品储值价指使用储值余额购买时可享受的优惠价，不设置即无优惠价'),
        },
        { title: '库存', slot: 'stock', minWidth: 60 },
        { title: '销量', slot: 'sales', minWidth: 80 },
        { title: '创建时间', slot: 'create_time', minWidth: 120 },
        { title: '商品状态', slot: 'status', minWidth: 50 },
        { title: '操作', slot: 'operate', width: 160 },
      ],
      tableLoading: false,
      list: [],
      total: 0,
      statusDesc: {},
      typeDesc: {},
      sourcePlatformDesc: {},
    };
  },

  computed: {
    // 是否可下架
    isCanUndercarriage() {
      return row => {
        if (
          this.statusDesc[row.status].kw != 'STATUS_SHELVES_DOWN' &&
          row.source_platform != 'PLAT' &&
          row.source_platform != 'COM'
        ) {
          return true;
        } else {
          return false;
        }
      };
    },
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  methods: {
    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    onChangeStatus: function (action, id) {
      const actionDesc = action => {
        return { UP: '上架', DOWN: '下架', DEL: '删除' }[action];
      };

      io.post('clinic/goods.index.status', { id: id, act: action })
        .then(() => {
          this.$Message.success(actionDesc(action) + '成功');
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
        });
    },

    onCopy: function (id) {
      io.post('clinic/goods.index.copy', { id: id })
        .then(() => {
          this.$Message.success('复制成功');
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
        });
    },

    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.list = data.goods_items;
          this.total = data.total;
          this.statusDesc = data.statusDesc;
          this.typeDesc = data.typeDesc;
          this.sourcePlatformDesc = data.sourcePlatformDesc;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },

    addGoods() {
      alert('12321');
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.goods-table-wrapper {
  position: relative;
  .no-text-box {
    position: absolute;
    z-index: 10;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .nodata-text {
      margin: 10px 0;
    }
  }
}
p {
  margin: 0;
}
</style>
