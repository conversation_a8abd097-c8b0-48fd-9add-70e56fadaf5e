<template>
  <div class="category-sidebar">
    <div class="category-filter">
      <a class="mr-8" v-if="isSearching" @click="cancelSearch">返回</a>
      <div style="position: fixed; top: -200px">
        <input type="text" name="username" />
      </div>
      <Input v-model="categoryName" class="flex-1" placeholder="搜索分组名称" @keyup.enter.native="searchCategory">
        <Icon type="ios-search" @click="searchCategory" slot="suffix" />
      </Input>
    </div>
    <div class="category-tree-wrapper">
      <category-tree
        :current-category-id="currentCategoryId"
        :category-tree="renderCategoryTree"
        :category-select="categorySelect"
        :category-edit="categoryEdit"
        :category-delete="handleDeleteCategory"
        :deleting-id="delFormData.id"
        is-manager
      />
    </div>
    <div class="category-action fixed">
      <Button icon="ios-add" @click="addCategory">添加分组</Button>
      <Button class="ml-8" @click="categoryDrawerVisible = true">管理分组</Button>
    </div>
    <edit-category
      v-model="editCategoryVisible"
      :edit-category-info="editCategoryInfo"
      :asset-type="mediaTypeMap[categoryType]"
      :default-id="defaultEditId"
      @success="handleSuccess"
    />
    <category-drawer v-model="categoryDrawerVisible" :asset-type="mediaTypeMap[categoryType]" />
    <Poptip
      ref="delPop"
      :width="350"
      placement="top-start"
      :reference="popReference"
      style="height: 0; width: 0; display: none"
    >
      <div slot="content" class="del-pop">
        <div class="pop-title">确定删除所选分组吗？</div>
        <RadioGroup v-model="delFormData.type" class="pop-radio-group">
          <div v-for="item in delOptions" :key="item.value">
            <Radio :label="item.value">{{ item.name }}</Radio>
            <div class="desc">
              <span>{{ item.desc }}</span>
              <strong style="color: #ed6a0c; font-weight: 500">{{ item.tip }}</strong>
            </div>
          </div>
        </RadioGroup>
        <div class="pop-footer">
          <Button @click="cancelDelete">取消</Button>
          <Button class="ml-12" type="primary" @click="confirmDelete" :loading="delLoading">删除</Button>
        </div>
      </div>
    </Poptip>
  </div>
</template>

<script>
import CategoryTree from '@/components/MaterialCenter/CategoryTree.vue';
import EditCategory from './EditCategory.vue';
import { getUid } from '@/libs/runtime';
import CategoryDrawer from './CategoryDrawer.vue';
import { cloneDeep } from 'lodash';

const initCategoryInfo = {
  name: '',
  id: '',
  level: '',
  parent_id: '',
};
export default {
  name: 'CategoryManager',
  components: { EditCategory, CategoryTree, CategoryDrawer },
  provide() {
    return {
      // categorySelect: this.categorySelect,
      // categoryEdit: this.categoryEdit,
    };
  },
  props: {
    propName: {
      type: Number,
      default: 0,
    },
    categoryType: {
      type: String,
      default: 'image',
    },
    categoryTypeName: {
      type: String,
      default: 'image',
    },
  },
  data() {
    return {
      categoryName: '',
      categoryList: [],
      categoryListTree: [],
      isSearching: false,
      currentCategoryId: '',
      currentCategoryName: '',
      editCategoryVisible: false,
      renderCategoryTree: [],
      categoryDrawerVisible: false,
      editCategoryInfo: {
        ...initCategoryInfo,
      },
      mediaTypeMap: {
        image: 1,
        video: 2,
      },
      defaultCategoryId: null,
      defaultEditId: null,
      popReference: null,
      delFormData: {
        id: null,
        type: 1,
      },
      delOptions: [
        {
          name: '仅解除分组',
          value: 1,
          desc: '仅解除分组，组内全部将自动归入未分组。',
        },
        {
          name: '删除分组全部内容',
          value: 2,
          desc: '删除图片不会对已使用图片的业务造成影响。',
          // tip: '腾出的素材空间将在次日完全恢复。',
        },
      ],
      delLoading: false,
    };
  },
  created() {
    this.initCategoryData();
  },
  methods: {
    cancelDelete() {
      this.destroyPop();
    },
    confirmDelete() {
      this.delLoading = true;
      this.$api
        .deleteMtCategory({
          id: this.delFormData.id,
          type: this.delFormData.type,
        })
        .then(res => {
          this.$Message.success('删除成功');
          localStorage.removeItem(`attachment-${this.categoryType}-category-id-${getUid()}`);
          this.destroyPop();
          this.initCategoryData();
        })
        .catch(err => {
          this.$Message.error('删除失败');
        })
        .finally(res => {
          this.delLoading = false;
        });
    },
    resetDelForm() {
      setTimeout(() => {
        this.delFormData.id = null;
        this.delFormData.type = 1;
      }, 100);
    },
    destroyPop() {
      let popStone = this.$refs.delPop;
      popStone.visible = false;
      popStone.doDestroy();
      this.resetDelForm();
    },
    handleDeleteCategory(e, category) {
      console.log('%c [ category ]-175', 'font-size:13px; background:#048392; color:#48c7d6;', category);
      this.delFormData.id = category.id;
      const newDom = e.target;
      // this.$nextTick(() => {
      let popStone = this.$refs.delPop;
      popStone.visible = false;
      popStone.doDestroy();
      this.popReference = newDom;
      popStone.visible = true;
      // });
    },
    addCategory() {
      this.editCategoryVisible = true;
      this.editCategoryInfo = { ...initCategoryInfo };
    },
    categorySelect(category) {
      this.currentCategoryName = category.name;
      this.currentCategoryId = category.id;
      this.$emit('getCategoryInfo', category);
      const storeKey = `attachment-${this.categoryType}-category-id-${getUid()}`;
      localStorage.setItem(storeKey, category.id);
    },
    categoryEdit(category) {
      this.editCategoryVisible = true;
      this.editCategoryInfo = category;
    },
    searchCategory() {
      if (!this.categoryName) {
        this.isSearching = false;
        this.renderCategoryTree = this.buildTreeAndSearch(this.categoryList);
        return;
      }
      this.isSearching = true;
      this.renderCategoryTree = this.buildTreeAndSearch(this.categoryList, this.categoryName);
      if (this.renderCategoryTree.length) {
        this.categorySelect(this.renderCategoryTree[0]);
      }
    },
    cancelSearch() {
      this.categoryName = '';
      this.isSearching = false;
      this.searchCategory();
    },
    initCategoryStatus(data, categoryId) {
      data.forEach(item => {
        if (item.level === 0 && item.type === 2 && item.id) {
          item.isOpen = true;
        }
        if (item.id === categoryId) {
          this.currentCategoryName = item.name;
          item.isOpen = true;
          if (item.parent_id) {
            this.initCategoryStatus(data, item.parent_id);
          }
        }
      });
    },
    buildTreeAndSearch(data, rootName = null) {
      const map = new Map();
      let rootNode = null; // 用于存储搜索到的根节点
      // 初始化 Map，并附加 children 属性
      data.forEach(item => {
        if (item.type === 2 && item.level === 0) {
          this.defaultEditId = item.id;
        }
        item.isOpen = item.isOpen || false;
        map.set(item.id, { ...item, children: [] });
      });
      // 构建树结构
      data.forEach(item => {
        if (map.has(item.parent_id)) {
          const parent = map.get(item.parent_id);
          parent.children.push(map.get(item.id));
        }
      });
      // 如果指定了 rootName，搜索该节点
      if (rootName) {
        rootNode = Array.from(map.values()).filter(node => node.name.includes(rootName));
      }

      // 如果未指定 rootName 或未找到目标节点，返回完整的树
      if (!rootName || !rootNode) {
        return Array.from(map.values()).filter(node => !map.has(node.parent_id));
      }
      // 返回以指定节点为根的子树
      console.log('%c=>(CategoryManager.vue:144) rootNode', 'font-size: 18px;color: #FF7043;', rootNode);
      return rootNode;
    },
    initCategoryData() {
      console.log(this.categoryType);
      this.$api
        .getMtCategoryAllList({
          asset_type: this.mediaTypeMap[this.categoryType],
        })
        .then(res => {
          this.categoryList = res.list;
          const list = this.categoryList;
          this.initDefaultId();
          const originTree = this.buildTreeAndSearch(list);
          this.categoryListTree = cloneDeep(originTree);
          this.renderCategoryTree = originTree;
        });
    },
    initDefaultId() {
      const categoryId = localStorage.getItem(`attachment-${this.categoryType}-category-id-${getUid()}`);
      const isExist = this.categoryList.some(item => item.id === Number(categoryId));
      if (!isExist) {
        localStorage.removeItem(`attachment-${this.categoryType}-category-id-${getUid()}`);
      }
      this.defaultCategoryId = this.categoryList.find(item => item.type === 1)?.id;
      this.currentCategoryId = isExist ? Number(categoryId) : this.defaultCategoryId;
      const category = this.categoryList.find(item => item.id === this.currentCategoryId);
      this.currentCategoryName = category?.name;
      this.$emit('getCategoryInfo', category); // 通知父组件当前分类信息
      this.initCategoryStatus(this.categoryList, this.currentCategoryId);
    },
    handleSuccess() {
      this.initCategoryData();
    },
  },
};
</script>

<style lang="less" scoped>
.category-sidebar {
  border: 1px solid #dcdee0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: none;
  width: 240px;

  .category-filter {
    display: flex;
    align-items: center;
    padding: 8px;
    box-shadow: 0 -1px 4px 0 rgba(0, 0, 0, 0.1);
    font-size: 14px;
  }

  .category-tree-wrapper {
    flex: auto;
    overflow-y: auto;
    padding: 0 8px 2px 12px;
    box-sizing: border-box;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .category-action {
    flex: none;
    box-sizing: border-box;
    text-align: center;
    padding: 12px 0;
    height: 56px;
    background-color: #fff;

    &.fixed {
      box-shadow: 0 -1px 4px 0 rgba(0, 0, 0, 0.1);
    }

    button {
      width: 108px;
    }
  }
}

.del-pop {
  padding: 13px 12px;

  .pop-title {
    margin-bottom: 24px;
    font-size: 14px;
    line-height: 20px;
  }

  :deep(.pop-radio-group) {
    display: flex;
    flex-direction: column;

    .ivu-radio-group-item {
      font-size: 14px;
      line-height: 20px;
      margin-bottom: 0px;
    }

    .desc {
      white-space: pre-wrap;
      padding-left: 17px;
      font-size: 12px;
      margin-bottom: 24px;
      margin-top: 4px;
    }
  }

  .pop-footer {
    text-align: right;
  }
}
</style>
<style lang="less">
.category-cascader {
  .el-scrollbar {
    max-height: 300px;
  }
}
</style>
