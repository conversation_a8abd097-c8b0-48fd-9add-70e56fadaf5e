<template>
  <div class="mt-20">
    <div class="block-header"><span>价格库存</span></div>
    <k-goods-specs
      v-model="specs_attrs"
      :storedList="storedList"
      :is_recharge_buy="formData.is_recharge_buy"
      :is_in_tc="echoData.is_in_tc"
      :source_platform="echoData.source_platform"
      :disabled="echoData.goods_type == 40"
      is_rst="isRstClinic"
      :goods_type="echoData.goods_type"
    />
    <div style="margin-top: 20px" v-if="echoData.goods_type != 40">
      <div class="block-header"><span>关联赠送服务卡券</span></div>
      <KWidget label="是否关联：" required class="flex-item-align">
        <Radio-group v-model="formData.relation_card">
          <Radio
            :disabled="!isCanEdit"
            :label="item.id"
            v-for="(item, index) in relationList"
            :key="'stored' + index"
            >{{ item.desc }}</Radio
          >
        </Radio-group>
        <Button
          type="default"
          :disabled="!isCanEdit"
          class="ml10"
          v-if="formData.relation_card == 1"
          @click="serviceModalVisible = true"
          >添加关联服务</Button
        >
      </KWidget>

      <KWidget label="发放方式：" v-if="formData.relation_card == 1" required class="flex-item-align">
        <Radio-group v-model="formData.grant_type">
          <Radio
            :disabled="!isCanEdit || echoData.goods_type == 40"
            :label="item.id"
            v-for="(item, index) in grantList"
            :key="'stored' + index"
            >{{ item.desc }}</Radio
          >
        </Radio-group>
      </KWidget>

      <KWidget label="" v-if="formData.relation_card == 1">
        <table class="table" style="width: 500px">
          <thead>
            <tr>
              <th>服务</th>
              <th>来源</th>
              <th>类型</th>
              <th>发放数量</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in formData.services" :key="key">
              <td>{{ item.name }}</td>
              <td>{{ item.source_platform_text }}</td>
              <td>{{ item.serv_type_text }}</td>
              <td>
                <InputNumber
                  :disabled="!isCanEdit"
                  v-model="item.times"
                  controls-outside
                  :min="1"
                  :max="50"
                  style="width: 100px"
                  v-if="formData.grant_type == 1"
                />
                <div v-else>-</div>
              </td>
              <td>
                <a :disabled="!isCanEdit" @click="onDelService(key)">删除</a>
              </td>
            </tr>
          </tbody>
        </table>
      </KWidget>
    </div>

    <div class="block-header"><span>其他设置</span></div>

    <KWidget label="上架范围：" required text>
      <CheckboxGroup v-model="formData.sw_scope_tmp">
        <Checkbox label="1" :disabled="echoData.goods_type == 40">零售服务</Checkbox>
        <Checkbox label="2" :disabled="echoData.goods_type == 40">问诊治疗</Checkbox>
      </CheckboxGroup>
    </KWidget>

    <KWidget label="储值购买：" required text v-if="echoData.goods_type != 40">
      <Radio-group v-model="formData.is_recharge_buy">
        <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">
          {{ item.desc }}
        </Radio>
      </Radio-group>
    </KWidget>

    <KWidget v-if="echoData.goods_type != 40" required text>
      <div style="display: inline-block" slot="label">
        销售方式<Tooltip
          max-width="200"
          content="销售方式的选择将影响小程序用户的下单流程，暂不影响后台手动创建订单流程，总部一件代发的商品暂时不支持后台建单销售。"
        >
          <Icon type="md-help-circle" size="16" /> </Tooltip
        >：
      </div>
      <CheckboxGroup v-model="formData.sale_type">
        <Checkbox
          :label="item.id"
          v-for="(item, index) in sellEntTypes"
          :key="item.id"
          :disabled="item.id === 'PMS_SELF' || echoData.goods_type == 40"
          v-if="isShowPmsSelf(item)"
        >
          <Tooltip
            v-if="isShowPmsSelf(item) && item.id === 'PMS_SELF' && is_pms_self !== '1'"
            placement="top"
            max-width="300"
          >
            <span style="color: #aaa">{{ item.desc }}</span>
            <div slot="content">
              <div>当前商品不支持设置 "总部一件代发" 原因如下：</div>
              <div v-for="(item, index) in errorReason" :key="index">
                <div>{{ item }}</div>
              </div>
            </div>
          </Tooltip>
          <span v-else>{{ item.desc }}</span>
        </Checkbox>
      </CheckboxGroup>
    </KWidget>

    <k-goods-services
      v-model="serviceModalVisible"
      @on-selected="onSelectedService"
      :checked-service="formData.services"
      :optionsList="optionsList"
    />
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep';
import KGoodsServices from '@/components/k-goods-services';
import KGoodsSpecs from '../../components/k-goods-specs';
import { isRstClinic } from '@/libs/util';
import S from 'libs/util';

export default {
  name: 'PhysicalGoods',
  props: {
    storedList: {
      type: Array,
      default: () => [],
    },
    sellEntTypes: {
      type: Array,
      default: () => [],
    },
    echoData: {
      type: Object,
      default: () => ({}),
    },
    is_pms_self: {
      type: String,
      default: '',
    },
    errorReason: {
      type: Array,
      default: () => [],
    },
    optionsList: {
      type: Object,
      default() {
        return {
          servTypeDesc: [],
          sourcePlatformDesc: [],
        };
      },
    },
  },
  mixins: [],
  components: {
    KGoodsSpecs,
    KGoodsServices,
  },
  mounted() {},
  watch: {
    echoData: {
      handler(val) {
        if (S.isEmptyObject(val) || (val.goods_type !== '10' && val.goods_type !== '40')) return;
        this.handleEchoData(val);
      },
      immediate: true,
    },
  },
  computed: {
    // 关联服务是否可以编辑
    isCanEdit() {
      return !this.echoData.id || (this.echoData.id && this.echoData.source_platform === 'CLI');
    },
    // 是否显示一件代发选项
    isShowPmsSelf() {
      return item => {
        if (this.echoData.goods_type == 40) {
          return item.id === 'USER_SELF';
        }
        if (item.id === 'PMS_SELF') {
          if (this.$route.query.id && this.echoData.source_platform === 'PLAT') {
            return true;
          } else {
            return false;
          }
        } else {
          return true;
        }
      };
    },

    isRstClinic() {
      return isRstClinic;
    },
  },
  data() {
    return {
      specs_attrs: [[], []],
      formData: {
        sale_type: ['USER_SELF'], // 销售方式
        shelf_scope: '',
        is_recharge_buy: 'yes', // 储值购买
        sw_scope_tmp: ['1'],
        specs_data: [],
        attrs: [],
        relation_card: '0',
        grant_type: '2',
        services: [],
      },

      relationList: [
        { id: '0', desc: '不关联' },
        { id: '1', desc: '关联' },
      ],
      grantList: [
        { id: '1', desc: '购买时发放' },
        { id: '2', desc: '订单交易后按需发放' },
      ],
      serviceModalVisible: false,
    };
  },
  methods: {
    onSelectedService: function (items) {
      this.formData.services = items.map(item => {
        // 编辑时携带次数
        let isExist = this.formData.services.findIndex(service_item => service_item.id === item.id);
        return {
          id: item.id,
          name: item.name,
          serv_type: item.serv_type,
          serv_type_text: item.serv_type_text,
          source_platform_text: item.source_platform_text,
          times: isExist === -1 ? 1 : Number(this.formData.services[isExist].times || 0),
        };
      });
      this.serviceModalVisible = false;
    },
    onDelService(index) {
      this.formData.services.splice(index, 1);
    },
    // 自动设置销售方式是否有一件代发
    autoSetSaleType() {
      if (this.$route.query.id) {
        if (this.is_pms_self === '1') {
          if (
            this.formData.sale_type?.indexOf('PMS_SELF') < 0 ||
            this.formData.sale_type?.indexOf('PMS_SELF') === undefined
          ) {
            this.formData.sale_type.push('PMS_SELF');
          }
        }
      }
    },
    validateForm() {
      let data = cloneDeep(this.formData);
      data.specs_data = this.specs_attrs[0];
      data.attrs = this.getAttrs(this.specs_attrs[1]);
      // 实物商品
      let specs_error_msg = '';
      data.specs_data.forEach(item => {
        if (item.name == '') {
          specs_error_msg = '规格名称不能为空';
          return false;
        }

        item.specs_atoms.forEach(item => {
          if (item[0] == '') {
            specs_error_msg = '规格值不能为空';
            return false;
          }
          if ((item.isAddImg && !item[1]) || item[1] == '') {
            specs_error_msg = '请选添加规格图片';
            return false;
          }
        });
      });
      if (specs_error_msg) {
        this.$Message.error(specs_error_msg);
        return false;
      }

      if (S.isEmptyObject(data.attrs)) {
        this.$Message.error('规格值不能为空');
        return false;
      }

      data.attrs.forEach(item => {
        let price = parseFloat(item.price);
        item.stored_price = item.stored_price == null ? '' : item.stored_price;
        // let stored_price = parseFloat( Number(item.stored_price) )
        if (!/^\d([\d\.]+)?/.test(price)) {
          specs_error_msg = '请输入有效的商品价格';
          return false;
        }

        if (price < 0.01) {
          specs_error_msg = '商品价格不允许小于0.01元';
          return false;
        }
        // 储值价非必填
        // if ( data.is_recharge_buy == 'yes' && stored_price < 0.01 ) {
        //   specs_error_msg = '储值价不允许小于0.01元'
        //   return false
        // }

        if (price > 50000) {
          specs_error_msg = '商品价格不允许大于50000.00元';
          return false;
        }
      });

      if (specs_error_msg) {
        this.$Message.error(specs_error_msg);
        return false;
      }

      if (!this.formData.relation_card) {
        this.$Message.error('请选择是否关联');
        return false;
      }

      if (!this.formData.grant_type) {
        this.$Message.error('请选择发放方式');
        return false;
      }

      if (this.formData.relation_card == '1' && this.formData.services?.length == 0) {
        this.$Message.error('请最少添加一条关联服务');
        return false;
      }

      let is_has_empty = false;
      this.formData.services?.some(item => {
        if (Number(item.times || 0) == 0) {
          this.$Message.error(`【${item.name}】的发放数量不能小于1`);
          is_has_empty = true;
          return true;
        }
      });
      return !is_has_empty;

      if (data.sale_type.length == 0) {
        this.$Message.error('请选择销售方式');
        return false;
      }

      if (!this.formData.sw_scope_tmp.length) {
        this.$Message.error('请选择上架范围');
        return false;
      }

      return true;
    },
    getFormData() {
      let data = {
        ...this.formData,
      };
      data.specs_data = this.specs_attrs[0];
      // formData.attrs = this.specs_attrs[1]
      data.attrs = this.getAttrs(this.specs_attrs[1]);
      data.attrs.forEach(item => {
        if (this.formData.is_recharge_buy === 'no') {
          item.stored_price = '';
        }
      });
      data.shelf_scope = this.formData.sw_scope_tmp.length == 2 ? '9' : this.formData.sw_scope_tmp[0];
      delete data.sw_scope_tmp;
      return data;
    },
    getAttrs(val) {
      let attrs = this.$lodash.cloneDeep(val);
      attrs.forEach((item, index) => {
        let _relate_his_prods = [];
        item.relate_his_prods &&
          item.relate_his_prods.length &&
          item.relate_his_prods.forEach(single_item => {
            console.log('single_item.buy_multiples', single_item.buy_multiples);
            _relate_his_prods.push({
              prod_id: single_item.prod_id,
              sales_unit: single_item._sales_unit,
              buy_multiples: single_item.buy_multiples,
            });
          });
        item.relate_his_prods = _relate_his_prods;
        // item.stock = item.stock;
        item.is_relate_his_prod = _relate_his_prods.length > 0 ? '1' : '0'; // 是否有关联货品的标识
      });
      return attrs;
    },
    // 编辑回显
    handleEchoData(data) {
      const attrs = data.attrs.map(item => {
        item.price = Number(item.price);
        item.vip_price = Number(item.vip_price || 0);
        item.stored_price = Number(item.stored_price);

        const divide_rules = item.divide_rules || {};
        item.first_tier_price = +item.first_tier_price || null;
        item.not_first_tier_price = +item.not_first_tier_price || null;
        item.first_tier_vip_price = +item.first_tier_vip_price || null;
        item.not_first_tier_vip_price = +item.not_first_tier_vip_price || null;
        item.divide_rules = {
          real_price: +divide_rules?.real_price || null,
          gte: {
            server: divide_rules?.gte?.serve || null,
            sales: {
              divide_type: divide_rules?.gte?.sales?.divide_type || '',
              divide_value: +divide_rules?.gte?.sales?.divide_value || null,
            },
          },
          lt: {
            server: divide_rules?.lt?.serve || null,
            sales: {
              divide_type: divide_rules?.lt?.sales?.divide_type || '',
              divide_value: +divide_rules?.lt?.sales?.divide_value || null,
            },
          },
        };

        return item;
      });
      this.specs_attrs = [data.specs_data, attrs];
      this.formData.attrs = attrs;
      console.log('🚀 ~ PhysicalGoods.vue:450 ~ handleEchoData ~ this.specs_attrs ---->', this.specs_attrs);
      this.formData.specs_data = data.specs_data;
      this.formData.sale_type = data.sale_type || [];
      this.formData.is_recharge_buy = data.is_recharge_buy;
      this.formData.shelf_scope = data.shelf_scope;
      this.formData.relation_card = data.relation_card;
      this.formData.grant_type = data.grant_type;
      for (let key in data.services ? data.services : []) {
        data.services[key].times = Number(data.services[key].times);
      }
      this.formData.services = data.services;
      this.formData.sw_scope_tmp = data.shelf_scope == '9' ? ['1', '2'] : [data.shelf_scope];
      this.autoSetSaleType();
    },
  },
};
</script>

<style lang="less" scoped></style>
