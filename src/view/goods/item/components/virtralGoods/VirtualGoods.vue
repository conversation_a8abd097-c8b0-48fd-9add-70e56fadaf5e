<template>
  <div class="virtual-goods-wrap mt-20">
    <KWidget label="服务权益:" text required>
      <span class="space6" v-if="isDetailDisabled">添加服务</span>
      <a v-else class="space6" @click="serviceModalVisible = true">添加服务</a>
      <span class="note" style="display: inline-block">设置此商品支持的服务可用次数</span>
      <table v-if="formData.services.length > 0" class="table" style="width: 600px">
        <thead>
          <tr>
            <th>已选服务</th>
            <th>价格</th>
            <th>可用次数</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, key) in formData.services" :key="key">
            <td>{{ item.name }}</td>
            <td v-if="!isRst">{{ item.price | number_format }}</td>
            <td v-if="isRst">
              <div>销售价：¥{{ item.price | number_format }}</div>
              <div>会员价：¥{{ item.vip_price | number_format }}</div>
            </td>
            <td>
              <InputNumber
                :disabled="isDetailDisabled"
                v-model="item.times"
                controls-outside
                :min="1"
                :max="50"
                style="width: 100px"
              />
            </td>
            <td>
              <span v-if="isDetailDisabled">-</span>
              <a v-else @click="onDelService(key)">删除</a>
            </td>
          </tr>
        </tbody>
      </table>
    </KWidget>

    <div class="block-header"><span>价格库存</span></div>

    <KWidget v-if="!isRst" label="价格：" required>
      <InputNumber v-model="formData.price" :precision="2" :active-change="false" :min="0" :disabled="isRst" />
    </KWidget>

    <KWidget v-else label="价格：" required>
      <div class="flex gap-10">
        <div>
          销售价：<InputNumber
            v-model="formData.price"
            :precision="2"
            :active-change="false"
            :min="0"
            :disabled="isRst"
          />
        </div>
        <div>
          会员价：<InputNumber
            v-model="formData.vip_price"
            :precision="2"
            :active-change="false"
            :min="0"
            :disabled="isRst"
          />
        </div>
      </div>
    </KWidget>
    <KWidget label="储值价：" v-show="formData.is_recharge_buy == 'yes' && !isRst">
      <InputNumber
        v-model="formData.stored_price"
        ref="stored"
        style="width: 120px"
        placeholder="请输入储值价"
        :precision="2"
        :min="0"
        @on-change="storedPriceChange"
        :active-change="false"
        :disabled="echoData.goods_type == 30"
      />
      元
    </KWidget>
    <KWidget label="库存：" required>
      <InputNumber v-model="formData.stock" :precision="0" :active-change="false" :min="0" />
    </KWidget>

    <div class="block-header"><span>服务设置</span></div>

    <KWidget label="商品有效期" required>
      <RadioGroup v-model="formData.service_info.expiration.type">
        <Radio label="1" :disabled="echoData.goods_type == 30"><span></span></Radio>
        <div style="display: inline-block; margin-left: -10px">
          购买后
          <InputNumber
            v-model="formData.service_info.expiration.days"
            :min="1"
            placeholder="自定义"
            :disabled="formData.service_info.expiration.type != 1 || echoData.goods_type == 30"
          />
          天内有效
        </div>
        <div class="block_10"></div>
        <Radio label="2" :disabled="echoData.goods_type == 30"><span></span></Radio>
        <div style="display: inline-block; margin-left: -10px">
          截止到
          <DatePicker
            type="date"
            v-model="formData.service_info.expiration.date"
            placeholder="请选择日期"
            format="yyyy-MM-dd"
            @on-change="formData.service_info.expiration.date = arguments[0]"
            :options="expiration_datepicker_options"
            :disabled="formData.service_info.expiration.type != 2 || echoData.goods_type == 30"
          />
          日有效
        </div>
      </RadioGroup>
    </KWidget>
    <KWidget label="是否可约：" text>
      <RadioGroup v-model="formData.service_info.is_appointment">
        <Radio label="1" :disabled="echoData.goods_type == 30">需要预约</Radio>
        <Radio label="2" :disabled="echoData.goods_type == 30">免预约</Radio>
      </RadioGroup>
    </KWidget>
    <KWidget label="是否可退：" text>
      <RadioGroup v-model="formData.service_info.is_can_refund">
        <Radio label="1" :disabled="echoData.goods_type == 30">可退</Radio>
        <Radio label="2" :disabled="echoData.goods_type == 30">不可退</Radio>
      </RadioGroup>
      <span class="note" style="display: inline-block">未使用是否可以退款</span>
    </KWidget>
    <KWidget label="使用时间：" required>
      <Input v-model="formData.service_info.working_time" :disabled="echoData.goods_type == 30" />
      <span class="note">如：工作日8:00-19:00点间可用</span>
    </KWidget>
    <KWidget label="不可使用日期：">
      <Input v-model="formData.service_info.not_working_date" :disabled="echoData.goods_type == 30" />
      <span class="note">如：周六周日、法定节假日不可用</span>
    </KWidget>

    <KWidget label="使用规则：" v-if="echoData.goods_type != 30">
      <Input
        v-model="formData.service_info.note_rule"
        type="textarea"
        :rows="5"
        placeholder="请输入使用规则"
        maxlength="100"
        show-word-limit
      />
    </KWidget>

    <KWidget label="上架范围：" required text>
      <CheckboxGroup v-model="formData.xn_scope_tmp">
        <Checkbox :disabled="echoData.goods_type == 30" label="1">零售服务</Checkbox>
        <Checkbox :disabled="echoData.goods_type == 30" label="2">问诊治疗</Checkbox>
      </CheckboxGroup>
    </KWidget>
    <KWidget label="储值购买: " required text v-if="echoData.goods_type != 30">
      <Radio-group v-model="formData.is_recharge_buy">
        <Radio :label="item.id" v-for="(item, index) in storedList" :key="'stored' + index">
          {{ item.desc }}
        </Radio>
      </Radio-group>
    </KWidget>
    <k-goods-services
      v-model="serviceModalVisible"
      @on-selected="onSelectedService"
      :checked-service="formData.services"
      :optionsList="optionsList"
    />
  </div>
</template>

<script>
import S from 'libs/util';
import KGoodsServices from '@/components/k-goods-services';
import cloneDeep from 'lodash/cloneDeep';
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'VirtualGoods',
  mixins: [],
  props: {
    storedList: {
      type: Array,
      default: () => [],
    },
    sellEntTypes: {
      type: Array,
      default: () => [],
    },
    echoData: {
      type: Object,
      default: () => ({}),
    },
    optionsList: {
      type: Object,
      default() {
        return {
          servTypeDesc: [],
          sourcePlatformDesc: [],
        };
      },
    },
  },
  components: {
    KGoodsServices,
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('-> %c val  ===    %o', 'font-size: 15px;color: #fa8c16 ;', val);
        if (S.isEmptyObject(val) || (val.goods_type !== '15' && val.goods_type !== '30')) return;
        this.handleEchoData(val);
      },
      immediate: true,
    },
  },
  computed: {
    // 是否禁止修改
    isDetailDisabled() {
      // 详情禁止编辑，如果是诊所自定义的，可以编辑
      let isCLI = this.echoData.source_platform === 'CLI';
      return this.$route.query.id && !isCLI;
    },

    isRst() {
      return isRstClinic();
    },
  },
  mounted() {},
  data() {
    return {
      formData: {
        service_info: {
          expiration: {
            type: '1', // 1 购买后${days}天内有效；2 ${date}日期前有效；
            date: '', //
            days: 365, //
          },
          is_appointment: '1', // 1免预约 2需要预约
          is_can_refund: '1', // 1不可退 2可退
          working_time: '', // 工作时间
          not_working_date: '', // 不可使用日期：
          note_rule: '', // 使用规则
        },
        price: 0,
        vip_price: 0,
        is_recharge_buy: 'yes', // 虚拟服务的储值购买
        stored_price: null, // 虚拟服务的储值价
        stock: null, // 库存
        services: [],
        xn_scope_tmp: ['1'],
        xn_scope: '',
      },
      serviceModalVisible: false,
      expiration_datepicker_options: {
        disabledDate(date) {
          return date && date.valueOf() <= Date.now();
        },
      },
    };
  },
  methods: {
    storedPriceChange(val) {
      if (Number(val) === 0) {
        this.formData.stored_price = null;
        this.$refs.stored.currentValue = null;
      }
    },
    onDelService(index) {
      this.formData.services.splice(index, 1);
    },
    onSelectedService: function (items) {
      console.log('-> %c items  === %o ', 'font-size: 15px', items);
      this.formData.services = items.map(item => {
        // 编辑时携带次数
        let isExist = this.formData.services.findIndex(service_item => service_item.id === item.id);
        return {
          id: item.id,
          name: item.name,
          price: item.price,
          times: isExist === -1 ? 1 : this.formData.services[isExist].times,
        };
      });
      this.serviceModalVisible = false;
    },
    validateForm() {
      const data = this.formData;
      if (data.services.length <= 0) {
        this.$Message.error('请添加服务权益');
        return false;
      }

      if (!/^\d([\d\.]+)?/.test(data.price)) {
        this.$Message.error('请输入有效的商品价格');
        return false;
      }
      let price = parseFloat(data.price);
      if (price < 0.01) {
        this.$Message.error('商品价格不允许小于0.01元');
        return false;
      }
      if (price > 50000) {
        this.$Message.error('商品价格不允许大于50000.00元');
        return false;
      }

      if (data.stock <= 0) {
        this.$Message.error('请填写库存');
        return false;
      }

      if (data.xn_scope_tmp.length == 0) {
        this.$Message.error('请选择虚拟商品上架范围');
        return false;
      }
      if (data.service_info.expiration.type === '1') {
        if (!data.service_info.expiration.days) {
          this.$Message.error('请输入购买后有效期天数');
          return false;
        }
      } else {
        if (!data.service_info.expiration.date) {
          this.$Message.error('请选择购买后有效期');
          return false;
        }
      }
      if (!data.service_info.working_time) {
        this.$Message.error('请输入使用时间');
        return false;
      }

      return true;
    },
    getFormData() {
      let formData = {
        ...this.formData,
      };
      formData.xn_scope = this.formData.xn_scope_tmp.length == 2 ? '9' : this.formData.xn_scope_tmp[0];
      delete formData.xn_scope_tmp;
      return formData;
    },
    handleEchoData(goods) {
      this.formData.is_recharge_buy = goods.is_recharge_buy;
      this.formData.price = Number(goods.price);
      this.formData.vip_price = Number(goods.vip_price);
      this.formData.stock = Number(goods.stock);
      this.formData.stored_price = goods.stored_price == '' ? null : Number(goods.stored_price);
      for (let key in goods.services ? goods.services : []) {
        goods.services[key].times = Number(goods.services[key].times);
      }
      this.formData.services = goods.services;
      this.formData.service_info = !S.isEmptyObject(goods.service_info)
        ? goods.service_info
        : this.formData.service_info;
      this.formData.service_info.expiration.days = Number(this.formData.service_info.expiration.days);
      this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1', '2'] : [goods.xn_scope];
      this.formData.xn_scope = goods.xn_scope;
      this.originFormData = cloneDeep(this.formData);
      this.$delete(this.originFormData, 'xn_scope_tmp');
    },
  },
};
</script>

<style lang="less" scoped></style>
