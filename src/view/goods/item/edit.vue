<template>
  <div class="goods-item-wrapper" v-if="pageShow">
    <div class="block-header"><span>商品类型</span></div>
    <div
      v-if="!formData.id || (formData.id && formData.goods_type == 10)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 10 }"
      @click="formData.goods_type = '10'"
    >
      <span>
        <span class="ks-goods-type_name">实物商品</span>
        <span class="ks-goods-type_desc">(物流发货)</span>
      </span>
    </div>

    <div
      v-if="!formData.id || (formData.id && formData.goods_type == 15)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 15 }"
      @click="formData.goods_type = '15'"
    >
      <span>
        <span class="ks-goods-type_name">虚拟商品</span>
        <span class="ks-goods-type_desc">(无需物流)</span>
      </span>
    </div>

    <div
      v-if="!formData.id || (formData.id && (formData.goods_type == 25 || formData.goods_type == 45))"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 25 || formData.goods_type == 45 }"
      @click="formData.goods_type = '25'"
    >
      <span>
        <span class="ks-goods-type_name">通兑券</span>
        <span class="ks-goods-type_desc">(仅限兑换虚拟商品)</span>
      </span>
    </div>

    <div
      v-if="(!formData.id || (formData.id && formData.goods_type == 20)) && canUsePackage"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 20 }"
      @click="formData.goods_type = '20'"
    >
      <span>
        <span class="ks-goods-type_name">套餐商品</span>
        <span class="ks-goods-type_desc">商品组合售卖</span>
      </span>
    </div>

    <div
      v-if="(!formData.id && is_rst) || (formData.id && formData.goods_type == 40)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 40 }"
      @click="formData.goods_type = '40'"
    >
      <span>
        <span class="ks-goods-type_name">实物商品</span>
        <span class="ks-goods-type_desc">(物流发货)</span>
      </span>
    </div>
    <div
      v-if="(!formData.id && is_rst) || (formData.id && formData.goods_type == 30)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 30 }"
      @click="formData.goods_type = '30'"
    >
      <span>
        <span class="ks-goods-type_name">虚拟商品</span>
        <span class="ks-goods-type_desc">(无需物流)</span>
      </span>
    </div>
    <div
      v-if="(!formData.id && is_rst) || (formData.id && formData.goods_type == 35)"
      class="ks-goods-type"
      :class="{ active: formData.goods_type == 35 }"
      @click="formData.goods_type = '35'"
    >
      <span>
        <span class="ks-goods-type_name">套餐商品</span>
        <span class="ks-goods-type_desc">商品组合售卖</span>
      </span>
    </div>

    <goods-base ref="goodsBase" :base-info="baseInfo" :echo-data="goodsFormData" />
    <component
      :is="getComponentInfo.componentName"
      :ref="getComponentInfo.ref"
      :stored-list="storedList"
      :echo-data="goodsFormData"
      :sell-ent-types="sellEntTypes"
      :is_pms_self="is_pms_self"
      :errorReason="errorReason"
      :optionsList="optionsList"
    ></component>

    <div class="block_40"></div>
    <div class="block_40"></div>
    <div class="block_40"></div>

    <div class="fixed-bottom-wrapper">
      <Poptip class="mr10" confirm transfer title="确定复制？" @on-ok="onCopy" v-if="!is_rst">
        <Button :loading="saveBtnLoading">复制</Button>
      </Poptip>
      <!--      <Button @click="onCopy" class="mr-10" :loading="saveBtnLoading">复制</Button>-->
      <!-- 200:上架 209:售罄 800:下架 -->
      <Button @click="onSave" class="mr-10" :loading="saveBtnLoading">保存</Button>
      <Button @click="onPublish" class="mr-10" type="primary" :loading="publishBtnLoading">
        {{ formData.status === '800' || !$route.query.id ? '确认发布并上架' : '保存并更新发布' }}
      </Button>
      <back-button></back-button>
    </div>

    <confirm-modal
      :confirmVisible.sync="confirmAddressVisible"
      :cancelText="cancelText"
      :confirmText="confirmText"
      :content="'确认诊所地址'"
      :contentText="''"
      @ok="confirmAddress"
      @cancel="cancelAddress"
    >
      <div slot="contentTxt" v-if="hover_type === 'ERR_ADDR'" style="padding-left: 52px">
        诊所尚未设置门店地址，无法选择用户到店自提，是否前往设置？
      </div>
      <div slot="contentTxt" v-else style="padding-left: 52px">
        <div>请确认门店的提货地址</div>
        <div style="color: #0000ff">{{ cli_addr_detail }}</div>
      </div>
    </confirm-modal>
  </div>
  <div v-else>
    <Spin fix></Spin>
  </div>
</template>

<script>
import S from '@/libs/util';
import confirmModal from '@/components/confirmModal/confirmModal';
// 基础数据
import GoodsBase from './components/GoodsBase.vue';
// 实体商品
import PhysicalGoods from './components/physicalGoods/PhysicalGoods.vue';
// 虚拟商品
import VirtualGoods from './components/virtralGoods/VirtualGoods.vue';
// 通兑券
import ExchangeCoupon from './components/exchangeCoupon/ExchangeCoupon.vue';
// 套餐商品
import TaoCanGoods from './components/taocanGoods/TaoCanGoods.vue';
import { canUsePackageProd, isRstClinic } from '@/libs/runtime';
import io from '@/libs/io';
import config from '@/config';

export default {
  name: 'edit',
  mixins: [],
  components: {
    GoodsBase,
    VirtualGoods,
    PhysicalGoods,
    ExchangeCoupon,
    TaoCanGoods,
    confirmModal,
  },

  computed: {
    is_rst() {
      return isRstClinic();
    },
    getComponentInfo() {
      return this.goodsTypeMaps[this.formData.goods_type];
    },
    canUsePackage() {
      return canUsePackageProd();
    },
  },
  watch: {},

  data() {
    return {
      pageShow: false,
      formData: {
        id: '',
        goods_type: '10',
      },
      goodsFormData: {},
      getLoading: true,

      saveBtnLoading: false, // 保存按钮状态
      publishBtnLoading: false, // 确认发布按钮状态
      saveUpBtnLoading: false, // 保存并发布按钮状态

      storedList: [], // 储值购买枚举
      sellEntTypes: [], // 售卖主体枚举
      baseInfo: {
        name: '',
        slide_imgs: [],
        detail_imgs: [],
      },

      submitConfirmVisible: false, // 提交二次确认
      updateConfirmVisible: false, // 修改模板后，字段异动提示弹窗
      goodsTypeMaps: {
        10: { componentName: 'PhysicalGoods', ref: 'physicalGoods' },
        15: { componentName: 'VirtualGoods', ref: 'virtualGoods' },
        20: { componentName: 'TaoCanGoods', ref: 'taocanGoods' },
        25: { componentName: 'ExchangeCoupon', ref: 'exchangeCoupon' },
        30: { componentName: 'VirtualGoods', ref: 'virtualGoods' },
        35: { componentName: 'TaoCanGoods', ref: 'taocanGoods' },
        40: { componentName: 'PhysicalGoods', ref: 'physicalGoods' },
        45: { componentName: 'ExchangeCoupon', ref: 'exchangeCoupon' },
      },
      diffData: [], // 异动数据

      checked_sell_ent_type: [], // 勾选的售卖主体,

      optionsList: {
        // 服务列表-todo
        servTypeDesc: [],
        sourcePlatformDesc: [],
      },

      is_pms_self: '',
      errorReason: [], // 实物商品禁止一件代发的原因

      // 诊所尚未设置门店自提拦截
      confirmAddressVisible: false,
      cancelText: '',
      confirmText: '',
      contentText: '',
      hover_type: '',
      cli_addr_detail: '',
    };
  },

  created() {
    this.getIndexOptions();
    if (this.$route.query.id) {
      // 编辑时才有的参数
      this.formData.id = this.$route.query.id;
    }

    if (this.formData.id) {
      this.get();
    } else {
      this.pageShow = true;
    }
  },
  mounted() {
    console.log(this.formData);
  },
  methods: {
    storedPriceChange(val) {
      if (Number(val) == 0) {
        this.formData.stored_price = null;
        this.$refs.stored.currentValue = null;
      }
    },

    getFormData: function () {
      if (!this.$refs.goodsBase.validateForm()) {
        return false;
      }
      // 实体商品校验拦截
      if (this.formData.goods_type == 10 && !this.$refs.physicalGoods.validateForm()) {
        return false;
      }
      // 虚拟商品校验拦截
      if (this.formData.goods_type == 15 && !this.$refs.virtualGoods.validateForm()) {
        return false;
      }
      // 通兑券校验拦截
      if (
        (this.formData.goods_type == 25 || this.formData.goods_type == 45) &&
        !this.$refs.exchangeCoupon.validateForm()
      ) {
        return false;
      }
      // 套餐校验拦截
      if (this.formData.goods_type == 20 && !this.$refs.taocanGoods.validateForm()) {
        return false;
      }
      // 虚拟商品校验拦截
      if (this.formData.goods_type == 30 && !this.$refs.virtualGoods.validateForm()) {
        return false;
      }
      // 套餐校验拦截
      if (this.formData.goods_type == 35 && !this.$refs.taocanGoods.validateForm()) {
        return false;
      }
      // 套餐校验拦截
      if (this.formData.goods_type == 40 && !this.$refs.physicalGoods.validateForm()) {
        return false;
      }

      let formData = {
        ...this.formData,
        ...this.$refs.goodsBase.formData,
      };

      if (this.formData.goods_type === '10' || this.formData.goods_type === '40') {
        formData = { ...formData, ...this.$refs.physicalGoods.getFormData() };
      }
      if (this.formData.goods_type === '15' || this.formData.goods_type === '30') {
        formData = { ...formData, ...this.$refs.virtualGoods.getFormData() };
      }
      // 合并通兑券数据
      if (this.formData.goods_type === '25' || this.formData.goods_type === '45') {
        formData = { ...formData, ...this.$refs.exchangeCoupon.getFormData() };
      }
      // 合并套餐数据
      if (this.formData.goods_type === '20' || this.formData.goods_type === '35') {
        formData = { ...formData, ...this.$refs.taocanGoods.getFormData() };
      }
      console.log('🚀 ~ formData=>', formData);

      return formData;
    },

    submit() {
      let formData = this.getFormData();
      if (!formData) {
        return;
      }
      this.checked_sell_ent_type = formData.sell_ent_type || [];
      this.submitConfirmVisible = true;
    },

    updateSubmit() {
      let formData = this.getFormData();
      if (!formData) {
        return;
      }
      let diffData = [...this.$refs.goodsBase.getDiffData(), ...this.$refs[this.getComponentInfo.ref].getDiffData()];
      console.log('-> %c diffData  ===    %o', 'font-size: 15px;color: #fa8c16 ;', diffData);
      if (diffData && diffData.length) {
        this.diffData = diffData;
        this.updateConfirmVisible = true;
        return;
      }
      this.onSave();
    },

    // 保存
    onSave: function () {
      let formData = this.getFormData();
      if (!formData) {
        return;
      }
      if (formData.name.length > 25) {
        this.$Message.error('商品名称不能大于25个字');
        return;
      }

      this.saveBtnLoading = true;

      let params = {
        ...formData,
        stored_price: formData.stored_price == null ? '' : formData.stored_price,
      };

      // 不支持储值价时，储值价置空
      if (params.is_recharge_buy === 'no') {
        params.stored_price = '';
      }

      this.$api
        .setGoodsIndexEdit(params)
        .then(res => {
          if (!this.handleConfirm(res)) {
            return;
          }

          this.$Message.success({
            content: '保存成功',
          });
          this.$router.push({
            path: '/goods/item/list',
          });
        })
        .catch(error => {
          throw new Error(error);
        })
        .finally(() => {
          this.saveBtnLoading = false;
        });
    },

    // 确认发布
    onPublish: function () {
      let formData = this.getFormData();
      if (!formData) {
        return;
      }
      if (formData.name.length > 25) {
        this.$Message.error('商品名称不能大于25个字');
        return;
      }
      this.publishBtnLoading = true;
      let params = {
        ...formData,
        stored_price: formData.stored_price == null ? '' : formData.stored_price,
        sw_scope_tmp: formData.goods_type === '10' ? formData.sw_scope_tmp : '',
      };

      this.$api
        .setGoodsIndexPublish(params)
        .then(res => {
          if (!this.handleConfirm(res)) {
            return;
          }
          this.$Message.success({
            content: '发布成功',
          });
          this.$router.push({ path: '/goods/item/list' });
        })
        .catch(error => {})
        .finally(() => {
          this.publishBtnLoading = false;
        });
    },

    get: function () {
      let params = { id: this.formData.id };
      this.$api
        .getGoodsDetailInfo(params)
        .then(data => {
          let goods = data.goods;
          this.formData.id = goods.id;
          this.formData.status = goods.status;
          this.formData.goods_type = goods.goods_type;

          // 销售方式
          this.is_pms_self = data.is_pms_self;
          this.errorReason = data.error_pms_self;

          // 基础信息回显
          this.baseInfo.name = goods.name;
          this.baseInfo.detail_imgs = goods.detail_imgs;
          this.baseInfo.slide_imgs = goods.slide_imgs;
          this.baseInfo.out_sales_goods_names = goods?.out_sales_goods_names || [];
          console.log('🚀 ~ goods=>', goods);
          this.goodsFormData = goods;

          this.pageShow = true;
          console.log('🚀 ~ data=>', data);
        })
        .catch(error => {
          console.error(error);
        });
    },

    // 储值购买枚举值
    getIndexOptions() {
      this.$api.getIndexOptions().then(res => {
        this.storedList = S.descToArrHandle(res.isRechargeBuyDesc);
        this.sellEntTypes = S.descToArrHandle(res.saleTypeDesc);
        this.optionsList.servTypeDesc = S.descToArrHandle(res.servTypeDesc);
        this.optionsList.sourcePlatformDesc = S.descToArrHandle(res.sourcePlatformDesc);
        console.log('-> %c this.optionsList  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.optionsList);
      });
    },

    confirmAddress() {
      if (this.hover_type === 'ERR_ADDR') {
        this.confirmAddressVisible = false;
        const a = document.createElement('a');
        a.setAttribute('href', '/setting/general_set');
        a.setAttribute('target', '_blank');
        a.setAttribute('style', 'display:none');
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        // window.open('/setting/general_set')
        // this.$router.push({path:'/setting/general_set', query:{back: 'true'}})
      }
      if (this.hover_type === 'ERR_CONFIRM_ADDR') {
        this.confirmAddr();
      }
    },

    cancelAddress() {
      if (this.hover_type === 'ERR_CONFIRM_ADDR') {
        this.confirmAddressVisible = false;
        window.open('/setting/general_set');
        // this.$router.push({path:'/setting/general_set', query:{back: 'true'}})
      }
    },

    confirmAddr() {
      this.$api
        .confirmAddr()
        .then(() => {
          this.confirmAddressVisible = false;
        })
        .catch(err => {});
    },

    handleConfirm(res) {
      let flag = true;
      this.hover_type = res.hover_type;
      if (this.hover_type === 'ERR_ADDR') {
        this.confirmAddressVisible = true;
        this.cancelText = '取消';
        this.confirmText = '去设置';
        flag = false;
        return flag;
      }
      if (this.hover_type === 'ERR_CONFIRM_ADDR') {
        this.confirmAddressVisible = true;
        this.cli_addr_detail = res.addr_detail;
        this.cancelText = '地址有误, 去修改';
        this.confirmText = '确定';
        flag = false;
        return flag;
      }
      return flag;
    },

    onCopy() {
      io.post('clinic/goods.index.copy', { id: this.$route.query.id })
        .then(res => {
          this.$Message.success('复制成功');
          // this.$router.push({
          //   path: '/goods/item/edit',
          //   query: { id: res.id },
          // });
          let envDomain = config.ApiDomain.replace('/api-server/', '');
          console.log('=>(edit.vue:519) envDomain', envDomain);
          const a = document.createElement('a');
          a.target = '_blank';
          // 区分本地环境和打包环境的跳转
          if (!S.isServeRun) {
            a.href = `${envDomain}/goods/item/edit?id=${res.id}`;
          } else {
            a.href = `/goods/item/edit?id=${res.id}`;
          }
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        })
        .catch(error => {});
    },
  },
};
</script>

<style lang="less" scoped>
.goods-item-wrapper {
  .ivu-input-wrapper,
  .ivu-select {
    width: 80%;
    max-width: none;
  }

  .ivu-input {
    max-width: none;
  }

  .ks-goods-type {
    position: relative;
    display: inline-block;
    cursor: pointer;
    margin: 0 0 0 10px;
    text-align: center;
    width: 115px;
    border-radius: 2px;
    border: 1px solid #cacaca;
    padding: 8px 0;
  }

  .ks-goods-type.active {
    border-color: #155bd4;
  }

  .ks-goods-type.active:after {
    content: '';
    display: inline-block;
    position: absolute;
    width: 24px;
    height: 24px;
    bottom: -1px;
    right: -1px;
    background: url(https://img-sn-i01s-cdn.rsjxx.com/image/2021/0224/155801_7284881.png) no-repeat;
    background-size: 24px auto;
  }

  .ks-goods-type_name {
    display: block;
    font-weight: bold;
  }

  .ks-goods-type_desc {
    display: block;
    color: #999;
  }

  .ks-input-number {
    width: 100px;

    .ivu-input-number-input {
      text-align: center;
    }
  }

  .ivu-date-picker-editor {
    width: 100% !important;
  }
}

::v-deep .picture-display {
  display: inline-block;
}
</style>
