<template>
  <div>
    <standard-table
      class="goods-table"
      :no-data-text="''"
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button v-if="!isRstClinic()" type="primary" :to="{ path: '/goods/item/edit' }">发布商品</Button>
        </div>
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <FormItem label="">
            <Input type="text" v-model="queryFormData.name" placeholder="商品名称" />
          </FormItem>
          <FormItem label="">
            <Select v-model="queryFormData.goods_type" placeholder="商品类型" clearable>
              <Option value="">全部</Option>
              <Option v-for="(type, typeKey) in typeDesc" :value="typeKey" :key="typeKey">{{ type.desc }}</Option>
            </Select>
          </FormItem>

          <FormItem label="" v-if="!isOpc">
            <Select v-model="queryFormData.source_platform" placeholder="商品来源">
              <Option value="">全部</Option>
              <Option v-for="(status, statusKey) in sourcePlatformDesc" :value="statusKey" :key="statusKey">
                {{ status.desc }}
              </Option>
            </Select>
          </FormItem>
          <FormItem v-if="isOutSaleClinic" label="">
            <Input type="text" v-model="queryFormData.out_goods_name" placeholder="外部渠道商品名称" />
          </FormItem>
          <FormItem>
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <span class="list-reset-btn" @click="onResetSearch">
              <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
              <span>清除条件</span>
            </span>
          </FormItem>
        </Form>
        <div class="table-action flex flex-item-between">
          <div class="panel-nav">
            <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')">
              全部
            </a>
            <a
              class="nav"
              :class="{ active: $route.query.status == '200' }"
              @click.prevent.capture="onStatusChange('200')"
            >
              上架中
              <Tag>{{ status_count[200] }}</Tag>
            </a>
            <a
              class="nav"
              :class="{ active: $route.query.status == '209' }"
              @click.prevent.capture="onStatusChange('209')"
            >
              已售罄
              <Tag>{{ status_count[209] }}</Tag>
            </a>
            <a
              class="nav"
              :class="{ active: $route.query.status == '800' }"
              @click.prevent.capture="onStatusChange('800')"
            >
              下架中
              <Tag>{{ status_count[800] }}</Tag>
            </a>
          </div>
        </div>
      </template>
      <template #emptyData v-if="!tableLoading && list.length === 0">
        <div class="no-text-box">
          <img src="@/assets/image/base/empty.png" alt="暂无数据" />
          <p class="nodata-text">暂无商品，快去商品库选择商品添加进来吧~</p>
          <Button @click="$router.push('/goods/warehouse/list')">去商品库</Button>
        </div>
      </template>
      <template slot-scope="{ row }" slot="info">
        <div class="flex" style="align-items: center">
          <div class="media-left media-middle" v-viewer="[row.main_img]">
            <img :src="row.main_img | imageStyle" style="width: 35px; object-fit: cover" class="img-rounded" />
          </div>
          <div class="flex-1" style="overflow: hidden">
            <Tooltip
              max-width="200"
              v-if="row.is_valid_icon === '0'"
              content="商品已从总部下架，无法采用总部一件代发的销售方式，请修改销售方式或下架商品"
            >
              <svg-icon iconClass="jurassic" class="helpIcon"></svg-icon>
            </Tooltip>
            <KLink :key="row.id" :to="{ path: '/goods/item/edit', query: { id: row.id } }">
              {{ row.name }}
            </KLink>
          </div>
        </div>
      </template>
      <template slot-scope="{ row }" slot="price_section">
        <div v-if="isRst">
          <div v-if="row.goods_type == 40">
            <div>
              <span>销售价：</span>
              <span v-if="row?.price_section?.max">
                <span>￥{{ row.price_section?.min | number_format }}</span>
                <span>-</span>
                <span>￥{{ row.price_section?.max | number_format }}</span>
              </span>
              <span v-else="">
                <span>￥{{ row.price_section?.min | number_format }}</span>
              </span>
            </div>
            <div>
              <span>会员价：</span>
              <span v-if="row?.vip_price_section?.max">
                <span>￥{{ row.vip_price_section?.min | number_format }}</span>
                <span>-</span>
                <span>￥{{ row.vip_price_section?.max | number_format }}</span>
              </span>
              <span v-else="">
                <span>￥{{ row.vip_price_section?.min | number_format }}</span>
              </span>
            </div>
          </div>
          <div v-else>
            <p>销售价：¥ {{ row.price | number_format }}</p>
            <p>会员价：¥ {{ row.vip_price | number_format }}</p>
          </div>
        </div>
        <div v-else>
          <p v-if="!row.price_section.max">￥{{ row.price_section.min | number_format }}</p>
          <p v-else>￥{{ row.price_section.min | number_format }} - ￥{{ row.price_section.max | number_format }}</p>
        </div>
      </template>
      <!-- <template slot-scope="{ row }" slot="price_section">
        <p v-if="!row.price_section.max">￥{{ row.price_section.min }}</p>
        <p v-else>￥{{ row.price_section.min }} - ￥{{ row.price_section.max }}</p>
      </template> -->
      <!-- 储值价 -->
      <template slot-scope="{ row }" slot="stored_price_section">
        <p v-if="!row.stored_price || row.is_recharge_buy === 'no'">-</p>
        <template v-else>
          <p v-if="!row.stored_price_section.max">￥ {{ row.stored_price_section.min | number_format }}</p>
          <p v-else>
            ￥ {{ row.stored_price_section.min | number_format }} - ￥
            {{ row.stored_price_section.max | number_format }}
          </p>
        </template>
      </template>
      <template slot-scope="{ row }" slot="stock">
        {{ row.stock }}
      </template>
      <template slot-scope="{ row }" slot="sales">
        {{ row.sales }}
      </template>
      <template slot-scope="{ row }" slot="create_time">
        {{ row.create_time | data_format }}
      </template>
      <template slot-scope="{ row }" slot="status">
        {{ statusDesc[row.status].desc }}
      </template>
      <template slot-scope="{ row }" slot="out_sales_goods_names">
        <div class="text-ellipsis" v-overflow-tooltip v-for="(good, i) in row.out_sales_goods_names" :key="i">
          {{ good.out_sales_channel_name + (good.out_sales_channel_name ? '：' : '') }}{{ good.out_goods_name }}
        </div>
        <div v-if="row?.out_sales_goods_names?.length === 0">-</div>
      </template>
      <template slot-scope="{ row, index }" slot="operate">
        <OperationFolding
          :actions="getActions(row, index)"
          :row="row"
          :maxVisibleActions="getActions(row, index).filter(item => !item.isHidden).length > 3 ? 2 : 3"
        ></OperationFolding>
      </template>
    </standard-table>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */
import renderHeader from '@/mixins/renderHeader';
import search from '@/mixins/search';
import { isRstClinic, isOpcClinic } from '@/libs/runtime';
import StandardTable from '@/components/StandardTable/index.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  id: '',
  name: '',
  goods_type: '',
  r: '',
  source_platform: '',
  out_goods_name: '',
};

export default {
  name: 'list',
  components: { StandardTable },
  mixins: [renderHeader, search],

  data() {
    return {
      isRstClinic,
      isOutSaleClinic: false,
      queryFormData: { ...init_query_form_data },
      apiName: 'getCureRecordList',
      tableLoading: false,
      list: [],
      total: 0,
      statusDesc: {},
      typeDesc: {},
      sourcePlatformDesc: {},
      status_count: {},
    };
  },

  computed: {
    isRst() {
      return isRstClinic();
    },
    isOpc() {
      return isOpcClinic();
    },

    // 是否可下架
    isCanUndercarriage() {
      return row => {
        // &&
        //   row.source_platform != 'PLAT' &&
        //   row.source_platform != 'COM' &&
        //   row.source_platform != 'CP'
        return this.statusDesc[row.status].kw !== 'STATUS_SHELVES_DOWN';
      };
    },
    tableCols() {
      const list = [
        { title: 'ID', key: 'id', minWidth: 60 },
        { title: '商品', slot: 'info', minWidth: 180 },
        { title: '商品类型', key: 'goods_type_text', width: 80 },
        { title: '商品来源', key: 'source_platform_text', width: 80 },
        { title: '价格', slot: 'price_section', minWidth: 240 },
        {
          title: '储值价',
          slot: 'stored_price_section',
          align: 'center',
          minWidth: 80,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '商品储值价指使用储值余额购买时可享受的优惠价，不设置即无优惠价'),
        },
        { title: '库存', slot: 'stock', minWidth: 60 },
        { title: '销量', slot: 'sales', minWidth: 80 },
        { title: '储值赠送数量', key: 'gift_quantity', minWidth: 120 },
        {
          title: '渠道名称',
          slot: 'out_sales_goods_names',
          align: 'center',
          minWidth: 100,
          renderHeader: (h, params) =>
            this._renderHeader(
              h,
              params,
              '是指此商品在美团/抖音上有对应的商品，且两个商品已关联生效，可以在美团/抖音上售卖。'
            ),
        },
        { title: '创建时间', slot: 'create_time', minWidth: 160 },
        { title: '商品状态', slot: 'status', minWidth: 80 },
        { title: '操作', slot: 'operate', width: 120, fixed: 'right' },
      ];
      if (this.isOutSaleClinic) {
        return list.filter(
          item => item.key !== 'gift_quantity' && item.slot !== 'stored_price_section' && item.slot !== 'sales'
        );
      }
      if (isRstClinic()) {
        return list.filter(
          item => item.key !== 'gift_quantity' && item.slot !== 'stored_price_section' && item.slot !== 'sales' && item.slot !== 'out_sales_goods_names'
        );
      }
      return list.filter(item => item.slot !== 'out_sales_goods_names');
    },

    getActions() {
      return (row, index) => {
        return [
          {
            label: '下架',
            confirmText: '确定下架？',
            handler: this.onChangeStatus,
            params: {
              action: 'DOWN',
              id: row.id,
            },
            tagType: 'Poptip',
            isHidden: !this.isCanUndercarriage(row),
          },
          {
            label: '上架',
            handler: this.toEdit,
            tagType: 'a',
            params: row.id,
            isHidden: !(this.statusDesc[row.status].kw === 'STATUS_SHELVES_DOWN'),
          },
          {
            label: '详情',
            handler: this.toDetail,
            tagType: 'a',
            params: row.id,
          },
          {
            label: '编辑',
            handler: this.toEdit,
            tagType: 'a',
            params: row.id,
            isHidden: row.goods_type === '40' || row.goods_type == '35',
          },
          {
            label: '删除',
            confirmText: '确定删除？',
            handler: this.onChangeStatus,
            params: {
              action: 'DEL',
              id: row.id,
            },
            tagType: 'Poptip',
            isHidden: !(this.statusDesc[row.status].kw !== 'STATUS_SHELVES_UP' && row.source_platform === 'CLI'),
          },
        ];
      };
    },
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getUserIsOutSaleClinic()
  },

  methods: {
    getUserIsOutSaleClinic() {
      this.$api.getUserIsOutSaleClinic().then(res => {
        this.isOutSaleClinic = res?.is_allow_out_sales_channel === '1';
      });
    },
    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    onChangeStatus: function ({ action, id }) {
      const actionDesc = action => {
        return { UP: '上架', DOWN: '下架', DEL: '删除' }[action];
      };

      io.post('clinic/goods.index.status', { id: id, act: action })
        .then(() => {
          this.$Message.success(actionDesc(action) + '成功');
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
        });
    },

    onCopy: function (id) {
      io.post('clinic/goods.index.copy', { id: id })
        .then(() => {
          this.$Message.success('复制成功');
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
        });
    },

    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.list = data.goods_items;
          this.total = data.total;
          this.statusDesc = data.statusDesc;
          this.typeDesc = data.typeDesc;
          this.sourcePlatformDesc = data.sourcePlatformDesc;
          this.status_count = data.status_count;
        })
        .catch(error => {})
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },

    toEdit(id) {
      this.$router.push({
        path: '/goods/item/edit',
        query: { id },
      });
    },
    toDetail(id) {
      this.$router.push({
        path: '/goods/item/detail',
        query: { id },
      });
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.no-text-box {
  position: absolute;
  z-index: 10;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .nodata-text {
    margin: 10px 0;
  }
}

p {
  margin: 0;
}

.mr10 {
  margin-right: 10px;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
