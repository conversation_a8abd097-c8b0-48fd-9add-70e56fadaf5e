<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :row-class-name="rowClassName"
      :columns="tableCols"
      :data="list"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" @click="addService">添加服务</Button>
          <Button style="margin-left: 8px" @click="importReport">导入分佣金额</Button>
        </div>
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <FormItem>
            <Input type="text" v-model="queryFormData.keyword" placeholder="服务名称" />
          </FormItem>
          <FormItem>
            <Select v-model="queryFormData.serv_type" placeholder="类型">
              <Option v-for="desc in getServTypeDesc" :key="desc.id" :value="desc.id">{{ desc.desc }}</Option>
            </Select>
          </FormItem>

          <FormItem>
            <Select v-model="queryFormData.source_platform" placeholder="来源" clearable>
              <Option v-for="desc in sourcePlatformDesc" :key="desc.id" :value="desc.id">{{ desc.desc }}</Option>
            </Select>
          </FormItem>

          <FormItem>
            <Select v-model="queryFormData.fast_order_support" placeholder="快捷下单" clearable>
              <Option v-for="desc in getFastOrderDesc" :key="desc.id" :value="desc.id">{{ desc.desc }}</Option>
            </Select>
          </FormItem>
          <FormItem>
            <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
            <span class="list-reset-btn" @click="onResetSearch">
              <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
              <span>清除条件</span>
            </span>
          </FormItem>
        </Form>
        <div class="flex flex-item-between">
          <div class="panel-nav">
            <a class="nav" :class="{ active: !$route.query.status }" @click.prevent.capture="onStatusChange('')">
              全部
            </a>
            <a
              :class="{ active: $route.query.status == item.id }"
              class="nav"
              v-for="item in statusList"
              :key="item.id"
              @click.prevent.capture="onStatusChange(item.id)"
            >
              {{ item.desc }}
              <Tag :color="getTagColor(item.id)">
                {{ statusTotal[item.id] }}
              </Tag>
            </a>
          </div>
        </div>
      </template>
      <template slot-scope="{ row }" slot="info">
        {{ row.name }}
      </template>
      <template slot-scope="{ row }" slot="price"> ¥ {{ row.price | number_format }} </template>
      <template slot-scope="{ row }" slot="source_platform_text">
        {{ row.source_platform_text || '-' }}
      </template>
      <template slot-scope="{ row, index }" slot="relate_goods">
        <poptip-table
          v-if="row.goods_count > 0"
          :key="index"
          :columns="columns"
          :data="row.goods_relation_info"
          width="500"
        >
          <a>{{ row.goods_count }}</a>
        </poptip-table>
        <div v-else>{{ row.goods_count || 0 }}</div>
      </template>
      <template slot-scope="{ row }" slot="fast_order_support_text">
        {{ row.fast_order_support_text }}
      </template>

      <template slot-scope="{ row }" slot="fixed_divide_value">
        {{ +row.fixed_divide_value > 0 ? row.fixed_divide_value : '-' }}
      </template>
      <template slot-scope="{ row }" slot="status">
        {{ row.status_text }}
      </template>
      <template slot-scope="{ row }" slot="operate">
        <a @click="toDetail(row)">编辑</a>
        <a class="ml10" @click="toDetail(row, 'detail')">详情</a>
        <template v-if="!is_system(row)">
          <a class="ml10" v-if="row.status === 'ON'" @click="changeGoodsserviceStatus(row, 'OFF')">禁用</a>
          <a class="ml10" v-else @click="changeGoodsserviceStatus(row, 'ON')">启用</a>
        </template>
        <Poptip class="ml10" confirm transfer title="确定删除？" v-if="isCanDelete(row)" @on-ok="onDelete(row.id)">
          <a>删除</a>
        </Poptip>
      </template>
    </standard-table>
    <import-report
      :visible.sync="reportModalVisible"
      :excel-key-map="excelKeyMap"
      :import-api-name="importApiName"
      :chunk-num="10"
      @refresh="submitQueryForm"
    />
  </div>
</template>

<script>
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import search from '@/mixins/search';
/* eslint-disable */
import poptipTable from '@/components/poptipTable';
import RenderHeader from '@/mixins/renderHeader';
import StandardTable from '@/components/StandardTable/index.vue';
import ImportReport from '@/view/goods/service/components/importReport.vue';
import importSchedule from "@/view/setting/member/management/components/importSchedule.vue";

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  keyword: '',
  status: '',
  source_platform: '',
  serv_type: '',
  fast_order_support: '',
  r: '',
};

export default {
  name: 'list',
  mixins: [search, RenderHeader],
  components: {
    importSchedule,
    StandardTable,
    poptipTable,
    ImportReport
  },
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getGoodsServiceList',
      tableCols: [
        { title: 'ID', key: 'id', width: '150' },
        { title: '服务', slot: 'info' },
        { title: '价格', slot: 'price' },
        { title: '类型', key: 'serv_type_text' },
        { title: '来源', slot: 'source_platform_text' },
        { title: '关联商品', slot: 'relate_goods' },
        { title: '固定分佣金额', slot: 'fixed_divide_value' },
        {
          title: '快捷下单',
          slot: 'fast_order_support_text',
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '设置后快捷下单后，可以在手动建单时，直接下单购买服务'),
        },
        { title: '状态', slot: 'status', width: 120 },
        { title: '操作', slot: 'operate', width: 160 },
      ],
      tableLoading: false,

      list: [],
      total: 0,
      statusDesc: {},
      typeList: [], // 服务类型数据

      columns: [
        { title: '商品名', key: 'name', width: 200, align: 'left' },
        { title: '类型', key: 'goods_type_text', slot: 'goods_type_text', textColor: '#909399', align: 'left' },
        {
          title: '来源',
          key: 'source_platform_text',
          slot: 'source_platform_text',
          textColor: '#909399',
          align: 'left',
        },
        { title: '单价', key: 'price', slot: 'price', align: 'right', isMoney: true },
      ],
      statusList: [],
      statusCount: {},
      quickOrderList: [
        { label: '支持', value: '1' },
        { label: '不支持', value: '2' },
      ],
      getFastOrderDesc: [],
      getServTypeDesc: [],
      sourcePlatformDesc: [],
      statusTotal: {},
      type: '',
      reportModalVisible: false,
      excelKeyMap: {
        服务ID: { key: 'goods_service_id', required: false },
        固定分佣金额: { key: 'fixed_divide_value', required: false },
      },
      importApiName: 'importGoodsServiceTemplate'
    };
  },
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);

    // 获取服务类型
    this.getServiceType();
    // 获取枚举
    this.getGoodsserviceOptions();
  },
  watch: {},
  computed: {
    is_system() {
      return row => {
        if (
          row.type == 'HIS' ||
          row.source_platform == 'PLAT' ||
          row.source_platform == 'COM' ||
          row.source_platform == 'CP'
        ) {
          return true;
        } else {
          return false;
        }
      };
    },
    // 自定义并且关联商品为0可以删除
    isCanDelete() {
      return ({ type, source_platform, goods_count }) => {
        if (
          type == 'HIS' ||
          source_platform == 'PLAT' ||
          source_platform == 'COM' ||
          source_platform == 'CP' ||
          goods_count > 0
        ) {
          return false;
        } else {
          return true;
        }
      };
    },
    getTagColor() {
      return status => {
        switch (status) {
          case 'ON':
            return 'success';
          case 'DELETED':
            return 'error';
          default:
            return 'default';
        }
      };
    },
  },
  methods: {
    toDetail(row, type) {
      let query = {
        id: row.id,
      };
      if (type) {
        query.type = type;
      }
      this.$router.push({
        path: '/goods/service/detail',
        query: query,
      });
    },
    importReport() {
      this.reportModalVisible = true;
    },
    addService() {
      this.$router.push('/goods/service/detail');
    },
    // 给表格行设置样式
    rowClassName(row, index) {
      return row.status === 'OFF' ? 'del-cell' : '';
    },
    // 状态切换
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    onDelete: function (id) {
      io.post('clinic/goods.goodsservice.status', {
        id: id,
        act: 'DEL',
      }).then(() => {
        this.$Message.success('删除成功');
        this.submitQueryForm();
      });
    },

    changeGoodsserviceStatus(row, act) {
      let params = {
        id: row.id,
        act: act,
      };
      this.$api.changeGoodsserviceStatus(params).then(res => {
        let text = act === 'ON' ? '启用' : '禁用';
        this.$Message.success(`${text}成功`);
        this.submitQueryForm();
      });
    },

    // 获取服务类型、
    getServiceType() {
      this.$api.getServiceType().then(res => {
        this.typeList = S.descToArrHandle(res || {});
      });
    },

    // 获取枚举
    getGoodsserviceOptions() {
      this.$api.getGoodsserviceOptions().then(res => {
        this.getFastOrderDesc = S.descToArrHandle(res.getFastOrderDesc || {});
        this.getServTypeDesc = S.descToArrHandle(res.getServTypeDesc || {});
        this.sourcePlatformDesc = S.descToArrHandle(res.sourcePlatformDesc || {});
      });
    },

    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          console.log(data, 'data');
          this.total = data.total;
          this.list = data.list;
          this.statusDesc = data.statusDesc;
          this.statusList = S.descToArrHandle(data.statusDesc);
          this.statusTotal = data.statusTotal;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
// 禁用的表格行置灰样式
::v-deep .ivu-table .del-cell td {
  color: #c0c4cc;

  a,
  p,
  div,
  span {
    color: #c0c4cc;
  }
}
</style>
