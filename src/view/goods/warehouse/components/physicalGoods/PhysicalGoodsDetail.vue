<template>
  <div>
    <div class="block-header"><span>价格库存</span></div>
    <FormItem label="规格明细">
      <Table :columns="columns" :data="tableData" :span-method="handleSpan" disabled-hover border>
        <template v-for="(item, index) in specsData" :slot="item.name + index" slot-scope="{ row }">
          {{ row.specs_atoms[item.name] }}
        </template>

        <template slot-scope="{ row }" slot="price">
          <div v-if="!isRst">{{ row.price | number_format }}</div>
          <div v-else>
            <div>销售价：¥ {{ row.price | number_format }}</div>
            <div>会员价：¥ {{ row.vip_price | number_format }}</div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="stored_price">
          <div>{{ row.stored_price | number_format }}</div>
        </template>

        <template slot-scope="{ row }" slot="sku">
          <div>{{ row.sku || '-' }}</div>
        </template>

        <!--                诊所货品-->
        <template slot-scope="{ row }" slot="generic_id">
          <div v-if="row.relate_his_prods?.length > 0">
            <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item.prod_id }}</div>
          </div>
          <div v-else>-</div>
        </template>
        <!--                诊所货品-->
        <template slot-scope="{ row }" slot="generic_name">
          <div v-if="row.relate_his_prods?.length > 0">
            <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item.generic_name }}</div>
          </div>
          <div v-else>-</div>
        </template>
        <!--                诊所货品-->
        <template slot-scope="{ row }" slot="generic_type_text">
          <div v-if="row.relate_his_prods?.length > 0">
            <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item.prod_type_text }}</div>
          </div>
          <div v-else>-</div>
        </template>
        <!--                售卖单位-->
        <template slot-scope="{ row }" slot="sales_unit">
          <div v-if="row.relate_his_prods?.length > 0">
            <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item._sales_unit }}</div>
          </div>
          <div v-else>-</div>
        </template>
        <template slot-scope="{ row }" slot="_price">
          <div v-if="row.relate_his_prods?.length > 0">
            <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item._price }}</div>
          </div>
          <div v-else>-</div>
        </template>
        <template slot-scope="{ row }" slot="stock">
          {{ row.stock || '-' }}
        </template>
        <template slot-scope="{ row }" slot="buy_multiples">
          <div v-if="row.relate_his_prods?.length > 0">
            <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item.buy_multiples }}</div>
          </div>
          <div v-else>-</div>
        </template>
      </Table>
    </FormItem>
    <FormItem label="分账策略" v-if="isRst">
      <Table :columns="fenzhangColumns" :data="tableData" :span-method="handleSpan" disabled-hover border>
        <template v-for="(item, index) in specsData" :slot="item.name + index" slot-scope="{ row }">
          {{ row.specs_atoms[item.name] || '-' }}
        </template>

        <template slot-scope="{ row }" slot="fenzhang">
          <!-- <div>
            实付时≥单次成本价{{ row.divide_rules?.real_price || 0 }}元，销售提成按{{
              formatFenZhangText(row, 'gte')
            }}分账
          </div>
          <div>
            实付时&lt;单次成本价{{ row.divide_rules?.real_price || 0 }}元，销售提成按{{
              formatFenZhangText(row, 'lt')
            }}分账
          </div> -->
          <div>
            <span>
              根据实收金额，销售提成按
              {{ formatFenZhangText(row, 'gte') }}
              分账
            </span>
          </div>
        </template>
      </Table>
    </FormItem>
    <div v-if="!isRst">
      <div class="block-header"><span>关联赠送服务卡券</span></div>
      <KWidget label="是否关联" required class="flex-item-align">
        <Radio-group v-model="relation_card">
          <Radio disabled :label="item.id" v-for="(item, index) in relationList" :key="'stored' + index">{{
            item.desc
          }}</Radio>
        </Radio-group>
      </KWidget>

      <KWidget label="发放方式" v-if="relation_card == 1" required class="flex-item-align">
        <Radio-group v-model="grant_type">
          <Radio disabled :label="item.id" v-for="(item, index) in grantList" :key="'stored' + index">{{
            item.desc
          }}</Radio>
        </Radio-group>
      </KWidget>

      <KWidget label="" v-if="relation_card == 1">
        <table class="table" style="width: 500px">
          <thead>
            <tr>
              <th>服务</th>
              <th>类型</th>
              <th>来源</th>
              <th>发放数量</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in services" :key="key">
              <td>{{ item.name }}</td>
              <td>{{ item.serv_type_text }}</td>
              <td>{{ item.source_platform_text }}</td>
              <td>
                <span style="width: 100px" v-if="grant_type == 1">{{ item.times }}</span>
                <div v-else>-</div>
              </td>
              <td>-</td>
            </tr>
          </tbody>
        </table>
      </KWidget>
    </div>
    <div class="block-header"><span>其他设置</span></div>
  </div>
</template>

<script>
import S from '@/libs/util';
import renderHeader from '@/mixins/renderHeader';
import cloneDeep from 'lodash/cloneDeep';
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'PhysicalGoodsDetail',
  mixins: [renderHeader],
  components: {},
  props: {
    echoData: {
      type: Object,
      default: () => {},
    },
    is_goods_manage_page: Boolean,
  },
  data() {
    return {
      tableData: [],
      specsData: [],
      services: [],
      relation_card: '',
      grant_type: '',
      columns: [
        { title: '价格(元)', key: 'price', slot: 'price', align: 'center', minWidth: 50 },
        {
          title: '储值价',
          slot: 'stored_price',
          key: 'stored_price',
          align: 'center',
          minWidth: 80,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '商品储值价指使用储值余额购买时可享受的优惠价，不设置即无优惠价'),
        },
        { title: '编码', slot: 'sku', align: 'center', minWidth: 50 },
        { title: '货品id', slot: 'generic_id', align: 'left', minWidth: 50 },
        { title: '货品', slot: 'generic_name', align: 'left', minWidth: 50 },
        { title: '货品类型', slot: 'generic_type_text', align: 'left', minWidth: 50 },
        { title: '售卖单位', slot: 'sales_unit', align: 'center', minWidth: 50 },
        { title: '单位售价(元)', slot: '_price', align: 'center', minWidth: 50 },
        {
          title: '起购倍数',
          slot: 'buy_multiples',
          align: 'center',
          minWidth: 50,
          className: 'table-col-right-border',
        },
      ],
      fenzhangColumns: [{ title: '分账规则', slot: 'fenzhang', align: 'left', minWidth: '70%' }],
      relationList: [
        { id: '0', desc: '不关联' },
        { id: '1', desc: '关联' },
      ],
      grantList: [
        { id: '1', desc: '购买时发放' },
        { id: '2', desc: '订单交易后按需发放' },
      ],
    };
  },
  watch: {
    echoData: {
      handler(val) {
        console.log('=>(PhysicalGoodsDetail.vue:132) val', val);
        if (S.isEmptyObject(val)) return;
        this.tableData = cloneDeep(val.tableData || []);
        console.log('=>(PhysicalGoodsDetail.vue:187) this.tableData', this.tableData);
        this.specsData = cloneDeep(val.specs_data || []);
        this.services = cloneDeep(val.services || []);
        this.relation_card = cloneDeep(val.relation_card || '');
        this.grant_type = cloneDeep(val.grant_type || '');
        // 处理表格规格
        this.tableSpecs();
        // 处理关联货品数据
        this.handleRelateProd();

        if (val.is_recharge_buy === 'no') {
          this.columns = this.columns.filter(item => {
            return item.key !== 'stored_price';
          });
        }
      },
      immediate: true,
    },
  },
  computed: {
    isRst() {
      return isRstClinic();
    },
    formatFenZhangText() {
      return (row, type) => {
        if (!row.divide_rules?.[type]) return '-';
        const { divide_type, divide_value } = row.divide_rules?.[type].sales || {};
        const text1 = `${divide_type === 'ratio' ? '分账比例' : '固定金额'}`;
        const text2 = `${divide_type === 'ratio' ? '%' : '元'}`;
        return ` ${text1 || '-'}   ${divide_value || '-'}${text2 || '-'} `;
      };
    },
  },
  mounted() {},
  methods: {
    handleSpan({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        let x = row.mergeFirstCol === 0 ? 0 : row.mergeFirstCol;
        let y = row.mergeFirstCol === 0 ? 0 : 1;
        return [x, y];
      }
      if (columnIndex === 1) {
        //计算合并的行数列数
        let x = row.mergeSecCol === 0 ? 0 : row.mergeSecCol;
        let y = row.mergeSecCol === 0 ? 0 : 1;
        return [x, y];
      }
    },

    // 处理表格规格
    tableSpecs() {
      this.tableData.forEach(item => {
        item.specsAtoms = Object.values(item.specs_atoms);
      });
      this.integratedData(this.tableData);
      let arr = this.$lodash.cloneDeep(this.specsData);
      console.log('-> arr', arr);
      arr &&
        arr.forEach((item, index) => {
          if (index === 0) {
            item.className = 'table-col-left-border';
          }
          if (index < arr.length - 1) {
            if (index === 0) {
              item.className = 'table-col-border';
            } else {
              item.className = 'table-col-right-border';
            }
          }
          item.title = item.name;
          item.slot = item.name + index;
          item.minWidth = 40;
          item.align = 'center';
        });

      if (isRstClinic()) {
        // 储值价
        this.columns = this.columns.filter(item => item.key != 'stored_price');
        //   const newColumns = [
        //     { title: '一线城市定价', slot: 'first_tier_price', align: 'center', minWidth: 50 },
        //     { title: '非一线城市定价', slot: 'not_first_tier_price', align: 'center', minWidth: 50 },
        //   ];
        //   this.columns = newColumns.concat(this.columns);
      }
      let newArr = arr.concat(this.columns);
      let newArrFenzhang = arr.concat(
        this.fenzhangColumns?.map(item => ({ ...item, minWidth: `${100 - 10 * arr.length}%` }))
      );
      if (this.is_goods_manage_page) {
        newArr.push({
          title: '库存',
          slot: 'stock',
          align: 'center',
          minWidth: 50,
        });
      }
      this.columns = newArr;
      this.fenzhangColumns = newArrFenzhang;
    },

    // 合并单元格
    integratedData(data) {
      // 处理第一列规格
      for (let index = 0; index < data.length; index++) {
        if (data[index].firstAlready !== 1) {
          if (index + 1) {
            data[index].mergeFirstCol = 1;
            for (let idx = index + 1; idx < data.length; idx++) {
              if (data[index].specs_atoms[this.specsData[0]?.name] === data[idx].specs_atoms[this.specsData[0]?.name]) {
                data[index].mergeFirstCol++;
                data[idx].mergeFirstCol = 0;
                data[idx].firstAlready = 1;
              } else {
                break;
              }
            }
          }
        }
      }

      // 处理第二列规格
      for (let index = 0; index < data.length; index++) {
        if (data[index].mergeFirstCol > 1) {
          for (let idx = 0; idx < data[index].mergeFirstCol; idx++) {
            if (data[index + idx].secondAlready !== 1) {
              if (idx + 1 < data[index].mergeFirstCol) {
                data[index + idx].mergeSecCol = 1;
                for (let i = idx + 1; i < data[index].mergeFirstCol; i++) {
                  if (
                    data[index + idx].specs_atoms[this.specsData[1]] &&
                    data[index + idx].specs_atoms[this.specsData[1]?.name] ===
                      data[index + i].specs_atoms[this.specsData[1]?.name]
                  ) {
                    data[index + idx].mergeSecCol++;
                    data[index + i].mergeSecCol = 0;
                    data[index + i].secondAlready = 1;
                  } else {
                    break;
                  }
                }
              }
            }
          }
        }
      }
      this.tableData = data;
    },

    // 关联货品
    handleRelateProd() {
      console.log('->this.tableData ', this.tableData);
      this.tableData.forEach(item => {
        item.relate_his_prods.forEach(prods => {
          prods._sales_unit = this.setDefaultUnit(prods).unit || '';
          // prods._stock_num = this.setDefaultUnit(prods).stock_num || ''
          prods._price = this.setDefaultUnit(prods).price || '';
        });
      });
    },

    // 如果货单单位有选中的，默认带出单位，库存，单位售价
    setDefaultUnit(item) {
      let resultList = item.sales_units.filter(item => item.checked == '1');
      console.log('-> resultList', resultList);
      return resultList[0] || {};
    },
  },
};
</script>

<style lang="less" scoped></style>
