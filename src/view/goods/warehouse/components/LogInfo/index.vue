<template>
  <div :class="['log-box', isBefore ? 'log-box-before' : 'log-box-after']">
    <div class="log-title flex-inline">
      <span class="title-txt">{{ isBefore ? '变更前' : '变更后' }}</span>
      <span class="trapezoid-box"></span>
      <span class="trapezoid-box"></span>
    </div>
    <div class="content-box">
      <!--      <div class="block-title" v-if="showBaseTitle">基本信息</div>-->
      <div class="info-item" v-if="hasModifiedItem('name')">
        <div class="item-label">商品名：</div>
        <div class="item-value">
          <span>{{ getGoodsInfo.name }}</span>
        </div>
      </div>

      <div class="info-item" v-if="hasModifiedItem('goods_type')">
        <div class="item-label">商品类型：</div>
        <div class="item-value">
          <span>{{ goodsTypes[getGoodsInfo.goods_type]?.desc }}</span>
        </div>
      </div>
      <div class="info-item" v-if="hasModifiedItem('slide_imgs')">
        <div class="item-label align-start">商品图：</div>
        <div class="item-value">
          <div style="align-self: flex-start" :class="['img-box']">
            <MaterialPicture :value="getGoodsInfo.slide_imgs" disabled></MaterialPicture>
          </div>
        </div>
      </div>
      <div class="info-item" v-if="hasModifiedItem('detail_imgs')">
        <div class="item-label align-start">商品详情图：</div>
        <div class="item-value">
          <div style="align-self: flex-start" :class="['img-box']">
            <MaterialPicture :value="getGoodsInfo.detail_imgs" disabled></MaterialPicture>
          </div>
        </div>
      </div>
      <component
        :is="goodsTypes[getGoodsType].componentName"
        :hasModifiedItem="hasModifiedItem"
        :getGoodsInfo="getGoodsInfo"
        :goods-type="getGoodsInfo.goods_type"
      ></component>
      <div class="info-item" v-if="hasModifiedItem('sell_ent_type')">
        <div class="item-label">售卖主体：</div>
        <div class="item-value">
          <span>{{ getSellEntType }}</span>
        </div>
      </div>
      <div class="info-item" v-if="getGoodsInfo.goods_type !== '10' && hasModifiedItem('xn_scope')">
        <div class="item-label">上架范围：</div>
        <div class="item-value">
          <span>
            <span v-if="getGoodsInfo.xn_scope !== '9'">
              {{ getGoodsInfo.xn_scope == '1' ? '零售服务' : getGoodsInfo.xn_scope == '2' ? '问诊治疗' : '-' }}
            </span>
            <span v-else>零售服务 问诊治疗</span></span
          >
        </div>
      </div>
      <div class="info-item" v-else-if="getGoodsInfo.goods_type === '10' && hasModifiedItem('shelf_scope')">
        <div class="item-label">上架范围：</div>
        <div class="item-value">
          <span>
            <span v-if="getGoodsInfo.shelf_scope !== '9'">
              {{ getGoodsInfo.shelf_scope == '1' ? '零售服务' : getGoodsInfo.shelf_scope == '2' ? '问诊治疗' : '-' }}
            </span>
            <span v-else>零售服务 问诊治疗</span>
          </span>
        </div>
      </div>
      <div class="info-item" v-if="hasModifiedItem('is_recharge_buy')">
        <div class="item-label">储值购买：</div>
        <div class="item-value">
          <span>{{ isRechargeBuyDesc[getGoodsInfo.is_recharge_buy]?.desc }}</span>
        </div>
      </div>
      <div class="info-item" v-if="hasModifiedItem('sale_type')">
        <div class="item-label">销售方式：</div>
        <div class="item-value">
          <span>{{ getGoodsInfo.sale_type_text || '-' }}</span>
        </div>
      </div>
      <div class="info-item" v-if="getGoodsInfo.goods_type === '10' && hasModifiedItem('is_drop_shopping')">
        <div class="item-label">一件代发：</div>
        <div class="item-value">
          <span>{{ getGoodsInfo.is_drop_shopping === '1' ? '支持平台一件代发' : '不支持平台一件代发' }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RenderHeader from '@/mixins/renderHeader';
import PhysicalLog from './PhysicalLog.vue';
import VirtualLog from './VirtualLog.vue';
import PackageLog from './PackageLog.vue';
import CouponsLog from './CouponsLog.vue';

export default {
  name: 'LogInfo',
  components: {
    PhysicalLog,
    VirtualLog,
    PackageLog,
    CouponsLog,
  },
  mixins: [RenderHeader],
  props: {
    isBefore: {
      type: Boolean,
      default: true,
    },
    getGoodsInfo: {
      type: Object,
      default: () => {},
    },
    hasModifiedItem: {
      type: Function,
      required: true,
    },
    sellEntType: {
      type: Object,
      default: () => {},
    },
    isRechargeBuyDesc: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      goodsTypes: {
        10: { desc: '实物商品', componentName: 'PhysicalLog' },
        15: { desc: '虚拟商品', componentName: 'VirtualLog' },
        20: { desc: '套餐商品', componentName: 'PackageLog' },
        25: { desc: '通兑券商品', componentName: 'CouponsLog' },

        40: { desc: '实物商品', componentName: 'PhysicalLog' },
        30: { desc: '虚拟商品', componentName: 'VirtualLog' },
        35: { desc: '套餐商品', componentName: 'PackageLog' },
        45: { desc: '通兑券商品', componentName: 'CouponsLog' },
      },
      goodsCols: [
        { title: '价格(元)', key: 'price', align: 'center', minWidth: 50 },
        {
          title: '储值价',
          key: 'stored_price',
          align: 'center',
          minWidth: 80,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '商品储值价指使用储值余额购买时可享受的优惠价，不设置即无优惠价'),
        },
        { title: '编码', key: 'sku', align: 'center', minWidth: 50 },
        { title: '货品', slot: 'generic_name', align: 'left', minWidth: 50 },
        { title: '售卖单位', slot: 'sales_unit', align: 'center', minWidth: 50 },
        { title: '单位售价(元)', slot: 'price', align: 'center', minWidth: 50 },
        {
          title: '起购倍数',
          slot: 'buy_multiples',
          align: 'center',
          minWidth: 50,
          className: 'table-col-right-border',
        },
      ],
      tableData: [],
      specsData: [],
    };
  },
  computed: {
    getGoodsType() {
      console.log('-> %c this.getGoodsInfo  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.getGoodsInfo);
      return this.getGoodsInfo.goods_type || '15';
    },
    getSellEntType() {
      const sell_ent_type = this.getGoodsInfo.sell_ent_type;
      if (sell_ent_type && sell_ent_type.length) {
        return sell_ent_type.map(item => this.sellEntType[item].desc).join('、');
      }
    },
    showBaseTitle() {
      return (
        this.hasModifiedItem('name') ||
        this.hasModifiedItem('slide_imgs') ||
        this.hasModifiedItem('detail_imgs') ||
        this.hasModifiedItem('services')
      );
    },
  },
};
</script>

<style scoped lang="less">
.log-title {
  font-size: 14px;
  color: #666666;
  line-height: 18px;

  .title-txt {
    padding: 6px 16px 6px 10px;
    background: #f3f3f3;
    clip-path: polygon(0 0, 100% 0%, 90% 100%, 0% 100%);
  }

  .trapezoid-box {
    background: #f3f3f3;
    clip-path: polygon(40% 0, 100% 0, 60% 100%, 0% 100%);
    width: 20px;

    &:last-child {
      width: 16px;
      clip-path: polygon(50% 0, 100% 0, 50% 100%, 0% 100%);
    }
  }
}

.log-box-after {
  .log-title {
    color: #0061ee;
  }

  .title-txt,
  .trapezoid-box {
    background: #eff5fe;
  }
}
</style>
<style scoped lang="less">
.align-start {
  align-self: flex-start;
}

.info-item {
  margin: 10px 0;
  display: flex;
  align-items: center;

  .item-label {
    width: 100px;
    text-align: right;
    margin-right: 8px;
    font-size: 12px;
    color: #999999;
    line-height: 16px;
  }

  .item-value {
    flex: 1;
    line-height: 16px;
    display: inline-flex;

    span {
      display: inline-block;
    }
  }

  .base-changed {
    font-size: 12px;
    color: #999999;

    .img-box {
      background: #e4ebf9;
      padding: 4px 6px;
    }

    span {
      background: #e4ebf9;
      border-radius: 2px;
      padding: 6px 10px;
    }
  }
}

.block-title {
  line-height: 32px;
  background: #f7f7f7;
  text-indent: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #999999;
  margin-bottom: 16px;
  margin-top: 20px;
}
</style>
