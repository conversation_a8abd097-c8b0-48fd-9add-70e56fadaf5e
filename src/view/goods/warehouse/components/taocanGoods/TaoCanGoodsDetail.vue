<template>
  <div>
    <FormItem label="设置套餐">
      <table v-if="echoData.tc_infos?.length > 0" class="table" style="width: 1000px">
        <thead>
          <tr>
            <th>ID</th>
            <th>商品名</th>
            <th>类型</th>
            <th>规格</th>
            <th>价格</th>
            <th>数量</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, key) in echoData?.tc_infos" :key="key">
            <td>{{ item.goods?.id }}</td>
            <td>{{ item.name }}</td>
            <td>{{ item.goods_type_text }}</td>
            <td>{{ item.goods_type === '10' || item.goods_type == '40' ? item.spec : '-' }}</td>
            <td>
              <!-- <template v-if="item.goods_type === '40'">
                <div>
                  <span>销售价：{{ getPyGoodsPrice(item).first_tier_price || '0.00' }}</span>
                  <span style="margin-left: 8px">会员价：{{ getPyGoodsPrice(item).first_tier_vip_price || '0.00' }}</span>
                </div>
                <div>
                  <span>非一线：{{ getPyGoodsPrice(item).not_first_tier_price || '0.00' }}</span>
                  <span style="margin-left: 8px">会员价：{{ getPyGoodsPrice(item).not_first_tier_vip_price || '0.00' }}</span>
                </div>
              </template> -->
              <div v-if="!isRst">{{ item.price | number_format }}</div>
              <div v-else>
                <div>销售价：¥{{ item.price | number_format }}</div>
                <div>会员价：¥{{ item.vip_price | number_format }}</div>
              </div>
            </td>
            <td>{{ item.num }}</td>
          </tr>
        </tbody>
      </table>
    </FormItem>

    <div class="block-header"><span>价格库存</span></div>

    <FormItem label="价格">
      <p v-if="!isRst" v-text-format.number="echoData.price"></p>
      <div v-else class="flex gap-10">
        <div>销售价：¥{{ echoData.price | number_format }}</div>
        <div>会员价：¥{{ echoData.vip_price | number_format }}</div>
      </div>
    </FormItem>

    <FormItem label="储值价" v-show="echoData.is_recharge_buy == 'yes' && !is_rst">
      <p v-if="echoData.stored_price" v-text-format.number="echoData.stored_price"></p>
      <p v-else>-</p>
    </FormItem>
    <FormItem label="库存" v-if="isDetailPage">
      <p>{{ echoData.stock || '-' }}</p>
    </FormItem>

    <div v-if="!is_rst">
      <div class="block-header"><span>关联赠送服务卡券</span></div>
      <KWidget label="是否关联" required class="flex-item-align">
        <Radio-group v-model="relation_card">
          <Radio disabled :label="item.id" v-for="(item, index) in relationList" :key="'stored' + index">{{
            item.desc
          }}</Radio>
        </Radio-group>
      </KWidget>

      <KWidget label="发放方式" v-if="relation_card == 1" required class="flex-item-align">
        <Radio-group v-model="grant_type">
          <Radio disabled :label="item.id" v-for="(item, index) in grantList" :key="'stored' + index">{{
            item.desc
          }}</Radio>
        </Radio-group>
      </KWidget>

      <KWidget label="" v-if="relation_card == 1">
        <table class="table" style="width: 500px">
          <thead>
            <tr>
              <th>服务</th>
              <th>类型</th>
              <th>来源</th>
              <th>发放数量</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, key) in services" :key="key">
              <td>{{ item.name }}</td>
              <td>{{ item.serv_type_text }}</td>
              <td>{{ item.source_platform_text }}</td>
              <td>
                <span style="width: 100px" v-if="grant_type == 1">{{ item.times }}</span>
                <div v-else>-</div>
              </td>
              <td>-</td>
            </tr>
          </tbody>
        </table>
      </KWidget>
    </div>

    <div class="block-header"><span>服务设置</span></div>
  </div>
</template>

<script>
import S from '@/libs/util';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'TaoCanGoodsDetail',
  mixins: [],
  components: {},
  props: {
    echoData: {
      type: Object,
      default: () => {},
    },
    is_rst: Boolean,
    isDetailPage: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      services: [],
      relation_card: '',
      grant_type: '',
      relationList: [
        { id: '0', desc: '不关联' },
        { id: '1', desc: '关联' },
      ],
      grantList: [
        { id: '1', desc: '购买时发放' },
        { id: '2', desc: '订单交易后按需发放' },
      ],
    };
  },
  watch: {
    echoData: {
      handler(val) {
        if (S.isEmptyObject(val)) return;
        this.services = cloneDeep(val.services || []);
        this.relation_card = cloneDeep(val.relation_card || '');
        this.grant_type = cloneDeep(val.grant_type || '');
      },
      immediate: true,
    },
  },
  computed: {
    isRst() {
      return this.is_rst;
    },
    getPyGoodsPrice() {
      return item => {
        return item?.goods.attrs?.[item.id] || {};
      };
    },
  },
  mounted() {},
  methods: {},
};
</script>

<style lang="less" scoped></style>
