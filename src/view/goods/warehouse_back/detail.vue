<template>
  <div class="goods-item-wrapper" v-if="pageShow">
    <Row>
      <Col span="24">
        <Form ref="formData" :model="formData" :label-width="130" :label-colon="true">
          <div class="block-header"><span>基本信息</span></div>
          <FormItem label="商品名">
            <p>{{ formData.name }}</p>
          </FormItem>

          <FormItem label="商品类型">
            <p>{{ typeDesc[formData.goods_type].desc }}</p>
          </FormItem>

          <FormItem label="售卖主体">
            <p>{{ sellEntType[formData.sell_ent_type].desc }}</p>
          </FormItem>

          <FormItem label="商品图">
            <MaterialPicture v-model="formData.slide_imgs" :limit="0" :disabled="true" style="height: 65px" />
            <div class="note">建议尺寸：800*800像素，图片大小不超过3.0M，最多9张，你可以拖拽图片调整顺序</div>
          </FormItem>

          <FormItem label="商品详情" prop="leading_person">
            <MaterialPicture v-model="formData.detail_imgs" :limit="0" :disabled="true" style="height: 65px" />
            <div class="note">图片大小不超过3.0M，最多30张，你可以拖拽图片调整顺序</div>
          </FormItem>

          <template v-if="formData.goods_type == 10">
            <div class="block-header"><span>价格库存</span></div>
            <FormItem label="规格明细">
              <Table :columns="columns" :data="tableData" :span-method="handleSpan" disabled-hover>
                <template v-for="(item, index) in specsData" :slot="item.name + index" slot-scope="{ row }">
                  {{ row.specs_atoms[item.name] }}
                </template>

                <!--                诊所货品-->
                <template slot-scope="{ row }" slot="generic_name">
                  <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item.generic_name }}</div>
                </template>
                <!--                售卖单位-->
                <template slot-scope="{ row }" slot="sales_unit">
                  <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item._sales_unit }}</div>
                </template>
                <template slot-scope="{ row }" slot="price">
                  <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item._price }}</div>
                </template>
                <template slot-scope="{ row }" slot="buy_multiples">
                  <div v-for="item in row.relate_his_prods" :key="item.prod_id">{{ item.buy_multiples }}</div>
                </template>
              </Table>
            </FormItem>

            <FormItem label="上架范围">
              <p v-if="formData.shelf_scope !== '9'">
                {{ formData.shelf_scope == '1' ? '零售服务' : formData.shelf_scope == '2' ? '问诊治疗' : '-' }}
              </p>
              <p v-else>零售服务 问诊治疗</p>
            </FormItem>

            <FormItem label="储值购买">
              <p>{{ isRechargeBuyDesc[formData.is_recharge_buy].desc }}</p>
            </FormItem>
          </template>

          <template v-if="formData.goods_type == 15">
            <FormItem label="服务权益">
              <table v-if="formData.services.length > 0" class="table" style="width: 500px">
                <thead>
                  <tr>
                    <th>已选服务</th>
                    <th>价格</th>
                    <th>可用次数</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="(item, key) in formData.services" :key="key">
                    <td>{{ item.name }}</td>
                    <td>{{ item.price | number_format }}</td>
                    <td>{{ item.times }}</td>
                  </tr>
                </tbody>
              </table>
            </FormItem>
            <div class="block-header"><span>价格库存</span></div>

            <FormItem label="价格">
              <p>{{ formData.price }}元</p>
            </FormItem>
            <FormItem label="储值价" v-show="formData.is_recharge_buy == 'yes'">
              <p v-if="formData.stored_price">{{ formData.stored_price }}元</p>
              <p v-else>-</p>
            </FormItem>

            <div class="block-header"><span>服务设置</span></div>

            <FormItem label="商品有效期">
              <p v-if="formData.service_info.expiration.type == '1'">
                购买后{{ formData.service_info.expiration.days }}天内有效
              </p>
              <p v-if="formData.service_info.expiration.type == '2'">
                截止到{{ formData.service_info.expiration.date }}日有效
              </p>
            </FormItem>

            <FormItem label="是否可约">
              <p>{{ formData.service_info.is_appointment == '1' ? '需要预约' : '免预约' }}</p>
            </FormItem>

            <FormItem label="是否可退">
              <p>{{ formData.service_info.is_can_refund == '1' ? '可退' : '不可退' }}</p>
            </FormItem>

            <FormItem label="使用时间">
              <p>{{ formData.service_info.working_time }}</p>
            </FormItem>

            <FormItem label="不可使用日期">
              <p>{{ formData.service_info.not_working_date }}</p>
            </FormItem>

            <FormItem label="上架范围">
              <p v-if="formData.xn_scope !== '9'">
                {{ formData.xn_scope == '1' ? '零售服务' : formData.xn_scope == '2' ? '问诊治疗' : '-' }}
              </p>
              <p v-else>零售服务 问诊治疗</p>
            </FormItem>

            <FormItem label="储值购买">
              <p>{{ isRechargeBuyDesc[formData.is_recharge_buy].desc }}</p>
            </FormItem>
          </template>

          <div class="block-header"><span>修改记录</span></div>
          <Table :columns="changeColumns" :data="changeRecord">
            <template slot="create_time" slot-scope="{ row }">
              {{ row.create_time | data_format }}
            </template>
            <template slot="operator_name" slot-scope="{ row }">
              {{ row.operator_name }}({{ row.operator_role_name }})
            </template>
            <template slot="content_describe" slot-scope="{ row }">
              <div v-if="row.exist_snapshot === '0'">{{ row.content_describe }}</div>
              <div v-else>
                <span>{{ row.content_describe }}修改了商品的规格明细</span> <a @click="lookUp(row.id)">查看更改明细</a>
              </div>
            </template>
          </Table>
        </Form>
      </Col>
    </Row>

    <div class="block_40"></div>
    <div class="block_40"></div>
    <div class="block_40"></div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
    </div>

    <KGoodsService v-model="stateManager.serviceModal" @on-selected="onSelectedService" />
    <log-change v-model="logVisible" :diff-id="diff_id"></log-change>
  </div>
  <div v-else>
    <Spin fix></Spin>
  </div>
</template>

<script>
import S from '@/libs/util'; // Some commonly used tools
import moment from 'moment';
import renderHeader from '@/mixins/renderHeader';

import KGoodsService from '@/components/k-goods-services';
import LogChange from './components/log-change';
import cloneDeep from 'lodash/cloneDeep';

let init_form_data = {
  id: '',
  name: '',
  goods_type: 10,
  sell_ent_type: '',
  desc: '',
  slide_imgs: [],
  detail_imgs: [],
  specs_data: [],
  attrs: [],
  price: 0,
  is_recharge_buy: 'yes', // 虚拟服务的储值购买
  stored_price: null, // 虚拟服务的储值价
  services: [],
  xn_scope_tmp: ['1', '2'],
  xn_scope: '',
  sw_scope_tmp: ['1', '2'],
  shelf_scope: '',
  r: '',
};

export default {
  name: 'edit',
  components: {
    KGoodsService,
    LogChange,
  },
  mixins: [renderHeader],

  data() {
    return {
      pageShow: false,
      formData: {
        service_info: {
          expiration: {
            type: '1', // 1 购买后${days}天内有效；2 ${date}日期前有效；
            date: '', //
            days: 365, //
          },
          is_appointment: '1', // 1免预约 2需要预约
          is_can_refund: '1', // 1不可退 2可退
          working_time: '', // 工作时间
          not_working_date: '', // 不可使用日期：
          note_rule: '', // 使用规则
        },
        ...cloneDeep(init_form_data),
      },
      getLoading: true,

      specs_attrs: [[], []],

      stateManager: {
        serviceModal: false,
        saveBtnLoading: false, // 保存按钮状态
        publishBtnLoading: false, // 发布按钮状态
      },

      storedList: [], // 储值购买枚举
      isRechargeBuyDesc: {},
      sellEntType: {},
      typeDesc: {},
      xnScopeDesc: [],
      specsData: [],

      expiration_datepicker_options: {
        disabledDate(date) {
          return S.moment(date).unix() <= S.moment(new Date()).unix();
        },
      },

      columns: [
        { title: '价格(元)', key: 'price', align: 'center', minWidth: 50 },
        {
          title: '储值价',
          key: 'stored_price',
          align: 'center',
          minWidth: 80,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '商品储值价指使用储值余额购买时可享受的优惠价，不设置即无优惠价'),
        },
        { title: '编码', key: 'sku', align: 'center', minWidth: 50 },
        { title: '诊所货品', key: 'goods_type_text', minWidth: 50 },
        { title: '售卖单位', key: 'goods_type_text', minWidth: 50 },
        { title: '单位售价(元)', key: 'goods_type_text', minWidth: 50 },
        { title: '起购倍数', key: 'goods_type_text', minWidth: 50 },
      ],
      columnsRecharge: [
        { title: '价格(元)', key: 'price', align: 'center', minWidth: 50 },
        {
          title: '储值价',
          key: 'stored_price',
          align: 'center',
          minWidth: 80,
          renderHeader: (h, params) =>
            this._renderHeader(h, params, '商品储值价指使用储值余额购买时可享受的优惠价，不设置即无优惠价'),
        },
        { title: '编码', key: 'sku', align: 'center', minWidth: 50 },
        { title: '诊所货品', slot: 'generic_name', align: 'center', minWidth: 50 },
        { title: '售卖单位', slot: 'sales_unit', align: 'center', minWidth: 50 },
        { title: '单位售价(元)', slot: 'price', align: 'center', minWidth: 50 },
        {
          title: '起购倍数',
          slot: 'buy_multiples',
          align: 'center',
          minWidth: 50,
          className: 'table-col-right-border',
        },
      ],
      columnsNoRecharge: [
        { title: '价格(元)', key: 'id', align: 'center', minWidth: 50 },
        { title: '编码', key: 'sell_ent_type_text', align: 'center', minWidth: 50 },
        { title: '诊所货品', slot: 'generic_name', align: 'center', minWidth: 50 },
        { title: '售卖单位', slot: 'sales_unit', align: 'center', minWidth: 50 },
        { title: '单位售价(元)', slot: 'price', align: 'center', minWidth: 50 },
        {
          title: '起购倍数',
          slot: 'buy_multiples',
          align: 'center',
          minWidth: 50,
          className: 'table-col-right-border',
        },
      ],
      tableData: [],

      changeColumns: [
        { title: '时间', slot: 'create_time', align: 'center', width: 140 },
        { title: '操作人', slot: 'operator_name', align: 'center', width: 100 },
        { title: '操作记录', slot: 'content_describe' },
      ],
      changeRecord: [],
      source_platform: '',
      logVisible: false, // 日志变更弹框
      diff_id: '',
    };
  },

  created() {
    this.getIndexOptions();

    if (this.$route.query.id) {
      // 编辑时才有的参数
      this.formData.id = this.$route.query.id;
    }

    if (this.formData.id) {
      this.get();
    } else {
      this.pageShow = true;
    }
  },
  mounted() {},
  methods: {
    onSelectedService: function (items) {
      console.log('-> %c items  === %o ', 'font-size: 15px', items);
      console.log(this.formData.services);
      let goodsIds = this.formData.services.map(item => item.id);
      items.forEach(item => {
        if (goodsIds.includes(item.id)) {
          this.$Message.error(`服务${item.name}已存在,请勿重复添加`);
        } else {
          this.formData.services.push({
            id: item.id,
            name: item.name,
            price: item.price,
            times: 1,
          });
        }
      });
      this.stateManager.serviceModal = false;
    },

    get: function () {
      // let id = this.formData.id
      let params = { id: this.formData.id };
      this.$api
        .getGoodsLibInfo(params)
        .then(data => {
          let goods = data.goods;
          this.formData.id = goods.id;
          this.formData.name = goods.name;
          this.formData.desc = goods.desc;
          this.formData.sell_ent_type = goods.sell_ent_type;
          this.formData.is_recharge_buy = goods.is_recharge_buy;
          this.formData.price = Number(goods.price);
          this.formData.stored_price = goods.stored_price == '' ? null : Number(goods.stored_price);
          this.formData.stock = Number(goods.stock);
          this.formData.goods_type = goods.goods_type;
          this.formData.detail_imgs = goods.detail_imgs;
          this.formData.slide_imgs = goods.slide_imgs;
          this.formData.specs_data = goods.specs_data;
          this.formData.attrs = goods.attrs;
          this.specs_attrs = [this.formData.specs_data, this.formData.attrs];
          for (let key in goods.services ? goods.services : []) {
            goods.services[key].times = Number(goods.services[key].times);
          }
          this.formData.services = goods.services;
          this.formData.service_info = !S.isEmptyObject(goods.service_info)
            ? goods.service_info
            : this.formData.service_info;
          this.formData.service_info.expiration.days = Number(this.formData.service_info.expiration.days);
          this.formData.service_info.expiration.date =
            this.formData.service_info.expiration.date === ''
              ? '-'
              : moment(this.formData.service_info.expiration.date).format('YYYY-MM-DD');
          this.formData.xn_scope_tmp = goods.xn_scope == '9' ? ['1', '2'] : [goods.xn_scope];
          this.formData.sw_scope_tmp = goods.shelf_scope == '9' ? ['1', '2'] : [goods.shelf_scope];
          this.formData.shelf_scope = goods.shelf_scope;
          this.formData.xn_scope = goods.xn_scope;
          this.specsData = goods.specs_data;
          this.tableData = goods.attrs;
          if (this.formData.is_recharge_buy === 'yes') {
            this.columns = this.columnsRecharge;
          } else {
            this.columns = this.columnsNoRecharge;
          }
          console.log('=>(detail.vue:344) goods.source_platform', goods.source_platform);
          if (goods.source_platform === 'PLAT') {
            this.source_platform = 'PLAT_GOODS';
          } else if (goods.source_platform === 'COM') {
            this.source_platform = 'COM_GOODS';
          }
          // 获取修改记录
          this.getGoodsOperationlog();
          // 处理表格规格
          this.tableSpecs();

          // 处理关联货品数据
          this.handleRelateProd();

          this.pageShow = true;
        })
        .catch(error => {});
    },

    // 储值购买枚举值
    getIndexOptions() {
      this.$api.getGoodsLibOptions().then(res => {
        this.storedList = S.descToArrHandle(res.isRechargeBuyDesc);
        this.isRechargeBuyDesc = res.isRechargeBuyDesc;
        this.sellEntType = res.sellEntType;
        this.typeDesc = res.typeDesc;
        this.xnScopeDesc = res.xnScopeDesc;
      });
    },

    goBack() {
      this.$router.back();
    },

    tableSpecs() {
      this.tableData.forEach(item => {
        item.specsAtoms = Object.values(item.specs_atoms);
      });
      this.integratedData(this.tableData);
      let arr = this.$lodash.cloneDeep(this.specsData);
      arr.forEach((item, index) => {
        if (index === 0) {
          item.className = 'table-col-left-border';
        }
        if (index < arr.length - 1) {
          if (index === 0) {
            item.className = 'table-col-border';
          } else {
            item.className = 'table-col-right-border';
          }
        }
        item.title = item.name;
        item.slot = item.name + index;
        item.minWidth = 40;
        item.align = 'center';
      });
      console.log(arr);
      let newArr = arr.concat(this.columns);
      this.columns = newArr;
    },

    handleSpan({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        let x = row.mergeFirstCol === 0 ? 0 : row.mergeFirstCol;
        let y = row.mergeFirstCol === 0 ? 0 : 1;
        return [x, y];
      }
      if (columnIndex === 1) {
        //计算合并的行数列数
        let x = row.mergeSecCol === 0 ? 0 : row.mergeSecCol;
        let y = row.mergeSecCol === 0 ? 0 : 1;
        return [x, y];
      }
    },

    // 合并单元格
    integratedData(data) {
      // 处理第一列规格
      for (let index = 0; index < data.length; index++) {
        if (data[index].firstAlready !== 1) {
          if (index + 1) {
            data[index].mergeFirstCol = 1;
            for (let idx = index + 1; idx < data.length; idx++) {
              if (data[index].specs_atoms[this.specsData[0].name] === data[idx].specs_atoms[this.specsData[0].name]) {
                data[index].mergeFirstCol++;
                data[idx].mergeFirstCol = 0;
                data[idx].firstAlready = 1;
              } else {
                break;
              }
            }
          }
        }
      }

      // 处理第二列规格
      for (let index = 0; index < data.length; index++) {
        if (data[index].mergeFirstCol > 1) {
          for (let idx = 0; idx < data[index].mergeFirstCol; idx++) {
            if (data[index + idx].secondAlready !== 1) {
              if (idx + 1 < data[index].mergeFirstCol) {
                data[index + idx].mergeSecCol = 1;
                for (let i = idx + 1; i < data[index].mergeFirstCol; i++) {
                  if (
                    data[index + idx].specs_atoms[this.specsData[1].name] ===
                    data[index + i].specs_atoms[this.specsData[1].name]
                  ) {
                    data[index + idx].mergeSecCol++;
                    data[index + i].mergeSecCol = 0;
                    data[index + i].secondAlready = 1;
                  } else {
                    break;
                  }
                }
              }
            }
          }
        }
      }
      console.log('=>(detail.vue:450) data', data);

      this.tableData = data;
    },

    // 关联货品
    handleRelateProd() {
      this.tableData.forEach(item => {
        item.relate_his_prods.forEach(prods => {
          prods._sales_unit = this.setDefaultUnit(prods).unit || '';
          // prods._stock_num = this.setDefaultUnit(prods).stock_num || ''
          prods._price = this.setDefaultUnit(prods).price || '';
        });
      });
    },

    // 如果货单单位有选中的，默认带出单位，库存，单位售价
    setDefaultUnit(item) {
      // if ( !item.length ) return {}
      let resultList = item.sales_units.filter(item => item.checked == '1');
      return resultList[0] || {};
    },

    rowClassName(row, index) {
      console.log('=>(detail.vue:490) row', row);
      if (row === 0) {
        return 'ivu-table-first-col';
      }
    },

    // 获取操作记录
    getGoodsOperationlog() {
      let params = { type_id: this.$route.query.id, type: this.source_platform, page: 1, pageSize: 100 };
      this.$api
        .getGoodsOperationlog(params)
        .then(res => {
          this.changeRecord = res.list;
        })
        .catch(error => {
          {
          }
        });
    },

    lookUp(id) {
      this.logVisible = true;
      this.diff_id = id;
    },
  },
};
</script>

<style lang="less" scoped>
.goods-item-wrapper {
  .ivu-input-wrapper,
  .ivu-select {
    width: 80%;
    max-width: none;
  }

  .ivu-input {
    max-width: none;
  }

  .ks-goods-type {
    position: relative;
    display: inline-block;
    cursor: pointer;
    margin: 0 0 0 10px;
    text-align: center;
    width: 115px;
    border-radius: 2px;
    border: 1px solid #cacaca;
    padding: 8px 0;
  }

  .ks-goods-type.active {
    border-color: #155bd4;
  }

  .ks-goods-type.active:after {
    content: '';
    display: inline-block;
    position: absolute;
    width: 24px;
    height: 24px;
    bottom: -1px;
    right: -1px;
    background: url(https://img01.biranmall.com/image/2021/0224/155801_7284881.png) no-repeat;
    background-size: 24px auto;
  }

  .ks-goods-type_name {
    display: block;
    font-weight: bold;
  }

  .ks-goods-type_desc {
    display: block;
    color: #999;
  }

  .ks-input-number {
    width: 100px;

    .ivu-input-number-input {
      text-align: center;
    }
  }

  .ivu-date-picker-editor {
    width: 100% !important;
  }
}

::v-deep .picture-display {
  display: inline-block;
}

::v-deep .ivu-form-item {
  margin-bottom: 10px;

  // .ivu-form-item-label {
  //   width: 170px !important;
  // }

  // .ivu-form-item-content {
  //   margin-left: 170px !important;
  // }
}

.ivu-table-first-col {
  border-left: 1px solid #e8eaec;
  border-right: 1px solid #e8eaec;
}
</style>
<style>
.table-col-right-border {
  border-right: 1px solid #e8eaec;
}

.table-col-border {
  border-right: 1px solid #e8eaec;
  border-left: 1px solid #e8eaec;
}

.table-col-left-border {
  border-left: 1px solid #e8eaec;
}
</style>
