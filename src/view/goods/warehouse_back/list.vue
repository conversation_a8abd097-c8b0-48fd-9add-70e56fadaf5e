<template>
  <div>
    <div>
      <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
        <Row>
          <Col>
            <FormItem>
              <Input type="text" v-model="queryFormData.name" placeholder="请输入商品名称" />
            </FormItem>
          </Col>

          <Col>
            <FormItem>
              <Select v-model="queryFormData.goods_type" placeholder="请选择商品类型" clearable>
                <Option v-for="item in typeDesc" :key="item.kw" :value="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>

          <Col>
            <FormItem>
              <Select v-model="queryFormData.is_add" placeholder="请选择商品状态" clearable>
                <Option v-for="item in isAddDesc" :key="item.kw" :value="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>

          <Col>
            <FormItem>
              <Select v-model="queryFormData.source_platform" placeholder="请选择商品来源" clearable>
                <Option v-for="item in sourcePlatformDesc" :key="item.kw" :value="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>

          <Col>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="onResetSearch">
                <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                <span>清除条件</span>
              </span>
            </FormItem>
          </Col>
        </Row>
      </Form>

      <div class="table-wrapper">
        <Table
          :loading="tableLoading"
          ref="warehouseTable"
          :columns="tableCols"
          :data="list"
          :height="$store.state.app.clientHeight - 230"
          @on-select="handleSelect"
          @on-select-cancel="handleSelectCancel"
          @on-select-all="handleSelectAll"
          @on-select-all-cancel="handleCancelAll"
        >
          <template slot-scope="{ row }" slot="info">
            <div class="media-left media-middle">
              <img :src="row.main_img | imageStyle" style="width: 35px; margin-right: 5px" class="img-rounded" />
            </div>
            <div class="media-body media-middle">
              <div class="_3U0W">
                <KLink :to="{ path: '/goods/warehouse/edit', query: { id: row.id } }">{{ row.name }}</KLink>
              </div>
            </div>
          </template>
          <template slot-scope="{ row }" slot="price_section">
            <p v-if="!row.price_section.max">￥{{ row.price_section.min }}</p>
            <p v-else>￥{{ row.price_section.min }} - ￥{{ row.price_section.max }}</p>
          </template>

          <!-- 储值价 -->
          <template slot-scope="{ row }" slot="stored_price_section">
            <p v-if="!row.stored_price || row.is_recharge_buy === 'no'">-</p>
            <template v-else>
              <p v-if="!row.stored_price_section.max">￥{{ row.stored_price_section.min }}</p>
              <p v-else>￥{{ row.stored_price_section.min }} - ￥{{ row.stored_price_section.max }}</p>
            </template>
          </template>

          <template slot-scope="{ row }" slot="stock">
            {{ row.stock }}
          </template>
          <template slot-scope="{ row }" slot="sales">
            {{ row.sales }}
          </template>
          <template slot-scope="{ row }" slot="create_time">
            {{ row.create_time | data_format }}
          </template>
          <template slot-scope="{ row }" slot="status">
            {{ statusDesc[row.status].desc }}
          </template>
          <template slot-scope="{ row }" slot="operate">
            <!--            todo 后端调整后修改为is_add-->
            <div v-if="row.is_add_text === '未添加'">
              <Poptip confirm transfer title="确定添加到售卖列表？" @on-ok="addBatch(row)">
                <a>添加到售卖列表</a>
              </Poptip>
              <Divider type="vertical" />
              <KLink :to="{ path: '/goods/warehouse/detail', query: { id: row.id } }">详情</KLink>
            </div>
            <div v-else>-</div>
          </template>
        </Table>
      </div>

      <div class="block_20"></div>
      <div class="batch">
        <div class="batch_l flex">
          <!--        <Button @click="addBatch">批量添加至售卖列表</Button>-->
          <span class="ml10 batch-check"
            >已选中 <span class="num-color">{{ selectLen }}</span> 个商品</span
          >
          <div class="action-box">
            <Dvd />
            <Dvd />
            <Dvd />
            <Button type="primary" size="small" @click="SelectAll">全选</Button>
            <Dvd />
            <Button type="primary" size="small" @click="cancelSelectAll">重置</Button>
            <Dvd />
            <Button type="primary" size="small" :disabled="selectLen === 0" @click="confirmBatch"> 批量添加 </Button>
          </div>
        </div>
        <KPage
          :current="+queryFormData.page"
          :page-size="+queryFormData.pageSize"
          :total="total"
          style="text-align: center"
          @on-change="onPageChange"
        />
      </div>
    </div>
    <Spin fix v-if="batchLoading"></Spin>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from 'libs/util';
import renderHeader from '@/mixins/renderHeader';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  goods_type: '',
  sellEntType: '',
  is_add: '',
  source_platform: '',
  r: '',
};
export default {
  name: 'list',
  components: {},
  mixins: [search, renderHeader],
  props: {},
  data() {
    return {
      apiName: 'getGoodsLibList',
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { type: 'selection', minWidth: 30 },
        { title: 'ID', key: 'id', minWidth: 50 },
        { title: '商品', key: 'name', minWidth: 80 },
        { title: '商品类型', key: 'goods_type_text', minWidth: 50 },
        { title: '商品来源', key: 'source_platform_text', align: 'center', minWidth: 50 },
        { title: '价格', slot: 'price_section', align: 'center', minWidth: 100 },
        { title: '储值价', slot: 'stored_price_section', align: 'center', minWidth: 80 },
        { title: '同步时间', slot: 'create_time', minWidth: 120 },
        { title: '商品状态', key: 'is_add_text', minWidth: 50 },
        { title: '操作', slot: 'operate', align: 'center', width: 160 },
      ],
      tableLoading: false,

      list: [],
      total: 0,
      typeDesc: {},
      sellEntType: {},
      selectionList: [],
      selectObj: {},
      isAddDesc: [],
      sourcePlatformDesc: [],
      chunkItemsLength: 0,
      chunkIndex: 0,
      batchLoading: false,
    };
  },
  computed: {
    selectLen() {
      return Object.keys(this.selectObj).length;
    },
  },
  watch: {},
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    this.getOptionsList();
  },
  mounted() {},
  methods: {
    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    // 获取枚举值
    getOptionsList() {
      this.$api.getGoodsLibOptions().then(res => {
        this.typeDesc = S.descToArrHandle(res.typeDesc);
        this.sellEntType = S.descToArrHandle(res.sellEntType);
        this.isAddDesc = S.descToArrHandle(res.isAddDesc);
        this.sourcePlatformDesc = S.descToArrHandle(res.sourcePlatformDesc);
        console.log(
          '🚀 ~ file: list.vue ~ line 196 ~ this.$api.getGoodsLibOptions ~ this.sellEntType',
          this.sellEntType
        );
      });
    },

    onCopy(id) {
      let params = { id };
      this.$api
        .copyGoodsLib(params)
        .then(() => {
          this.$Message.success('复制成功');
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
        });
    },
    addBatchGoods(ids) {
      let params = { id_list: { list: ids } };
      this.$api
        .addBatchGoods(params)
        .then(res => {
          this.chunkIndex++;
          if (this.chunkIndex === this.chunkItemsLength) {
            this.getsList();
            this.$Message.success('添加商品成功');
            this.initBatch();
          } else {
            this.addBatch();
          }
        })
        .catch(error => {
          {
          }
        });
    },

    addBatch(row) {
      this.selectionList = this.getAuditOrderIds(row);
      // 轮询开始,loading开始
      this.batchLoading = true;
      // 切割数据
      let chunkItems = this.$lodash.chunk(this.selectionList, 20);
      this.chunkItemsLength = chunkItems.length;
      if (this.chunkIndex < +this.chunkItemsLength) {
        this.addBatchGoods(chunkItems[this.chunkIndex]);
      }
    },

    //选中项发生变化时就会触发
    handleSelect(selection, row) {
      this.$set(this.selectObj, row.id, row);
    },
    //选中项发生变化时就会触发
    handleSelectCancel(selection, row) {
      this.$delete(this.selectObj, row.id);
    },

    //点击全选时触发
    handleSelectAll(selection) {
      selection.forEach(item => {
        if (item._disabled === false) {
          this.$set(this.selectObj, item.id, item);
        }
      });
      console.log(this.selectObj);
    },
    //点击全选时触发
    handleCancelAll(selection) {
      console.log('=>(list.vue:250) 111', 111);
      for (let k in this.list) {
        if (this.list[k].id in this.selectObj) {
          this.$delete(this.selectObj, this.list[k].id);
        }
      }
      console.log('=>(list.vue:256) this.selectObj', this.selectObj);
    },

    SelectAll() {
      this.$refs.warehouseTable.selectAll(true);
      this.handleSelectAll(this.list);
    },
    cancelSelectAll() {
      this.$refs.warehouseTable.selectAll(false);
      this.selectObj = {};
    },

    confirmBatch() {
      this.$Modal.confirm({
        title: '批量添加',
        content: '您确定要将选中的商品添加到售卖列表吗？',
        onOk: () => {
          this.addBatch();
        },
      });
    },

    getAuditOrderIds(row) {
      if (row) {
        return [row.id];
      } else {
        return Object.values(this.selectObj).map(item => item.id);
      }
    },

    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(
          data => {
            this.total = +data.total;
            this.list = this.handler(data.list);
          },
          error => {}
        )
        .finally(() => {
          this.tableLoading = false;
        });
    },

    handler(list) {
      console.log('-> %c list  === %o', 'font-size: 15px;color: green;', list);
      list.forEach(item => {
        if (item.is_add_text === '已添加') {
          item._disabled = true;
        } else {
          item._disabled = false;
        }
      });
      for (let k in list) {
        for (let j in this.selectObj) {
          if (list[k].id == this.selectObj[j].id) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }
        // if (S.inArray(Number(list[k].id), this.disabledItemIds)) {
        //   list[k]['_disabled'] = true // 选中已选项
        // }
      }
      return list;
    },

    initBatch() {
      this.selectObj = {};
      this.chunkIndex = 0;
      this.chunkItemsLength = 0;
      this.selectionList = [];
      this.batchLoading = false;
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
  filters: {},
};
</script>

<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 16px;
  label {
    vertical-align: middle;
  }
}
.table-wrapper {
  .table-fun {
    padding-bottom: 10px;
  }
}
.ml10 {
  margin-left: 10px;
}

::v-deep p {
  margin: 0;
}

.batch {
  position: relative;
  .batch_l {
    position: absolute;
    top: 4px;
    .batch-check {
      line-height: 24px;
      .num-color {
        color: #1157e5;
        margin: 0 2px;
      }
    }
  }
}
</style>
