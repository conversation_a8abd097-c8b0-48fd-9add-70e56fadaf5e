.align-start {
  align-self: flex-start;
}

.info-item {
  margin: 10px 0;
  display: flex;
  align-items: center;

  .item-label {
    width: 100px;
    text-align: right;
    margin-right: 8px;
    font-size: 12px;
    color: #999999;
    line-height: 16px;
  }

  .item-value {
    flex: 1;
    line-height: 16px;
    display: inline-flex;

    span {
      display: inline-block;
    }
  }

  .base-changed {
    font-size: 12px;
    color: #999999;

    .img-box {
      background: #e4ebf9;
      padding: 4px 6px;
    }

    >span {
      background: #e4ebf9;
      border-radius: 2px;
      padding: 6px 10px;
    }
  }
}

.block-title {
  line-height: 40px;
  background: #f7f7f7;
  text-indent: 10px;
  font-size: 14px;
  font-weight: 500;
  color: #999999;
  margin-bottom: 16px;
}

.status-title {
  font-size: 28px;
  font-weight: 500;
  color: #d8d8d8;
  line-height: 34px;

  .update-tips {
    position: relative;
    background-color: #155bd4;
    padding: 4px 6px;
    font-size: 16px;
    color: #fff;
    border-radius: 4px;
    margin-left: 29px;
    line-height: 1;
    vertical-align: middle;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -27px;
      display: inline-block;
      clip-path: polygon(60% 50%, 100% 20%, 100% 75%);
      background: #155bd4;
      width: 28px;
      height: 28px;
    }
  }
}
