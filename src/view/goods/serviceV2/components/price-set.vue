<template>
  <div>
    <Form
      ref="formData"
      :model="formData"
      :rules="formDataRules"
      :disabled="disabled"
      style="max-width: 750px"
      label-colon
      :label-width="120"
    >
      <FormItem label="价格" prop="price">
        <div class="flex flex-item-align">
          <custom-input-number
            v-model="formData.price"
            :min="0.01"
            :precision="2"
            prefix="单次价"
            placeholder="请输入价格"
            style="min-width: 200px"
          />
          <custom-input-number
            v-model="formData.vip_price"
            :min="0.01"
            :precision="2"
            prefix="会员价"
            placeholder="请输入价格"
            style="margin-left: 20px; min-width: 200px"
          />
        </div>
      </FormItem>

      <FormItem label="分账策略" prop="share_price">
        <div class="flex flex-item-align">
          <div class="fit-content">促销实收价≥</div>
          <custom-input-number
            class="ml10 mr10"
            v-model="formData.single_price"
            :min="0.01"
            :precision="2"
            prefix="单次成本价"
            suffix="元"
            placeholder="请输入价格"
            style="min-width: 200px"
          />
          <div class="fit-content">服务提成按</div>
          <custom-input-number
            class="ml10 mr10"
            :isShowZero="true"
            v-model="formData.more_service_value"
            :min="0"
            :max="formData.more_service_type === 'ratio' ? 100 : formData.single_price"
            :precision="formData.more_service_type === 'ratio' ? 0 : 2"
            :suffix="formData.more_service_type === 'ratio' ? '%' : '元'"
            :placeholder="formData.more_service_type === 'ratio' ? '请输入比例' : '请输入价格'"
            prepend
            style="min-width: 220px"
          >
            <div slot="prepend">
              <Select v-model="formData.more_service_type" @on-change="typeChange($event, 'more_service_value')">
                <Option value="ratio">分账比例</Option>
                <Option value="fixed">固定金额</Option>
              </Select>
            </div>
          </custom-input-number>
          <div class="fit-content">分账，销售时按</div>
          <custom-input-number
            class="ml10 mr10"
            :isShowZero="true"
            v-model="formData.more_sale_value"
            :min="0"
            :max="formData.more_sale_type === 'ratio' ? 100 : formData.single_price"
            :precision="formData.more_sale_type === 'ratio' ? 0 : 2"
            :suffix="formData.more_sale_type === 'ratio' ? '%' : '元'"
            :placeholder="formData.more_sale_type === 'ratio' ? '请输入比例' : '请输入价格'"
            prepend
            style="min-width: 220px"
          >
            <div slot="prepend">
              <Select v-model="formData.more_sale_type" @on-change="typeChange($event, 'more_sale_value')">
                <Option value="ratio">分账比例</Option>
                <Option value="fixed">固定金额</Option>
              </Select>
            </div>
          </custom-input-number>
          <div class="fit-content">分账</div>
        </div>

        <div style="margin-top: 20px" class="flex flex-item-align">
          <div class="fit-content">促销实收价<</div>
          <custom-input-number
            class="ml10 mr10"
            v-model="formData.single_price"
            :min="0.01"
            :precision="2"
            prefix="单次成本价"
            suffix="元"
            placeholder="请输入价格"
            style="min-width: 200px"
          />
          <div class="fit-content">服务提成按</div>
          <custom-input-number
            class="ml10 mr10"
            :isShowZero="true"
            v-model="formData.less_service_value"
            :min="0"
            :max="formData.less_service_type === 'ratio' ? 100 : formData.single_price"
            :precision="formData.less_service_type === 'ratio' ? 0 : 2"
            :suffix="formData.less_service_type === 'ratio' ? '%' : '元'"
            :placeholder="formData.less_service_type === 'ratio' ? '请输入比例' : '请输入价格'"
            prepend
            style="min-width: 220px"
          >
            <div slot="prepend">
              <Select v-model="formData.less_service_type" @on-change="typeChange($event, 'less_service_value')">
                <Option value="ratio">分账比例</Option>
                <Option value="fixed">固定金额</Option>
              </Select>
            </div>
          </custom-input-number>
          <div class="fit-content">分账，同时不再参加销售分账</div>
        </div>
      </FormItem>
    </Form>
  </div>
</template>

<script>
import CustomInputNumber from '@/components/CustomInputNumber/index.vue';
import { cloneDeep, isEqual } from 'lodash';
export default {
  name: 'price-set',
  components: { CustomInputNumber },
  mixins: [],
  props: {
    value: {
      type: Object,
      default: () => {},
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    // 校验一线城市的单次价和会员价
    const validFirstPrice = (rule, value, callback) => {
      let price = Number(this.formData.price || 0);
      let vip_price = Number(this.formData.vip_price || 0);
      if (price === 0) {
        callback(new Error('请输入大于0的单次价'));
      } else if (vip_price === 0) {
        callback(new Error('请输入大于0的会员价'));
      } else if (vip_price > price) {
        callback(new Error('会员价不能大于单次价'));
      } else {
        callback();
      }
    };

    // 校验非一线城市的单次价和会员价
    const validOtherPrice = (rule, value, callback) => {
      let other_price = Number(this.formData.other_price || 0);
      let other_vip_price = Number(this.formData.other_vip_price || 0);
      if (other_price === 0) {
        callback(new Error('请输入大于0的单次价'));
      } else if (other_vip_price === 0) {
        callback(new Error('请输入大于0的会员价'));
      } else if (other_vip_price > other_price) {
        callback(new Error('会员价不能大于单次价'));
      } else {
        callback();
      }
    };

    // 校验分账策略
    const validSharePrice = (rule, value, callback) => {
      let single_price = this.formData.single_price;
      let more_service_type = String(this.formData.more_service_type);
      let more_service_value = this.formData.more_service_value;
      let more_sale_type = String(this.formData.more_sale_type);
      let more_sale_value = this.formData.more_sale_value;
      let less_service_type = String(this.formData.less_service_type);
      let less_service_value = this.formData.less_service_value;

      if (Number(single_price || 0) === 0) {
        callback(new Error('请输入大于0的单次成本价'));
      } else if (more_service_value === null || less_service_value === null) {
        callback(new Error('服务提成不能空'));
      } else if (more_sale_value === null) {
        callback(new Error('销售分账不能为空'));
      } else if (
        (more_service_type === 'fixed' && +more_service_value > +single_price) ||
        (less_service_type === 'fixed' && +less_service_value > +single_price)
      ) {
        callback(new Error('服务提成不能大于单次成本价'));
      } else if (more_sale_type === 'fixed' && +more_sale_value > +single_price) {
        callback(new Error('销售分账不能大于单次成本价'));
      } else {
        callback();
      }
    };

    return {
      formData: {},
      formDataRules: {
        first_price: [{ required: true, type: 'number', validator: validFirstPrice, trigger: 'change' }],
        other_price: [{ required: true, type: 'number', validator: validOtherPrice, trigger: 'change' }],
        share_price: [{ required: true, type: 'number', validator: validSharePrice, trigger: 'change' }],
        serv_type: [{ required: true, message: '请选择服务类型', trigger: 'change' }],
      },
    };
  },
  computed: {},
  watch: {
    value: {
      deep: true,
      immediate: true,
      handler(val) {
        if (!isEqual(val, this.formData)) {
          this.formData = cloneDeep(val);
        }
      },
    },
    formData: {
      deep: true,
      handler(val) {
        if (!isEqual(val, this.value)) {
          this.$emit('input', cloneDeep(val));
        }
      },
    },
  },
  created() {},
  mounted() {},
  methods: {
    typeChange(val, filed) {
      if (filed && filed in this.formData) {
        this.formData[filed] = null;
      }
    },
    validPriceSet() {
      return new Promise(resolve => {
        this.$refs.formData.validate(valid => {
          resolve(valid);
        });
      });
    },
  },
};
</script>

<style lang="less" scoped>
.fit-content {
  min-width: fit-content;
}
</style>
