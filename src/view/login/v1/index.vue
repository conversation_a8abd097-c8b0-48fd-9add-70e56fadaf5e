<template>
  <div>
    <div class="video-bg">
      <video
        autoplay="autoplay"
        height="100%"
        loop="loop"
        muted="muted"
        src="https://vod-sn-i01s-cdn.rsjxx.com/login_bg.mp4"
        type="video/mp4"
        width="100%"
      ></video>
      <div class="mask"></div>
      <div class="logo-box"><img alt="" src="@/assets/image/login/logo-white.png" /></div>
    </div>
    <div class="login-box">
      <div class="login flex">
        <div class="login-left flex flex-c flex-item-l-center">
          <div class="left-txt">
            <div class="plat">榕树家诊所管理平台</div>
            <div class="line-h"></div>
            <div class="company">海南榕树家信息科技有限公司</div>
          </div>
        </div>
        <div :class="{ sMax: forgetPassword }" class="line-s"></div>
        <div v-show="!forgetPassword" class="login-right">
          <div class="nav flex flex-item-between">
            <div :class="{ cur: status == 0 }" class="nav-password" @click="changeTap(0)">密码登录</div>
            <div :class="{ cur: status == 1 }" class="nav-code" @click="changeTap(1)">验证码登录</div>
          </div>
          <div class="login-input">
            <Form @submit.native.prevent @keyup.enter.native="onLogin">
              <FormItem v-if="status == 0" style="margin-top: 20px">
                <i class="fa fa-user myicon"></i>
                <Input
                  v-model="logindate.authNum"
                  clearable
                  placeholder="请输入手机号或用户名"
                  @on-blur="getShopData"
                  autocomplete="off"
                  @input="e => removeSpace(e, 'authNum')"
                  ref="loginAccount"
                  @on-clear="clearClinicList"
                ></Input>
              </FormItem>
              <!-- 手机号码 -->
              <FormItem v-if="status == 1" style="margin-top: 20px">
                <i class="fa fa-user myicon"></i>
                <Input
                  v-model="logindate.phone"
                  clearable
                  placeholder="请输入手机号"
                  type="number"
                  @on-blur="getShopData"
                  @input="e => removeSpace(e, 'phone')"
                  @on-clear="clearClinicList"
                  ref="loginPhone"
                ></Input>
              </FormItem>
              <!-- 选择门店 -->
              <FormItem style="margin-top: 20px">
                <div v-if="shopList.length > 1 || logindate.shopName == ''" style="position: relative">
                  <!-- <i class="fa fa-user myicon"></i> -->
                  <div class="icon-img">
                    <img alt="" src="@/assets/image/login/mendian-icon.png" />
                  </div>
                  <Select
                    v-model="logindate.shopId"
                    placeholder="诊所名称"
                    :filterable="shopList.length > 1"
                    :clearable="shopList.length > 1"
                    style="width: 100%; background: transparent; color: #fff"
                  >
                    <Option v-for="item in shopList" :key="item.id" :value="item.id">{{ item.name }}</Option>
                  </Select>
                </div>
                <div v-else>
                  <!-- <i class="fa fa-user myicon"></i> -->
                  <div class="icon-img">
                    <img alt="" src="@/assets/image/login/mendian-icon.png" />
                  </div>
                  <Input v-model="logindate.shopName" disabled></Input>
                </div>
              </FormItem>
              <!-- 密码 -->
              <FormItem v-if="status == 0" clearable style="margin-top: 20px">
                <i class="fa fa-lock myicon"></i>
                <Input v-model="logindate.password" clearable type="password" placeholder="请输入密码"></Input>
              </FormItem>
              <FormItem v-if="status == 0">
                <div class="login-msg" @click="goForget">忘记密码？</div>
              </FormItem>

              <!-- 验证码 -->

              <FormItem v-if="status == 1" style="margin-top: 20px; margin-bottom: 20px">
                <!-- <i class="fa fa-lock myicon"></i> -->
                <div class="icon-img">
                  <img alt="" src="@/assets/image/login/yannzheng-icon.png" />
                </div>
                <el-input v-model="logindate.authCode" placeholder="请输4位验证码" type="number"></el-input>
                <div class="login-auth">
                  <vac ref="vac" :auto-start="false" :left-time="60000" @finish="onCountDownFinish">
                    <span slot="process" slot-scope="{ timeObj }">
                      {{ timeObj.ceil.s }}
                    </span>
                    <span slot="before" @click="onCountDownStart">获取验证码</span>
                    <span slot="finish" @click="onCountDownStart">重新获取</span>
                  </vac>
                </div>
              </FormItem>

              <FormItem>
                <Button
                  style="width: 100%; height: 41px; font-size: 20px; margin-top: 10px"
                  type="primary"
                  @click="onLogin"
                  >登录
                </Button>
              </FormItem>
            </Form>
          </div>
        </div>
        <div v-show="forgetPassword" class="login-right changepsd">
          <div class="forget-tit">找回密码</div>
          <div class="login-input">
            <Form ref="psdForm" :model="logindate" :rules="ruleLoginData" autocomplete="off">
              <!-- 添加一个隐藏的表单来欺骗浏览器的自动填充 -->
              <div style="display: none">
                <input type="text" name="fakeusernameremembered" />
                <input type="password" name="fakepasswordremembered" />
              </div>

              <FormItem prop="phone" style="margin-top: 20px">
                <i class="fa fa-user myicon"></i>
                <Input
                  v-model="logindate.phone"
                  clearable
                  placeholder="请输入手机号"
                  type="number"
                  @on-blur="getShopData"
                  @input="e => removeSpace(e, 'phone')"
                  @on-clear="clearClinicList"
                ></Input>
              </FormItem>

              <FormItem style="margin-top: 20px">
                <div v-if="shopList.length > 1 || logindate.shopName == ''" style="position: relative">
                  <div class="icon-img">
                    <img alt="" src="@/assets/image/login/mendian-icon.png" />
                  </div>
                  <Select
                    v-model="logindate.shopId"
                    placeholder="诊所名称"
                    style="width: 100%; background: transparent; color: #fff"
                  >
                    <Option v-for="item in shopList" :key="item.id" :value="item.id">
                      {{ item.name }}
                    </Option>
                  </Select>
                </div>
                <div v-else>
                  <div class="icon-img">
                    <img alt="" src="@/assets/image/login/mendian-icon.png" />
                  </div>
                  <Input v-model="logindate.shopName" disabled></Input>
                </div>
              </FormItem>

              <FormItem prop="authCode" style="margin-top: 20px; margin-bottom: 10px">
                <div class="icon-img">
                  <img alt="" src="@/assets/image/login/yannzheng-icon.png" />
                </div>
                <el-input
                  v-model="logindate.authCode"
                  placeholder="请输4位验证码"
                  type="text"
                  pattern="[0-9]*"
                  inputmode="numeric"
                  autocomplete="off"
                  readonly
                  onfocus="this.removeAttribute('readonly')"
                ></el-input>
                <div class="login-auth">
                  <vac ref="vac2" :auto-start="false" :left-time="60000" @finish="onCountDownFinish">
                    <span slot="process" slot-scope="{ timeObj }">
                      {{ timeObj.ceil.s }}
                    </span>
                    <span slot="before" @click="onCountDownStart">获取验证码</span>
                    <span slot="finish" @click="onCountDownStart">重新获取</span>
                  </vac>
                </div>
              </FormItem>

              <FormItem prop="newPassword" style="margin-top: 20px">
                <i class="fa fa-lock myicon"></i>
                <el-input
                  v-model="logindate.newPassword"
                  clearable
                  placeholder="输入新密码(6-20位字母数字组合)"
                  type="password"
                  name="new-password-field"
                  autocomplete="new-password"
                  :data-lpignore="true"
                ></el-input>
              </FormItem>

              <FormItem prop="passwordAgain" style="margin-top: 20px">
                <i class="fa fa-lock myicon"></i>
                <el-input
                  v-model="logindate.passwordAgain"
                  clearable
                  placeholder="确认新密码"
                  type="password"
                  name="confirm-password-field"
                  autocomplete="new-password"
                  :data-lpignore="true"
                ></el-input>
              </FormItem>

              <FormItem>
                <Button
                  style="width: 100%; height: 41px; font-size: 20px; margin-top: 20px"
                  type="primary"
                  @click="changePassWord"
                  >保存
                </Button>
              </FormItem>
            </Form>
          </div>
          <div class="golgointxt" @click="goLogin">去登录</div>
        </div>
      </div>
    </div>
    <AccessNote :confirmVisible="visibleAccess" @cancel="cancelFunc" @ok="confirmFunc" />
    <ChangeProv
      :confirmVisible="provVisible"
      :old-prov-name="oldProvName"
      :new-prov-name="newProvName"
      @cancel="cancelProv"
      @ok="confirmProv"
    />
    <!-- <div class="login-wrapper" :style="{height: pageHeight}">
      <div class="bg-wrapper"></div>
      <div class="logo"></div>
      <div class="content-wrapper">
        <div class="title">榕树家诊所管理平台</div>
        <Form inline @submit.native.prevent @keyup.enter.native="onLogin">
          <div class="input-wrapper">
            <div class="input-item">
              <div class="input-item_left">账号</div>
              <div class="input-item_right">
                <span class="icon icon_mobile"></span>
                <input class="form-input" placeholder="请输入手机号" v-model="mobile"/>
              </div>
            </div>

            <div class="input-item" v-if='status == 0'>
              <div class="input-item_left">验证码</div>
              <div class="input-item_right">
                <span class="icon icon_auth"></span>
                <input class="form-input" placeholder="请输入验证码" v-model="auth_code"/>
                <div class="auth">
                  <vac :left-time="60000" :auto-start="false" @finish="onCountDownFinish" ref="vac">
                  <span slot="process" slot-scope="{ timeObj }">
                    {{ timeObj.ceil.s }}
                  </span>
                    <span slot="before" @click="onCountDownStart">获取验证码</span>
                    <span slot="finish" @click="onCountDownStart">重新获取</span>
                  </vac>
                </div>
              </div>
            </div>

            <div class="login-btn" @click="onLogin">马上登录</div>
          </div>
        </Form>
      </div>
      <div class="footer">上海树家医学科技有限公司提供技术支持</div>
    </div> -->
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */
import Vue from 'vue';
import AccessNote from '@/components/access-note';
import ChangeProv from '@/components/change-prov';

import config from '@/config';
import { throttle } from 'lodash';

let windowHeight = parseInt(window.outerHeight);

export default {
  name: 'index',
  components: {
    AccessNote,
    ChangeProv,
  },

  data() {
    return {
      pageHeight: '',
      countdowning: false,
      // mobile: '',
      // auth_code: '',
      logindate: {
        authNum: '', //账号
        shopId: '', //id
        shopName: '', //门店名称
        phone: '', //手机号
        password: '', //密码
        newPassword: '', //新密码
        passwordAgain: '', //再次密码
        authCode: '', //验证码
      },
      status: 0, //1,验证码登录，0,密码登录
      forgetPassword: false,
      ruleLoginData: {
        phone: [{ required: true, message: '请填写手机号码', trigger: 'blur' }],
        authCode: [{ required: true, message: '请填写验证码', trigger: 'blur' }],
        newPassword: [{ required: true, message: '请填新密码', trigger: 'blur' }],
        passwordAgain: [{ required: true, message: '请再次填写新密码', trigger: 'blur' }],
      },
      shopList: [],
      visibleAccess: false,
      loginUserInfo: {},
      provVisible: false,
      oldProvName: '',
      newProvName: '',
      isSwitchingTab: false,
      isTabSwitchBlur: false,
    };
  },

  created() {
    this.getWindowHeight();
    window.addEventListener('resize', this.getWindowHeight);
    // setTimeout(() => {
    //   this.visibleAccess = true;
    // }, 0);
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.loginAccount.focus();
    });
  },
  methods: {
    getWindowHeight() {
      this.pageHeight = windowHeight - 110 + 'px';
    },
    // 切换登录方式
    changeTap(state) {
      // if (this.status === 0 && this.$refs.loginAccount) {
      //   this.isTabSwitchBlur = true;
      //   this.$refs.loginAccount.blur();
      // }
      // if (this.status === 1 && this.$refs.loginPhone) {
      //   this.isTabSwitchBlur = true;
      //   this.$refs.loginPhone.blur();
      // }
      this.status = state;
      this.logindate.shopId = '';
      for (let key in this.logindate) {
        if (state === 1) {
          if (key === 'authNum' && this.mobileReg(this.logindate.authNum)) {
            this.logindate.phone = this.logindate[key];
            // console.log('-> %c this.logindate.phone  === %o', 'font-size: 15px;color: green;', this.logindate.phone);
          } else if (key !== 'phone' || state !== 1) {
            this.logindate[key] = '';
          }
        }
      }
      this.shopList = [];
      this.$nextTick(() => {
        if (this.logindate.phone) {
          this.getShopData();
        }
      });
    },
    // 忘记密码
    goForget() {
      for (let key in this.logindate) {
        this.logindate[key] = '';
      }
      this.shopList = [];
      this.forgetPassword = true;
    },
    // 获取验证码
    onCountDownStart() {
      if (this.logindate.phone.trim() == '') {
        this.$Message.error('请输入手机号');
        return;
      }

      io.post('clinic/mobile.sendauthcode', { mobile: this.logindate.phone })
        .then(() => {
          this.$Message.success('发送成功');
          if (!this.forgetPassword) {
            this.$refs.vac.startCountdown(true);
          } else {
            this.$refs.vac2.startCountdown(true);
          }
          this.countdowning = true;
        })
        .catch(error => {
          {
          }
        });
    },
    removeSpace(e, key) {
      this.$nextTick(() => {
        this.logindate[key] = e.replace(/\s+/g, '');
      });
    },
    // 获取诊所名称列表
    getShopData() {
      if (this.logindate.phone == '' && this.status == 1 && !this.forgetPassword) {
        return;
      }
      if (this.logindate.authNum == '' && this.status == 0 && !this.forgetPassword) {
        return;
      }
      if (this.logindate.phone == '' && this.forgetPassword) {
        return;
      }
      if (this.status == 1) {
        let telreg = this.mobileReg(this.logindate.phone);
        if (!telreg) {
          this.$Message.error('请输入正确的号码');
          return;
        }
      }
      let mobile;
      if (this.status == 0) {
        mobile = this.logindate.authNum;
      } else if (this.status == 1) {
        mobile = this.logindate.phone;
      }
      if (this.forgetPassword) {
        mobile = this.logindate.phone;
      }
      let query = {
        mobile: mobile,
        source: this.status === 0 ? '1' : '2',
      };
      this.$api.getshopList(query).then(
        res => {
          this.shopList = res.list;
          if (res.list.length > 0) {
            this.logindate.shopId = res.list[0].id;
            this.logindate.shopName = res.list[0].name;
          }
        },
        err => {
          // console.log(err)
          // {}
          // if (this.status == 0) {
          //   this.$Message.error('无效账号');
          // }
          // if (this.status == 1) {
          //   this.$Message.error('无效手机号');
          // }
          // if (this.forgetPassword) {
          //   this.$Message.error('无效手机号');
          // }
        }
      );
    },
    clearClinicList() {
      this.shopList = [];
      this.logindate.shopId = '';
      this.logindate.shopName = '';
    },

    onCountDownFinish() {
      this.countdowning = false;
    },
    writeUserInfo(userInfo) {
      console.log('-> %c userInfo  index.vue -- 410 ===    %o', 'font-size: 15px;color: #00B771 ;', userInfo);
      let from = this.$route.query.from || '/';
      const hisInfo = userInfo.his_info;
      var EXPIRES_MINUTES = 6;
      const inFiveMinutes = new Date(new Date().getTime() + EXPIRES_MINUTES * 60 * 1000);
      const isJJ = localStorage.getItem('is_jj');
      let clinicLoginInfo = {
        uid: userInfo['mer_uid'],
        name: userInfo['user_name'],
        clinicid: userInfo['clinic_id'],
        clinic_name: userInfo['clinic_name'],
        auth_expired_time: userInfo['auth_expired_time'],
        auth_id: userInfo['auth_id'],
        auth_seq: userInfo['auth_seq'],
        mobile: userInfo['mobile'],
        role_name: userInfo['role_name'],
        // expires: userInfo['expires'] ? Number(userInfo['expires']) : 7,
        expires: inFiveMinutes,
        time_stamp: this.$moment()
          .add(userInfo['expires'] ? Number(userInfo['expires']) : 7, 'days')
          .valueOf(),
        is_direct: userInfo['is_direct'],
        hy_menu_status: userInfo['hy_menu_status'],
        use_prod_package: userInfo['use_prod_package'],
        is_jishan_channel: userInfo['is_jishan_channel'],
        is_allow_out_sales_channel: userInfo['is_allow_out_sales_channel'],
        is_rst: userInfo['is_rst'],
        is_opc: userInfo['is_opc'],
        is_rst_opc: userInfo['is_rst_opc'],
        transform_type_flag: userInfo['transform_type_flag'],
        insure_status: userInfo['insure_status'],
        pay_platform_ap_status: userInfo['pay_platform_ap_status'], // 1:开通聚合支付，0:未开通聚合支付
        isLogin: true, // 是否已登录，针对踢下线功能上线时，未登录过的用户
      };
      let expiresTime = userInfo['expire_secs']
        ? new Date(new Date().getTime() + userInfo.expire_secs * 1000)
        : this.$moment()
            .add(userInfo['expires'] ? Number(userInfo['expires']) : 7, 'days')
            .valueOf();
      // 鉴权
      // clinicLoginInfo.expires = new Date(expiresTime);
      clinicLoginInfo.expires = isJJ ? inFiveMinutes : new Date(expiresTime);
      clinicLoginInfo.time_stamp = Math.floor(this.$moment(isJJ ? inFiveMinutes : expiresTime).valueOf() / 1000);
      // 将date格式的日期转化为时间戳存储
      runtime.writeLoginCookie({ ...clinicLoginInfo });
      let hisLoginInfo = {};
      if (!S.isEmptyObject(hisInfo)) {
        hisLoginInfo = {
          uid: hisInfo['mer_uid'],
          name: hisInfo['user_name'],
          clinicid: hisInfo['clinic_id'],
          clinic_name: hisInfo['clinic_name'],
          auth_expired_time: hisInfo['auth_expired_time'],
          auth_id: hisInfo['auth_id'],
          auth_seq: hisInfo['auth_seq'],
          // expires: userInfo['expires'] ? Number(userInfo['expires']) : 27,
          time_stamp: this.$moment()
            .add(userInfo['expires'] ? Number(userInfo['expires']) : 7, 'days')
            .valueOf(),
          // his_early_access: hisInfo['his_early_access'],
          // his_early_access_v3: hisInfo['his_early_access_v3'],
          his_version: hisInfo['his_version'],
          is_direct: hisInfo['is_direct'],
          role_source: hisInfo['role_source'],
          // role_name: hisInfo['role_name']
          roles: hisInfo['roles'],
          is_rst: userInfo['is_rst'],
          is_allow_out_sales_channel: userInfo['is_allow_out_sales_channel'],
          is_opc: userInfo['is_opc'],
          is_rst_opc: userInfo['is_rst_opc'],
          transform_type_flag: userInfo['transform_type_flag'],
          insure_status: hisInfo['insure_status'],
          isLogin: true, // 是否已登录，针对踢下线功能上线时，未登录过的用户
        };
        hisLoginInfo.time_stamp = Math.floor(this.$moment(expiresTime).valueOf() / 1000);
        hisLoginInfo.expires = new Date(expiresTime);
        runtime.writeHisLoginCookie({
          ...hisLoginInfo,
        });
      }
      try {
        let url = S.uri().path(decodeURIComponent(from)).build();
        if (url.indexOf('/his/newHis/newHis') > -1) {
          url = '/';
        }
        location.href = url;
      } catch (e) {
        location.href = '/';
      }
    },
    // 登录
    onLogin() {
      console.log('ads', this.logindate.shopId);
      let from = this.$route.query.from || '/';
      if (this.status == 1) {
        if (this.logindate.phone.trim() == '') {
          this.$Message.error('请输入手机号');
          return;
        }
        if (!this.logindate.shopId || this.logindate.shopId.trim() == '') {
          this.$Message.error('请选择门店');
          return;
        }
        if (this.logindate.authCode.trim() == '') {
          this.$Message.error('请输入验证码');
          return;
        }
        let telreg = this.mobileReg(this.logindate.phone);
        if (!telreg) {
          this.$Message.error('请输入正确的号码');
          return;
        }
        io.post('clinic/index.login', {
          mobile: this.logindate.phone,
          auth_code: this.logindate.authCode,
          clinic_id: this.logindate.shopId,
        })
          .then(userInfo => {
            console.log('-> userInfo', userInfo);
            this.loginUserInfo = userInfo;
            if (userInfo?.is_ues_system_box === '1') {
              this.visibleAccess = true;
            } else if (userInfo?.is_cli_tip === '1') {
              this.oldProvName = this.loginUserInfo.change_company_tip?.old?.name || '';
              this.newProvName = this.loginUserInfo.change_company_tip?.now?.name || '';
              this.provVisible = true;
            } else {
              this.$Message.success('登录成功');
              this.writeUserInfo(userInfo);
            }
          })
          .catch(error => {
            console.log('-> error', error);
            {
            }
          });
      } else {
        let query = {
          account: this.logindate.authNum,
          password: S.encrypt(
            JSON.stringify({
              password: this.logindate.password,
              expired_time: Date.parse(new Date()) / 1000,
            })
          ),
          clinic_id: this.logindate.shopId,
          version: config.cryptoVersion,
        };

        this.$api
          .getAccountlogin(query)
          .then(
            userInfo => {
              this.loginUserInfo = userInfo;
              if (userInfo?.is_ues_system_box === '1') {
                this.visibleAccess = true;
              } else if (userInfo?.is_cli_tip === '1') {
                this.oldProvName = this.loginUserInfo.change_company_tip?.old?.name || '';
                this.newProvName = this.loginUserInfo.change_company_tip?.now?.name || '';
                this.provVisible = true;
              } else {
                this.$Message.success('登录成功');
                this.writeUserInfo(userInfo);
              }
            },
            err => {
              {
              }
            }
          )
          .catch(e => {});
      }
    },
    // 修改密码
    changePassWord() {
      this.$refs.psdForm.validate(valid => {
        if (valid) {
          if (this.logindate.newPassword != this.logindate.passwordAgain) {
            this.$Message.error('两次密码不一致');
            return;
          }
          this.getRetrievepass();
        } else {
          this.$Message.error('请完善信息');
        }
      });
    },
    getRetrievepass() {
      let telreg = this.mobileReg(this.logindate.phone);
      if (!telreg) {
        this.$Message.error('请输入正确的号码');
        return;
      }
      let query = {
        mobile: this.logindate.phone,
        auth_code: this.logindate.authCode,
        clinic_id: this.logindate.shopId,
        new_password: S.encrypt(
          JSON.stringify({
            password: this.logindate.newPassword,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        confirm_password: S.encrypt(
          JSON.stringify({
            password: this.logindate.passwordAgain,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        version: config.cryptoVersion,
      };
      this.$api
        .changeRetrievepass(query)
        .then(
          res => {
            console.log(res);
            this.$Message.success('修改密码成功');
            this.forgetPassword = false;
            for (let key in this.logindate) {
              this.logindate[key] = '';
            }
            this.shopList = [];
          },
          err => {
            {
            }
          }
        )
        .catch(error => {});
    },
    mobileReg(mobile) {
      let reg = /^1[3456789]\d{9}$/;
      return reg.test(mobile);
    },
    goLogin() {
      for (let key in this.logindate) {
        this.logindate[key] = '';
      }
      this.shopList = [];
      this.forgetPassword = false;
    },
    confirmFunc() {
      this.$Modal.confirm({
        title: '确定开始使用系统？',
        onOk: () => {
          const { clinic_id, mer_uid } = this.loginUserInfo;
          // this.$Message.info('Clicked ok');
          this.$api.getConfirmUseSystem({ clinic_id, mer_uid }).then(
            res => {
              // this.$Message.success('登录成功');
              // this.writeUserInfo(res);
              this.onLogin();
            },
            err => {
              {
              }
            }
          );
        },
        onCancel: () => {},
      });
    },
    cancelFunc() {
      this.visibleAccess = false;
    },
    cancelProv() {
      this.provVisible = false;
    },
    confirmProv() {
      const { clinic_id, mer_uid } = this.loginUserInfo;
      this.$api.confirmChangeCompany({ clinic_id, mer_uid }).then(res => {
        this.cancelProv();
        this.onLogin();
      });
    },
  },

  destroyed() {
    window.removeEventListener('resize', this.getWindowHeight);
  },
};
</script>
<style lang="less">
@import './index.less';
</style>
<style lang="less" scoped>
.video-bg {
  width: 100vw;
  height: 100vh;
  background-color: #000;
  min-width: 1200px;
  overflow: hidden;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: -1;
  //video {
  //  width: 100%;
  //  height: 100%;
  //  object-fit: cover;
  //  position: absolute;
  //  top: 0;
  //  left: 0;
  //  z-index: -1;
  //}
}

.mask {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 0;
  left: 0;
}

.login-box {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.login {
  width: 1000px;
  height: 370px;
  margin-top: 15vw;
  margin-left: auto;
  margin-right: auto;
  // transform: translateY(-50%);
  color: #fff;

  .login-left {
    width: 520px;
    text-align: right;

    .plat {
      width: 430px;
      font-size: 38px;
      letter-spacing: 3px;
      line-height: 1;
    }

    .company {
      width: 430px;
      font-size: 20px;
      letter-spacing: 6px;
    }
  }
}

.line-h {
  margin: 20px 0;
  width: 430px;
  height: 2px;
  background: #f0f0f0;
  border-radius: 1px;
}

.line-s {
  width: 1px;
  height: 390px;
  background: #ffffff;
  border-radius: 1px;
}

.sMax {
  height: 460px;
}

.login-right {
  width: 298px;
  margin-left: 70px;
  margin-top: 26px;

  .nav {
    font-size: 20px;
    color: #fff;
    line-height: 36px;

    div {
      cursor: pointer;
    }
  }

  .login-msg {
    margin-top: 10px;
    font-size: 14px;
    color: #f0f0f0;
    text-align: right;
    cursor: pointer;
  }
}

.cur {
  border-bottom: 1px solid #f0f0f0;
  font-size: 26px;
  position: relative;
}

.cur::after {
  content: '';
  border-bottom: 8px solid transparent;
  border-top: 8px solid #fff;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  display: block;
  position: absolute;
  bottom: -17px;
  left: 50%;
  transform: translateX(-50%);
}

.myicon {
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  font-size: 20px;
}

.login-auth {
  position: absolute;
  right: 12px;
  top: 9px;
  cursor: pointer;
}

.forget-tit {
  font-size: 28px;
}

.changepsd {
  margin-top: 10px;
}

.logo-box {
  width: 152px;
  height: 41px;
  position: absolute;
  top: 79px;
  left: 15%;
}

.logo-box img {
  width: 100%;
}

.golgointxt {
  width: 100%;
  text-align: center;
  font-size: 14px;
  color: #4085e1;
  margin-top: 5px;
  cursor: pointer;
}

// ::v-deep .ivu-select-dropdown{
//   background-color: red;
//   color: #fff;
// }
.icon-img {
  width: 17px;
  height: 20px;
  position: absolute;
  top: 10px;
  left: 18px;
}

.icon-img img {
  width: 100%;
}

:deep(.login-input) {
  .el-input.is-active .el-input__inner,
  .el-input__inner:focus {
    border-color: #115bd4 !important;
  }
  .el-input__inner::placeholder {
    font-size: 14px;
    color: #c5c8ce;
  }
}
</style>
