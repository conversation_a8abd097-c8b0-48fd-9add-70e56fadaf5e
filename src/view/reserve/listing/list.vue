<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      @on-sort-change="sortChanged"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" @click="addReserve('1')">预约服务</Button>
          <Button class="light-btn" style="margin-left: 10px" type="default" @click="addReserve('2')">预约医生</Button>
          <Button class="light-btn link-btn" style="margin-left: 10px; padding: 0">
            <KLink
              style="height: 100%; display: flex; align-items: center; padding: 0 10px"
              :to="{ path: '/setting/member/list?componentsName=management' }"
              target="_blank"
            >
              排班管理
            </KLink>
          </Button>
        </div>
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <FormItem label="">
            <Input v-model="queryFormData.kw" placeholder="到店人/到店人手机号" clearable />
          </FormItem>

          <FormItem label="">
            <Input
              v-model="queryFormData.physio_name"
              :placeholder="is_rst ? '技师/医生' : getPhysioName + '/医生'"
              clearable
            />
          </FormItem>

          <FormItem label="">
            <DatePicker
              type="daterange"
              placeholder="下单时间"
              :value="timeRange"
              clearable
              @on-change="times => handleTimeChange(times)"
            ></DatePicker>
          </FormItem>

          <FormItem label="">
            <DatePicker
              type="daterange"
              placeholder="预约时间"
              :value="arriveTimeRange"
              clearable
              @on-change="times => handleTimeChange(times, 'arrival_st', 'arrival_et')"
            ></DatePicker>
          </FormItem>

          <FormItem label="">
            <Input v-model="queryFormData.code" placeholder="预约单号" clearable />
          </FormItem>
          <Row>
            <Col span="24">
              <FormItem v-if="is_rst" label="">
                <Select v-model="queryFormData.source" placeholder="预约单来源" clearable>
                  <Option v-for="source in sourceDesc" :key="source.id" :value="source.id">
                    {{ source.desc }}
                  </Option>
                </Select>
              </FormItem>

              <FormItem>
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div class="panel-nav">
          <a class="nav" :class="{ active: !queryFormData.list_status }" @click.prevent.capture="onStatusChange('')">
            全部
          </a>
          <a
            v-for="(item, key) in statusDesc"
            :key="key"
            class="nav"
            :class="{ active: $route.query.list_status == key }"
            @click.prevent.capture="onStatusChange(key)"
          >
            {{ item.desc }}
            <Tag :color="statusColor(key)">{{ list_count[key] }}</Tag>
          </a>
        </div>
      </template>
      <!-- 到店人 -->
      <template slot-scope="{ row }" slot="name">
        <div>
          <div class="lv-box">
            <div class="label">姓名：</div>
            <div class="value">{{ row.reserve_user?.name || '-' }}</div>
          </div>
          <div class="lv-box">
            <div class="label">手机号：</div>
            <div class="value">{{ row.reserve_user?.mobile || '-' }}</div>
          </div>
        </div>
      </template>

      <!-- 预约单号 -->
      <template slot-scope="{ row }" slot="code">
        {{ row.code || '-' }}
      </template>

      <!-- 预约状态 -->
      <template slot-scope="{ row }" slot="status_desc">
        {{ row.status_desc || '-' }}
      </template>

      <!-- 预约服务 -->
      <template slot-scope="{ row }" slot="services_desc">
        <div>{{ getServices(row.services) || '未选择服务' }}</div>
      </template>

      <!-- 理疗师 -->
      <template slot-scope="{ row }" slot="physio_desc">
        <div class="flex">
          <div>{{ getPhysical(row.services) || '-' }}</div>
          <div class="serve-tag" v-if="getPhysical(row.services)">{{ row.type_desc }}</div>
        </div>
      </template>

      <!-- 预约到店时间 -->
      <template slot-scope="{ row }" slot="reserve_time">
        <div :style="{ color: row.is_delay === '1' ? 'red' : '', 'text-align': 'center' }">
          <div>{{ row.reserve_ms }}</div>
          <div>{{ row.reserve_day_text }}</div>
          <div v-if="row.is_delay === '1'">{{ row.delay_text }}</div>
        </div>
      </template>

      <!-- 实际到店时间 -->
      <template slot-scope="{ row }" slot="arrival_time">
        <div v-if="row.is_arrival === '1'" style="text-align: center">
          <div>{{ row.arrival_ms }}</div>
          <div>{{ row.arrival_day_text }}</div>
        </div>
        <div v-else style="text-align: center">-</div>
      </template>

      <!-- 开单时间 -->
      <template slot-scope="{ row }" slot="order_at">
        <div>{{ row.order_at || '-' }}</div>
      </template>

      <!-- 顾客备注 -->
      <template slot-scope="{ row }" slot="user_remark">
        <Tooltip :content="row.user_remark" :disabled="row.user_remark?.length < 10">
          <div class="ecs ecs-2">{{ row.user_remark || '-' }}</div>
        </Tooltip>
      </template>

      <!-- 门店备注 -->
      <template slot-scope="{ row }" slot="remark">
        <Tooltip :content="row.remark" :disabled="row.remark?.length < 10">
          <div class="ecs ecs-2">{{ row.remark || '-' }}</div>
        </Tooltip>
      </template>

      <!-- 上次服务时间 -->
      <template slot-scope="{ row }" slot="last_svc_time">
        <div>{{ row.last_svc_at || '-' }}</div>
      </template>

      <!-- 下单时间 -->
      <template slot-scope="{ row }" slot="create_time">
        <div>{{ row.create_at || '-' }}</div>
      </template>

      <!-- 支付状态 -->
      <template slot-scope="{ row }" slot="is_payed_desc">
        <div style="text-align: center">
          <div>{{ row.source === 'channel_my_pingan' ? '平台月结' : row.is_payed_desc || '-' }}</div>
          <template v-if="row.is_payed === '1'">
            <div v-if="row.out_sales_channel == '1'" class="payed-tip">(美团付款)</div>
            <div v-else-if="row.out_sales_channel == '2'" class="payed-tip">(抖音付款)</div>
            <div v-else-if="row.is_card_deduct == '1'" class="payed-tip">(卡券抵扣)</div>
            <div v-if="row.source === 'channel_my_pingan'" class="payed-tip">隔月结算</div>
          </template>
        </div>
      </template>

      <template slot-scope="{ row, index }" slot="operator">
        <OperationFolding :maxVisibleActions="2" :actions="getActions(row, index)" :row="row"></OperationFolding>
      </template>
    </standard-table>

    <!-- 添加预约 -->
    <add-reserve-modal
      v-model="addReserveVisible"
      @success="onSearch"
      :row="currentRow"
      :type="reserve_type"
      :options="{
        out_sales_channel_desc,
      }"
    ></add-reserve-modal>
    <!-- 已支付取消预约 -->
    <cancel-reservation v-model="cancelReservationVisible" @success="onSearch" :row="currentRow"></cancel-reservation>

    <!-- 支付 -->
    <template v-if="is_rst">
      <rst-pay-dialog v-model="rstPayVisible" :orderId="order_id" @changeVisible="rstChangeVisible"></rst-pay-dialog>
    </template>
    <template v-else>
      <k-pay-dialog
        v-model="payVisible"
        :order_id="order_id"
        :is_rst="is_rst"
        @changeVisible="changeVisible"
      ></k-pay-dialog>
    </template>
    <!-- 支付成功弹窗 -->
    <success-pay-dialog v-model="successVisible" :success-data="successData" v-if="successVisible" />
    <reason-modal
      :visible.sync="cancelVisible"
      :loading="cancelLoading"
      title="取消原因"
      label="请输入取消原因"
      placeholder="请输入取消原因"
      @ok="channelCancel"
      @change="cancelVisible = false"
    />

    <!--未通过主体验证 拦截收款弹窗-->
    <auth-warning-modal ref="authWarningRef"></auth-warning-modal>
    <custom-dialog
      :visible.sync="scanCodePayVisible"
      okText="去扫码"
      content="此预约单来源为抖音或美团，请扫描抖音或美团的券码，确认完成服务。"
      :on-ok="handleToCardVerification"
    />
  </div>
</template>

<script>
import { getPhysioName, isRstClinic } from '@/libs/runtime';
import S from '@/libs/util'; // Some commonly used tools
// import io from '@/libs/io'; // Http request
import search from '@/mixins/search'; // Runtime information
import addReserveModal from './components/addReserveModal.vue';
import cancelReservation from './components/cancelReservation.vue';
import {
  all_columns,
  wait_columns,
  arrive_columns,
  finished_columns,
  cancel_columns,
  channel_columns,
} from './data/reserve_data';
import RstPayDialog from '@/components/k-pay-dialog/rst-pay-dialog.vue';
import KPayDialog from '@/components/k-pay-dialog/index.vue';
import StandardTable from '@/components/StandardTable/index.vue';
import SuccessPayDialog from '@/components/k-pay-dialog/pay-status/SuccessPayDialog.vue';
import ReasonModal from '../board/components/cancelModel.vue';
import AuthWarningModal from '@/components/AuthWarning/AuthWarningModal.vue';
import CustomDialog from '@/components/custom-dialog/index.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  kw: '',
  physio_name: '',
  st: '',
  et: '',
  arrival_st: '',
  arrival_et: '',
  list_status: '',
  sort_type: '',
  sort_method: '',
  code: '',
  source: '',
  r: '',
};

export default {
  name: 'list',
  mixins: [search],
  components: {
    CustomDialog,
    ReasonModal,
    SuccessPayDialog,
    StandardTable,
    addReserveModal,
    cancelReservation,
    KPayDialog,
    RstPayDialog,
    AuthWarningModal,
  },
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getReservev2ReserveList',
      tableCols: [],
      tableLoading: false,
      list: [],
      total: 0,
      list_count: {},
      services: [],
      source_desc: [],
      out_sales_channel_desc: [],
      addReserveVisible: false,
      statusDesc: {},
      currentRow: {},
      cancelReservationVisible: false,
      reserve_type: '1', // 默认服务

      // 支付逻辑
      payVisible: false, // 支付弹窗显示的标识
      rstPayVisible: false, // 支付弹窗显示的标识
      order_id: '', // 订单id,
      successVisible: false,
      successData: {},
      cancelVisible: false,
      scanCodePayVisible: false,
      cancelLoading: false,
      selectedRow: {},
    };
  },

  computed: {
    is_rst() {
      return isRstClinic();
    },
    sourceDesc() {
      const source_desc = this.source_desc;
      if (this.is_rst) return source_desc.filter(item => item.is_rst === '1');
      return source_desc;
    },
    getPhysioName() {
      return getPhysioName();
    },
    getActions() {
      return row => {
        return [
          // 保险预约过来的，待到店状态
          {
            label: '预约确认',
            handler: this.edit,
            tagType: 'a',
            params: row,
            isHidden: !(row.is_arrival !== '1' && row.reserve_status === '5'),
          },
          // 非已完成，已取消状态下，并且未到店（非小程序预约单）
          {
            label: '确认到店',
            handler: this.isNeedCheckedChoose(row) ? this.checkChoose : this.arrival,
            confirmText: ' 确定客户到店？',
            tagType: this.isNeedCheckedChoose(row) ? 'a' : 'Poptip',
            params: row,
            isHidden: !(
              row.is_arrival !== '1' &&
              row.reserve_status != '3' &&
              row.reserve_status != '4' &&
              row.reserve_status != '5' &&
              row.source !== 'rst_weapp'
            ),
          },
          // 待收款（status: 1100））
          // 非已完成，已取消状态下，并且非医生非小程序，状态为已到店，未支付
          {
            label: '收款',
            handler: ['channel_my_pingan'].includes(row.source) ? this.otherPay : this.preCollection,
            tagType: ['channel_my_pingan'].includes(row.source) ? 'Poptip' : 'a',
            confirmText: '该订单为月结订单，用户无需付款是否确认进行「收款」操作？',
            params: row,
            isHidden: !(
              row.is_arrival === '1' &&
              row.is_payed !== '1' &&
              row.reserve_status != '3' &&
              row.reserve_status != '4' &&
              row.type != 'DOCTOR' &&
              row.source !== 'rst_weapp'
            ),
          },
          // 非已完成，已取消状态下，并且非医生，状态为已到店，后台/小程序需满足已付款
          {
            label: '完成服务',
            handler: this.finished,
            confirmText: '确认服务已完成?',
            tagType: 'Poptip',
            params: row,
            isHidden: !(
              row.reserve_status != '3' &&
              row.reserve_status != '4' &&
              row.type != 'DOCTOR' &&
              row.is_arrival == '1' &&
              row.is_payed == '1'
            ),
          },
          {
            label: '详情',
            handler: this.toDetail,
            tagType: 'a',
            params: row,
          },
          // 非已完成，已取消状态下，并且非医生，状态为未到店，未付款
          {
            label: '修改',
            handler: this.edit,
            tagType: 'a',
            params: row,
            isHidden: !(
              row.is_arrival !== '1' &&
              row.is_payed !== '1' &&
              row.reserve_status != '3' &&
              row.reserve_status != '4' &&
              row.reserve_status != '5' &&
              row.type != 'DOCTOR' &&
              row.source !== 'rst_weapp'
            ),
          },
          // 非已完成，已取消状态下，都可以取消
          {
            label: '取消预约',
            handler: this.cancel,
            confirmText: '确定取消该预约吗？',
            tagType: row.source !== 'channel_my_pingan' ? 'Poptip' : 'a',
            params: row,
            isHidden: !(row.reserve_status != '3' && row.reserve_status != '4'),
          },
        ];
      };
    },
    getServices() {
      return list => {
        let name = [];
        list?.forEach(item => {
          name.push(item.goods_service.name);
        });
        return name.join('、') || '';
      };
    },
    getPhysical() {
      return list => {
        let name = [];
        list?.forEach(item => {
          name.push(item.physio.name);
        });
        return name.join('、') || '';
      };
    },
    statusColor() {
      return status => {
        switch (status) {
          case '2': // 已到店
            return '#ffad33';
          case '3': // 已完成
            return '#19be6b';
          case '1': // 待到店
            return '#ed4014';
          case '4': // 已取消
            return 'default';
        }
      };
    },
  },

  created() {
    this.getReservev2ReserveOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);

    let val = this.$route.query.list_status || '';
    this.changeColumn(val);

    this.$nextTick(() => {
      if (this.$route.query.id && this.$route.query.code && this.$route.query.from === '/daily/appoint/list') {
        this.edit({
          id: this.$route.query.id,
          reserve_status: '5',
          type: this.$route.query.type,
        });
      }
    });
  },

  methods: {
    rstChangeVisible(val) {
      if (!val) {
        this.submitQueryForm();
      }
    },
    handleToCardVerification() {
      this.$router.push('/service/card/verification');
    },
    changeVisible(val) {
      if (!val) {
        this.submitQueryForm();
      }
    },
    changeColumn(val) {
      const map = {
        1: wait_columns,
        2: arrive_columns,
        3: finished_columns,
        4: cancel_columns,
        5: channel_columns,
      };
      if (!val) {
        this.tableCols = all_columns;
      } else {
        this.tableCols = map[val];
      }
    },
    preCollection(row) {
      // 如果是美团和抖音，pop提示 跳转去卡券核销
      if (['meituan', 'douyin'].includes(row.source)) {
        this.scanCodePayVisible = true;
        return;
      }
      if (Number(row.order_id)) {
        this.order_id = row.order_id;
        if (this.is_rst) {
          this.$refs.authWarningRef.verify().then(() => {
            this.rstPayVisible = true;
          });
          // this.rstPayVisible = true;
        } else {
          this.payVisible = true;
        }
      } else {
        this.collection(row);
      }
    },
    // 收款
    async collection(row) {
      let params = {
        id: row.id,
        services: this.handlerServices(row),
      };

      this.$api
        .getReservev2ReserveConfirm(params)
        .then(res => {
          this.order_id = res.order_id;
          return this.$refs.authWarningRef.verify();
        })
        .then(() => {
          if (this.is_rst) {
            this.rstPayVisible = true;
          } else {
            this.payVisible = true;
          }
        });
      // this.$api.getReservev2ReserveConfirm(params).then(res => {
      //   this.order_id = res.order_id;
      //   if (this.is_rst) {
      //     this.rstPayVisible = true;
      //   } else {
      //     this.payVisible = true;
      //   }
      // });
    },
    otherPay(row) {
      let params = {
        id: row.id,
        services: this.handlerServices(row),
      };
      this.$api.getReservev2ReserveConfirm(params).then(res => {
        this.successData = res || {};
        this.successVisible = true;
        this.submitQueryForm();
      });
    },

    // 收款 services 参数
    handlerServices(row) {
      let list = [];
      row?.services?.forEach(item => {
        list.push({
          goods_service_id: item.goods_service?.id,
          physio_id: item.physio_id,
          quantity: item.quantity,
        });
      });
      return list;
    },

    // 修改
    edit(row) {
      this.currentRow = row;
      if (row.type === 'DOCTOR') {
        this.reserve_type = '2';
      } else {
        this.reserve_type = '1';
      }
      this.addReserveVisible = true;
    },
    // 详情
    toDetail(row) {
      this.$router.push({
        path: '/reserve/listing/detail',
        query: {
          id: row.id,
          type: row.type,
        },
      });
    },
    // 先确认技师或服务
    checkChoose(row) {
      this.$Modal.confirm({
        title: `请先确认${this.getPhysioName}或服务`,
        content: '',
        loading: false,
        okText: '去选择',
        onOk: () => {
          // 唤起修改
          this.$Modal.remove();
          this.edit(row);
        },
      });
    },
    // 检测是否需要先确认技师和服务
    isNeedCheckedChoose(row) {
      // 后台下单的技师才有拦截校验
      if (
        row.is_arrival !== '1' &&
        row.type != 'DOCTOR' &&
        row.source !== 'rst_weapp' &&
        (!this.getPhysical(row.services) || !this.getServices(row.services))
      ) {
        return true;
      } else {
        return false;
      }
    },
    // 预约到店
    arrival(row, completeReserve = false) {
      let params = {
        id: row.id,
        complete_reserve: completeReserve ? 1 : 0,
      };
      this.$api.getReservev2ReserveArrival(params).then(() => {
        this.$Message.success('到店成功');
        this.submitQueryForm();
      });
    },
    // 取消预约
    cancel(row) {
      if (row.source === 'channel_my_pingan') {
        this.selectedRow = row;
        this.cancelVisible = true;
        return;
      }
      let params = {
        id: row.id,
      };
      this.$api.getReservev2ReserveCancel(params).then(() => {
        this.$Message.success('取消预约成功');
        this.submitQueryForm();
      });
    },
    // 取消预约
    channelCancel(txt) {
      let params = {
        id: this.selectedRow.id,
        reason: txt,
      };
      this.cancelLoading = true;
      this.$api
        .getReservev2ReserveCancel(params)
        .then(() => {
          this.$Message.success('取消预约成功');
          this.submitQueryForm();
          this.cancelVisible = false;
          this.selectedRow = {};
        })
        .finally(() => {
          this.cancelLoading = false;
        });
    },
    // 取消已支付的预约（弃用，当前版本不存在部分退款了）
    cancelPay(row) {
      this.currentRow = row;
      this.cancelReservationVisible = true;
    },

    // 完成服务
    finished(row) {
      let params = {
        id: row.id,
      };
      this.$api.getReservev2ReserveFinished(params).then(() => {
        this.$Message.success('服务已完成');
        this.submitQueryForm();
      });
    },

    sortChanged({ column: { slot }, order }) {
      if (order === 'normal') {
        order = '';
      }

      if (slot) {
        this.queryFormData.sort_type = slot;
        this.queryFormData.sort_method = order;
        this.submitQueryForm();
      }
    },
    handlerListData(data) {
      this.list_count = data.list_status_count;
    },
    /**
     * val 1:服务 2:医生
     * */
    addReserve(val) {
      this.reserve_type = val;
      this.currentRow = {};
      this.addReserveVisible = true;
    },
    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.list_status = status;
      this.submitQueryForm();
      setTimeout(() => {
        this.changeColumn(status);
      });
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    getReservev2ReserveOptions() {
      this.$api.getReservev2ReserveOptions().then(res => {
        this.statusDesc = res.list_status_desc;
        this.source_desc = S.descToArrHandle(res.source_desc);
        this.out_sales_channel_desc = S.descToArrHandle(res.out_sales_channel_desc);
      });
    },
  },

  watch: {},

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getTimeRange('arrival_st', 'arrival_et', 'arriveTimeRange');
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.status-tip {
  color: #999;
  font-size: 12px;
}

.mr10 {
  margin-right: 10px;
}
</style>

<style lang="less" scoped>
.lv-box {
  display: flex;

  .label {
    width: 60px;
    min-width: 60px;
    text-align: right;
  }
}

.serve-tag {
  margin-left: 10px;
  color: #155bd4;
  min-width: fit-content;
}

.light-btn {
  border-color: #155bd4;
  color: #155bd4;
}

.link-btn {
  & > p {
    height: 100%;
  }
}

.payed-tip {
  color: #ccc;
  font-size: 12px;
}
</style>
