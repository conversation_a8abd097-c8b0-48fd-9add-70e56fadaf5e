<template>
  <Modal
    ref="customModal"
    :value="value"
    width="500px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="tip-text" slot="header">
        <Icon type="ios-alert" size="22" color="#ffa500" />
        <div class="text">确定取消该预约吗？</div>
      </div>
      <div class="form-box">
        <Form :label-width="100" ref="formData" :label-colon="true" :model="formData" :rules="ruleValidate">
          <FormItem label="退款方式" prop="type">
            <Select v-model="formData.type" placeholder="请选择退款方式">
              <Option v-for="item in typeList" :value="item.type" :key="item.type">{{ item.name }}</Option>
            </Select>
            <div class="type-desc-tip" v-show="getCurrentItem?.desc">{{ getCurrentItem?.desc }}</div>
          </FormItem>

          <FormItem label="退款金额" v-show="formData.type">
            <div class="refund-fee">¥{{ getCurrentItem?.can_refund_fee | number_format }}</div>
          </FormItem>

          <FormItem label="退款原因" prop="reason">
            <Input
              v-model="formData.reason"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 6 }"
              maxlength="100"
              show-word-limit
              placeholder="请输入退款原因"
            />
          </FormItem>
        </Form>
      </div>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'cancelReservation',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '取消预约',
    },
    row: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      confirmLoading: false,
      formData: {
        type: '',
        reason: '',
      },
      ruleValidate: {
        type: [{ required: true, message: '请选择退款方式', trigger: 'change' }],
      },
      typeList: [],
    };
  },

  computed: {
    getCurrentItem() {
      let item = this.typeList.find(item => item.type === this.formData.type);
      return item || {};
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
        this.getReservev2Canrefundinfo();
      } else {
        this.closeModal();
      }
    },

    clearData() {
      this.$refs.formData.resetFields();
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.cancel();
        }
      });
    },

    // 获取退款信息
    getReservev2Canrefundinfo() {
      let params = {
        id: this.row.id,
      };
      this.$api.getReservev2Canrefundinfo(params).then(res => {
        this.typeList = res;
      });
    },

    // 取消预约
    cancel() {
      this.confirmLoading = true;
      let params = {
        id: this.row.id,
        ...this.formData,
      };
      this.$api
        .getReservev2ReserveCancel(params)
        .then(res => {
          this.$Message.success('取消预约成功');
          this.$emit('success');
          this.closeModal();
        })
        .finally(() => (this.confirmLoading = false));
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-header {
  display: none;
}
.content {
  .tip-text {
    margin-bottom: 30px;
    display: flex;
    align-items: center;
    .text {
      margin-left: 6px;
      font-size: 14px;
    }
  }
  .type-desc-tip {
    color: #b8a9a9;
    height: 10px;
  }
  .refund-fee {
    color: red;
    font-size: 14px;
  }
}
</style>
