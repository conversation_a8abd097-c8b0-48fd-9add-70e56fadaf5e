<template>
  <div>
    <Modal
      ref="customModal"
      :value="value"
      width="760px"
      :title="title"
      :footer-hide="false"
      :mask-closable="false"
      :lock-scroll="true"
      @on-visible-change="changeVisible"
      :styles="{ top: '5vh' }"
      class-name="reserve-modal"
    >
      <div class="content" v-if="value">
        <add-reserve
          ref="addReserve"
          :type="type"
          @close="closeModal"
          :row="row"
          :user-info="userInfo"
          :is-not-remove-user="isNotRemoveUser"
          :options="options"
          @getLoading="val => (loading = val)"
          @success="success"
        ></add-reserve>
      </div>
      <div slot="footer" class="reserve-footer">
        <Button @click="closeModal">取消</Button>
        <div class="button-group-modal">
          <Button :loading="loading" type="primary" @click="confirm" class="main-button-modal">
            {{ confirmText }}
          </Button>
          <Dropdown @on-click="handleConfirmAction" placement="bottom-end" class="dropdown-button-modal">
            <Button type="primary" class="dropdown-trigger-modal">
              <Icon type="ios-more"></Icon>
            </Button>
            <DropdownMenu slot="list">
              <DropdownItem name="confirmAndArrival">创建预约单并确认到店</DropdownItem>
            </DropdownMenu>
          </Dropdown>
        </div>
      </div>
    </Modal>

    <!-- 二次确认弹窗 -->
    <ConfirmModal
      v-model="confirmModalVisible"
      :title="confirmModalConfig.title"
      :content="confirmModalConfig.content"
      :ok-text="confirmModalConfig.okText"
      :cancel-text="confirmModalConfig.cancelText"
      @on-ok="confirmModalConfig.onOk"
    />
  </div>
</template>

<script>
import addReserve from '@/components/addReserveCom/addReserve';
import ConfirmModal from '@/view/reserve/board/components/ConfirmModal.vue';
export default {
  name: 'addReserveModal',
  mixins: [],

  components: { addReserve, ConfirmModal },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    row: {
      type: Object,
      default: () => ({}),
    },
    userInfo: {
      type: Object,
      default: () => ({}),
    },
    type: {
      type: String,
      default: '1',
    },
    options: {
      type: Object,
      default: () => ({
        out_sales_channel_desc: [],
      }),
    },
    isNotRemoveUser: Boolean,
  },

  data() {
    return {
      loading: false,
      needArrivalAfterCreate: false, // 标记创建后是否需要确认到店
      confirmModalVisible: false, // 二次确认弹窗显示状态
      confirmModalConfig: {
        title: '',
        content: '',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {},
      }, // 二次确认弹窗配置
    };
  },

  computed: {
    title() {
      const row = this.row || {};
      if (row.is_arrival !== '1' && row.reserve_status === '5') {
        return '确认预约';
      }
      return row?.id ? '修改预约' : '添加预约';
    },
    confirmText() {
      const row = this.row || {};
      if (row.is_arrival !== '1' && row.reserve_status === '5') {
        return '确认预约';
      }
      return '创建预约';
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    success(isDrag, reset, row) {
      const reserve_date = row.reserve_date;
      const params = {
        ...row,
        isDrag,
        reset,
        id: row.id,
      };

      // 检查是否需要在创建预约后确认到店
      if (this.needArrivalAfterCreate && row.id) {
        this.needArrivalAfterCreate = false; // 重置标记
        // 延迟执行确认到店，确保预约创建完成
        this.executeArrivalAfterCreate(row.id, params);

        // 创建后确认到店的情况下，只刷新看板，不修改日期
        // 传递一个特殊的标记来表示只刷新不切换日期
      } else {
        // 正常创建预约的情况
        this.$emit('success', reserve_date, 'date', params);
      }
    },
    changeVisible(visible) {
      if (visible) {
        this.$nextTick(() => {
          this.$refs.addReserve.init();
        });
      } else {
        this.closeModal();
      }
    },
    closeModal() {
      this.loading = false;
      this.$refs.addReserve.clearData();
      this.$emit('input', false);
    },
    confirm() {
      this.$refs.addReserve.confirm();
    },
    // 处理确认按钮dropdown菜单点击事件
    handleConfirmAction(action) {
      if (action === 'confirmAndArrival') {
        // 创建后确认到店，弹出二次确认
        // 调用addReserve组件的校验方法
        const isValid = this.$refs.addReserve.validFields();
        if (!isValid) {
          return; // 校验失败，不继续执行
        }
        this.showConfirmAndArrivalModal();
      }
    },
    // 显示创建后确认到店确认弹窗
    showConfirmAndArrivalModal() {
      this.confirmModalConfig = {
        title: '创建预约后直接确认到店，将跳过"确认到店"步骤',
        content: '该操作旨在减少某些场景下店员的操作复杂度。请门店结合实际情况进行操作',
        okText: '确定提交',
        cancelText: '我再想想',
        onOk: () => {
          this.confirmModalVisible = false;
          this.confirmAndArrival();
        },
      };
      this.confirmModalVisible = true;
    },
    // 执行创建后确认到店
    confirmAndArrival() {
      // 先进行表单校验
      if (!this.$refs.addReserve) {
        this.$Message.error('表单未准备就绪，请稍后重试');
        return;
      }



      // 设置标记，表示创建成功后需要确认到店
      this.needArrivalAfterCreate = true;
      // 执行创建预约
      this.$refs.addReserve.confirm();
    },
    // 执行创建预约后的确认到店操作
    executeArrivalAfterCreate(reserveId, params) {
      this.$api
        .getReservev2ReserveArrival({
          complete_reserve: 0,
          id: reserveId,
        })
        .then(() => {
          this.$emit('success', null, 'refresh', params);
          this.$Message.success('预约创建成功，已自动确认到店');
        })
        .catch(error => {
          console.error('确认到店失败:', error);
          this.$Message.error('预约创建成功，但确认到店失败，请手动确认');
        });
    },
  },
};
</script>

<style scoped lang="less">
.reserve-modal {
  ::v-deep .ivu-modal-body {
    max-height: 500px;
    min-height: 500px;
    overflow-y: auto;
  }
}

/* 组合按钮样式 */
.button-group-modal {
  display: inline-flex;
  margin-left: 8px;

  .main-button-modal {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-right: none !important;
  }

  .dropdown-button-modal {
    .dropdown-trigger-modal {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
      border-left: 1px solid rgba(255, 255, 255, 0.2) !important;
      padding: 0 8px !important;
      min-width: auto !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }
}

.reserve-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
