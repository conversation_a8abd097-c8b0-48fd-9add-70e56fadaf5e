<template>
  <div class="board">
    <div class="board-action">
      <div>
        <div class="arrow arrow-left" @click="e => changeDate(e, 'left')">
          <Button style="padding: 0 5px">
            <Icon type="ios-arrow-back" :size="20" />
          </Button>
        </div>
        <DatePicker
          type="date"
          class="mr-10"
          :value="selectedDate"
          placeholder="请选择日期"
          format="yyyy-MM-dd"
          style="width: 120px"
          :options="disabledOption"
          :clearable="false"
          @on-change="changeDate"
          @keyup.enter.native="getReservePanelList"
        />
        <div class="arrow arrow-right" @click="e => changeDate(e, 'right')">
          <Button :disabled="isDisabledNextDay" style="padding: 0 5px">
            <Icon type="ios-arrow-forward" :size="20" />
          </Button>
        </div>
        <Button class="mr-10" @click="e => changeDate(e, 'current')">回到今天</Button>
      </div>
      <div>
        <Tooltip content="调整看板大小" placement="top" :offset="-6">
          <Poptip title="" popper-class="resize_btn-popper" :offset="-5" :width="200" placement="bottom">
            <div class="resize_btn mr10">
              <Icon type="ios-add-circle-outline" :size="14" />
            </div>
            <template #content>
              <div class="resize_btn-action">
                <div>
                  <div>宽度</div>
                  <Slider
                    v-model="initBorder.width"
                    @on-input="changeBorderWidth"
                    :min="110"
                    :max="550"
                    :step="10"
                  ></Slider>
                </div>
                <div>
                  <div>高度</div>
                  <Slider
                    v-model="initBorder.height"
                    @on-input="changeBorderHeight"
                    :min="45"
                    :max="450"
                    :step="5"
                  ></Slider>
                </div>
              </div>
            </template>
          </Poptip>
        </Tooltip>
        <Button class="mr10" type="primary" @click="addReserveEvent('1')">预约服务</Button>
        <Button class="mr10 doc-btn" type="default" @click="addReserveEvent('2')">预约医生</Button>
        <Button class="mr10" style="padding: 0">
          <KLink
            style="height: 100%; display: flex; align-items: center; padding: 0 10px"
            :to="{ path: '/setting/member/list?componentsName=management' }"
            target="_blank"
          >
            排班管理
          </KLink>
        </Button>
        <Button @click="openBoardSetting">看板设置</Button>
        <div style="width: 50px"></div>
      </div>
    </div>
    <div
      class="board-panel"
      :style="{ '--panel-cell-w': initBorderBox.width + 'px', '--panel-cell-h': initBorderBox.height + 'px' }"
    >
      <div class="panel-box">
        <div class="panel-header">
          <div class="panel-nav">
            <div
              class="top-scroll-container"
              :style="{ left: -scroll.x + 'px' }"
              @mousedown="e => handleResize(e, 'w', 'down')"
              @mousemove="e => handleResizeMove(e, 'w')"
              @mouseup="e => handleResize(e, 'w', 'up')"
            >
              <div
                :class="`header-cell ${+item.is_rest === 1 && 'angled-135'}`"
                :style="{
                  width: item.width + 'px',
                }"
                v-for="(item, index) in list"
                :key="index"
              >
                <span>{{ item.label }}</span>
                <span
                  class="role-tag"
                  v-if="item.type"
                  :style="{
                    color: item.type === 'DOCTOR' ? '#63AAF0' : '#8A71D9',
                    borderColor: item.type === 'DOCTOR' ? '#63AAF0' : '#8A71D9',
                    backgroundColor: item.type === 'DOCTOR' ? '#FAFDFF' : '#FAF9FF',
                    overflow: 'unset',
                  }"
                >
                  {{ item.type_text }}
                </span>
                <div class="handle-resize" :data-index="index">
                  <span class="drag-line"></span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-left-bar">
          <div
            class="left-scroll-container"
            :style="{ top: -scroll.y + 'px' }"
            @mousedown="e => handleResize(e, 'h', 'down')"
            @mousemove="e => handleResizeMove(e, 'h')"
            @mouseup="e => handleResize(e, 'h', 'up')"
          >
            <div
              v-for="(item, index) in leftCellList"
              :style="{ height: item.height + 'px' }"
              :key="index"
              class="left-header-cell time"
            >
              <div class="time-text">{{ item.start }}</div>
              <div v-if="index !== 0" class="handle-resize" :data-index="index">
                <span class="drag-line"></span>
              </div>
            </div>
          </div>
        </div>
        <div ref="panel-right-bar" class="panel-right-bar">
          <div class="right-scroll-container">
            <div class="current-time-fixed" :style="{ top: -scroll.y + 'px' }">
              <div ref="current-time" class="current-time" :style="{ marginTop: currentTimeTop - 25 + 'px' }">
                <div>最近时间</div>
                <div>{{ currentTime }}</div>
              </div>
            </div>
          </div>
          <div class="back-cur-time">
            <div
              :style="{
                display: isViewPort ? 'none' : 'flex',
              }"
              @click="toScrollCurTime('smooth')"
            >
              当前时间
              <img class="arrow-hover" src="../../../assets/image/reserve/l-arrow-hover.png" />
              <img class="arrow" src="../../../assets/image/reserve/l-arrow.png" />
            </div>
          </div>
        </div>
        <div ref="panelContent" v-loading="loading" class="panel-content">
          <div ref="panelContentScroll" class="panel-content-scroll" @scroll="handleScroll">
            <div ref="contentContainer" class="content-container">
              <div ref="emptyCellList" class="emptyCellList">
                <div
                  class="emptyCellList-column"
                  :style="{
                    width: item.width + 'px',
                  }"
                  v-for="(item, index) in list"
                  :key="index"
                  @dragover.prevent
                >
                  <div
                    class="emptyCellList-row"
                    v-for="(itm, i) in item.children"
                    :key="i"
                    :style="{
                      width: itm.width + 'px',
                      height: itm.height + 'px',
                      backgroundColor: isDraggingArea(itm, index, i),
                    }"
                    :data-x="index"
                    :data-y="i"
                    @dragover.prevent
                  >
                    <div
                      v-if="itm.is_rest"
                      class="rest-reserve"
                      :style="{ backgroundColor: isDraggingArea(itm, index, i) }"
                    >
                      休息
                    </div>
                    <div
                      v-if="isTodayAfter && !itm.is_rest && !isDisabled(itm, i)"
                      class="add-reserve"
                      @click.prevent="handleAdd({ ...item, ...itm }, item.type === 'DOCTOR' ? '2' : '1')"
                    >
                      新增预约
                    </div>
                  </div>
                </div>
              </div>
              <div class="boardCellList">
                <el-popover
                  v-for="(item, idx) in serveList"
                  :key="item.id"
                  placement="right"
                  popper-class="custom-board-tooltip"
                  :open-delay="500"
                  width="300"
                  effect=""
                  trigger="hover"
                  enterable
                  :disabled="isDragging"
                  @hide="handleTooltipHide"
                  ref="serveItemPopover"
                >
                  <!-- 当需要开启拖拽的时候，将下面的替换进去 -->
                  <!-- :draggable="item.type === 'PHYSIO' && isTodayAfter && item.is_cur_clinic === '1'"-->
                  <div
                    :ref="`panel-cell-${item.reserve_id}`"
                    class="serve-item"
                    slot="reference"
                    :draggable="false"
                    :key="`serve-item-${item.id}`"
                    :style="{
                      left: item.x + 2 + 'px',
                      top: item.y + 2 + 'px',
                      width: item.w - 4 + 'px',
                      height: item.h - 5 + 'px',
                      backgroundColor: backgroundColor(item),
                      cursor: item.type === 'PHYSIO' && isTodayAfter && item.is_cur_clinic === '1' ? 'move' : 'default',
                      // borderLeft: `3px solid ${compute_style(item, 'fontColor')}`,
                    }"
                    :data-id="item.reserve_id"
                    :data-x="Math.ceil(item.x / initBorderBox.width)"
                    :data-y="Math.ceil(item.y / initBorderBox.height)"
                    @click.prevent="handleEditReserve(item)"
                  >
                    <div
                      class="item-left"
                      :style="{ background: compute_style(item, 'fontColor') }"
                      :data-id="item.reserve_id"
                      :data-x="Math.ceil(item.x / initBorderBox.width)"
                      :data-y="Math.ceil(item.y / initBorderBox.height)"
                    ></div>
                    <div
                      class="item-right"
                      :data-id="item.reserve_id"
                      :data-x="Math.ceil(item.x / initBorderBox.width)"
                      :data-y="Math.ceil(item.y / initBorderBox.height)"
                      :style="{
                        paddingTop:
                          item.h > initBorderBox.height / 2
                            ? '6px'
                            : item.h > initBorderBox.height / 4 && item.h > 25
                            ? '2px'
                            : 0,
                      }"
                    >
                      <div
                        class="item-right-info"
                        :data-id="item.reserve_id"
                        :data-x="Math.ceil(item.x / initBorderBox.width)"
                        :data-y="Math.ceil(item.y / initBorderBox.height)"
                        :style="{
                          paddingLeft: Math.ceil((item.w / initBorderBox.width) * 15) + 'px',
                          paddingTop: 0,
                        }"
                      >
                        <div
                          class="text text_ellipsis"
                          :data-id="item.reserve_id"
                          :data-x="Math.ceil(item.x / initBorderBox.width)"
                          :data-y="Math.ceil(item.y / initBorderBox.height)"
                          v-if="item.h >= 45"
                          :style="{
                            marginBottom:
                              item.h < initBorderBox.height * 0.7 && item.h > initBorderBox.height * 0.5 ? '5px' : 0,
                          }"
                        >
                          <div v-if="item.is_cur_clinic === '1'">{{ item.h_m_time }} 开始</div>
                          <div v-else>
                            {{ item.start_time | data_format('HH:mm') }} - {{ item.end_time | data_format('HH:mm') }}
                          </div>
                        </div>
                        <div
                          class="txt-serve text_ellipsis"
                          :style="{
                            color: compute_style(item, 'fontColor'),
                            margin: item.h >= 83 ? '10px 0' : '2px 0',
                          }"
                          :data-id="item.reserve_id"
                          :data-x="Math.ceil(item.x / initBorderBox.width)"
                          :data-y="Math.ceil(item.y / initBorderBox.height)"
                          v-if="item.type === 'PHYSIO' && item.h >= 67"
                        >
                          <div v-if="item.is_cur_clinic === '1'">{{ item.service_text || '未选择服务' }}</div>
                          <div v-else class="ecs">已在"{{ item.clinic_name }}"被预约</div>
                        </div>
                        <div
                          class="text text_ellipsis"
                          :data-id="item.reserve_id"
                          :data-x="Math.ceil(item.x / initBorderBox.width)"
                          :data-y="Math.ceil(item.y / initBorderBox.height)"
                          v-if="item.is_cur_clinic === '1'"
                        >
                          <span
                            :data-id="item.reserve_id"
                            :data-x="Math.ceil(item.x / initBorderBox.width)"
                            :data-y="Math.ceil(item.y / initBorderBox.height)"
                          >
                            {{ item.user_name }}
                          </span>
                          <span
                            :data-id="item.reserve_id"
                            :data-x="Math.ceil(item.x / initBorderBox.width)"
                            :data-y="Math.ceil(item.y / initBorderBox.height)"
                          >
                            {{ item.mobile }}
                          </span>
                        </div>
                        <div
                          class="text text_ellipsis"
                          :data-id="item.reserve_id"
                          :data-x="Math.ceil(item.x / initBorderBox.width)"
                          :data-y="Math.ceil(item.y / initBorderBox.height)"
                          v-if="item.is_cur_clinic !== '1' && item.h < 67"
                        >
                          <span
                            :data-id="item.reserve_id"
                            :style="{
                              color: compute_style(item, 'fontColor'),
                            }"
                            :data-x="Math.ceil(item.x / initBorderBox.width)"
                            :data-y="Math.ceil(item.y / initBorderBox.height)"
                          >
                            已在"{{ item.clinic_name }}"被预约
                          </span>
                        </div>
                      </div>
                      <div
                        v-if="(item.user_remark || item.remark) && item.h > 125"
                        class="item-right-remark"
                        :data-id="item.reserve_id"
                        :data-x="Math.ceil(item.x / initBorderBox.width)"
                        :data-y="Math.ceil(item.y / initBorderBox.height)"
                        :style="{
                          borderColor: compute_style(item, 'lineColor'),
                          paddingLeft: Math.ceil((item.w / initBorderBox.width) * 15) + 'px',
                          paddingTop:
                            item.h >= initBorderBox.height / 3 ? '6px' : item.h >= initBorderBox.height / 4 ? '2px' : 0,
                        }"
                      >
                        <div
                          v-if="
                            (item.user_remark && !item.remark && item.h > 125) ||
                            (item.user_remark && item.remark && item.h > 142)
                          "
                          class="remark-text text_ellipsis"
                          :data-id="item.reserve_id"
                          :data-x="Math.ceil(item.x / initBorderBox.width)"
                          :data-y="Math.ceil(item.y / initBorderBox.height)"
                        >
                          顾客备注: {{ item.user_remark }}
                        </div>
                        <div
                          v-if="
                            (item.remark && !item.user_remark && item.h > 125) ||
                            (item.user_remark && item.remark && item.h > 142)
                          "
                          class="remark-text text_ellipsis"
                          style="margin-top: 6px"
                          :data-id="item.reserve_id"
                          :data-x="Math.ceil(item.x / initBorderBox.width)"
                          :data-y="Math.ceil(item.y / initBorderBox.height)"
                        >
                          门店备注: {{ item.remark }}
                        </div>
                      </div>
                    </div>
                    <div
                      class="item-status"
                      :style="{ background: compute_style(item, 'fontColor') }"
                      :data-id="item.reserve_id"
                      :data-x="Math.ceil(item.x / initBorderBox.width)"
                      :data-y="Math.ceil(item.y / initBorderBox.height)"
                      v-if="item.w > initBorderBox.width / 2 && item.is_cur_clinic === '1'"
                    >
                     <svg-icon name="mini_program" size="11" v-if="item.source === 'rst_weapp'"></svg-icon> {{ compute_style(item, 'desc') }}
                    </div>
                  </div>
                  <div ref="reserve-tooltip">
                    <div class="reserve-tooltip-box" v-if="item.is_cur_clinic === '1'">
                      <div>
                        <div class="user">
                          <div
                            class="avatar"
                            v-viewer="[
                              item.user_avatar || 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png',
                            ]"
                          >
                            <img
                              style="width: 40px; height: 40px"
                              :src="
                                item.user_avatar || 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png'
                              "
                              alt=""
                            />
                          </div>
                          <div class="info">
                            <div class="name-box">
                              <div class="username">{{ item.user_name || '-' }}</div>
                              <div class="tag" v-if="item.is_new === '1'">新客</div>
                            </div>
                            <div class="mobile">{{ item.mobile || '-' }}</div>
                          </div>
                        </div>
                        <div class="serve">
                          <div v-if="item.type === 'DOCTOR'">
                            预约服务:
                            <span :style="{ color: compute_style(item, 'fontColor'), marginLeft: '3px' }">
                              预约咨询
                            </span>
                          </div>
                          <div v-else>
                            预约服务:
                            <span :style="{ color: compute_style(item, 'fontColor'), marginLeft: '3px' }">
                              {{ item.service_text || '未选择服务' }}
                            </span>
                          </div>
                          <div>
                            {{ item.type === 'DOCTOR' ? '医生' : getPhysioName }}:
                            <span :style="{ color: compute_style(item, 'fontColor'), marginLeft: '3px' }">
                              {{ item.name || '-' }}
                            </span>
                          </div>
                        </div>
                        <div class="user_remark flex" v-if="item.source === 'rst_weapp'">
                          <div style="margin-right: 5px; flex-shrink: 0">顾客备注:</div>
                          <div class="text">
                            {{ item.user_remark || '-' }}
                          </div>
                        </div>
                        <div class="user_remark flex">
                          <div style="margin-right: 5px; flex-shrink: 0">门店备注:</div>
                          <div class="text">
                            {{ item.remark || '-' }}
                          </div>
                        </div>
                      </div>
                      <div class="footer">
                        <Poptip
                          confirm
                          title="确定取消该预约吗?"
                          popper-class="zIndex999"
                          v-if="item.reserve_status != '3' && item.reserve_status != '4'"
                          @on-ok="cancelReserve(item)"
                          ok-text="确定取消"
                          cancel-text="暂不取消"
                          :ref="'cancelPoptip' + idx"
                          :transfer="false"
                          @on-popper-show="handlePopperPopTipShow(idx, 'cancelPoptip')"
                        >
                          <Button class="ml10" size="small">取消预约</Button>
                        </Poptip>
                        <Poptip
                          confirm
                          title="是否确认到店吗?"
                          popper-class="zIndex999"
                          v-if="
                            item.is_arrival !== '1' &&
                            item.reserve_status != '3' &&
                            item.reserve_status != '4' &&
                            item.reserve_status != '5' &&
                            item.source !== 'channel_my_pingan' &&
                            item.source !== 'rst_weapp'
                          "
                          @on-ok="getReservev2ReserveArrival(item)"
                          ok-text="确认到店"
                          cancel-text="取消"
                          :ref="'arrivePoptip' + idx"
                          :transfer="false"
                          @on-popper-show="handlePopperPopTipShow(idx, 'arrivePoptip')"
                        >
                          <Button class="ml10" type="primary" size="small">确认到店</Button>
                        </Poptip>
                        <!-- 小程序预约单的确认到店dropdown -->
                        <div
                          v-if="
                            item.is_arrival !== '1' &&
                            item.reserve_status != '3' &&
                            item.reserve_status != '4' &&
                            item.reserve_status != '5' &&
                            item.source === 'rst_weapp'
                          "
                          class="ml10 button-group"
                        >
                          <Button type="primary" size="small" @click="getReservev2ReserveArrival(item)" class="main-button">
                            确认到店
                          </Button>
                          <Dropdown @on-click="(name) => handleArrivalAction(name, item)" :transfer="false" class="dropdown-button dropdown-button--small">
                            <Button type="primary" size="small" class="dropdown-trigger">
                              <Icon type="ios-more"></Icon>
                            </Button>
                            <DropdownMenu slot="list" class="high-z-index">
                              <DropdownItem name="arrivalAndComplete">确认到店后直接完成服务</DropdownItem>
                            </DropdownMenu>
                          </Dropdown>
                        </div>
                        <Button
                          v-if="
                            item.is_arrival !== '1' &&
                            item.reserve_status != '3' &&
                            item.reserve_status != '4' &&
                            item.reserve_status != '5' &&
                            item.source === 'channel_my_pingan'
                          "
                          class="ml10"
                          type="primary"
                          size="small"
                          @click="getReservev2ReserveArrival(item)"
                        >
                          确认到店
                        </Button>
                        <Button
                          v-if="item.is_arrival !== '1' && item.reserve_status == '5'"
                          class="ml10"
                          type="primary"
                          size="small"
                          @click="handleEditReserve(item)"
                        >
                          确认预约
                        </Button>
                          <template
                          v-if="
                            (item.reserve_status != '3' && item.reserve_status != '4') &&
                            item.type !== 'DOCTOR' &&
                            +item.order_id &&
                            +item.is_payed === 1
                          "
                        >
                          <Button type="primary" class="ml10" size="small" @click="toDetail(item)">订单详情</Button>
                        </template>
                        <Poptip
                          confirm
                          title="是否确认完成服务?"
                          popper-class="zIndex999"
                          v-if="
                            item.reserve_status != '3' &&
                            item.reserve_status != '4' &&
                            item.type != 'DOCTOR' &&
                            item.is_arrival == '1' &&
                            item.is_payed == '1'
                          "
                          @on-ok="finished(item)"
                          ok-text="确认完成"
                          cancel-text="暂未完成"
                          :ref="'finishedPoptip' + idx"
                          :transfer="false"
                          :width="180"
                          @on-popper-show="handlePopperPopTipShow(idx, 'finishedPoptip')"
                        >
                          <Button class="ml10" type="primary" size="small"> 完成服务</Button>
                        </Poptip>
                        <template
                          v-if="
                            item.is_arrival === '1' &&
                            item.is_payed !== '1' &&
                            item.reserve_status != '3' &&
                            item.reserve_status != '4' &&
                            item.reserve_status != '5' &&
                            item.type != 'DOCTOR' &&
                            item.source !== 'rst_weapp' &&
                            item.source !== 'channel_my_pingan'
                          "
                        >
                          <div class="ml10 button-group">
                            <Button type="primary" size="small" @click="preCollection(item, false)" class="main-button">
                              收款
                            </Button>
                            <Dropdown placement="bottom-end" @on-click="(name) => handlePaymentAction(name, item, false)" :transfer="false" class="dropdown-button dropdown-button--small">
                              <Button type="primary" size="small" class="dropdown-trigger">
                                <Icon type="ios-more"></Icon>
                              </Button>
                              <DropdownMenu slot="list" class="high-z-index">
                                <DropdownItem name="payAndComplete">收款后直接完成服务</DropdownItem>
                              </DropdownMenu>
                            </Dropdown>
                          </div>
                        </template>
                        <Poptip
                          confirm
                          title="该订单为月结订单，用户无需付款。是否确认进行「收款」操作?"
                          popper-class="zIndex999"
                          v-if="
                            item.is_arrival === '1' &&
                            item.is_payed !== '1' &&
                            item.reserve_status != '3' &&
                            item.reserve_status != '4' &&
                            item.reserve_status != '5' &&
                            item.type != 'DOCTOR' &&
                            item.source === 'channel_my_pingan'
                          "
                          @on-ok="preCollection(item, false)"
                          ok-text="确认"
                          cancel-text="取消"
                          :ref="'finishedPoptip' + idx"
                          :transfer="false"
                          :width="180"
                          @on-popper-show="handlePopperPopTipShow(idx, 'finishedPoptip')"
                        >
                          <Button class="ml10" type="primary" size="small"> 收款</Button>
                        </Poptip>

                      </div>
                    </div>
                    <div class="reserve-tooltip-box" v-else>
                      <div>
                        {{ item.start_time | data_format('HH:mm') }} - {{ item.end_time | data_format('HH:mm') }}
                      </div>
                      <div
                        :style="{
                          color: compute_style(item, 'fontColor'),
                        }"
                      >
                        已在"{{ item.clinic_name }}"被预约
                      </div>
                    </div>
                  </div>
                </el-popover>
              </div>
              <div
                class="current-time-line"
                :style="{ width: scroll.w + 'px', top: currentTimeTop + 'px', left: scroll.line_x + 'px' }"
              >
                <div class="point"></div>
              </div>
            </div>
          </div>
        </div>
        <div class="status">
          <div class="status-item" v-for="item in status_options" :key="item.status">
            <div
              class="status-icon"
              :style="{
                'background-image': `url('${item.img}')`,
                backgroundColor: item.color,
                color: compute_style(item),
                border: item.borderColor ? `1px solid ${item.borderColor}` : 'unset',
              }"
            ></div>
            <div class="status-text">{{ item.desc }}</div>
          </div>
        </div>
        <div class="action-arrow">
          <div class="action-arrow-item action-arrow-l">
            <Icon type="ios-arrow-dropleft" style="cursor: pointer" :size="24" @click="handleScrollLeft" />
          </div>
          <div class="action-arrow-item action-arrow-r">
            <Icon type="ios-arrow-dropright" style="cursor: pointer" :size="24" @click="handleScrollRight" />
          </div>
        </div>
      </div>
    </div>
    <board-setting :visible.sync="boardVisible" @init="getReservePanelList" />
    <add-reserve
      v-model="addReserve"
      :type="reserve_type"
      :row="{
        ...selectedRow,
      }"
      :options="{
        out_sales_channel_desc,
      }"
      @success="changeDate"
    />
    <edit-reserve
      :visible.sync="editReserve"
      :selected-row.sync="selectedRow"
      :selectedDate="selectedDate"
      @success="changeDate"
      @addReserve="handleAdd"
      @cancelReserve="cancelReserve"
      @preCollection="preCollection"
      @preCollectionAndComplete="preCollectionAndCompleteFromModal"
      @getReservev2ReserveArrival="getReservev2ReserveArrival"
      @getReservev2ReserveArrivalAndComplete="getReservev2ReserveArrivalAndCompleteFromModal"
      @toDetail="toDetail"
      @finished="finished"
      @cancelPay="cancelPay"
      :options="{
        out_sales_channel_desc,
      }"
    />
    <!-- 已支付取消预约 -->
    <cancel-reservation
      v-model="cancelReservationVisible"
      @success="getReservePanelList"
      :row="selectedRow"
    ></cancel-reservation>

    <!-- 支付 -->
    <template v-if="is_rst">
      <rst-pay-dialog
        v-model="rstPayVisible"
        @changeVisible="val => !val && getReservePanelList()"
        :orderId="order_id"
        :isLocalClose="true"
        :completeAfterPay="completeAfterPay"
        @localClose="handlePaySuccess"
      ></rst-pay-dialog>
    </template>
    <template v-else>
      <k-pay-dialog
        v-model="payVisible"
        @changeVisible="val => !val && getReservePanelList()"
        :order_id="order_id"
        :is_rst="is_rst"
        :isLocalClose="true"
        :completeAfterPay="completeAfterPay"
        @localClose="handlePaySuccess"
      ></k-pay-dialog>
    </template>
    <!-- 支付成功弹窗 -->
    <success-pay-dialog
      v-model="successVisible"
      :success-data="successData"
      v-if="successVisible"
      :isLocalClose="true"
      @input="handleSuccessPayDialogClose"
    />
    <cancel-model
      :visible.sync="cancelVisible"
      :loading="cancelLoading"
      title="取消原因"
      label="请输入取消原因"
      placeholder="请输入取消原因"
      @ok="channelCancel"
      @change="cancelVisible = false"
    />
    <custom-dialog
      :visible.sync="scanCodePayVisible"
      okText="去扫码"
      content="此预约单来源为抖音或美团，请扫描抖音或美团的券码，确认完成服务。"
      :on-ok="handleToCardVerification"
    />

    <!--未通过主体验证 拦截收款弹窗-->
    <auth-warning-modal ref="authWarningRef"></auth-warning-modal>

    <!-- 通用确认弹窗 -->
    <confirm-modal
      v-model="confirmModalVisible"
      :title="confirmModalConfig.title"
      :content="confirmModalConfig.content"
      :ok-text="confirmModalConfig.okText"
      :cancel-text="confirmModalConfig.cancelText"
      @on-ok="confirmModalConfig.onOk"
    />
  </div>
</template>
<script>
import { getPhysioName } from '@/libs/runtime';
import BoardSetting from './components/setting.vue';
import moment from 'moment';
import RstPayDialog from '@/components/k-pay-dialog/rst-pay-dialog.vue';
import KPayDialog from '@/components/k-pay-dialog/index.vue';
import {
  handleCollisions,
  culAxisY,
  culHeight,
  minutesSinceMidnight,
  parseTimeIntervals,
  dragMouseTopOfStart,
} from './utils/helper';
import AddReserve from '../listing/components/addReserveModal.vue';
import EditReserve from './components/edit-reserve.vue';
import ConfirmModal from './components/ConfirmModal.vue';
import './utils/draggable';
import { isEmpty } from '../../../utils/helper';
import { isRstClinic } from '../../../libs/runtime';
import cancelReservation from '../listing/components/cancelReservation.vue';
import { throttle } from 'lodash';
import SuccessPayDialog from '@/components/k-pay-dialog/pay-status/SuccessPayDialog.vue';
import CancelModel from './components/cancelModel.vue';
import CustomDialog from '@/components/custom-dialog/index.vue';
// import ReasonModal from '@/view/reserve/board/components/cancelModel.vue';
import S from '@/libs/util';
import AuthWarningModal from '@/components/AuthWarning/AuthWarningModal.vue';

export default {
  name: 'index',
  components: {
    // ReasonModal,
    CustomDialog,
    CancelModel,
    SuccessPayDialog,
    cancelReservation,
    AddReserve,
    BoardSetting,
    EditReserve,
    KPayDialog,
    RstPayDialog,
    AuthWarningModal,
    ConfirmModal,
  },
  data() {
    return {
      moment,
      list: [],
      listDate: '',
      times: [],
      selectedDate: moment().format('YYYY-MM-DD'),
      boardVisible: false,
      addReserve: false,
      editReserve: false,
      cancelReservationVisible: false,
      scanCodePayVisible: false,
      items: [],
      initBorder: {
        width: 220,
        height: 90,
      },
      initBorderBox: {
        width: 220,
        height: 90,
      },
      scroll: {
        x: 0,
        y: 0,
        line_x: 0,
        w: 0,
      },
      status_options: [
        { status: 7, desc: '待确认', color: '#F97D4A', borderColor: '#F97D4A', fontColor: '#F97D4A' },
        {
          status: 2,
          desc: '待到店',
          color: '#8A71D9',
          borderColor: '#F7F5FD',
          lineColor: '#E3E4F5',
          fontColor: '#8A71D9',
        },
        {
          status: 1,
          desc: '已到店',
          color: '#62BBBC',
          borderColor: '#F0FEFF',
          lineColor: '#E3F4F5',
          fontColor: '#62BBBC',
        },
        {
          status: 9999,
          desc: '已完成',
          color: '#5785F6',
          borderColor: '#EDF2FF',
          lineColor: '#DCE6FF',
          fontColor: '#5785F6',
        },
        { status: 6, desc: '占用', color: '#E04F4F', borderColor: '#E04F4F', fontColor: '#E04F4F' },
        { status: 4, desc: '空闲', color: '#fffff', borderColor: '#ebedf0', fontColor: '#333' },
        { status: 5, desc: '休息', color: '#EBEBEB', borderColor: '#C4C4C4', fontColor: '#EBEBEB' },
      ],
      setting_info: {
        interval_min: 15,
      },
      serveList: [],
      out_sales_channel_desc: [],
      serveImgList: {},
      selectedRow: {},
      panelData: {},
      dragData: {
        targetId: '', // 被拖拽的id
        mouseY: 0, // 点击拖拽时鼠标在被拖拽目标距离顶点的位置
        hoverNodes: [], // 放置的cell，放置的是start
        x: 0, //
      },
      mouseAxis: {
        x: 0,
        y: 0,
        index: 0,
        down: false,
      },
      loading: false,
      isDragging: false,
      isViewPort: true,
      reserve_type: '1',
      disabledOption: {},
      max_day: 0,

      // 支付逻辑
      payVisible: false, // 支付弹窗显示的标识
      rstPayVisible: false, // 支付弹窗显示的标识
      order_id: '', // 订单id,
      onceShowAddReserveEvent: false, // '打开添加预约弹窗'
      successVisible: false,
      successData: {},
      cancelVisible: false,
      cancelLoading: false,
      completeAfterPay: false, // 标记支付后是否需要完成服务

      getSetInfo: {},

      // 通用确认弹窗
      confirmModalVisible: false,
      confirmModalConfig: {
        title: '',
        content: '',
        okText: '确定',
        cancelText: '取消',
        onOk: ()=>{}
      },
    };
  },
  computed: {
    isTodayAfter() {
      const today = moment().format('YYYY-MM-DD 00:00:00');
      const selectday = moment(this.selectedDate).format('YYYY-MM-DD 00:00:00');
      return moment(today).isSameOrBefore(selectday);
    },
    leftCellList() {
      console.log('=>(index.vue:828) this.this.list', this.list);
      return this.list?.[0]?.children || [];
    },
    getPhysioName() {
      return getPhysioName();
    },
    currentTime() {
      const times = this.times;
      const currentDate = moment().format('HH:mm');
      const findRecentTime = times?.find(time => {
        return moment('1999-09-09 ' + currentDate).isBefore(moment('1999-09-09 ' + time.start));
      });
      return findRecentTime?.start || times?.[times.length - 1]?.start;
    },
    currentTimeTop() {
      const currentTime = this.currentTime;
      const times = this.times;
      const index = times.findIndex(item => item.start === currentTime);
      if (index === 0) return 0;
      const now = minutesSinceMidnight(currentTime);
      const list = this.list?.[0]?.children?.filter(item => now > minutesSinceMidnight(item.start)) || [];
      return list.reduce((acc, item) => acc + item.height, 0);
    },
    isDisabled() {
      return (val, i) => {
        const today = moment().format('YYYY-MM-DD 00:00:00');
        const selectedDate = moment(this.selectedDate).format('YYYY-MM-DD 00:00:00');
        if (moment(today).isAfter(selectedDate)) return true;
        // 未来不可预约的时间置灰
        // 获取开始预约的时间点
        let time_start = this.getSetInfo?.time_start;
        if (
          time_start &&
          moment(`${this.selectedDate} ${val.start}`).isBefore(moment(`${this.selectedDate} ${time_start}`))
        ) {
          return true;
        }
        if (moment(today).isBefore(selectedDate)) return false;
        const currentTime = this.currentTime;
        const times = this.times;
        let index = times.findIndex(item => item.start === currentTime);
        if (index !== 0) {
          index = index * this.initBorderBox.height;
        }
        return index / this.initBorderBox.height > i;
      };
    },
    backgroundColor() {
      return item => {
        let orig_status = +item.orig_status;
        if (item.orig_is_arrival === '2') {
          orig_status = 2;
        }
        if (item.orig_is_arrival === '1') {
          orig_status = 1;
        }
        if (+item.orig_status === 9999) {
          orig_status = 9999;
        }
        if (item.reserve_status === '5') {
          return '#FFF6EF';
        }
        const bgc = this.status_options.find(status => orig_status === +status.status);
        if (orig_status === 5) return `url('${bgc.img}')`;
        return bgc?.borderColor;
      };
    },
    compute_style() {
      return (item, key = 'fontColor') => {
        let orig_status = +item.orig_status;
        if (item.orig_is_arrival === '2') {
          orig_status = 2;
        }
        if (item.orig_is_arrival === '1') {
          orig_status = 1;
        }
        if (+item.orig_status === 9999) {
          orig_status = 9999;
        }
        if (item.is_cur_clinic === '0') {
          orig_status = 6;
        }
        if (item.reserve_status === '5') {
          orig_status = 7;
        }
        if(orig_status === 1 && item.is_payed!=='1' && key === 'desc'){
          return '到店未付款'
        }
        return this.status_options.find(status => orig_status === +status.status)?.[key];
      };
    },
    isDraggingArea() {
      return (item, index, i) => {
        const flag = this.dragData?.hoverNodes?.includes(item.start) && index === +this.dragData.x;
        if (flag) return 'rgba(99, 170, 240, 0.2)';
        if (item.is_rest) return '#fbfbfb';
        if (this.isDisabled(item, i)) return '#f5f5f5';
        return '#fff';
      };
    },
    getServices() {
      return list => {
        let name = [];
        list?.forEach(item => {
          name.push(item.goods_service.name);
        });
        return name.join('、') || '';
      };
    },
    getPhysical() {
      return list => {
        let name = [];
        list?.forEach(item => {
          name.push(item.physio.name);
        });
        return name.join('、') || '';
      };
    },
    is_rst() {
      return isRstClinic();
    },
    isDisabledNextDay() {
      const maxDay = moment(moment().format('YYYY-MM-DD 00:00:00')).add(this.max_day || 365, 'd');
      const curDay = moment(moment(this.selectedDate).format('YYYY-MM-DD 00:00:00'));
      return curDay.isSameOrAfter(maxDay);
    },
  },
  watch: {
    loading: {
      immediate: true,
      handler(val) {
        if (!val && this.onceShowAddReserveEvent) {
          this.onceShowAddReserveEvent = false; // 只打开一次
          setTimeout(() => {
            this.addReserveEvent('1');
          }, 300);
        }
      },
    },
  },
  mounted() {
    this.init();
    this.getReservev2ReserveOptions();
  },
  activated() {
    this.init();
    this.getReservev2ReserveOptions();
  },
  destroyed() {
    const container = this.$refs.contentContainer;
    container?.removeEventListener('dragstart', this.dragstart);
    container?.removeEventListener('dragover', this.dragover);
    container?.removeEventListener('drop', this.ondrag);
  },
  methods: {
    init() {
      const dragInfo = localStorage.getItem('dragInfo');
      if (dragInfo) {
        const initBorderBox = JSON.parse(dragInfo);
        this.initBorder.height = initBorderBox?.height || 90;
        this.initBorder.width = initBorderBox?.width || 220;
        this.initBorderBox.height = initBorderBox?.height || 90;
        this.initBorderBox.width = initBorderBox?.width || 220;
      }
      const { uid, from } = this.$route.query;
      if (uid && from) {
        this.onceShowAddReserveEvent = true;
      }
      this.getReservePanelList();
      this.getReserveSettingInfo();
    },
    handleTooltipHide() {
      document.body.click();
    },
    handlePopperPopTipShow(index, type = '') {
      try {
        const refList = ['cancelPoptip', 'arrivePoptip', 'finishedPoptip'];
        refList.forEach(ref => {
          if (ref !== type) {
            const realRef = this.$refs[ref + index];
            if (realRef) {
              realRef[0]?.handleClose();
            }
          }
        });
      } catch (e) {
        console.log('error: ', e);
      }
    },
    getReservev2ReserveOptions() {
      this.$api.getReservev2ReserveOptions().then(res => {
        this.out_sales_channel_desc = S.descToArrHandle(res.out_sales_channel_desc);
      });
    },
    handleResize(e, dic, type) {
      if (['drag-line', 'handle-resize'].includes(e.target._prevClass) && type === 'down') {
        this.mouseAxis.x = 0;
        this.mouseAxis.y = 0;
        this.mouseAxis.index = e.target.dataset.index;
        this.mouseAxis.down = true;
        if (dic === 'h') {
          this.mouseAxis.y = e.clientY;
        }
        if (dic === 'w') {
          this.mouseAxis.x = e.clientX;
        }
      }
      if (type === 'up') {
        this.mouseAxis.x = 0;
        this.mouseAxis.y = 0;
        this.mouseAxis.index = 0;
        this.mouseAxis.down = false;
      }
    },
    handleResizeMove: throttle(function (e, dic) {
      if (!this.mouseAxis.down) return;
      if (dic === 'h') {
        this.list.forEach((item, i) => {
          const height = this.mouseAxis.y !== 0 ? e.clientY - this.mouseAxis.y : 0;
          item.children.forEach((item2, i2) => {
            if (item2.height + height <= 45) return;
            if (item2.height + height >= 450) return;
            if (+this.mouseAxis.index - 1 === i2) {
              this.$set(this.list[i].children[i2], 'height', item2.height + height);
            }
          });
        });
        this.mouseAxis.y = e.clientY;
      }
      if (dic === 'w') {
        this.list.forEach((item, i) => {
          const width = this.mouseAxis.x !== 0 ? e.clientX - this.mouseAxis.x : 0;
          if (item.width + width <= 110) return;
          if (item.width + width >= 550) return;
          if (+this.mouseAxis.index === i) {
            this.$set(this.list[i], 'width', item.width + width);
            item.children.forEach((item2, i2) => {
              this.$set(this.list[i].children[i2], 'width', item2.width + width);
            });
          }
        });
        this.mouseAxis.x = e.clientX;
      }
      this.initServeList();
    }, 40),
    initList(res) {
      if (!res.setting_info) return;
      this.list = (res?.header_list || [])?.map(item => ({
        ...item,
        ...this.initBorderBox,
        label: item.name,
        userId: item.physio_id,
        avatar: item.avatar,
      }));
      if (+res.setting_info.use_member_setting === 2) {
        if (res?.list.length < 6) {
          const list = Array.from(6 - res?.list.length).fill({
            ...this.initBorderBox,
            label: '',
            userId: '0',
            physio_id: '0',
          });
          res.list = [...res.list, ...list];
        }
        this.list = res?.list
          ?.map((item, i) => ({ label: '', index: i }))
          ?.map(item => ({ ...item, ...this.initBorderBox }));
      }
      // 示例使用
      const timeRanges = [
        { start: '06:30', end: '24:00' }, // 注意：这里的 interval 字段在输出时会被覆盖
        // 可以添加更多时间区间对象...
      ];
      const result = parseTimeIntervals(timeRanges, `${this.setting_info.interval_min}m`);
      this.times = result;
      for (let i = 0; i < this.list.length; i++) {
        let list = [];
        list = result?.map((item, ii) => ({
          ...item,
          ...this.initBorderBox,
          level: 2,
          rowId: item.start,
          label: i + 1 + '-' + (ii + 1),
          is_rest: this.isRest(this.list[i], item),
        }));
        this.$set(this.list[i], 'children', list);
      }
    },
    isRest(item, target) {
      if (isEmpty(item.sleep_range)) return false;
      if (!Array.isArray(item.sleep_range)) return false;
      let flag = false;
      for (let i = 0; i < item.sleep_range.length; i++) {
        const times = item.sleep_range[i];
        const st = moment(`2020-10-01 ${times.st}`).format('YYYY-MM-DD HH:mm:ss');
        const et = moment(`2020-10-01 ${times.et}`).format('YYYY-MM-DD HH:mm:ss');
        const start = moment(`2020-10-01 ${target.start}`).format('YYYY-MM-DD HH:mm:ss');
        const end = moment(`2020-10-01 ${target.end}`).format('YYYY-MM-DD HH:mm:ss');
        if (moment(start).isSameOrBefore(moment(st)) && moment(end).isAfter(moment(st))) {
          flag = true;
          break;
        }
        if (moment(start).isSameOrAfter(moment(st)) && moment(end).isSameOrBefore(moment(et))) {
          flag = true;
          break;
        }
        if (moment(start).isBefore(moment(et)) && moment(end).isAfter(moment(et))) {
          flag = true;
          break;
        }
      }
      return flag;
    },
    initServeList() {
      const list = this.serveList
        .map(item => ({
          ...item,
          h_m_time: moment(item.reserve_time * 1000).format('HH:mm'),
        }))
        .sort((a, b) => +b.reserve_id - +a.reserve_id);
      // 根据时间，生成坐标系
      const serveList = this.list
        .map((item, i) =>
          list
            .filter((serve, index) => {
              if (+this.setting_info.use_member_setting === 2) {
                return index === i;
              }
              return serve.physio_id === item.physio_id;
            })
            ?.map(serve => ({ ...serve, ...item }))
        )
        .map((yItem, i) => {
          // 根据i的值，判断是在x哪个坐标。
          yItem.forEach(itm => {
            const x = this.list.filter((row, _i) => _i < i).reduce((pre, cur) => pre + +cur.width, 0);
            // 占用高度
            itm.x = x;
            itm.y = culAxisY(+itm.duration, itm.h_m_time, this.list[0].children);
            itm.h = culHeight(+itm.duration, itm.h_m_time, this.list[0].children);
            itm.w = itm.width;
          });
          return handleCollisions(yItem);
        });
      this.serveList = serveList.flat();
    },
    handleAdd(item, val) {
      this.reserve_type = val;
      this.selectedRow = item || {};
      if (!Number(this.selectedRow.physio_id || 0)) {
        this.selectedRow.name = '';
      }
      this.addReserve = true;
    },
    addReserveEvent(val) {
      this.reserve_type = val;
      this.selectedRow = {};
      this.addReserve = true;
    },
    handleEditReserve(item) {
      this.$refs.serveItemPopover?.forEach(c => {
        c?.doClose && c.doClose();
      });
      if (item.is_cur_clinic === '0') {
        return;
      }
      this.selectedRow = item || {};
      console.log('=>(index.vue:1156) this.selectedRow', this.selectedRow);
      this.editReserve = true;
    },
    cancelReserve(row) {
      if (row.source === 'channel_my_pingan') {
        this.selectedRow = row;
        this.cancelVisible = true;
        this.$nextTick(() => {
          this.$refs.serveItemPopover?.forEach(c => {
            c?.doClose && c.doClose();
          });
        });
        return;
      }
      this.$api
        .getReservev2ReserveCancel({
          id: row.reserve_id || row.id,
        })
        .then(() => {
          this.$Message.success('取消成功!');
          this.getReservePanelList();
        });
    },
    // 取消预约
    channelCancel(txt) {
      let params = {
        id: this.selectedRow.reserve_id || this.selectedRow.id,
        reason: txt,
      };
      this.cancelLoading = true;
      this.$api
        .getReservev2ReserveCancel(params)
        .then(() => {
          this.$Message.success('取消预约成功');
          this.getReservePanelList();
          this.cancelVisible = false;
          this.selectedRow = {};
        })
        .finally(() => {
          this.cancelLoading = false;
        });
    },
    cancelPay(row) {
      this.selectedRow = {
        ...row,
        id: row.reserve_id || row.id,
      };
      this.cancelReservationVisible = true;
      this.$refs.serveItemPopover?.forEach(c => {
        c?.doClose && c.doClose();
      });
    },
    // 确认到店
getReservev2ReserveArrival(item, completeReserve = false) {
      if (this.isNeedCheckedChoose(item)) {
        this.handleEditReserve(item);
        this.$nextTick(() => {
          this.$refs.serveItemPopover?.forEach(c => {
            c?.doClose && c.doClose();
          });
        });
        return;
      }
      this.$api
        .getReservev2ReserveArrival({
          complete_reserve: completeReserve ? 1 : 0,
          id: item.reserve_id || item.id,
        })
        .then(() => {
          this.$Message.success('到店成功');
          this.getReservePanelList();
        });
    },
    // 检测是否需要先确认技师和服务
    isNeedCheckedChoose(row) {
      // 后台下单的技师才有拦截校验
      if (
        row.is_arrival !== '1' &&
        row.type != 'DOCTOR' &&
        row.source !== 'rst_weapp' &&
        (!this.getPhysical(row.services) || !this.getServices(row.services))
      ) {
        return true;
      } else {
        return false;
      }
    },
    getReservev2ReserveUpdate(params, isDrag) {
      this.$api.getReservev2ReserveUpdate(params).then(() => {
        this.$Message.success('保存成功!');
        this.getReservePanelList(isDrag, params.id);
        this.isDragging = false;
      });
    },
    getReserveSettingInfo() {
      this.$api.fetchReserveSettingInfo().then(res => {
        this.getSetInfo = res;
        this.max_day = +res?.max_day || 365;
        this.disabledOption = {
          disabledDate(date) {
            if (!date) return true;
            const maxDay = moment(moment().format('YYYY-MM-DD 00:00:00')).add(+res?.max_day || 365, 'd');
            const curDay = moment(moment(date).format('YYYY-MM-DD 00:00:00'));
            return curDay.isAfter(maxDay);
          },
        };
      });
    },
    // 完成服务
    finished(row) {
      let params = {
        id: row.reserve_id || row.id,
      };
      this.$api.getReservev2ReserveFinished(params).then(() => {
        this.$Message.success('服务已完成');
        this.getReservePanelList();
      });
    },
    // 详情
    toDetail(row) {
      // this.$router.push({
      //   path: '/trade/order/detail',
      //   query: {
      //     orderid: row.order_id,
      //     orderType: 'shop_order',
      //   },
      // });
      // 新窗口打开
      const href = this.$router.resolve({
        path: '/trade/order/detail',
        query: {
          orderid: row.order_id,
          orderType: 'shop_order',
        },
      }).href;
      window.open(href, '_blank');
    },
    jumpToOrder(row) {
      this.$router.push({
        path: '/reserve/listing/list',
        query: {
          code: row.code,
        },
      });
    },

    handleToCardVerification() {
      this.$router.push('/service/card/verification');
    },
    // 处理收款dropdown菜单点击事件
    handlePaymentAction(action, item, is_default = true) {
      // 立即关闭popover
      this.$nextTick(() => {
        this.$refs.serveItemPopover?.forEach(c => {
          c?.doClose && c.doClose();
        });
      });

      if (action === 'payAndComplete') {
        // 收款后直接完成服务，弹出二次确认
        this.showPayAndCompleteConfirm(item, is_default);
      }
    },
    // 处理确认到店dropdown菜单点击事件
    handleArrivalAction(action, item) {
      // 立即关闭popover
      this.$nextTick(() => {
        this.$refs.serveItemPopover?.forEach(c => {
          c?.doClose && c.doClose();
        });
      });

      if (action === 'arrivalAndComplete') {
        // 确认到店后直接完成服务，弹出二次确认
        this.showArrivalAndCompleteConfirm(item);
      }
    },
    // 收款后直接完成服务
    preCollectionAndComplete(row, is_default = true) {
      this.$nextTick(() => {
        this.$refs.serveItemPopover?.forEach(c => {
          c?.doClose && c.doClose();
        });
      });
      // 如果是美团和抖音，pop提示 跳转去卡券核销
      if (['meituan', 'douyin'].includes(row.source)) {
        this.scanCodePayVisible = true;
        return;
      }
      if (row.source === 'channel_my_pingan') {
        this.otherPayAndComplete(row, is_default);
        return;
      }
      if (Number(row.order_id)) {
        this.order_id = row.order_id;
        this.completeAfterPay = true; // 标记支付后需要完成服务

        // 主体校验，未通过数字化无法打开收款弹窗
        if (this.is_rst) {
          this.$refs.authWarningRef.verify().then(() => {
            this.rstPayVisible = true;
          });
        } else {
          this.payVisible = true;
        }
      } else {
        this.collectionAndComplete(row, is_default);
      }
    },
    // 显示收款后完成服务确认弹窗
    showPayAndCompleteConfirm(row, is_default = true) {
      this.confirmModalConfig = {
        title: '收款后直接完成服务，将跳过"服务确认"的步骤',
        content: '该操作旨在减少某些场景下店员的操作复杂度。请门店结合实际情况进行操作',
        okText: '我已知晓，去收款',
        cancelText: '我再想想',
        onOk: () => {
          this.confirmModalVisible = false;
          this.directCollectionAndComplete(row, is_default);
        }
      };
      this.confirmModalVisible = true;
    },
    // 显示确认到店后完成服务确认弹窗
    showArrivalAndCompleteConfirm(item) {
      this.confirmModalConfig = {
        title: '确认到店后直接完成服务，将跳过"服务确认"的步骤',
        content: '该操作旨在减少某些场景下店员的操作复杂度。请门店结合实际情况进行操作',
        okText: '确定提交',
        cancelText: '我再想想',
        onOk: () => {
          this.confirmModalVisible = false;
          this.directArrivalAndComplete(item);
        }
      };
      this.confirmModalVisible = true;
    },
    // 直接收款后完成服务（确认后执行）
    directCollectionAndComplete(row, is_default = true) {
      // 如果是美团和抖音，pop提示 跳转去卡券核销
      if (['meituan', 'douyin'].includes(row.source)) {
        this.scanCodePayVisible = true;
        return;
      }
      if (row.source === 'channel_my_pingan') {
        this.otherPayAndComplete(row, is_default);
        return;
      }
      if (Number(row.order_id)) {
        this.order_id = row.order_id;
        this.completeAfterPay = true; // 标记支付后需要完成服务

        // 主体校验，未通过数字化无法打开收款弹窗
        if (this.is_rst) {
          this.$refs.authWarningRef.verify().then(() => {
            this.rstPayVisible = true;
          });
        } else {
          this.payVisible = true;
        }
      } else {
        this.collectionAndComplete(row, is_default);
      }
    },
    // 直接确认到店后完成服务（确认后执行）
    directArrivalAndComplete(item) {
      if (this.isNeedCheckedChoose(item)) {
        this.handleEditReserve(item);
        this.$nextTick(() => {
          this.$refs.serveItemPopover?.forEach(c => {
            c?.doClose && c.doClose();
          });
        });
        return;
      }

      // 先调用确认到店
      this.$api
        .getReservev2ReserveArrival({
          complete_reserve: 0, // 先不完成服务
          id: item.reserve_id || item.id,
        })
        .then(() => {
          this.$Message.success('到店成功');
          // 确认到店成功后，再调用完成服务
          return this.$api.getReservev2ReserveFinished({
            id: item.reserve_id || item.id,
          });
        })
        .then(() => {
          this.$Message.success('服务已完成');
          this.getReservePanelList();
        })
        .catch(error => {
          console.error('操作失败:', error);
          this.$Message.error('操作失败，请重试');
        });
    },
    // is_default: true 标识沿用预约列表的收款处理， false用看板的， 两边接口数据格式不一致
    preCollection(row, is_default = true) {
      this.$nextTick(() => {
        this.$refs.serveItemPopover?.forEach(c => {
          c?.doClose && c.doClose();
        });
      });
      // 如果是美团和抖音，pop提示 跳转去卡券核销
      if (['meituan', 'douyin'].includes(row.source)) {
        this.scanCodePayVisible = true;
        return;
      }
      if (row.source === 'channel_my_pingan') {
        this.otherPay(row, is_default);
        return;
      }
      if (Number(row.order_id)) {
        this.order_id = row.order_id;
        this.completeAfterPay = false; // 标记支付后不需要完成服务

        // 主体校验，未通过数字化无法打开收款弹窗
        if (this.is_rst) {
          this.$refs.authWarningRef.verify().then(() => {
            this.rstPayVisible = true;
          });
        } else {
          this.payVisible = true;
        }
      } else {
        this.collection(row, is_default);
      }
    },
    // 大弹窗收款后直接完成服务
    preCollectionAndCompleteFromModal(row) {
      this.showPayAndCompleteConfirm(row, true);
    },
    // 大弹窗确认到店后直接完成服务
    getReservev2ReserveArrivalAndCompleteFromModal(row) {
      this.showArrivalAndCompleteConfirm(row);
    },
    otherPay(row, is_default) {
      let params = {
        id: is_default ? row.id : row.reserve_id,
        services: this.handlerServices(row, is_default),
      };
      this.$api.getReservev2ReserveConfirm(params).then(res => {
        this.successData = res || {};
        this.successVisible = true;
        this.getReservePanelList();
      });
    },
    otherPayAndComplete(row, is_default) {
      let params = {
        id: is_default ? row.id : row.reserve_id,
        services: this.handlerServices(row, is_default),
        complete_reserve: 1, // 支付后直接完成服务
      };
      this.$api.getReservev2ReserveConfirm(params).then(res => {
        this.successData = res || {};
        this.successVisible = true;
        this.getReservePanelList();
      });
    },

    // 处理支付成功事件
    handlePaySuccess() {
      // 支付成功后刷新预约看板
      this.getReservePanelList();
    },
    // 处理支付成功弹窗关闭事件
    handleSuccessPayDialogClose(visible) {
      console.log('visible: ', visible);
      if (!visible) {
        // 支付成功弹窗关闭时刷新预约看板
        this.getReservePanelList();
      }
    },

    //TODO 支付前进行主体认证校验
    // 收款
    async collection(row, is_default) {
      // const res = await this.$api.verify();

      let params = {
        id: is_default ? row.id : row.reserve_id,
        services: this.handlerServices(row, is_default),
      };
      this.$api
        .getReservev2ReserveConfirm(params)
        .then(res => {
          this.order_id = res.order_id;
          return this.$refs.authWarningRef.verify();
        })
        .then(() => {
          if (this.is_rst) {
            this.rstPayVisible = true;
          } else {
            this.payVisible = true;
          }
        });
    },
    // 收款后直接完成服务
    async collectionAndComplete(row, is_default) {
      let params = {
        id: is_default ? row.id : row.reserve_id,
        services: this.handlerServices(row, is_default),
        complete_reserve: 1, // 支付后直接完成服务
      };
      this.$api
        .getReservev2ReserveConfirm(params)
        .then(res => {
          this.order_id = res.order_id;
          this.completeAfterPay = true; // 标记支付后需要完成服务
          return this.$refs.authWarningRef.verify();
        })
        .then(() => {
          if (this.is_rst) {
            this.rstPayVisible = true;
          } else {
            this.payVisible = true;
          }
        });
    },
    // 收款 services 参数
    handlerServices(row, is_default) {
      let list = [];
      if (is_default) {
        row?.services?.forEach(item => {
          list.push({
            goods_service_id: item.goods_service?.id,
            physio_id: item.physio_id,
            quantity: item.quantity,
          });
        });
      } else {
        list.push({
          goods_service_id: row.goods_service_id,
          physio_id: row.physio_id,
          quantity: row.quantity,
        });
      }
      return list;
    },
    changeDate: throttle(function (date, type, row = {}) {
      if (type === 'date') {
        this.selectedDate = moment(date).format('YYYY-MM-DD');
      }
      if (type === 'left') {
        this.selectedDate = moment(this.selectedDate).subtract(1, 'd').format('YYYY-MM-DD');
      }
      if (type === 'current') {
        this.selectedDate = moment().format('YYYY-MM-DD');
      }
      if (type === 'right') {
        this.selectedDate = moment(this.selectedDate).add(1, 'd').format('YYYY-MM-DD');
      }
      if (type === 'refresh') {
        // 只刷新看板，不修改日期
        // 这种情况通常用于创建后确认到店等操作
      }
      const { isDrag, reset, id } = row;
      // 如果有id， 说明修改时，修改了其他天
      this.getReservePanelList(isDrag, reset, id);
    }, 500),
    openBoardSetting() {
      this.boardVisible = true;
    },
    handleScroll(event) {
      const scrollTop = event.target.scrollTop;
      const scrollLeft = event.target.scrollLeft;
      const emptyCellListWidth = this.$refs.emptyCellList.clientWidth;
      const scrollbarWidth = emptyCellListWidth - event.target.clientWidth;
      this.scroll.x = scrollLeft;
      this.scroll.y = scrollTop;
      this.scroll.w = event.target.clientWidth;
      this.scroll.line_x = scrollLeft;
      if (scrollLeft >= scrollbarWidth) {
        this.scroll.line_x = scrollbarWidth;
      }
      if (this.scroll.line_x < 0) this.scroll.line_x = 0;

      const container = event.target;
      const element = container.querySelector('.current-time-line');
      this.isViewPort = this.isElementInViewport(element, container);
    },
    isElementInViewport(el, container) {
      const rect = el.getBoundingClientRect();
      const containerRect = container.getBoundingClientRect();
      return rect.top >= containerRect.top && rect.bottom <= containerRect.bottom;
    },
    handleScrollLeft() {
      const container = this.$refs.panelContentScroll;
      const scrollLeft = container.scrollLeft;
      if (scrollLeft - this.initBorderBox.width <= 0) {
        container.scrollTo({
          left: 0,
          behavior: 'smooth',
        });
      } else {
        container.scrollTo({
          left: container.scrollLeft - this.initBorderBox.width,
          behavior: 'smooth',
        });
      }
    },
    handleScrollRight() {
      const container = this.$refs.panelContentScroll;
      const scrollLeft = container.scrollLeft;
      const scrollWidth = container.scrollWidth;
      if (scrollLeft + this.initBorderBox.width >= scrollWidth) {
        container.scrollTo({
          left: scrollWidth,
          behavior: 'smooth',
        });
      } else {
        container.scrollTo({
          left: container.scrollLeft + this.initBorderBox.width,
          behavior: 'smooth',
        });
      }
    },
    changeBorderWidth: throttle(function (value) {
      this.initBorderBox.width = value;
      localStorage.setItem('dragInfo', JSON.stringify(this.initBorderBox));
      this.initList(this.panelData);
      this.initServeList();
      this.$nextTick(() => {
        this.toScrollCurTime();
      });
    }, 40),
    changeBorderHeight: throttle(function (value) {
      this.initBorderBox.height = value;
      localStorage.setItem('dragInfo', JSON.stringify(this.initBorderBox));
      this.initList(this.panelData);
      this.initServeList();
      this.$nextTick(() => {
        this.toScrollCurTime();
      });
    }, 40),
    getReservePanelList(isDrag, reset, id) {
      this.loading = true;
      this.serveList = [];
      this.$api
        .getReservePanelList({
          date: this.selectedDate,
        })
        .then(res => {
          this.panelData = res || {};
          this.setting_info = res?.setting_info || {};
          this.setting_info.interval_min = res?.setting_info?.interval_min || 15;
          this.serveList = res?.list || [];
          this.source_desc = S.descToArrHandle(res.source_desc);
          if (this.list?.length === 0 || this.listDate !== this.selectedDate || reset) {
            this.initList(res);
            this.listDate = this.selectedDate;
          }
          this.initServeList();
          this.$nextTick(() => {
            if (!isEmpty(id)) {
              this.toScrollTargetTimeById(id);
              return;
            }
            if (isDrag) return;
            this.toScrollCurTime();
          });
        })
        .finally(() => {
          this.loading = false;
          this.$forceUpdate();
          this.$nextTick(() => {
            const container = this.$refs.contentContainer;
            container?.removeEventListener('dragstart', this.dragstart);
            container?.removeEventListener('dragover', this.dragover);
            container?.removeEventListener('drop', this.ondrag);
            container.addEventListener('dragstart', this.dragstart);
            container.addEventListener('dragover', this.dragover);
            container.addEventListener('drop', this.ondrag);
          });
        });
    },
    toScrollCurTime(behavior = 'instant') {
      this.$nextTick(() => {
        this.scroll.w = this.$refs?.panelContent?.clientWidth - 10 || 0;
        const container = this.$refs.panelContentScroll;
        const rect = container.getBoundingClientRect();
        let top = this.currentTimeTop - rect.height / 2 + 150;
        top = top < 0 ? 0 : top;
        container.scrollTo({
          left: 0,
          top,
          behavior,
        });
      });
    },
    toScrollTargetTimeById(id, behavior = 'instant') {
      let element = this.$refs[`panel-cell-${id}`]?.[0];
      if (element) {
        element.scrollIntoView({ behavior, block: 'center' });
      }
    },
    dragstart(event) {
      this.isDragging = true;
      const { id } = event.target.dataset;
      const rect = event.target.getBoundingClientRect();
      const mouseY = event.clientY - rect.top;
      this.dragData.targetId = id;
      this.dragData.mouseY = mouseY;
      event.dataTransfer.setData('targetId', id);
      event.dataTransfer.setData('targetMouseY', mouseY);
    },
    dragover: throttle(function (e) {
      let { id, x, y } = e.target.dataset;
      const { targetId, mouseY } = this.dragData;
      const data = this.getDragData(e, id, x, y, targetId, mouseY);
      const start = data?.start;
      x = data.x;
      const tid = data?.targetId;
      const targetReserve = this.serveList?.find(item => item.reserve_id === tid) || {};
      let duration = +targetReserve.duration;
      // 获取起点到格子起点的时间差
      const startTime = minutesSinceMidnight(start);
      const endTime = startTime + duration;
      const interval_min = +this.setting_info.interval_min;
      this.$set(this.dragData, 'hoverNodes', []);
      this.list?.[x]?.children
        ?.filter(item => {
          if (duration < interval_min) {
            if (item.m >= startTime && item.em <= endTime + interval_min) return true;
          }
          if (item.m >= startTime && item.em <= endTime) return true;
          return false;
        })
        ?.forEach(item => {
          this.dragData.hoverNodes.push(item.start);
        });
      this.dragData.x = x;
      this.isDragging = true;
      e.preventDefault();
    }, 300),
    ondrag(event) {
      event.preventDefault();
      this.dragData.hoverNodes = [];
      this.dragData.targetId = '';
      this.dragData.mouseY = 0;
      this.dragData.x = 0;
      this.isDragging = false;
      let { id, x, y } = event.target.dataset;
      // 被拖拽目标ID
      let tId = event.dataTransfer.getData('targetId');
      // 开始拖拽时， 鼠标在被拖拽元素内部距离顶点的距离
      let startMouseY = event.dataTransfer.getData('targetMouseY');
      const data = this.getDragData(event, id, x, y, tId, startMouseY);
      const start = data?.start;
      x = data.x;
      y = data.y;
      const targetId = data?.targetId;
      if (isEmpty(start)) {
        event.dataTransfer.clearData();
        return;
      }
      const targetReserve = this.serveList?.find(item => item.reserve_id === targetId) || {};
      if (isEmpty(targetReserve)) {
        event.dataTransfer.clearData();
        return;
      }
      let physio = this.list?.[x] || {};

      if (this.setting_info.use_member_setting === '2') {
        physio = targetReserve;
      }
      if (physio.type === 'DOCTOR') {
        this.$Message.warning('技师不能占用医生时间!');
        return;
      }
      const emptyCell = physio?.children?.find(item => item.start === start) || {};
      if (emptyCell.is_rest) {
        this.$Message.warning('休息时间不支持快捷修改!');
        return;
      }
      const params = {
        id: targetReserve.reserve_id,
        name: targetReserve.user_name,
        uid: targetReserve.uid,
        duration: {
          hour: 0,
          minute: targetReserve.duration,
        },
        remark: targetReserve.remark,
        services: [
          {
            goods_service_id: targetReserve.goods_service_id,
            physio_id: physio?.physio_id || '0',
            quantity: targetReserve.quantity,
          },
        ],
        reserve_date: this.selectedDate,
        reserve_time: start,
        is_order: targetReserve.is_order,
      };
      event.dataTransfer.clearData();
      params.id && this.getReservev2ReserveUpdate(params, true);
    },
    getDragData(event, id, x, y, targetId, startMouseY) {
      const rect = event.target.getBoundingClientRect();
      // 拖拽结束后，鼠标在放置元素内部距离顶点的距离
      let endMouserY = event.clientY - rect.top;
      let start = '';
      let dragOffset = 0;

      // console.log(`落点元素的id: ${id}，落点的x-y：${x}-${y}，落点的鼠标到当前格子顶部的距离：${endMouserY}，`);
      // console.log(`被拖拽元素的id: ${targetId}，被拖拽元素的鼠标到当前格子顶部的距离：${endMouserY}`);
      // id为空 targetId不为空， 拖拽时落点是空白区域;
      // id不存在时，表示拖拽结束时，鼠标不在被拖拽的原目标区域内
      if (isEmpty(id) && !isEmpty(targetId)) {
        start = dragMouseTopOfStart(this.list?.[x]?.children, y, endMouserY, startMouseY);
        // 该场景不存在, targetId不可能为空
      } else if (!isEmpty(id) && isEmpty(targetId)) {
        // 如果是targetId 和 id 相同， 则落在自身
      } else if (!isEmpty(id) && !isEmpty(targetId) && id === targetId) {
        // 落在自身时，需要根据落点，算出背后的空白格所在区域和到顶点的高度
        const targetReserve = this.serveList.find(item => item.reserve_id === id);
        // 如果拖拽结束后， 是放置在自身。 则只需要根据结束时的鼠标落点top - 开始时的鼠标落点top
        // 如果为正， 则表示向下拖，否则，向上拖
        // 此时向下取整，如果拖动超过一格，才参与计算
        dragOffset = Math.floor((endMouserY - startMouseY) / this.initBorderBox.height);
        targetId = targetReserve.reserve_id;
        const axiosY = Math.ceil(targetReserve.y / this.initBorderBox.height);
        const axiosX = Math.floor(targetReserve.x / this.initBorderBox.width);
        x = axiosX;
        let offsetY = axiosY + dragOffset;
        offsetY = offsetY > 0 ? offsetY : 0;
        // if (targetReserve.h <= this.initBorderBox.height) {
        //   offsetY = offsetY - 1;
        // }
        start = this.list?.[axiosX]?.children?.[offsetY].start;
        // y = this.list?.[x]?.children.findIndex(item => item.start === )
        start = dragMouseTopOfStart(this.list?.[x]?.children, y, endMouserY, startMouseY);
        // 拖拽时，落点在其他预约单
      } else if (!isEmpty(id) && !isEmpty(targetId) && id !== targetId) {
        // id 是落点预约单 reserve_id
        // targetId 是被拖拽的预约单 reserve_id
        // 被拖动的预约单距离顶点的位置 startMouseY
        // 落点预约单鼠标距离顶部的位置 endMouserY
        // 拿到落点预约单对象
        const curReserve = this.serveList.find(item => item.reserve_id === id);
        // 修正x值 计算方式，x / 每格宽度 向下取整
        x = Math.floor((curReserve.x + 1) / this.initBorderBox.width);
        // 修正y值。计算方式是，x / 每格高度 向上取整 加上 endMouserY / 72 向下取整
        y =
          Math.ceil((curReserve.y - 1) / this.initBorderBox.height) +
          Math.floor((endMouserY + 1) / this.initBorderBox.height);
        // 修正落点值，去掉整数格后，剩余值重新计算
        endMouserY = endMouserY % this.initBorderBox.height;
        dragOffset = Math.floor(startMouseY / this.initBorderBox.height);
        let remain = (startMouseY % this.initBorderBox.height) - endMouserY;
        const remainOffset = remain > 0 ? -1 : 0;
        dragOffset = y - dragOffset + remainOffset;
        dragOffset = dragOffset > 0 ? dragOffset : 0;
        start = this.list?.[x]?.children?.[dragOffset].start;
      }
      return {
        start,
        x,
        y,
        id,
        targetId,
      };
    },
  },
};
</script>

<style scoped lang="less">
.board {
  width: 100%;
  height: 100%;
  overflow-x: auto;
}

.board-action {
  padding: 0 16px;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;

  > div {
    display: flex;
    align-items: center;

    .arrow {
      margin-right: 8px;
      cursor: pointer;
    }

    .arrow-left:hover {
      color: #155bd4;
    }

    .arrow-right:hover {
      color: #155bd4;
    }
  }
}

.board-panel {
  .panel-box {
    width: 100%;
    height: 80vh;
    position: relative;

    .panel-header {
      width: ~'calc(100% - 128px)';
      height: 56px;
      margin-left: 63px;
      border: 1px solid #ebedf0;

      .panel-nav {
        position: relative;
        overflow: hidden;
        height: 100%;
        margin-left: unset;

        .top-scroll-container {
          position: absolute;
          display: flex;
          left: 0;

          .header-cell {
            display: flex;
            align-items: center;
            justify-content: center;
            align-self: center;
            width: var(--panel-cell-w);
            height: 56px;
            font-size: 12px;
            flex-shrink: 0;
            padding: 0 12px;
            box-sizing: border-box;
            white-space: nowrap;
            position: relative;
            border-right: 1px solid #ebedf0;
            border-left: unset;

            > span {
              color: #323232;
              font-weight: 500;
              font-size: 14px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .handle-resize {
              position: absolute;
              width: 20px;
              height: 56px;
              right: -11px;
              top: 0;
              cursor: ew-resize;
              background: transparent;

              .drag-line {
                position: absolute;
                width: 1px;
                height: 56px;
                right: 50%;
                top: 0;
                background-color: #ebedf0;
              }
            }

            .handle-resize:hover .drag-line {
              background-color: #8558fa;
            }
          }

          .header-cell:first-child {
            border-left: unset;
          }

          .angled-135 {
          }
        }
      }
    }

    .panel-left-bar {
      top: 56px;
      height: ~'calc(100% - 56px)';
      width: 64px;
      position: absolute;
      overflow: hidden;

      .left-scroll-container {
        width: 100%;
        top: 0;
        position: absolute;
        display: flex;
        flex-direction: column;

        .left-header-cell {
          flex-shrink: 0;
          font-size: 12px;
          padding-left: 8px;
          box-sizing: border-box;
          background-color: #fff;
          border: none;
          width: 100%;
          position: relative;
          border-right: 1px solid #ebedf0;

          .time-text {
            position: absolute;
            top: -10px;
          }
        }

        .left-header-cell:first-child {
          .time-text {
            top: 0;
          }
        }

        .handle-resize {
          position: absolute;
          width: 100%;
          height: 20px;
          left: 0;
          top: -10px;
          cursor: ns-resize;

          .drag-line {
            position: absolute;
            width: 16px;
            height: 1px;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
            background-color: #ebedf0;
          }
        }

        .handle-resize:hover .drag-line {
          background-color: #8558fa;
        }
      }
    }

    .panel-right-bar {
      top: 56px;
      height: ~'calc(100% - 56px)';
      width: 65px;
      position: absolute;
      overflow: hidden;
      right: 0;
      padding-left: 12px;
      border-left: 1px solid #ebedf0;

      .right-scroll-container {
        position: relative;
        width: 100%;
      }

      .current-time-fixed {
        position: absolute;
      }

      .current-time {
        color: #8558fa;
        font-size: 12px;
        font-weight: 500;
      }
    }

    .current-time-line {
      width: ~'calc(100vw - 370px)';
      height: 2px;
      background: #8558fa;
      position: absolute;
      top: 0;

      .point {
        position: absolute;
        top: -4px;
        width: 10px;
        height: 10px;
        background: #8558fa;
        border-radius: 50%;
        right: -3px;
      }
    }

    .panel-content {
      display: inline-block;
      vertical-align: top;
      width: ~'calc(100% - 128px)';
      height: ~'calc(100% - 56px)';
      margin-left: 64px;

      .panel-content-scroll {
        vertical-align: top;
        overflow: auto;
        width: 100%;
        scroll-behavior: smooth;
        height: 100%;
      }

      /* 滚动条样式 */

      .panel-content-scroll::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        border-radius: 5px;
      }

      /* 滑块样式 */

      .panel-content-scroll::-webkit-scrollbar-thumb {
        background-color: #c1c1c1;
        border-radius: 5px;
      }

      /* 滚动条轨道样式 */

      .panel-content-scroll::-webkit-scrollbar-track {
        background-color: #e5e5e5;
        border-radius: 5px;
      }

      .content-container {
        display: flex;
        flex-direction: column;
        position: relative;
        width: fit-content;

        .emptyCellList {
          position: relative;
          display: flex;

          .emptyCellList-column {
            flex-shrink: 0;

            .emptyCellList-row {
              border-bottom: 1px solid #eee;
              border-left: 1px solid #eee;

              .add-reserve {
                color: #765ec5;
                font-size: 14px;
                visibility: hidden;
                width: 100% !important;
                height: 100% !important;
                display: none;
                align-items: center;
                justify-content: center;
                background: #f7f5fd;
                cursor: pointer;
              }

              .rest-reserve {
                width: 100%;
                height: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background: #fbfbfb;
                font-size: 12px;
                color: #c4c4c4;
                cursor: default;
              }
            }

            .emptyCellList-row:hover {
              .add-reserve {
                display: flex;
                visibility: unset;
              }
            }
          }

          .emptyCellList-column:first-child {
            .emptyCellList-row {
              border-left: unset;
            }
          }

          .emptyCellList-column:last-child {
            .emptyCellList-row {
              border-right: 1px solid #eee;
            }
          }
        }

        .boardCellList {
          width: 100%;
          height: 0;
        }
      }
    }

    .status {
      position: absolute;
      z-index: 99;
      bottom: 16px;
      right: 90px;
      background-color: #fff;
      height: 48px;
      box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.08);
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 24px;

      .status-item {
        display: inline-flex;
        align-items: center;
        margin-right: 16px;
        border-radius: 2px;

        .status-icon {
          display: inline-block;
          width: 14px;
          height: 14px;
          vertical-align: top;
          border-radius: 1px;
        }

        .status-text {
          margin-left: 8px;
        }
      }
    }

    .action-arrow-item {
      position: absolute;
      top: 0;
      width: 64px;
      height: 56px;
      border-top: 1px solid #ebedf0;
      border-bottom: 1px solid #ebedf0;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .action-arrow-l {
      left: 0;
    }

    .action-arrow-r {
      right: 0;
    }
  }
}

.reserve-tooltip-box {
  max-height: 300px;
  background-color: #fff;
  padding: 10px 12px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.08);

  .user {
    width: 268px;
    display: flex;
    align-items: center;

    .avatar {
      width: 60px;
      padding-right: 20px;
      overflow: hidden;
      border-radius: 2px;
    }

    .info {
      display: flex;
      flex-direction: column;
      flex: 1;

      .name-box {
        display: flex;
        align-items: center;

        .username {
          flex: 1;
          font-size: 16px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #323233;
          line-height: 20px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .tag {
          width: 40px;
          height: 16px;
          line-height: 16px;
          text-align: center;
          border-radius: 2px;
          border: 1px solid #fd678b;
          font-size: 12px;
          font-weight: 400;
          color: #fd678b;
        }
      }

      .mobile {
        margin-top: 5px;
      }
    }
  }

  .serve {
    background: #f2f2f2;
    margin-bottom: 16px;
    padding: 12px;
    width: 268px;
    word-break: break-all;
    margin-top: 12px;

    > div {
      line-height: 1.5;
    }
  }

  .user_remark {
    width: 268px;
    margin-bottom: 8px;

    .text {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      overflow: hidden;
    }
  }

  .footer {
    width: 268px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}

.role-tag {
  margin-left: 6px;
  padding: 2px 4px;
  background: #fafdff;
  border-radius: 2px;
  border: 1px solid #63aaf0;
  font-size: 10px !important;
  line-height: 10px;
  color: #63aaf0;
}

.back-cur-time {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  right: 0;
  background: transparent;

  > div {
    display: none;
    position: absolute;
    top: 30px;
    left: 2px;
    width: 60px;
    height: 22px;
    text-align: center;
    padding-right: 5px;
    cursor: pointer;
    background-image: url('../../../assets/image/reserve/location.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    font-weight: 500;
    font-size: 10px;
    color: #8a71d9;
    align-items: center;
    justify-content: center;

    > img {
      width: 4px;
      height: 7px;
    }

    .arrow {
      position: absolute;
      top: 8px;
      right: 5px;
      display: none;
    }

    .arrow-hover {
      position: absolute;
      top: 8px;
      right: 5px;
      display: block;
    }
  }

  > div:hover {
    color: #ffffff;
    background-image: url('../../../assets/image/reserve/location-hover.png');

    .arrow {
      display: block;
    }

    .arrow-hover {
      display: none;
    }
  }
}

.resize_btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 1px solid #dcdee2;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

.resize_btn:hover {
  border: 1px solid #447cdd;
}

.resize_btn-action {
  padding: 10px 16px;

  > div {
    display: flex;
    align-items: center;

    > div:first-child {
      width: 32px;
      text-align: left;
    }

    > div:last-child {
      flex: 1;
      padding-right: 10px;
    }
  }
}

.resize_btn-popper :deep(.ivu-poptip-inner) {
  border-radius: 4px !important;
}

:deep(.ivu-slider .ivu-slider-wrap) {
  margin: 10px 0;
}
</style>
<style lang="less">
.custom-board-tooltip {
  padding: 0;
}

.app-inner:has(.board-panel) {
  padding: 16px 0 !important;
}

.zIndex999 {
  z-index: 999999999 !important;
}

.serve-item {
  position: absolute;
  top: 0;
  left: 0;
  cursor: move;
  overflow: hidden;
  color: #ffffff;
  display: flex;
  border: 1px solid transparent;
  border-radius: 2px;

  .item-left {
    width: 3px;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 2px;
  }

  .item-right {
    width: 100%;
    padding-left: 3px;

    .item-right-info {
      padding: 6px 0 6px 15px;
    }

    .item-right-remark {
      margin-top: 5px;
      padding-top: 12px;
      padding-left: 15px;
      padding-right: 5px;
      border-top: 1px solid #e3e4f5;
    }
  }

  .item-status {
    position: absolute;
    right: 0;
    top: 0;
    padding: 2px 6px;
    border-radius: 0 2px 0 4px;
    font-size: 10px;
    color: #ffffff;
    line-height: 14px;
  }

  .txt-serve {
    font-weight: 500;
    font-size: 14px;
  }

  .text {
    font-size: 12px;
    color: #9d9d9d;
    line-height: 17px;
  }

  .remark-text {
    font-size: 12px;
    color: #999999;
    line-height: 17px;
  }

  .text_ellipsis {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.doc-btn {
  border-color: #155bd4;
  color: #155bd4;
}

/* 组合按钮样式 */
.button-group {
  display: inline-flex;

  .main-button {
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
    border-right: none !important;
    width: 65px;
  }

  .dropdown-button {
    .dropdown-trigger {
      border-top-left-radius: 0 !important;
      border-bottom-left-radius: 0 !important;
      border-left: 1px solid rgba(255, 255, 255, 0.2) !important;
      padding: 0 8px !important;
      min-width: auto !important;
      display: inline-flex !important;
      align-items: center !important;
      justify-content: center !important;
    }
  }
}

/* 下拉菜单样式优化 */
:deep(.ivu-dropdown-menu) {
  min-width: 160px !important;
  padding: 4px 0 !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;

  .ivu-dropdown-item {
    padding: 8px 12px !important;
    font-size: 14px !important;
    line-height: 1.4 !important;

    &:hover {
      background-color: #f5f5f5 !important;
    }
  }
}


</style>
<style>
.dropdown-button--small{
  .ivu-select-dropdown{
    .ivu-dropdown-item{
      padding: 4px 12px;
      font-size: 12px;
    }
  }
}
</style>
