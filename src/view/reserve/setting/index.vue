<template>
  <div class="setting-container">
    <div class="header-tabs" v-if="!is_rst">
      <div :class="+activeIndex === 1 && 'active'" @click="handleChangeTabs(1)">预约设置</div>
      <div :class="+activeIndex === 2 && 'active'" @click="handleChangeTabs(2)">{{ getPhysioName }}管理</div>
    </div>
    <div class="setting-content" v-show="+activeIndex === 1">
      <div class="desc-title" style="padding-left: 10px">预约设置</div>
      <div class="form-content">
        <Form ref="settingFormRef" :model="formData" :rules="rules" :label-width="80">
          <FormItem label="预约时间:" required>
            <div class="flex">
              <FormItem label="" :label-width="0" prop="work_time_desc">
                <Poptip
                  title=""
                  transfer
                  :offset="50"
                  style="width: 100%"
                  ref="datePoptip"
                  placement="bottom"
                  @on-popper-hide="showPoptipHandler"
                  popper-class="time-picker-poptip"
                >
                  <div slot="content">
                    <div class="rsj-checkbox-box flex flex-item-align">
                      <div class="rsj-checkbox-group">
                        <label
                          :class="['rsj-checkbox-wrap', { 'rsj-checkbox-isChecked': date.isChecked }]"
                          v-for="(date, index) in weekDays"
                          :key="index + 'date'"
                        >
                          <span class="rsj-checkbox">
                            <span class="zent-checkbox-inner"></span>
                            <input @click.stop="checkDate(date, index)" />
                          </span>
                          <span>{{ date.desc }}</span>
                        </label>
                      </div>
                      <div class="rsj-checkbox-btn">
                        <Button type="primary" style="margin-left: 8px" @click="selectDateHandler">确定</Button>
                      </div>
                    </div>
                  </div>
                  <Input style="width: 180px" v-model="formData.work_time_desc" readonly placeholder="请选择营业日期" />
                </Poptip>
              </FormItem>
              <FormItem label="" :label-width="0" prop="time_start" style="margin-left: 5px">
                <Select
                  v-model="formData.time_start"
                  placeholder="请选择营业开始时间"
                  clearable
                  style="width: 160px"
                  @on-change="timeStartChange"
                >
                  <Option v-for="item in time_list" :value="item" :key="item">{{ item }}</Option>
                </Select>
              </FormItem>
              <FormItem label="" :label-width="0" prop="time_end" style="margin-left: 5px">
                <Select v-model="formData.time_end" placeholder="请选择营业结束时间" clearable style="width: 160px">
                  <Option v-for="item in time_end_list" :value="item" :key="item">
                    {{ item }}
                  </Option>
                </Select>
              </FormItem>
            </div>
          </FormItem>
          <FormItem label="预约范围:" required>
            <div class="flex">
              <div>客户可预约未来</div>
              <FormItem label="" :label-width="0" prop="max_day">
                <Select v-model="formData.max_day" style="width: 120px; margin: 0 8px">
                  <Option v-for="item in dayOption" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                </Select>
              </FormItem>
              <div>以内的任意时间到店</div>
            </div>
          </FormItem>
          <FormItem label="到店提醒:" required>
            <div class="flex">
              <div>预约到店时间前</div>
              <FormItem label="" :label-width="0" prop="reminder_min_time">
                <Select v-model="formData.reminder_min_time" style="width: 120px; margin: 0 8px">
                  <Option v-for="item in timeOption" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                </Select>
              </FormItem>
              <div>短信提醒客户</div>
            </div>
          </FormItem>
          <FormItem label="取消预约:" required>
            <div class="flex">
              <div>根据预约到店时间，用户到店前</div>
              <FormItem label="" :label-width="0" prop="cancel_before_return_min_time">
                <Select v-model="formData.cancel_before_return_min_time" style="width: 120px; margin: 0 8px">
                  <Option
                    v-for="item in [{ id: '0', desc: '任意时间' }, ...timeOption]"
                    :key="item.id"
                    :value="item.id"
                    >{{ item.desc }}</Option
                  >
                </Select>
              </FormItem>
              <div>退单，退实付金额的</div>
              <FormItem label="" :label-width="0" prop="cancel_before_return_scale">
                <div class="flex" style="width: 120px; margin: 0 8px">
                  <InputNumber
                    class="custom-input-number"
                    :max="100"
                    :min="0"
                    :precision="0"
                    v-model="formData.cancel_before_return_scale"
                    style="flex: 1"
                    placeholder="退款比例"
                    @on-focus="e => e.currentTarget.select()"
                    :disabled="is_rst"
                  />
                  <div class="append">%</div>
                </div>
              </FormItem>
            </div>
          </FormItem>
          <FormItem label="">
            <div class="flex">
              <div>根据预约到店时间，用户超时未到店退单，退实付金额的</div>
              <FormItem label="" :label-width="0" prop="user_timeout_cancel_return_scale">
                <div class="flex" style="width: 120px; margin: 0 8px">
                  <InputNumber
                    class="custom-input-number"
                    :max="100"
                    :min="0"
                    :precision="0"
                    v-model="formData.user_timeout_cancel_return_scale"
                    style="flex: 1"
                    placeholder="退款比例"
                    @on-focus="e => e.currentTarget.select()"
                    :disabled="is_rst"
                  />
                  <div class="append">%</div>
                </div>
              </FormItem>
            </div>
          </FormItem>
          <FormItem label="">
            <div class="flex">
              <div>根据预约到店时间，用户到店后任意时间退单，退实付金额的</div>
              <FormItem label="" :label-width="0" prop="cancel_after_return_scale">
                <div class="flex" style="width: 120px; margin: 0 8px">
                  <InputNumber
                    class="custom-input-number"
                    :max="100"
                    :min="0"
                    :precision="0"
                    v-model="formData.cancel_after_return_scale"
                    style="flex: 1"
                    placeholder="退款比例"
                    @on-focus="e => e.currentTarget.select()"
                    :disabled="is_rst"
                  />
                  <div class="append">%</div>
                </div>
              </FormItem>
            </div>
          </FormItem>
          <FormItem label="预约销量:" class="custom-form-item" prop="show_sales">
            <div class="mt-2" style="position: relative">
              <RadioGroup v-model="formData.show_sales">
                <Radio label="1">
                  <span>展示</span>
                </Radio>
                <Radio label="2">
                  <span>不展示</span>
                </Radio>
              </RadioGroup>
              <div style="color: #cccccc; position: absolute; bottom: -36px">
                预约量展示在买家端的员工个人主页，可进行配置是否显示顾客的预约量
                <tooltip placement="top">
                  <span style="margin-left: 10px; cursor: pointer; color: #2277ff">示例</span>
                  <template #content>
                    <img src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1212/095603_91220.png-B.w200" />
                  </template>
                </tooltip>
              </div>
            </div>
          </FormItem>
          <!--          <FormItem style="margin-top: 32px" label="卡券核销:" class="custom-form-item" prop="show_sales">-->
          <!--            <RadioGroup class="mt-2" v-model="formData.show_sales">-->
          <!--              <Radio label="1">-->
          <!--                <span>支持使用卡券兑换服务</span>-->
          <!--              </Radio>-->
          <!--              <Radio label="2">-->
          <!--                <span>不支持使用卡券兑换服务</span>-->
          <!--              </Radio>-->
          <!--            </RadioGroup>-->
          <!--          </FormItem>-->
        </Form>
      </div>

      <!-- <div v-if="is_rst" class="desc-title" style="padding-left: 10px; margin-top: 30px">问诊设置</div>
      <div v-if="is_rst" class="consultation-content">
        <div>
          <div class="consultation-item">
            <div class="text">
              <span>上午:</span>
            </div>
            <div class="text ml10">每个班次可预约的人数</div>
            <InputNumber
              class="number-box"
              v-model="consulation_number.morn_num"
              :control="false"
              :active-change="false"
              placeholder="请输入"
              style="width: 100px"
              :min="1"
              :max="99"
              :precision="0"
            >
            </InputNumber>
            <div class="text">人</div>
          </div>
          <div class="tip">上午：8:00-12:00</div>
        </div>

        <div>
          <div class="consultation-item">
            <span>下午:</span>
            <div class="text ml10">每个班次可预约的人数</div>
            <InputNumber
              class="number-box"
              v-model="consulation_number.noon_num"
              :control="false"
              :active-change="false"
              placeholder="请输入"
              style="width: 100px"
              :min="1"
              :max="99"
              :precision="0"
            >
            </InputNumber>
            <div class="text">人</div>
          </div>
          <div class="tip">下午：12:00-18:00</div>
        </div>

        <div>
          <div class="consultation-item">
            <span>晚上:</span>
            <div class="text ml10">每个班次可预约的人数</div>
            <InputNumber
              class="number-box"
              v-model="consulation_number.night_num"
              :control="false"
              :active-change="false"
              placeholder="请输入"
              style="width: 100px"
              :min="1"
              :max="99"
              :precision="0"
            >
            </InputNumber>
            <div class="text">人</div>
          </div>
          <div class="tip">晚上：18:00-22:00</div>
        </div>
      </div> -->
    </div>
    <div class="waiter-content" v-if="+activeIndex === 2 && !is_rst">
      <physical-list :extra-height="46" />
    </div>
    <div class="fixed-bottom-wrapper" v-if="+activeIndex === 1">
      <Button type="primary" :loading="saveLoading" @click="onSave">保存</Button>
    </div>
  </div>
</template>
<script>
import { getPhysioName } from '@/libs/runtime';
import S from '@/libs/util';
import cloneDeep from 'lodash.clonedeep';
import PhysicalList from '@/view/setting/member/physical/list.vue';
import { isRstClinic } from '../../../libs/runtime';
// import StandardTable from "@/components/StandardTable/index.vue";
const initForm = {
  id: void 0,
  work_time_desc: '',
  week: [],
  time_start: '',
  time_end: '',
  max_day: '',
  reminder_min_time: '',
  cancel_before_return_min_time: '',
  cancel_before_return_scale: null,
  cancel_after_return_min_time: '',
  cancel_after_return_scale: null,
  user_timeout_cancel_return_scale: null,
  show_sales: null,
};
export default {
  name: 'index',
  components: { PhysicalList },
  computed: {
    is_rst() {
      return isRstClinic();
    },
    getPhysioName() {
      return getPhysioName();
    },
    time_end_list() {
      const start = this.formData.time_start;
      return this.time_list.filter(time => this.compareTimes(start, time));
    },
  },
  data() {
    return {
      activeIndex: 1,
      weekDays: [],
      formData: cloneDeep(initForm),
      rules: {
        work_time_desc: [{ required: true, message: '请选择营业日期', trigger: 'change' }],
        time_start: [{ required: true, message: '请选择营业开始时间', trigger: 'change' }],
        time_end: [{ required: true, message: '请选择营业结束时间', trigger: 'change' }],
        max_day: [{ required: true, message: '请选择时长', trigger: 'change' }],
        reminder_min_time: [{ required: true, message: '请选择时长', trigger: 'change' }],
        cancel_before_return_min_time: [{ required: true, message: '请选择时长', trigger: 'change' }],
        cancel_before_return_scale: [{ required: true, type: 'number', message: '请选择退款比例', trigger: 'change' }],
        cancel_after_return_min_time: [{ required: true, message: '请选择时长', trigger: 'change' }],
        cancel_after_return_scale: [{ required: true, type: 'number', message: '请选择退款比例', trigger: 'change' }],
        user_timeout_cancel_return_scale: [
          { required: true, type: 'number', message: '请选择退款比例', trigger: 'change' },
        ],
        show_sales: [{ required: true, message: '预约销量', trigger: 'change' }],
      },
      dayOption: [],
      timeOption: [],
      time_list: [],
      saveLoading: false,
      // consulation_number: {
      //   morn_num: null,
      //   noon_num: null,
      //   night_num: null,
      // },
    };
  },
  mounted() {
    this.activeIndex = this.$route?.query?.tabIndex || '1';
    this.getOptions();
  },
  methods: {
    handleQueryInfo() {
      this.$api.fetchReserveSettingInfo({}).then(res => {
        this.formData = {
          id: res?.id || void 0,
          work_time_desc: res?.work_time_desc || '',
          week: res?.week?.map(key => String(key)) || [],
          time_start: res?.time_start || '08:30',
          time_end: res?.time_end || '',
          max_day: res?.max_day || '7',
          reminder_min_time: res?.reminder_min_time || '',
          cancel_before_return_min_time: res?.cancel_before_return_min_time || '',
          cancel_before_return_scale: +res?.cancel_before_return_scale || null,
          cancel_after_return_min_time: res?.cancel_after_return_min_time || '',
          cancel_after_return_scale: +res?.cancel_after_return_scale || null,
          user_timeout_cancel_return_scale: +res?.user_timeout_cancel_return_scale || null,
          show_sales: res?.show_sales || null,
        };
        // this.consulation_number = {
        //   morn_num: Number(res.morn_num || 0) || null,
        //   noon_num: Number(res.noon_num || 0) || null,
        //   night_num: Number(res.night_num || 0) || null,
        // };
        this.getBusinessDatetext();
        this.showPoptipHandler();
      });
    },
    handleChangeTabs(key) {
      this.activeIndex = key;
      this.$router.push({
        query: {
          tabIndex: key,
        },
      });
    },
    getOptions() {
      this.$api.getReserveSettingOptions().then(res => {
        this.weekDays = S.descToArrHandle(res.week).map(item => {
          return {
            ...item,
            isChecked: false,
          };
        });
        this.dayOption = S.descToArrHandle(res.day);
        this.timeOption = S.descToArrHandle(res.time);
        this.time_list = this.generateTimeSlots();
        this.handleQueryInfo();
      });
    },
    // 以分钟进行隔断
    generateTimeSlots(minute = 10) {
      const timeSlots = [];
      let currentTime = new Date(0); // 获取一个从 1970 年 1 月 1 日 00:00:00 UTC 开始的时间

      for (let i = 0; i < (24 * 60) / minute; i++) {
        let hours = currentTime.getUTCHours().toString().padStart(2, '0'); // 小时，补充两位
        let minutes = currentTime.getUTCMinutes().toString().padStart(2, '0'); // 分钟，补充两位
        if (this.compareTimes('06:30', `${hours}:${minutes}`, '<=')) {
          timeSlots.push(`${hours}:${minutes}`);
        }
        currentTime.setUTCMinutes(currentTime.getUTCMinutes() + minute); // 每次加5分钟
      }
      return timeSlots;
    },

    // 比较两个时间段的大小 time1 < time 2
    compareTimes(time1, time2, symbol) {
      // 将时间字符串转化为 Date 对象，日期部分可以使用当天的日期
      const today = new Date().toISOString().split('T')[0]; // 获取今天的日期（不含时间）

      const time1Date = new Date(`${today}T${time1}`); // 将时间拼接到今天的日期
      const time2Date = new Date(`${today}T${time2}`);
      if (symbol === '<=') {
        return time1Date <= time2Date;
      } else {
        return time1Date < time2Date;
      }
    },
    timeStartChange(time) {
      if (!this.compareTimes(time, this.formData.time_end)) {
        this.formData.time_end = '';
      }
    },
    showPoptipHandler() {
      this.$nextTick(() => {
        this.weekDays.map(item => {
          if (this.formData.week.indexOf(item.id) !== -1) {
            item.isChecked = true;
          } else {
            item.isChecked = false;
          }
        });
        this.$set(this, 'weekDays', this.weekDays);
      });
    },
    checkDate(date, index) {
      console.log(this.weekDays);
      this.weekDays[index].isChecked = !date.isChecked;
    },
    selectDateHandler() {
      console.log(this.weekDays);
      const selectedDate = this.weekDays.filter(item => item.isChecked).map(item => item.id);
      console.log('-> %c selectedDate  === %o', 'font-size: 15px;color: green;', selectedDate);
      this.formData.week = selectedDate;
      this.getBusinessDatetext();
      this.$refs.datePoptip.handleClose();
    },
    getBusinessDatetext() {
      const days = this.formData.week.sort();
      console.log('-> %c days  === %o', 'font-size: 15px;color: green;', days);
      if (!days.length) {
        this.formData.work_time_desc = '';
        return;
      }
      const dayMap = {
        1: '周一',
        2: '周二',
        3: '周三',
        4: '周四',
        5: '周五',
        6: '周六',
        7: '周日',
      };
      let resultArr = [],
        i = 0;
      resultArr[i] = [days[0]];
      days.reduce((pre, cur) => {
        cur - pre === 1 ? resultArr[i].push(cur) : (resultArr[++i] = [cur]);
        return cur;
      });
      this.formData.work_time_desc = resultArr
        .map(item => {
          if (item.length === 1) {
            return dayMap[item[0]];
          } else {
            return `${dayMap[item[0]]}至${dayMap[item[item.length - 1]]}`;
          }
        })
        .join('、');
    },
    // validData() {
    //   if (this.is_rst) {
    //     for (let key in this.consulation_number) {
    //       if (this.consulation_number[key] < 1) {
    //         this.$Message.error('每个班次可预约人数最少为1个人');
    //         return false;
    //       }
    //       if (this.consulation_number[key] > 99) {
    //         this.$Message.error('每个班次可预约人数最大为99个人');
    //         return false;
    //       }
    //     }
    //   }
    //   return true;
    // },
    onSave() {
      this.$refs.settingFormRef.validate(valid => {
        if (valid) {
          this.saveLoading = true;
          let params = {
            ...this.formData,
            // ...this.consulation_number,
          };
          this.$api
            .saveReserveSetting(params)
            .then(() => {
              this.$Message.success('保存成功!');
            })
            .finally(() => {
              this.saveLoading = false;
            });
        }
      });
    },
  },
};
</script>

<style scoped lang="less">
.setting-container {
  width: 100%;
  height: 100%;
}
.header-tabs {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  > div {
    width: 84px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    background: #9b9b9b;
    cursor: pointer;
  }
  .active {
    background: #2277ff;
  }
}
.setting-content {
  width: 100%;
  padding-bottom: 52px;
  display: flex;
  flex-direction: column;
  align-items: center;
  .desc-title {
    width: 100%;
    background: #ececec;
    padding: 12px 0;
    margin-bottom: 12px;
  }
  .form-content {
    width: 100%;
    padding: 0 60px;
  }
}

.consultation-content {
  width: 100%;
  padding: 0 60px;
  .consultation-item {
    margin-top: 20px;
    display: flex;
    align-items: center;
    .number-box {
      margin: 0 10px;
    }
  }

  .tip {
    color: #ccc;
  }
}

.custom-form-item :deep(.ivu-form-item-error-tip) {
  top: 25px;
}

.append {
  padding: 4px 7px;
  font-size: inherit;
  font-weight: normal;
  line-height: 22px;
  color: #333;
  text-align: center;
  background-color: #f8f8f9;
  border: 1px solid #dcdee2;
  border-radius: 2px;
  border-left: 0;
  white-space: nowrap;
  vertical-align: middle;
  border-bottom-left-radius: 0 !important;
  border-top-left-radius: 0 !important;
}
:deep(.custom-input-number) {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}
.mt-2 {
  margin-top: -2px;
}
</style>
<style lang="less">
@theme-color: #1157e5;
.time-picker-poptip {
  .ivu-poptip-body {
    padding: 13px 12px;
  }
  .ivu-poptip-body-content {
    overflow: hidden;
  }
  .rsj-checkbox-group {
    width: 660px;

    .rsj-checkbox-wrap {
      position: relative;
      text-align: center;
      line-height: 30px;
      border: 1px solid #bbb;
      width: 80px;
      height: 30px;
      border-radius: 2px;
      cursor: pointer;
      font-weight: 400;
      margin-right: 10px;
      padding: 0;
      vertical-align: middle;
      display: inline-block;
      font-size: 14px;
      box-sizing: border-box;
      .rsj-checkbox {
        opacity: 0;
        position: absolute;
        bottom: 0;
        right: 0;
        background-size: 100% 100%;
        background-image: url('~@/assets/image/base/rsj-checked.png');
        display: inline-block;
        width: 16px;
        height: 16px;
        white-space: nowrap;
        outline: none;
        vertical-align: middle;
        line-height: 1;
        margin: 0;
        padding: 0;

        .zent-checkbox-inner {
        }

        input {
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          top: 0;
          margin: 0;
          padding: 0;
          width: 100%;
          height: 100%;
          z-index: 1;
          opacity: 0;
          cursor: pointer;
        }
      }
    }

    .rsj-checkbox-isChecked {
      border: 1px solid @theme-color;
      color: @theme-color;

      .rsj-checkbox {
        opacity: 1;
      }
    }
  }
}
.custom {
  .ivu-tooltip-inner {
    max-height: max-content;
  }
}
.cursor {
  cursor: pointer;
}
</style>
