<template>
  <div class="visit-wrapper">
    <Button type="primary" icon="md-add" @click="addUserFollow()">记一笔</Button>
    <div class="content-wrap">
      <Timeline>
        <TimelineItem v-for="follow in userFollowList" :key="follow.id">
          <div class="user-follow-header">
            <div class="user-follow-header-time">
              {{ follow.create_time | data_format }} 由{{ follow?.operator_info?.operator || '-' }}创建
              <div v-if="+follow?.update_operator_info?.operator_time" style="margin-left: 24px">
                {{ moment(follow.update_operator_info.operator_time * 1000).format('MM/DD') }}
                由{{ follow?.update_operator_info?.operator || '-' }}更新
              </div>
            </div>
            <a class="user-follow-header-time-edit" @click="addUserFollow(follow)">修改</a>
            <Poptip confirm title="是否删除该条跟随信息?" @on-ok="deleteUserFollow(follow)">
              <a class="user-follow-header-time-delete">删除</a>
            </Poptip>
          </div>
          <div class="user-follow-content">{{ follow.description || '-' }}</div>
        </TimelineItem>
      </Timeline>
    </div>
    <add-user-follow
      :visible.sync="addUserFollowVisible"
      :selected-row="selectedRow"
      :user-info="userInfo"
      @success="getUserFollowUpTips"
    />
  </div>
</template>

<script>
import AddUserFollow from '@/view/user/components/add-user-follow.vue';
import moment from 'moment';

export default {
  name: 'userFollow',
  components: { AddUserFollow },
  props: {
    userInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      moment,
      userFollowList: [],
      addUserFollowVisible: false,
      selectedRow: {},
    };
  },
  mounted() {
    this.getUserFollowUpTips();
  },
  methods: {
    addUserFollow(row = {}) {
      this.selectedRow = row;
      this.addUserFollowVisible = true;
    },
    getUserFollowUpTips() {
      this.$api
        .getUserFollowUpTips({
          uid: this.$route.query.uid,
          page: 1,
          pageSize: 9999,
        })
        .then(res => {
          this.userFollowList = res?.list || {};
        });
    },
    deleteUserFollow(row) {
      this.$api
        .delUserFollowUpTips({
          id: row.id,
        })
        .then(() => {
          this.$Message.success('删除成功！');
          this.getUserFollowUpTips();
        });
    },
  },
};
</script>

<style scoped lang="less">
.content-wrap {
  margin-top: 32px;
}
.user-follow-header {
  display: flex;
  align-items: center;
  font-size: 12px;
  line-height: 18px;
  .user-follow-header-time {
    display: flex;
    color: #909399;
  }
  .user-follow-header-time-edit {
    color: #155bd4;
    margin-left: 24px;
  }
  .user-follow-header-time-delete {
    color: #ee3838;
    margin-left: 16px;
  }
}
.user-follow-content {
  width: 100%;
  min-height: 88px;
  background: #f9fafb;
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 14px;
  color: #303133;
  line-height: 22px;
  margin-top: 12px;
  word-break: break-all;
  white-space: pre-wrap;
}
</style>
