<template>
  <div class="exchange-wrapper">
    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight-473">
      <!-- 所属用户 -->
      <template slot-scope="{row}" slot="user_info">
        <p class="flex">
          <span class="slot-label">姓名：</span>
          <KLink :to="{path: '/user/detail', query: {uid: row.uid}}" target="_blank">{{ row.user_info && row.user_info.real_name }}</KLink>
        </p>
        <p class="flex">
          <span class="slot-label">注册名：</span>
          <span>{{ row.user_info && row.user_info.nickname }}</span>
        </p>
        <p class="flex"><span class="slot-label">手机号：</span>{{ row.user_info && row.user_info.mobile }}</p>
      </template>

      <!-- 备注 -->
      <template slot-scope="{row}" slot="remark">
        {{row.remark || '-'}}
      </template>

      <!-- 状态 -->
      <template slot-scope="{row}" slot="status_text">
        <status-text :status="row.status"><span>{{ row.status_text }}</span></status-text>
      </template>

      <!-- 服务范围 -->
      <template slot-scope="{row}" slot="services_name">
        <Tooltip :max-width="200" :content="row.services_name">
          <div class="ecs ecs-3">
            {{row.services_name || '-'}}
          </div>
        </Tooltip>
      </template>
      <!-- 关联订单号 -->
      <template slot-scope="{row}" slot="out_trade_no">
        <KLink v-if="row.source == 3" :to="{path: '/trade/give/list', query: {out_trade_no: row.order_info.out_trade_no}}" target="_blank">{{row.order_info && row.order_info.out_trade_no }}</KLink>
        <KLink v-else :to="{path: '/trade/order/list', query: {out_trade_no: row.order_info.out_trade_no}}" target="_blank">{{row.order_info && row.order_info.out_trade_no }}</KLink>
      </template>

      <!-- 创建时间 -->
      <template slot-scope="{row}" slot="create_time">
        {{row.create_time | data_format}}
      </template>

      <!-- 截止时间 -->
      <template slot-scope="{row}" slot="expire_time">
        {{row.expire_time | data_format}}
      </template>

      <template slot-scope="{row}" slot="action">
        <a v-if="row.status === 'WAIT' || row.status === 'PART'" @click="exchange(row)">兑换</a>
        <a @click="toDetail(row)" class="ml10">详情</a>
      </template>
    </Table>
    <div class="block_20"></div>
    <KPage :total="total"
           :page-size.sync="queryFormData.pageSize"
           :current.sync="queryFormData.page"
           @on-change="onPageChange"
           style="text-align:center"
    />
    <!-- 卡券明细 -->
    <k-exchange-modal v-model="exchangeVisible" :exchange-id="exchangeId" @refresh="getExchangeCardList"></k-exchange-modal>
    <k-exchange-detail v-model="detailVisible" :exchange-id="exchangeId"></k-exchange-detail>
  </div>

</template>

<script>
import KExchangeModal from "@/components/k-exchange-modal";
import KExchangeDetail from '@/components/k-exchange-detail'
import StatusText from '@/components/StatusText'
let init_query_form_data = {
  page: 1,
  pageSize: 20,
}
export default {
  name: "",
  components: {
    KExchangeModal,
    KExchangeDetail,
    StatusText
  },
  mixins: [],
  props: {

  },
  data () {
    return {
      tableLoading: false, // 表格获取的数据的loading
      tableCols: [
        {title:'序号', type: 'index', width: 60, align: 'center'},
        {title: '通兑券名称', key: 'name', minWidth: 100, align: 'center'},
        {title: '备注', slot: 'remark', minWidth: 100, align: 'center'},
        {title: '总次数', key: 'num',minWidth:80, align: 'center'},
        {title: '剩余次数', key: 'surplus_num',minWidth:80, align: 'center'},
        {title: '状态', slot: 'status_text', minWidth:80, align: 'center'},
        {title: '兑换服务范围', slot: 'services_name', minWidth: 100},
        {title: '关联订单号', slot: 'out_trade_no', minWidth: 100, align: 'center'},
        {title: '创建时间', slot: 'create_time', minWidth: 130, align: 'center'},
        {title: '截止时间', slot: 'expire_time', minWidth: 130, align: 'center'},
        {title: '操作', slot: 'action', width: 100, align: 'center'}
      ],

      list: [],
      total: 0,
      queryFormData: {...init_query_form_data},
      statusDesc: {}, // 状态

      batch_id: '', // 获取批次详情得id
      exchangeVisible: false,
      serviceList: [],
      exchangeId: '',
      detailVisible: false,

    }
  },
  computed: {

  },
  watch: {

  },
  created() {
    // 获取卡券明细
    this.getExchangeCardList();
  },
  mounted() {

  },
  methods: {
    onPageChange (page, pageSize) {
      this.queryFormData.page = page
      this.queryFormData.pageSize = pageSize
      this.getExchangeCardList()
    },
    // 获取卡券明细
    getExchangeCardList () {
      let params = {
        uid: this.$route.query.uid,
        ...this.queryFormData
      }
      this.tableLoading = true
      this.$api.getExchangeCardList(params).then(res => {
        this.list = res.list
        this.total = res.total
        // this.statusDesc = res.statusDesc
      }).catch(err => {})
      .finally(() => this.tableLoading = false)
    },

    // 跳转到核销页面
    jumpVerification (card_no) {
      this.$router.push({
        path: '/service/card/verification',
        query: {card: card_no}
      })
    },
    // 获取核销的卡券编号
    getCardno (batch_id) {
      let params = {
        batch_id
      }
      this.$api.getCardno(params).then( res => {
        this.jumpVerification(res.card_no)
      } ).catch( err => {} )
    },

    exchange(row){
      console.log("-> row", row);
      this.exchangeVisible = true
      this.serviceList = row.services
      this.exchangeId = row.id
    },

    // 点击详情
    toDetail (row) {
      this.detailVisible = true
      this.exchangeId = row.id
    },
  },
  filters: {

  }
}
</script>

<style lang="less" scoped>
/deep/ .ivu-table thead th {
  background-color: #f2f2f2 !important;
}
p {
  margin: 0;
}
/deep/ .ivu-page {
  text-align: right !important;
}
.ml10 {
  margin-left: 10px;
}
</style>
