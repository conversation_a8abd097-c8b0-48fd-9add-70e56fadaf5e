<template>
  <Modal
    :value="refundVisible"
    title="储值余额 退款"
    width="780"
    :mask-closable="false"
    top="10vh"
    :transfer="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="content-h flex flex-item-align">
        <div class="flex flex-item-align">
          <div class="charge-content flex flex-c flex-item-center">
            <span class="title">储值余额</span>
            <span class="money">￥{{ wallet.total_money }}</span>
          </div>
          <div class="charge-dot">
            <span style="margin-top: -2px"> = </span>
          </div>

          <div class="charge-content flex flex-c flex-item-center">
            <span class="title">实充余额</span>
            <span class="money">￥{{ wallet.real_money }}</span>
          </div>
          <div class="charge-dot">
            <span style="margin-top: -2px"> + </span>
          </div>

          <div class="charge-content flex flex-c flex-item-center">
            <span class="title">赠送余额</span>
            <span class="money">￥{{ wallet.give_money }}</span>
          </div>
        </div>

        <div class="charge-content flex flex-c flex-item-center" style="margin-left: 30px">
          <span class="title"
            >最近优惠金额
            <Tooltip placement="right" max-width="400"
              ><svg-icon iconClass="explain" class="cursor-pointer" style="color: #9fa6c1"></svg-icon>
              <template #content>
                <h3 style="margin-bottom: 20px">按当前实充余额与上一次充值金额对比</h3>
                <p>若当前实充余额＞上次实充金额</p>
                <span> 最近优惠金额：自倒数第二次充值时间以来 产生的所有订单优惠(享受储值价的优惠部分) </span>
                <p style="margin-top: 20px">若当前实充余额≤上次实充金额</p>
                <span> 最近优惠金额：自倒数第一次充值时间以来 产生的所有订单优惠(享受储值价的优惠部分)</span>
              </template>
            </Tooltip>
          </span>
          <span class="money">￥{{ refundFormData.last_discount }}</span>
        </div>
      </div>
      <!-- <h4 class="tips">
        系统支持针对全部储值余额发起退款。实际退款金额指实际退给用户的金额，系统计算时将在实充余额基础上扣除已享受的储值价购买优惠；<span
          style="color: red;"
          >实际退款金额最低等于0。</span
        >
      </h4> -->
      <Form :model="refundFormData" :rules="refundRules" ref="refundRef">
        <div style="margin-right: 14px; margin-top: 14px">
          <FormItem required label="余额扣除" :label-width="80">
            <FormItem label="实充扣除：" :label-width="85">
              <div class="flex">
                <FormItem prop="real_refund_money">
                  <InputNumber
                    :max="Number(wallet.real_money)"
                    size="small"
                    :precision="2"
                    :active-change="false"
                    placeholder="实充扣除"
                    style="width: 70px"
                    :min="0"
                    v-model="refundFormData.real_refund_money"
                  />
                </FormItem>
                <span style="margin-left: 20px"
                  >扣除后，实充余额<span style="color: red">
                    ¥<span>{{ getRealMoney() }}</span></span
                  ></span
                >
              </div>
            </FormItem>

            <FormItem label="赠送扣除：" :label-width="85">
              <div class="flex">
                <FormItem prop="given_money">
                  <InputNumber
                    :max="Number(wallet.give_money)"
                    size="small"
                    :precision="2"
                    :active-change="false"
                    placeholder="赠送扣除"
                    style="width: 70px"
                    :min="0"
                    v-model="refundFormData.given_money"
                  />
                </FormItem>
                <span style="margin-left: 20px"
                  >扣除后，赠送余额<span style="color: red">
                    ¥<span>{{ getSendMoney() }}</span></span
                  ></span
                >
              </div>
            </FormItem>
          </FormItem>

          <FormItem required label="实际退款" :label-width="80">
            <FormItem label="退款方式：" :label-width="85">
              <div class="flex">
                <FormItem :label-width="80" prop="refund_method">
                  <Select size="small" placeholder="请选择线下退款方式" v-model="refundFormData.refund_method">
                    <Option v-for="item in refundTypes" :key="item.id" :label="item.desc" :value="item.id"></Option>
                  </Select>
                </FormItem>
              </div>
            </FormItem>

            <FormItem label="退款金额：" :label-width="85">
              <div class="flex ">
                <FormItem prop="apply_money" >
                  <InputNumber
                    :max="Number(refundFormData.real_refund_money)"
                    size="small"
                    :min="0"
                    :precision="2"
                    :active-change="false"
                    placeholder="退款金额"
                    style="width: 70px"
                    v-model="refundFormData.apply_money"
                  />
                </FormItem>
<!--                <InputNumber-->
<!--                  disabled-->
<!--                  size="small"-->
<!--                  v-else-->
<!--                  class="mt-3"-->
<!--                  placeholder="退款金额"-->
<!--                  :value="refundFormData.real_refund_money"-->
<!--                ></InputNumber>-->
                <span style="margin-left: 20px">实际退款给客户的金额</span>
              </div>
            </FormItem>
          </FormItem>
          <FormItem label="退款凭证" :label-width="80" style="margin-bottom: 0">
            <MaterialPicture v-model="refundFormData.evidence_img" :limit="5" />
          </FormItem>
          <FormItem label="退款说明" :label-width="80">
            <Input
              v-model="refundFormData.description"
              show-word-limit
              maxlength="300"
              type="textarea"
              :autosize="{ minRows: 3, maxRows: 5 }"
            />
          </FormItem>
        </div>
      </Form>
    </div>
    <div slot="footer">
      <div>
        <Button @click="cancel">取消</Button>
        <Button @click="submit" type="primary">确定</Button>
      </div>
    </div>
  </Modal>
</template>

<script>
import S from 'libs/util';
import { $operator } from '@/libs/operation';
import { isDirectClinic } from '@/libs/runtime';

export default {
  name: 'ChangeDetails',
  mixins: [],

  components: { },

  props: {
    refundVisible: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      initFormData: {
        evidence_img: [],
        last_discount: 0,
        description: '',
        refund_method: '',
        real_refund_money: null,
        given_money: null,
        apply_money: null
      },
      refundFormData: {
        evidence_img: [],
        last_discount: 0,
        description: '',
        refund_method: '',
        real_refund_money: null,
        given_money: null,
        apply_money: null
      },
      refundRules: {
        refund_method: [{ type: 'string', required: true, message: '请选择退款方式', trigger: 'change' }]
      },
      isAdjust: false,
      wallet: {
        real_money: '',
        give_money: '',
        total_money: ''
      },
      refundTypes: {}
    };
  },

  computed: {
    getRealMoney() {
      return function () {
        return $operator.subtract(this.wallet.real_money || 0, this.refundFormData.real_refund_money || 0);
      };
    },
    getSendMoney() {
      return function () {
        return $operator.subtract(this.wallet.give_money || 0, this.refundFormData.given_money || 0);
      };
    },
    isDirect() {
      return isDirectClinic();
    }
  },

  watch: {
    refundVisible(val) {
      if (val) {
        this.getUserWallet();
      }
    }
  },

  created() {
    this.getRefundOptions();
  },

  mounted() {},

  destroyed() {},

  methods: {
    adjustPrice() {
      this.isAdjust = !this.isAdjust;
      console.log(this.isAdjust);
      if (!this.isAdjust) {
        this.getLastDiscount();
      }
    },
    getRefundOptions() {
      this.$api.getRefundOptions().then(res => {
        this.refundTypes = S.descToArrHandle(res.methodDesc);
      });
    },
    // changeLastDiscount(val) {
    //   this.refundFormData.last_discount = val;
    //   if (val) {
    //     if (val >= this.wallet.real_money) {
    //       this.refundFormData.real_refund_money = 0;
    //     } else {
    //       this.refundFormData.real_refund_money = S.mathSub(this.wallet.real_money, val);
    //     }
    //   } else {
    //     this.refundFormData.real_refund_money = this.wallet.real_money;
    //   }
    // },
    // changeRealRefundMoney(val) {
    //   console.log('-> %c val  === %o ', 'font-size: 15px', val);
    //   if (val) {
    //     if (val >= this.wallet.real_money) {
    //       this.refundFormData.last_discount = 0;
    //     } else {
    //       this.refundFormData.last_discount = S.mathSub(this.wallet.real_money, val);
    //     }
    //   } else {
    //     this.refundFormData.real_refund_money = 0;
    //     this.refundFormData.last_discount = this.wallet.real_money;
    //   }
    // },
    formValidation() {
      if (!this.refundFormData.refund_method) {
        this.$Message.error('请选择退款方式');
        return;
      }
      // if (this.isDirect) {
      //   this.refundFormData.apply_money = this.refundFormData.real_refund_money;
      // }
      if (this.refundFormData.apply_money > this.refundFormData.real_refund_money) {
        this.$Message.error('退款金额不得大于实充扣除金额');
        return;
      }
      if (!this.refundFormData.apply_money && this.refundFormData.apply_money !== 0) {
        this.$Message.error('请输入退款金额');
        return;
      }
      console.log(this.refundFormData.apply_money);
      return true;
    },
    submit() {
      // this.$refs['refundRef'].validate(valid => {
      //   if (valid) {

      //   }
      // });
      console.log(this.refundFormData)
      if (!!this.formValidation()) {

        let params = {
          uid: this.$route.query.uid,
          ...this.refundFormData
        };
        console.log('-> %c params  === %o ', 'font-size: 15px', params);
        this.$api.refundApply(params).then(
          res => {
            this.$Message.success('退款申请成功'), this.cancel();
            this.$parent.getUserInfo();
          },
          err => {
            {};
          }
        );
      }
    },
    getUserWallet() {
      this.$api.getUserWalletInfo({ uid: this.$route.query.uid }).then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px', res);
        this.wallet.real_money = Number(res.real_money);
        this.wallet.give_money = Number(res.give_money);
        this.wallet.total_money = Number(res.total_money);
        this.getLastDiscount();
      });
    },
    getLastDiscount() {
      this.$api.getLastDiscount({ uid: this.$route.query.uid }).then(res => {
        console.log('-> %c res  === %o ', 'font-size: 15px', res);
        console.log('-> %c res  === %o ', 'font-size: 15px', res);
        this.refundFormData.last_discount = Number(res.last_discount);
        // this.refundFormData.real_refund_money =
        //   S.mathSub(this.wallet.real_money, res.last_discount) > 0
        //     ? S.mathSub(this.wallet.real_money, res.last_discount)
        //     : 0;
      });
    },
    checkDetail() {
      this.$emit('checkDetail', true);
    },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getStoredList();
    },
    changeVisible(visible) {
      this.refundFormData = this.$lodash.cloneDeep(this.initFormData);
      !visible && this.$refs['refundRef'].resetFields();
      !visible && this.$emit('update:refund-visible', visible);
    },
    cancel() {
      this.changeVisible(false);
    }
  }
};
</script>

<style scoped lang="less">
.content-refund-info {
  background: #f8f9fd;
  border-radius: 4px;
  border: 1px solid #efeef1;
  color: #737c9e;
  padding: 20px 0 20px 32px;
}

.content-h {
  background: #f8f9fd;
  border-radius: 4px;
  border: 1px solid #efeef1;
  color: #737c9e;
  padding: 20px 30px 20px 32px;
  width: 100%;
  justify-content: space-between;

  .charge-content {
    .money {
      font-size: 20px;
      font-weight: 400;
      color: #000000;
      line-height: 32px !important;
    }

    .title {
      height: 12px;
      font-size: 12px;
      font-family: PingFangSC-Light, PingFang SC;
      font-weight: 300;
      color: #747c9b;
      line-height: 12px;
      margin-bottom: 8px;
    }

    .ivu-form-item {
      margin-bottom: 0;
    }

    .ivu-form-item-content {
      line-height: normal;
    }
  }

  .charge-dot {
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 32px;
    font-size: 13px;
    line-height: 30px;
    width: 18px;
    height: 18px;
    background: #e0e2e9;
    color: #f8f9fd;
  }
}

.tips {
  font-size: 14px;
  font-weight: 300;
  color: #7f868d;
  line-height: 20px;
  margin: 14px 0;
}

.detail {
  font-size: 14px;
  color: #1157e5;
  vertical-align: top;
}

.adjust-btn {
  align-self: flex-start;
  margin-top: 4px;
  font-size: 14px;
  color: #1157e5;
  line-height: 20px;
  cursor: pointer;
}
</style>
