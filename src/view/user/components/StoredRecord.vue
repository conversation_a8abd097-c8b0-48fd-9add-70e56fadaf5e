<!--  -->
<template>
  <div class="stored-wrapper">
    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 473">
      <!-- 储值总额 -->
      <template slot-scope="{ row, index }" slot="total_fee">
        {{ Number(row.total_fee) ? '￥' + row.total_fee : '-' }}
      </template>

      <template slot-scope="{ row, index }" slot="payment_fee">
        {{ Number(row.payment_fee) ? '￥' + row.payment_fee : '-' }}
      </template>

      <template slot-scope="{ row, index }" slot="recharge_fee">
        {{ Number(row.recharge_fee) ? '￥' + row.recharge_fee : '-' }}
      </template>

      <template slot-scope="{ row, index }" slot="goods_names">
        <Tooltip v-if="row.goods_names.length" :content="row.goods_names.join(',')">
          <p class="ecs cursor">{{ row.goods_names.join(',') }}</p>
        </Tooltip>
        <p v-else>-</p>
      </template>

      <template slot-scope="{ row, index }" slot="given_fee">
        {{ Number(row.given_fee) ? '￥' + row.given_fee : '-' }}
      </template>

      <template slot-scope="{ row, index }" slot="discount_fee">
        {{ Number(row.discount_fee) ? '￥' + row.discount_fee : '-' }}
      </template>

      <template slot-scope="{ row, index }" slot="pay_time">
        <p>{{ row.pay_time | data_format }}</p>
      </template>

      <template slot-scope="{ row, index }" slot="relate_staff_text">
        <p>{{ row.relate_staff_text || '-' }}</p>
      </template>
    </Table>
    <p style="height: 10px"></p>
    <KPage
      :total="Number(total)"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: center"
    />
  </div>
</template>

<script>
import { isRstClinic } from '@/libs/runtime';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
};
export default {
  name: 'order',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      tableLoading: false, // 表格获取的数据的loading
      total: 0, // 总条数
      list: [], // 随访记录表格的数据
      queryFormData: { ...init_query_form_data },
    };
  },
  computed: {
    tableCols() {
      let list = [
        { title: '储值订单号', key: 'out_trade_no', align: 'center' },
        { title: '储值活动名称', key: 'recharge_activity_name', align: 'center', isRstHide: true },
        { title: '实充金额', slot: 'total_fee', align: 'center' },
        { title: '赠送金额', slot: 'given_fee', align: 'center', isRstHide: true },
        { title: '储值金额', slot: 'recharge_fee', align: 'center' },
        { title: '赠送商品', slot: 'goods_names', align: 'center', isRstHide: true },
        { title: '优惠金额', slot: 'discount_fee', align: 'center', isRstHide: true },
        { title: '实付金额', slot: 'payment_fee', align: 'center' },
        { title: '支付时间', slot: 'pay_time', align: 'center' },
        { title: '支付方式', key: 'pay_type_text', align: 'center' },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '关联人', slot: 'relate_staff_text', align: 'center' },
      ];
      if (isRstClinic()) {
        return list.filter(item => !item.isRstHide);
      }
      return list;
    },
  },
  watch: {},
  created() {},
  mounted() {
    // 获取随访记录
    this.getStoredList();
  },
  methods: {
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getStoredList();
    },
    //
    toVisitDetail({ id, type, uid }) {
      this.$router.push({
        path: type === 'ACTIVITY_VISIT' ? '/daily/visit/activity-visit' : '/daily/visit/detail',
        query: { id, uid },
      });
    },
    // 获取随访记录
    getStoredList() {
      this.tableLoading = true;
      console.log(this.$route.query);
      let params = {
        uid: this.$route.query.uid,
        ...this.queryFormData,
      };
      console.log('-> %c params  === %o ', 'font-size: 15px', params);

      this.$api
        .getStoredList(params)
        .then(
          res => {
            this.list = res.list;
            this.total = res.total;
          },
          rej => this.$Message.error(rej.errmsg)
        )
        .finally(() => (this.tableLoading = false));
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
/deep/ .ivu-table thead th {
  background-color: #f2f2f2 !important;
}

/deep/ .ivu-page {
  text-align: right !important;
}

p {
  margin: 0;
}

.cursor {
  cursor: pointer;
}
</style>
