<!--  -->
<template>
  <div>
    <div class="title-box flex flex-item-align">
      <div class="title">实时概况</div>
      <div class="time">更新时间:{{ today.update_time|data_format('YYYY.MM.DD HH:mm')}}</div>
    </div>
    <div class="info-box flex flex-item-between">
<!--      :style="{backgroundColor:item.color}"-->
      <div class="realtime-info" v-for="(item,index) in userData" :key="index" >
        <div class="flex info">
          <div class="flex-1 left-txt">{{ item.name }}</div>
          <div class="right-txt">昨日: {{ item.yesterday }}</div>
        </div>
        <div class="num-box">{{ item.today }}</div>
      </div>
    </div>
    <div class="title-box flex flex-item-align">
      <div class="title">数据趋势</div>
    </div>

    <div class="chart-nav flex">
      <div class="nav" @click="changeNav(item.id)" :class="{cur:item.id == navId}" v-for="(item,index) in navList"
           :key="index">{{ item.title }}
      </div>
    </div>
    <div class="search-time flex">
      <div>
        <DatePicker type="daterange" :options="disabledTime" format="yyyy-MM-dd" placeholder="请选择时间"
                    style="width: 255px"
                    v-model="showTimes" @on-change="timeChange"
        ></DatePicker>
      </div>
      <div class="search-btn" @click="searchChatData">搜索</div>
    </div>
    <div id="line-chart">

    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now()
        },
      },
      showTimes: [],
      searchTime: {
        start_time: '',
        end_time: '',
      },
      navList: [
        {id: 1, title: '访问人数'},
        {id: 2, title: '新注册人数'},
        {id: 3, title: '支付人数'},
      ],
      navId: 1,
      userData: [], //实时概况
      uvNum: [], //访问人数
      regNum: [], //注册人数
      payNum: [], //支付人数
      lineChart: '',
      today: {}
    }
  },
  created() {

  },
  mounted() {
    this.getUserData()
    this.resizeEcharts()
  },

  methods: {
    timeChange(values) {
      console.log(values)
      if (values) {
        this.searchTime.start_time = values[0]
        this.searchTime.end_time = values[1]
      } else {
        this.searchTime.start_time = ''
        this.searchTime.end_time = ''
      }
    },
    resizeEcharts() {
      this.$nextTick(() => {
        window.onresize = () => {
          this.lineChart.resize()
        }
      })

    },
    searchChatData() {
      let query = {
        st_time: this.searchTime.start_time,
        et_time: this.searchTime.end_time,
      }
      this.getUserData(query)
    },
    changeNav(id) {
      this.navId = id
      switch (id) {
        case 1:
          this.ininLineChart('line-chart', this.uvNum)
          break
        case 2:
          this.ininLineChart('line-chart', this.regNum)
          break
        case 3:
          this.ininLineChart('line-chart', this.payNum)
          break
        default:
          break
      }
    },
    getUserData(query) {
      this.$api.getfansStatistics(query).then(res => {
        console.log(res)
        let list = [
          {today: res.today.visit_uv, yesterday: res.yesterday.visit_uv, name: '访问人数', color: '#3CCAC2'},
          {today: res.today.reg_num, yesterday: res.yesterday.reg_num, name: '新注册人数', color: '#E98281'},
          {today: res.today.user_pay_num, yesterday: res.yesterday.user_pay_num, name: '支付人数', color: '#EBC942'},
          {today: res.today.user_per_price, yesterday: res.yesterday.user_per_price, name: '客单价', color: '#5FCDDB'},
          {today: res.today.total_reg_num, yesterday: res.yesterday.total_reg_num, name: '累计注册人数', color: '#FAB134'},
        ]
        this.userData = list
        this.uvNum = res.uv_num
        this.regNum = res.reg_num
        this.payNum = res.pay_num
        this.showTimes = [res.st_time,res.et_time]
        this.today = res.today
        this.changeNav(this.navId)
      }, err => {
        {}
      })
    },
    ininLineChart(id, data) {
      let showed = false
      if (data.length <= 0) {
        showed = true
      }
      this.lineChart = this.$echarts.init(document.getElementById(id))
      this.lineChart.setOption({
        title: {
          show: showed, // 是否显示title
          text: '暂无数据',
          left: 'center',
          top: 'center',
          textStyle: {
            color: '#000',
            fontSize: 16,
            fontWeight: 400,
          },
        },

        color:['#155BD4'],
        tooltip: {
          trigger: "axis",
          formatter: (val)=>{
            return `<div> 日期：${val[0].data.date} <br/>  人数：${val[0].data.num}人</div>`
          }
        },
        dataset: {
          dimensions: ['date', 'num'],
          source: data,
        },
        xAxis: {
          type: 'category',
          name:'日期'
        },
        grid: {
          width: '88%',
          left: 60,
          // right: 80
        },
        yAxis: {
          type: 'value',
          name:'人数'
        },
        series: [{
          type: 'line',
          // symbol: 'none',
          showSymbol:false
        }],
      })
    },
  },
}

</script>
<style lang='less' scoped>
.title-box {
  width: 100%;
  height: 59px;
  line-height: 59px;
  padding: 0 20px;
  background: #F8F8F8;
}

.title {
  font-size: 14px;
  margin-right: 14px;
  font-weight: 800;
}

.time {
  margin-top: 2px;
  font-size: 12px;
}

.info-box {
  margin-top: 20px;
  margin-bottom: 30px;
  width: 100%;
  padding: 0 20px;
  //overflow: auto;
}

.realtime-info {
  margin-right: 20px;
  flex: 1;
  //min-width: 220px;
  height: 120px;
  box-shadow: 0px 0px 8px rgba(153, 153, 153, 0.2);
  border-radius: 4px;
  padding: 14px 10px;
}

.realtime-info:nth-of-type(5) {
  margin-right: 0;
}

.info {
  line-height: 20px;

  .left-txt {
    font-size: 14px;
  }

  .right-txt {
    font-size: 12px;
    margin-right: 20px;
  }
}

.num-box {
  width: 100%;
  text-align: center;
  font-size: 24px;
  line-height: 34px;
  margin-top: 30px;
}

.chart-nav {
  width: 100%;
  margin-top: 20px;
  border-bottom: 1px solid #E2E4E6;
  border-left: 1px solid #E2E4E6;

  .nav {
    width: 104px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
    text-align: center;
    border-right: 1px solid #E2E4E6;
    border-top: 1px solid #E2E4E6;
    background-color: #fff;
    cursor: pointer;
  }

  .cur {
    background-color: #E2E4E6;
  }
}

.search-time {
  margin-top: 20px;
}

.search-btn {
  width: 62px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  background: #155BD4;
  border-radius: 4px;
  margin-left: 40px;
  font-size: 14px;
  color: #fff;
  cursor: pointer;
}

#line-chart {
  width: 100%;
  height: 25vw;
}
</style>
