<template>
  <div>
    <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
      <Row>
        <FormItem label="" :label-width="0">
          <Input type="text" v-model="queryFormData.code" placeholder="档案编号" />
        </FormItem>

        <FormItem label="">
          <Input type="text" v-model="queryFormData.keyword" placeholder="用户姓名/手机号" />
        </FormItem>

        <FormItem label="">
          <Select v-model="queryFormData.client_status" placeholder="客户状态" clearable>
            <Option v-for="desc_item in clientStatusDesc" :key="desc_item.id" :value="desc_item.id"
              >{{ desc_item.desc }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="">
          <DatePicker
            type="daterange"
            v-model="timeRange"
            @on-change="times => handleTimeChange(times, 'create_time_st', 'create_time_et')"
            placeholder="创建时间"
          ></DatePicker>
        </FormItem>

        <FormItem>
          <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
          <span class="list-reset-btn" @click="onResetSearch">
            <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
            <span>清除条件</span>
          </span>
        </FormItem>
      </Row>
    </Form>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight - 250">
      <!-- 用户信息 -->
      <template slot-scope="{ row }" slot="user_info">
        <div>
          <div class="item">
            <div class="item-label">用户姓名:</div>
            <div class="item-value ecs ecs-2 cursor">{{ row.user?.name || '-' }}</div>
          </div>
          <div class="item">
            <div class="item-label">手机号:</div>
            <div class="item-value">{{ row.user?.mobile || '-' }}</div>
          </div>
        </div>
      </template>

      <!-- 创建时间 -->
      <template slot-scope="{ row }" slot="create_time">
        <div>{{ row.create_time | data_format }}</div>
      </template>

      <template slot-scope="{ row }" slot="operate">
        <a @click="editPlan(row)">编辑</a>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: center"
    />

    <!-- 专案计划 -->
    <project-plan-modal v-model="editPlanVisible" :row="currentRow" @success="onSearch"></project-plan-modal>
  </div>
</template>

<script>
import S from '@/libs/util';
import search from '@/mixins/search';
import ProjectPlanModal from '../components/projectPlanModal.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  code: '',
  keyword: '',
  create_time_st: '',
  create_time_et: '',
  client_status: '',
  r: '',
};
export default {
  name: 'list',
  components: { ProjectPlanModal },
  mixins: [search],
  data() {
    return {
      apiName: 'getUserPlanList',
      queryFormData: { ...init_query_form_data },
      timeRange: [],
      tableCols: [
        { title: '档案编号', key: 'code', align: 'center', width: 80 },
        { title: '客户状态', key: 'client_status_text', align: 'center', minWidth: 240 },
        { title: '用户信息', slot: 'user_info', align: 'center', minWidth: 240 },
        { title: '专病病症/部位', key: 'disease_group_text', align: 'center', minWidth: 240 },
        { title: '任务数量', key: 'task_count', align: 'center', minWidth: 160 },
        { title: '创建人', key: 'operator_name', align: 'center', minWidth: 160 },
        { title: '创建时间', slot: 'create_time', align: 'center', minWidth: 200 },
        { title: '操作', slot: 'operate', align: 'center', fixed: 'right', width: 110 },
      ],
      clientStatusDesc: [],
      editPlanVisible: false,
      currentRow: {},
    };
  },

  created() {
    this.getUserPlanOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  computed: {},

  methods: {
    editPlan(row) {
      this.currentRow = row;
      this.editPlanVisible = true;
    },

    onResetSearch: function () {
      this.timeRange = [];
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    getUserPlanOptions() {
      this.$api.getUserPlanOptions().then(res => {
        this.clientStatusDesc = S.descToArrHandle(res.clientStatusDesc);
      });
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange('create_time_st', 'create_time_et', 'timeRange');
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.ml10 {
  margin-left: 10px;
}

p {
  margin: 0;
}

.item {
  display: flex;
  align-items: flex-start;

  .item-label {
    width: 82px;
    min-width: 82px;
    text-align: right;
    color: #aaa;
    margin-right: 4px;
  }

  .item-value {
    text-align: left;
  }
}
</style>
