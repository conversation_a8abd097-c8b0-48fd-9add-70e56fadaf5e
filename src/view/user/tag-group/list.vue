<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :row-class-name="row => (row.status === '1' ? '' : 'tag-group-table-info-row')"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <div class="flex">
            <Button type="primary" icon="md-add" @click="toDetail">新建标签组</Button>
            <div style="margin-left: auto">
              <FormItem label="" :label-width="0">
                <Input
                  type="text"
                  v-model="queryFormData.keywords"
                  placeholder="请输入标签组或标签值"
                  clearable
                  @on-clear="onResetSearch"
                />
              </FormItem>
            </div>
          </div>
        </Form>
      </template>
      <template #tag_text="{ row }">
        {{ row.tag_text || '-' }}
      </template>
      <template slot-scope="{ row }" slot="action">
        <a class="mr10" @click="toDetail(row, 'edit')">编辑</a>
        <a v-if="row.source !== '1'" class="mr10" @click="changeStatus(row)">
          {{ row.status === '1' ? '禁用' : '启用' }}
        </a>
        <a v-if="row.source !== '1'" @click="deleteTag(row)">删除</a>
      </template>
    </standard-table>
    <custom-dialog :visible.sync="openDialog" :content="dialogText" :loading="savaLoading" :on-ok="onOk" />
  </div>
</template>

<script>
import S from '@/libs/util'; // Some commonly used tools
import search from '@/mixins/search';
import CustomDialog from '@/components/custom-dialog/index.vue';
import StandardTable from '@/components/StandardTable/index.vue';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  keywords: '',
  r: '',
};

export default {
  name: 'list',
  components: { StandardTable, CustomDialog },
  mixins: [search],
  data() {
    return {
      apiName: 'getUserTagGroupList',
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { title: '标签组名称', key: 'name', align: 'center' },
        { title: '标签值', slot: 'tag_text', align: 'center', tooltip: true, maxWidth: 300 },
        { title: '打标方式', key: 'type_text', align: 'center' },
        { title: '来源', key: 'source_text', align: 'center' },
        { title: '状态', key: 'status_text', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      openDialog: false,
      dialogText: '',
      savaLoading: false,
      onOk: null,
    };
  },
  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  methods: {
    changeStatus(row) {
      this.openDialog = true;
      this.dialogText =
        row.status === '1'
          ? '禁用后的标签，将不再支持在诊所手动添加或按照规则自动添加该标签组，已打标签的客户会继续保留标签。'
          : '是否启用该标签组？';
      this.onOk = () =>
        new Promise((resolve, reject) => {
          this.savaLoading = true;
          this.$api
            .changeUserTagStatus({
              id: row.id,
              status: row.status === '1' ? '2' : '1',
            })
            .then(() => {
              resolve(true);
              this.$Message.success(row.status === '1' ? '禁用成功！' : '启用成功！');
              this.submitQueryForm();
            })
            .catch(err => {
              reject(err);
            })
            .finally(() => {
              this.dialogText = '';
              this.savaLoading = false;
            });
        });
    },
    deleteTag(row) {
      // this.$dialog({
      //   component: test,
      //   title: '提示',
      //   props: {
      //     name: '1'
      //   },
      //   onOk: async formRef => {
      //     return new Promise((resolve, reject) => {
      //       setTimeout(() => {
      //         resolve(true);
      //       }, 3000)
      //     })
      //   },
      //   onCancel: (val) => {
      //     console.log(val, '取消')
      //   }
      // })
      this.openDialog = true;
      this.dialogText = '将删除标签组内的全部标签值，同时删除用户身上的标签，且不可恢复，确认删除？';
      this.onOk = () =>
        new Promise((resolve, reject) => {
          this.savaLoading = true;
          this.$api
            .deleteUserTagGroup({
              id: row.id,
            })
            .then(() => {
              resolve(true);
              this.$Message.success('删除成功!');
              this.submitQueryForm();
            })
            .catch(err => {
              reject(err);
            })
            .finally(() => {
              this.dialogText = '';
              this.savaLoading = false;
            });
        });
    },
    toDetail(row = {}, type = 'add') {
      this.$router.push({
        path: '/user/tag-group/detail',
        query: {
          id: row.id,
          type,
        },
      });
    },
    onResetSearch: function () {
      this.timeRange = [];
      this.queryFormData = { ...init_query_form_data };
      this.queryFormData.status = '1';
      this.submitQueryForm();
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style scoped lang="less">
:deep(.ivu-table .tag-group-table-info-row td) {
  color: #c0c4cc;
}
:deep(.ivu-table .tag-group-table-info-row td a) {
  color: #c0c4cc;
}
</style>
