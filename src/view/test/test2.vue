<template>
  <div id="app">
    <!-- directive -->
    <div class="images" v-viewer>
      <div>
        <img src="https://test2img.biranmall.com/test2image/2021/0303/113306_74139.jpg" style="width: 30px">
      </div>
      <div style="display: none;">
        <img src="https://test2img.biranmall.com/test2image/2021/0303/113306_74139.jpg" style="width: 30px">
      </div>
      <div>
        <img src="https://test2img.biranmall.com/test2image/2021/0303/113312_59675.jpg" style="width: 30px">
      </div>
    </div>
    <!-- component -->
    <viewer :images="images" >
      <img v-for="src in images" :src="src | imageStyle" :key="src"  style="width: 30px">
    </viewer>

    {{isUpdate}}
    <KLink :to="{path: '/goods/item/list', query: {status: 200}}">这一个KLink链接</KLink>
    <Button type="primary" @click="onUpdateVersion">更新</Button>
  </div>
</template>
<script>
import 'viewerjs/dist/viewer.css'
import Viewer from 'v-viewer'
import Vue from 'vue'
import {mapState} from "vuex";
import store from "@/store";
Vue.use(Viewer)
export default {
  data() {
    return {
      viewer_options: { "inline": true, "button": true, "navbar": true, "title": false, "toolbar": true, "tooltip": true, "movable": false, "zoomable": false, "rotatable": false, "scalable": false, "transition": true, "fullscreen": false, "keyboard": true, "url": "data-source" },
      images: ["https://test2img.biranmall.com/test2image/2021/0303/113306_74139.jpg", "https://test2img.biranmall.com/test2image/2021/0303/113309_94041.jpg", "https://test2img.biranmall.com/test2image/2021/0303/113312_59675.jpg"]
    }
  },

  methods: {
    onUpdateVersion: function () {
      store.dispatch('version/checkVersion', '1.1').then()
    },

  },

  computed: {
    ...mapState('version', {
      isUpdate: state => state.is_update,
    }),
  }

}
</script>
