<template>
  <div class="verification" v-loading="queryLoading">
    <div class="card-input">
      <div style="display: none">
        <input type="text" name="fakeusernameremembered1" />
        <input type="password" name="fakepasswordremembered1" />
      </div>
      <input type="password" name="password" autocomplete="on" style="position: fixed; left: -2000px" />
      <Input
        ref="input-card-no"
        style="width: 480px; max-width: 480px"
        search
        enter-button="验券"
        v-model="queryFormData.card_no"
        placeholder="请在此输入券码和扫码抢扫码"
        autocomplete="off"
        name="new-password"
        clearable
        @input.native="handleInput"
        @on-search="onSearch"
        @on-clear="reset"
      />
      <span class="list-reset-btn" style="width: 80px; margin-left: 8px; margin-right: auto" @click="reset">
        <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
        <span>清除条件</span>
      </span>
      <div class="header-right-wrap" v-if="is_out_sales_card === '1'">
        <div class="header-right">
          <div class="left-wrap">
            <div class="label">{{ detailInfo.out_sales_channel === '1' ? '美团/点评' : '抖音' }}商品名称：</div>
            <div class="text text-ellipsis" v-overflow-tooltip>{{ out_goods_name || '-' }}</div>
          </div>
          <div class="split-line" />
          <div class="right-wrap">
            <div class="right-wrap-item">
              <div class="label">支付金额：</div>
              <div class="text text-ellipsis">¥ {{ getPayMount }}</div>
            </div>
            <div class="right-wrap-item">
              <div class="label">支付时间：</div>
              <div class="text text-ellipsis">{{ pay_at || '-' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <out-verification
      ref="outVerification"
      v-if="is_out_sales_card === '1'"
      :detail="detailInfo"
      :card_no="queryFormData.card_no"
    />
    <template v-if="is_out_sales_card !== '1' && Object.keys(card).length > 0">
      <Row class="mt10">
        <Col>
          <KWidget label="服务名称:" text>
            {{ card.card_name }}
          </KWidget>
        </Col>

        <Col>
          <KWidget label="服务类型:" text>
            {{ struct.serv_type_text }}
          </KWidget>
        </Col>

        <Col>
          <KWidget label="核销状态:" text>
            {{ statusDesc[card.status].desc }}
          </KWidget>
        </Col>
      </Row>

      <Row>
        <Col>
          <KWidget label="所属商品/问诊:" text>
            {{ (struct.goods && struct.goods.name) || '-' }}
          </KWidget>
        </Col>

        <Col>
          <KWidget label="关联订单号:" text>
            <span v-if="card.from === 'MS_SYNC'">{{ extra_info.out_trade_no || '-' }}</span>
            <span v-else-if="card.from === 'H_ORDER'">{{ order.out_trade_no || '-' }}</span>
            <KLink
              v-else-if="card.from === 'S_ORDER'"
              :to="{ path: '/trade/order/list', query: { out_trade_no: order.out_trade_no } }"
              target="_blank"
            >
              {{ order.out_trade_no }}
            </KLink>
            <KLink
              v-else-if="card.from === 'RC_ORDER'"
              :to="{ path: '/trade/give/list', query: { out_trade_no: order.out_trade_no } }"
              target="_blank"
            >
              {{ order.out_trade_no }}
            </KLink>
          </KWidget>
        </Col>

        <Col>
          <KWidget label="附注:" text>
            {{ struct.notes || '-' }}
          </KWidget>
        </Col>
      </Row>
      <p class="line"></p>
      <Row>
        <Col>
          <KWidget label="客户姓名:" text>
            <KLink :to="{ path: '/user/detail', query: { uid: user.uid } }" target="_blank">{{ user.real_name }}</KLink>
          </KWidget>
        </Col>

        <Col>
          <KWidget label="客户手机号:" text>
            {{ user.mobile || '-' }}
          </KWidget>
        </Col>

        <Col>
          <KWidget label="客户性别:" text>
            {{ user.sex_text || '-' }}
          </KWidget>
        </Col>
      </Row>
      <p class="line"></p>
      <Row v-if="is_rst">
        <Col>
          <template>
            <KWidget label="服务人:" v-if="statusDesc[card.status].kw === 'STATUS_WAIT_USE'">
              <Select
                v-model="artificer_id"
                style="width: 240px"
                placeholder="请选择服务人"
                @on-change="rstPhysichange"
                clearable
              >
                <Option v-for="(item, index) in technicianList" :value="item.id" :key="index">{{
                  item.name_info
                }}</Option>
              </Select>
            </KWidget>
            <KWidget label="服务人:" text v-else>
              {{ (card.divide_info && card.divide_info.physio_name) || '-' }}
            </KWidget>
          </template>
        </Col>
<!--        <Col>-->
          <!--          <KWidget label="等级/比例:" text>-->
          <!--            <div class="readonly-box">-->
          <!--              &lt;!&ndash;              <div v-if="detailInfo.is_980_goods === '1'">固定分成</div>&ndash;&gt;-->
          <!--              <div class="flex" v-if="getRstCurrentPhysio.id || statusDesc[card.status].kw === 'STATUS_WAIT_USE'">-->
          <!--                <span v-if="getRstCurrentPhysio.id">-->
          <!--                  <span>{{ getRstCurrentPhysio.level_text }}</span>-->
          <!--                  <span style="margin-left: 10px; margin-right: 10px">/</span>-->
          <!--                  <span>{{ getRstCurrentPhysio.ratio }}%</span>-->
          <!--                </span>-->
          <!--                <span v-else>-</span>-->
          <!--              </div>-->
          <!--              <div class="flex" v-else>-->
          <!--                <span v-if="Number(card?.divide_info?.physio_id)">-->
          <!--                  <span>{{ card.divide_info?.level_text || '-' }}</span>-->
          <!--                  <span style="margin-left: 10px; margin-right: 10px">/</span>-->
          <!--                  <span>{{ card.divide_info?.ratio || 0 }}%</span>-->
          <!--                </span>-->
          <!--                <span v-else>-</span>-->
          <!--              </div>-->
          <!--            </div>-->
          <!--          </KWidget>-->
<!--        </Col>-->
<!--        <Col>-->
          <KWidget label="业绩提成:" text>
            <div class="readonly-box" style="width: 200px;">
              <!--              <span v-if="detailInfo.is_980_goods === '1'">10元</span>-->
              <span v-if="getRstCurrentPhysio.id || statusDesc[card.status].kw === 'STATUS_WAIT_USE'">
                <span v-if="getRstCurrentPhysio.id">{{ getRstDivideMoney | number_format }}元</span>
                <span v-else>-</span>
              </span>
              <span v-else>
                <span v-if="Number(card?.divide_info?.physio_id)"
                  >{{ Number(card.divide_info?.money || 0) | number_format }}元
                </span>
                <span v-else>-</span>
              </span>
            </div>
          </KWidget>
        </Col>
      </Row>
      <Row v-else>
        <Col>
          <template
            v-if="statusDesc[card.status].kw === 'STATUS_WAIT_USE' || statusDesc[card.status].kw === 'STATUS_HAS_USE'"
          >
            <KWidget label="服务技师:" v-if="statusDesc[card.status].kw === 'STATUS_WAIT_USE'">
              <Select v-model="artificer_id" style="width: 240px" placeholder="请选择技师" clearable>
                <Option v-for="(item, index) in technicianList" :value="item.artificer_id" :key="index">
                  {{ item.name }}
                </Option>
              </Select>
            </KWidget>
            <KWidget label="服务技师:" text v-else>
              {{ (card.artificer && card.artificer.name) || '-' }}
            </KWidget>
          </template>
        </Col>

        <Col>
          <template
            v-if="statusDesc[card.status].kw === 'STATUS_WAIT_USE' || statusDesc[card.status].kw === 'STATUS_HAS_USE'"
          >
            <KWidget label="技师分佣:" v-if="statusDesc[card.status].kw === 'STATUS_WAIT_USE'">
              <Input-number
                style="width: 240px"
                v-model="amount"
                placeholder="请输入本次服务技师可获得的佣金"
                :min="0"
                :active-change="false"
                :precision="2"
              ></Input-number>
            </KWidget>
            <KWidget label="技师分佣:" text v-else>
              ¥ {{ card.artificer && card.artificer.amount | number_format }}
            </KWidget>
          </template>
        </Col>
      </Row>
      <p class="line"></p>
      <Row>
        <Col>
          <KWidget label="核销验证:" v-if="statusDesc[card.status].kw === 'STATUS_WAIT_USE'">
            <Select v-model="verify_type" style="width: 240px" placeholder="请选择核销验证方式">
              <Option v-for="(item, index) in verifyTypeDescList" :value="item.id" :key="index">{{ item.desc }}</Option>
            </Select>
            <p class="selt-action-tip" v-show="verify_type == 3">注：请务必在用户的知晓下执行该操作</p>
          </KWidget>

          <KWidget label="核销验证:" text v-else>
            {{ (verifyTypeDesc[card.verify_type] && verifyTypeDesc[card.verify_type].desc) || '-' }}
          </KWidget>
        </Col>

        <Col v-if="card.status == 20">
          <KWidget label="核销时间:" text>
            {{ card.used_time | data_format }}
          </KWidget>
        </Col>

        <Col>
          <!-- 验证码核销 -->
          <KWidget label="手机验证码:" v-show="verify_type == 1 && statusDesc[card.status].kw === 'STATUS_WAIT_USE'">
            <div class="flex">
              <Input :maxlength="4" style="width: 138px" v-model="authcode" placeholder="输入手机验证码" clearable />
              <vac ref="vac" :auto-start="false" :left-time="60000" class="ml10">
                <template slot="process" slot-scope="{ timeObj }">
                  <Button type="primary" disabled>{{ timeObj.ceil.s }}s</Button>
                </template>

                <template slot="before">
                  <Button type="primary" :loading="auth_loading" @click="onCountDownStart">获取验证码</Button>
                </template>

                <template slot="finish">
                  <Button type="primary" :loading="auth_loading" @click="onCountDownStart">获取验证码</Button>
                </template>
              </vac>
            </div>
          </KWidget>

          <!-- 核验扫码 -->
          <KWidget label="核验扫码:" v-show="verify_type == 2">
            <div class="scan flex mt10">
              <!-- scan -->
              <div class="flex flex-c flex-item-center" v-show="statusDesc[card.status].kw === 'STATUS_WAIT_USE'">
                <div class="scan-img img-block">
                  <div ref="QrCode" class="flex flex-item-center"></div>
                </div>
                <p class="scan-tip">请用微信扫码</p>
              </div>

              <!-- 签名 -->
              <div class="autograph img-block" v-if="confirm_sign == 1">
                <viewer :images="[signature_image]">
                  <img :src="signature_image" class="cursor" alt="" />
                </viewer>
              </div>
            </div>
          </KWidget>
        </Col>
      </Row>
    </template>
    <div style="height: 35px"></div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <dvd />
      <dvd />
      <dvd />
      <template v-if="is_out_sales_card === '1'">
        <Button type="primary" @click="outCardCheckin()"> 确认核销 </Button>
      </template>
      <template v-else-if="statusDesc[card.status]?.kw === 'STATUS_WAIT_USE'">
        <Button type="primary" @click="check(card.id)"> 确认核销 </Button>
      </template>
    </div>

    <Modal
      :value="selectNumsVisible"
      title="请选择验券数量"
      width="350px"
      :mask-closable="false"
      :styles="{ top: '20vh' }"
      @on-ok="handleSelectedCardOk"
      @on-cancel="reset"
    >
      <div style="display: flex; align-items: center; justify-content: center; padding-left: 16px">
        <div style="font-size: 16px">本次验券数量：</div>
        <InputNumber
          class="selectCardNums"
          v-model="selectedCardNum"
          :min="1"
          :max="maxCardGroupedList.length"
          :precision="0"
          controls-outside
          style="flex: 1"
          @on-focus="event => event.currentTarget.select()"
        />
      </div>
      <template #footer>
        <Button @click="reset">取消</Button>
        <Button type="primary" @click="handleSelectedCardOk">确定</Button>
      </template>
    </Modal>
    <Modal
      :value="submitModalVisible"
      :mask-closable="false"
      width="450px"
      :styles="{ top: '20vh' }"
      class-name="outCardSubmitModal"
      @on-ok="outCardCheckin(true)"
      @on-cancel="closeSubmitModalVisible"
    >
      <div slot="header" class="outCardSubmitHeader">
        <img src="@/assets/image/warning.png" alt="" />
        确定核销吗？
      </div>
      <p>核销后，在订单管理页面将自动生成消费订单。</p>
      <p>订单备注：</p>
      <Input type="textarea" :rows="4" v-model="remarks" style="width: 100%" />
      <template #footer>
        <Button @click="closeSubmitModalVisible">取消</Button>
        <Button type="primary" :loading="submitLoading" @click="outCardCheckin(true)">确定</Button>
      </template>
    </Modal>
    <Modal
      :value="submitSuccessModalVisible"
      title=""
      :mask-closable="false"
      width="450px"
      footer-hide
      :styles="{ top: '20vh' }"
      class-name="outCardSubmitSuccessModal"
      @on-cancel="toCardList"
    >
      <div class="outCardSubmitSuccessModalContent">
        <img src="@/assets/image/success.png" alt="" />
        <div>核销成功</div>
      </div>
      <div class="outCardSubmitSuccessModalFooter">
        <Button @click="toCardList">查看卡券列表</Button>
        <Button type="primary" @click="toOrderDetail">查看订单详情</Button>
      </div>
    </Modal>
  </div>
</template>

<script>
/* eslint-disable */
import { isRstClinic } from '@/libs/runtime';
import { $operator } from '@/libs/operation';
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */
import { isLogin } from '@/libs/runtime';
import creatQrCode from '@/mixins/creatQrCode';
import OutVerification from '@/view/service/card/components/outVerification.vue';
import { cloneDeep, debounce, throttle } from 'lodash';
let init_query_from_data = {
  id: '',
  card_no: '',
};

export default {
  name: 'verification',
  components: { OutVerification },
  mixins: [creatQrCode],
  data() {
    return {
      queryFormData: { ...init_query_from_data },
      queryLoading: false,
      card: {},
      extra_info: {},
      order: {},
      user: {},
      struct: {},
      statusDesc: {},
      technicianList: [], // 可选技师的列表数据
      amount: null, // 技师分佣
      artificer_id: '', // 技师id
      verificationTitle: '确定核销吗？',

      // 核销
      verify_type: '1', // 核销方式
      authcode: '', // 短信验证码
      auth_loading: false, // 获取验证码loading
      verifyTypeDescList: [], // 核销验证枚举值
      confirm_sign: '', // 1:标识用户确认上传核销
      signature_image: '', // 用户签名
      loopTime: null,
      request_img_second: 0.1, // 用户请求签名得轮询时间
      is_rst: isRstClinic(),
      detailInfo: {},
      rst_phy_current_item: {}, // rst技师选中的item

      is_out_sales_card: '',
      pay_amount: '',
      out_goods_name: '',
      pay_at: '',

      selectNumsVisible: false,
      maxCardGroupedList: [],
      selectedCardNum: 1,
      remarks: '',
      submitModalVisible: false,
      submitLoading: false,
      submitSuccessModalVisible: false,
      submitSuccessInfo: {},
    };
  },

  watch: {},

  computed: {
    // is980ServiceCard() {
    //   return this.detailInfo.is_980_service_card === '1';
    // },
    getRstCurrentPhysio() {
      let current_item = this.technicianList.find(item => item.id === this.artificer_id);
      return current_item || {};
    },
    /**
     * rst分账策略获得的业绩提成
     * 1: 固定金额分成
     * 2: 按分账比例分成
     * 最终乘上 职级系数
     * */
    getRstDivideMoney() {
      const real_money = Number(this.card?.real_money || 0)
      let rst_current_item = this.getRstCurrentPhysio
      const fixed_factor = Number(rst_current_item?.fixed_factor || 0)
      const server = rst_current_item?.divide_rule?.server || {}
      const divide_type = server.divide_type
      let money = 0
      // 固定金额分成: 固定金额 * 职级系数
      if ( divide_type === 'fixed' ) {
        money = $operator.multiply(Number(server.divide_value || 0), fixed_factor)
      }else{
        // 比例分账 实收金额 * 比例 * 职级系数
        money = $operator.multiply(real_money, Number(server.divide_value / 100 || 0))
        money = $operator.multiply(money, fixed_factor)
      }
      return money || 0
    },
    getPayMount() {
      return $operator.multiply(+this.detailInfo.pay_price || 0, +this.selectedCardNum || 1);
    },
  },

  created() {
    this.$nextTick(() => {
      this.$refs['input-card-no'].focus();
      const { card } = this.$route.query;
      if (card) {
        this.queryFormData.card_no = card;
        this.onSearch();
      }
    });
  },

  methods: {
    handleInput(event) {
      // 使用正则表达式替换中文符号为英文符号
      const value = event.target.value
        .replace(/，/g, ',')
        .replace(/。/g, '.')
        .replace(/、/g, '/')
        .replace(/：/g, ':')
        .replace(/-/g, '-');
      // 这里可以根据需要添加更多的替换规则
      event.target.value = value; // 直接修改输入框的值（注意：在某些情况下，你可能需要使用this.$refs来访问输入框）
    },
    rstPhysichange(id) {
      this.rst_phy_current_item = this.technicianList.find(item => item.id == id) || {};
    },
    // 重置
    reset() {
      this.queryFormData.card_no = '';
      // 关闭延时器
      this.stopLoop();
      // 重置请求的数据
      this.resetData();
    },
    // 清除数据
    resetData() {
      this.detailInfo = {};
      this.card = {};
      this.extra_info = {};
      this.order = {};
      this.user = {};
      this.struct = {};
      this.statusDesc = {};
      this.amount = null; // 技师分佣
      this.artificer_id = ''; // 技师id
      this.authcode = '';
      this.confirm_sign = '';
      this.signature_image = '';
      this.request_img_second = 0.1;
      this.is_out_sales_card = '';
      this.pay_amount = '';
      this.out_goods_name = '';
      this.pay_at = '';
      this.selectNumsVisible = false;
      this.submitModalVisible = false;
      this.remarks = '';
      this.selectedCardNum = 1;
      this.maxCardGroupedList = [];
    },
    handleSelectedCardOk() {
      const cards = this.maxCardGroupedList?.filter((item, i) => i < this.selectedCardNum).flat(1);
      this.$set(this.detailInfo, 'cards', cards);
      this.handleParseData(this.detailInfo);
    },
    // 开始轮询
    startLoop() {
      this.stopLoop();
      this.loopTime = setInterval(() => {
        this.getsignatureimage(this.startLoop);
      }, this.request_img_second * 1000);
    },
    // 关闭轮询
    stopLoop() {
      clearInterval(this.loopTime);
      this.loopTime = null;
    },
    // 关闭验证码倒计时
    closeCountDown() {
      this.$nextTick(() => {
        this.$refs?.vac?.finishCountdown();
      });
    },
    getOrderRevenueMembers() {
      let params = {
        goods_service_id: this.card?.service_info?.id,
        card_id: this.card?.id,
        reserve_id: this.card?.reserve_id,
      };
      this.$api.getOrderRevenueMembers(params).then(res => {
        this.technicianList = res;
      });
    },
    // 获取验证码
    onCountDownStart() {
      let params = {
        mobile: this.user.mobile,
      };
      this.auth_loading = true;
      this.$api
        .serviceSendauthcode(params)
        .then(() => {
          this.$refs.vac.startCountdown(true);
          this.$Message.success('发送成功');
        })
        .catch(error => {})
        .finally(() => (this.auth_loading = false));
    },

    back() {
      this.$router.back();
    },

    check(id) {
      if (!this.validCondition()) {
        return false;
      }

      const { artificer_id, amount } = this;
      if (this.is_rst) {
        if (artificer_id == '') {
          this.$Message.error('尚未指定服务技师，无法核销');
          return;
        }
        this.$Modal.confirm({
          title: '确定核销吗？',
          onOk: () => {
            this.onCheckin(id);
          },
        });
        return;
      }

      if (amount !== null && artificer_id == '') {
        this.$Message.error('尚未指定服务技师，无法核销');
      } else {
        if (artificer_id !== '' && amount !== null) {
          let payment_fee = this.extra_info.is_rsj_order == '1' ? this.extra_info.payment_fee : this.order.payment_fee;
          let verificationTitle = '';
          if (Number(amount) > Number(payment_fee)) {
            verificationTitle = '佣金大于该笔订单金额，是否继续？';
          } else {
            verificationTitle = '确定核销吗？';
          }
          this.$Modal.confirm({
            title: verificationTitle,
            onOk: () => {
              this.onCheckin(id);
            },
          });
        } else {
          let title = '';
          if (artificer_id == '' && amount == null) {
            title = '尚未完善服务技师及分佣信息，是否确认核销？';
          } else {
            title = '尚未完善技师分佣信息，是否确认核销？';
          }
          this.$Modal.confirm({
            title: title,
            okText: '确认核销',
            cancelText: '取消',
            onOk: () => {
              this.onCheckin(id);
            },
            onCancel: () => {},
          });
        }
      }
    },
    closeSubmitModalVisible() {
      this.submitModalVisible = false;
      this.remarks = '';
    },
    outCardCheckin: debounce(function (flag) {
      let cards = cloneDeep(this.$refs.outVerification.cards);
      let reserves = cloneDeep(this.$refs.outVerification.reserves);
      const uid = this.$refs.outVerification.user?.uid;
      if (!uid) {
        this.$Message.error('请选择用户！');
        return;
      }
      const isNotHasPhysioIndex = cards.findIndex(card => !card.physio_id);
      if (isNotHasPhysioIndex > -1) {
        this.$Message.error(`请选择技师信息(${isNotHasPhysioIndex + 1})的服务技师!`);
        return;
      }
      let isSelected = -1;
      cards = cards?.map((card, index) => {
        const isRelateOrder = reserves.some(item => item.goods_service_id === card.goods_service_id);
        // 如果有关联预约单，则必选一个
        if (isRelateOrder && !card.reserve_id && isSelected === -1) {
          isSelected = index;
        }
        return {
          out_goods_id: card.out_goods_id,
          certificate_id: card.certificate_id,
          service_id: card.service_id,
          physio_id: card.user_type === 'physio' ? card.physio_id : '',
          member_id: card.user_type === 'member' ? card.physio_id : '',
          reserve_id: card.reserve_id === 'empty' ? '' : card.reserve_id,
          goods_service_id: card.goods_service_id,
        };
      });
      if (isSelected !== -1) {
        this.$Message.error(`请选择服务信息${isSelected + 1}关联预约单！`);
        return;
      }
      const params = {
        card_no: this.queryFormData.card_no,
        cards,
        use_nums: this.selectedCardNum,
        userid: uid,
        out_sales_channel: this.detailInfo.out_sales_channel,
        remark: this.remarks,
      };
      !flag && (this.submitModalVisible = true);
      if (flag) {
        this.submitLoading = true;
        this.$api
          .serviceOutCardCheckin(params)
          .then(res => {
            this.submitSuccessInfo = res;
            this.submitModalVisible = false;
            this.submitSuccessModalVisible = true;
          })
          .finally(() => {
            this.submitLoading = false;
          });
      }
    }, 300),
    toCardList() {
      this.submitSuccessModalVisible = false;
      this.$router.push('/service/card/list');
      this.resetData();
    },
    toOrderDetail() {
      this.submitSuccessModalVisible = false;
      this.$router.push({
        path: '/trade/order/detail',
        query: {
          orderid: this.submitSuccessInfo.order_id,
          orderType: 'shop_order',
        },
      });
      this.resetData();
    },
    onSearch() {
      if (this.queryLoading) return;
      if (!this.queryFormData.card_no && !this.queryFormData.id) {
        this.$Message.error('请输入券号');
        this.reset();
        return;
      }

      if (this.is_rst) {
        // 关闭延时器
        this.stopLoop();
        // 重置请求的数据
        this.resetData();
      }
      this.queryLoading = true;
      this.$api
        .serviceCardGet({ ...this.queryFormData })
        .then(
          data => {
            // 如果是抖音、美团券核销
            if (data?.is_out_sales_card === '1') {
              const cards = cloneDeep(data?.cards || []);

              const groupedItems = cards.reduce((acc, item) => {
                // 如果当前分类的数组还不存在，则创建一个新的数组
                if (!acc[item.certificate_id]) {
                  acc[item.certificate_id] = [];
                }
                // 将当前项添加到对应分类的数组中
                acc[item.certificate_id].push(item);
                return acc;
              }, {}); // 使用空对象作为累加器的初始值

              // 将结果转换为二维数组
              const maxCardGroupedList = Object.values(groupedItems);
              this.maxCardGroupedList = maxCardGroupedList;
              if (maxCardGroupedList.length > 1) {
                {
                  this.selectNumsVisible = true;
                  this.detailInfo = data;
                  return;
                }
              }
              this.handleParseData(data);
              return;
            }
            this.handleParseData(data);
          },
          rej => {
            this.stopLoop();
            this.resetData();
          }
        )
        .finally(() => {
          // this.queryFormData.id = ""
          // this.queryFormData.card_no = ""
          this.queryLoading = false;
        });
    },

    handleParseData(data) {
      this.detailInfo = data;
      this.is_out_sales_card = data?.is_out_sales_card || '';
      this.pay_amount = data?.pay_amount || '0.00';
      this.pay_at = data?.pay_at || '';
      this.out_goods_name = data?.out_goods_name || '';
      this.card = data.card;
      this.extra_info = data.extra_info;
      this.order = data.order;
      data?.user && (this.user = data.user);
      this.struct = data.struct;
      this.statusDesc = data.statusDesc;
      this.selectNumsVisible = false;
      if (this.is_out_sales_card === '1') return;
      if (!this.is_rst) {
        this.technicianList = data.artificer_list;
      }
      if (this.is_rst) {
        this.getOrderRevenueMembers();
      }
      this.verifyTypeDescList = S.descToArrHandle(data.verifyTypeDesc);
      this.verifyTypeDesc = data.verifyTypeDesc;
      // 核销类型
      if (data.card.verify_type) {
        this.verify_type = data.card.verify_type;
      }

      // 获取核销h5的地址
      this.getServiceCardWriteoffurl();
      // 重置新的请求时间，立即获取签名
      this.request_img_second = 0.1;
      this.startLoop();
      // 当获取一个新的卡券请求时,关闭上一个验证码倒计时
      this.closeCountDown();
    },
    // 判断用户是否可以核销
    validCondition() {
      if (!this.verify_type) {
        this.$Message.error('核销验证不可为空');
        return false;
      }

      if (this.verify_type == '1' && !this.authcode) {
        this.$Message.error('请输入手机验证码');
        return false;
      }

      if (this.verify_type == '2' && this.confirm_sign != 1) {
        this.$Message.error('请先进行签名');
        return false;
      }
      return true;
    },

    onCheckin: function (id) {
      const { artificer_id, amount } = this;
      let params = {
        artificer_id,
        amount: amount == null ? 0 : amount,
        id,
        verify_type: this.verify_type,
        verify_code: this.verify_type == 1 ? this.authcode : '',
      };
      if (this.is_rst) {
        this.$delete(params, 'artificer_id');
        this.$delete(params, 'amount');

        // 员工分账 / 技师分账
        if (this.rst_phy_current_item.type == 'member') {
          this.$set(params, 'member_id', this.artificer_id);
        } else {
          this.$set(params, 'physio_id', this.artificer_id);
        }
      }
      this.$api
        .serviceCardCheckin(params)
        .then(res => {
          this.$Message.success('核销成功');
          this.back();
          // this.queryFormData.card_no = this.card.card_no
          // this.onSearch()
        })
        .catch(error => {
          {
          }
        });
    },

    // 获取核销h5的二维码地址
    getServiceCardWriteoffurl() {
      let params = {
        id: this.card.id,
        card_no: this.queryFormData.card_no,
      };
      this.$api.getServiceCardWriteoffurl(params).then(res => {
        this._creatQrCode(res.url);
      });
    },

    // 获取核销H5签名
    getsignatureimage(cb) {
      let params = {
        id: this.card.id,
        card_no: this.queryFormData.card_no,
      };
      this.$api
        .getsignatureimage(params)
        .then(res => {
          // 用户签名图片
          this.signature_image = res.signature_image;
          // 用户核销状态
          this.confirm_sign = res.confirm_sign;
          // 后台控制签名轮询得间隔
          this.request_img_second = res.request_img_second;
        })
        .finally(() => {
          // 添加保障,当退出登录时，停止轮询
          if (!isLogin()) {
            this.stopLoop();
            return;
          }
          if (cb) {
            cb();
          }
        });
    },
  },
  deactivated() {
    console.log(213123);
    this.stopLoop();
  },
  beforeDestroy() {
    console.log(213213);
    this.stopLoop();
  },
};
</script>

<style lang="less" scoped>
.card-input {
  padding-bottom: 16px;
  border-bottom: 1px solid #ecedf0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .header-right-wrap {
    flex: 1;
    display: flex;
    padding-left: 30px;
    .header-right {
      max-width: 100%;
      width: 600px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px 16px;
      background: #f9fafb;
      border-radius: 4px;
      .left-wrap {
        width: 100%;
        min-width: 50%;
        max-width: 66%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .label {
          width: 100%;
          height: 20px;
          font-size: 13px;
          color: #999999;
          line-height: 20px;
        }
        .text {
          height: 20px;
          font-size: 13px;
          color: #333333;
          line-height: 20px;
          text-align: left;
        }
      }
      .split-line {
        width: 1px;
        height: 100%;
        background: #dcdde0;
        margin: 0 16px;
      }
      .right-wrap {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .right-wrap-item {
          width: 100%;
          display: flex;
          align-items: center;
          .label {
            width: 70px;
            height: 20px;
            font-size: 13px;
            color: #999999;
            line-height: 20px;
            text-align: left;
            flex-shrink: 0;
          }
          .text {
            flex: 1;
            height: 20px;
            font-size: 13px;
            color: #333333;
            line-height: 20px;
            text-align: left;
          }
        }
      }
    }
  }
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.selt-action-tip {
  margin-top: 10px;
  color: red;
}
.scan {
  .img-block {
    width: 100px;
    height: 100px;
    background: #ccc;
    img {
      width: 100px;
      height: 100px;
    }
  }
  .scan-tip {
    margin-top: 10px;
    color: #ccc;
  }
  .autograph {
    margin-left: 20px;
  }
}
.cursor {
  cursor: pointer;
}

.widget-form-group {
  margin-top: 0px;
}

.mt10 {
  margin-top: 10px;
}
.mt30 {
  margin-top: 30px;
}

.ml10 {
  margin-left: 10px;
}
.line {
  border-top: 1px dashed #ccc;
  margin: 10px 0px 10px 26px;
}
.ivu-row {
  padding: 10px 0px;
}
.ivu-col {
  min-width: 366px;
}
.outCardSubmitHeader {
  width: 100%;
  display: flex;
  align-items: center;
  font-weight: 600;
  font-size: 16px;
  color: #333333;
  line-height: 24px;
  > img {
    width: 22px;
    height: 22px;
    margin-right: 8px;
  }
}
.outCardSubmitSuccessModalContent {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 60px;
  > img {
    width: 56px;
    height: 56px;
  }
  > div {
    margin: 24px auto;
    font-weight: 600;
    font-size: 20px;
    color: #333333;
    line-height: 28px;
  }
}
.outCardSubmitSuccessModalFooter {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 24px 0 36px 0;
  > button:last-child {
    margin-left: 12px;
  }
}
</style>

<style lang="less">
.selectCardNums {
  input {
    text-align: center;
  }
}
.viewer-canvas {
  img {
    background: #fff !important;
  }
}
.verification {
  .widget-form-label {
    font-size: 12px;
  }
}
.readonly-box {
  width: 100%;
  height: 32px;
  background: #f3f3f3;
  margin-top: -6px;
  border-radius: 2px;
  padding: 0px 24px 0px 8px;
  display: flex;
  align-items: center;
  //color: #ccc;
  border: 1px solid #bbb;
}
.outCardSubmitModal {
  .ivu-input-wrapper {
    .ivu-select {
      width: 100% !important;
      max-width: 100% !important;
    }
  }
}

.outCardSubmitSuccessModal .ivu-modal-header {
  border-bottom: none;
}
</style>
