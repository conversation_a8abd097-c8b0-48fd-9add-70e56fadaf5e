<template>
  <Modal :value="value" title="通兑券作废" :mask-closable="false" :width="600" @on-visible-change="visibleChange">
    <Form :model="formData" label-position="right" :label-width="100" label-colon>
      <div class="flex" style="font-size: 14px">
        <div style="width: 280px">
          <FormItem label="所属用户">{{ invalidInfo.user_info?.real_name || '-' }}</FormItem>
          <FormItem label="用户手机号">{{ invalidInfo.user_info?.mobile || '-' }}</FormItem>
          <FormItem label="通兑券名称">{{ invalidInfo.name }}</FormItem>
          <FormItem label="关联订单">{{ invalidInfo.order_info?.out_trade_no }}</FormItem>
          <FormItem label="生效时间">{{ invalidInfo.created_at }}</FormItem>
          <FormItem label="过期时间">{{ invalidInfo.expired_at }}</FormItem>
        </div>
        <div>
          <FormItem label="总次数">{{ invalidInfo.num }}</FormItem>
          <FormItem label="剩余次数">{{ invalidInfo.surplus_num }}</FormItem>

          <FormItem label="作废次数">
            <InputNumber
              v-model="formData.num"
              :precision="0"
              :max="Number(invalidInfo.surplus_num)"
              :min="0"
            ></InputNumber>
          </FormItem>
          <FormItem label="作废后剩余次数">{{ leftNum }}</FormItem>
        </div>
      </div>
    </Form>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
import { $operator } from '@/libs/operation';
let init_query_from_data = {
  num: null,
};

export default {
  name: 'exchange-modal',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    exchangeId: {
      type: String,
      default: '',
    },
    invalidInfo: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      formData: {
        ...init_query_from_data,
      },
    };
  },

  created() {},
  computed: {
    leftNum() {
      if (!!Number(this.invalidInfo.surplus_num) && !!Number(this.formData.num)) {
        return $operator.subtract(this.invalidInfo.surplus_num, this.formData.num);
      } else {
        return '-';
      }
    },
  },

  methods: {
    visibleChange(val) {
      if (!val) {
        this.cancel();
        this.clearQuery();
      }
    },

    cancel() {
      this.$emit('input', false);
    },

    confirm() {
      if (!this.formData.num) {
        this.$Message.error('请输入作废次数');
        return;
      }
      this.$Modal.confirm({
        title: '确认作废？',
        content: '是否确认作废当前通兑券',
        onOk: () => {
          this.invalidExchangeCard();
        },
        onCancel: () => {},
      });
    },
    // 重置数据
    clearQuery() {
      this.formData = { ...init_query_from_data };
    },
    invalidExchangeCard() {
      let params = { ...this.formData, id: this.exchangeId };
      this.$api.invalidExchangeCard(params).then(res => {
        this.cancel();
        this.$emit('refresh');
        this.$Message.success('作废成功');
      });
    },
  },

  watch: {},
};
</script>

<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 0;
}
</style>
