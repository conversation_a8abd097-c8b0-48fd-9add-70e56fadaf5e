<template>
  <Modal :value="value" title="通兑券延期" :mask-closable="false" :width="600" @on-visible-change="visibleChange">
    <Form :model="formData" label-position="right" :label-width="100" label-colon>
      <div class="flex" style="font-size: 14px">
        <div style="width: 280px">
          <FormItem label="所属用户">{{ extensionInfo.user_info?.real_name || '-' }}</FormItem>
          <FormItem label="用户手机号">{{ extensionInfo.user_info?.mobile || '-' }}</FormItem>
          <FormItem label="通兑券名称">{{ extensionInfo.name }}</FormItem>
          <FormItem label="总次数">{{ extensionInfo.num }}</FormItem>
          <FormItem label="剩余次数">{{ extensionInfo.surplus_num }}</FormItem>
          <FormItem label="关联订单">{{ extensionInfo.order_info?.out_trade_no }}</FormItem>
        </div>
        <div>
          <FormItem label="原过期时间">
            <span style="color: red">{{ extensionInfo.expired_at }}</span>
          </FormItem>
          <FormItem label="延期至">
            <DatePicker
              style="width: 100%"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择延期时间"
              :value="formData.expiration_time"
              :options="datepickerOptions"
              @on-change="val => (formData.expiration_time = val)"
            ></DatePicker>
          </FormItem>
        </div>
      </div>
    </Form>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="confirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
let init_query_from_data = {
  expiration_time: '',
};

export default {
  name: 'exchange-modal',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    exchangeId: {
      type: String,
      default: '',
    },
    extensionInfo: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      formData: {
        ...init_query_from_data,
      },
      datepickerOptions: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000;
        },
      },
    };
  },

  created() {},

  methods: {
    visibleChange(val) {
      if (!val) {
        this.cancel();
        this.clearQuery();
      }
    },

    cancel() {
      this.$emit('input', false);
    },

    confirm() {
      if (!this.formData.expiration_time) {
        this.$Message.error('请选择延期时间');
        return;
      }
      let flag = new Date() > new Date(this.formData.expiration_time);
      if (flag) {
        this.$Message.error('延期时间不能小于当前时间');
        return;
      }
      this.$Modal.confirm({
        title: '确认延期？',
        content: '是否确认延期当前通兑券',
        onOk: () => {
          this.extensionExchangeCard();
        },
        onCancel: () => {},
      });
    },
    // 重置数据
    clearQuery() {
      this.formData = { ...init_query_from_data };
    },
    extensionExchangeCard() {
      let params = { ...this.formData, id: this.exchangeId };
      this.$api.extensionExchangeCard(params).then(res => {
        this.cancel();
        this.$emit('refresh');
        this.$Message.success('延期成功');
      });
    },
  },

  watch: {},
};
</script>

<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 0;
}
</style>
