<template>
  <div>
    <old-visit-list v-if="!isRstClinic()" />
    <new-visit-list v-if="isRstClinic()" />
  </div>
</template>

<script>
import OldVisitList from './old-list.vue';
import NewVisitList from './new-list.vue';
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'VisitList',
  components: {
    OldVisitList,
    NewVisitList,
  },
  data() {
    return {
      isRstClinic,
    };
  },
};
</script>

<style scoped lang="less"></style>
