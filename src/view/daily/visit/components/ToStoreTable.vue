<!--  -->
<template>
  <div class="order-wrapper">
    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight-623>300?$store.state.app.clientHeight-623:300">
      <template slot-scope="{row}" slot="arrival_time">
        {{row.arrival_time | data_format('YYYY-MM-DD')}}
      </template>
    </Table>

    <KPage :total="Number(total)"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="display: flex;justify-content: center;margin-top: 16px;"
    />
  </div>
</template>

<script>
  export default {
    name: "order",
    components: {

    },
    mixins: [],
    props: {
    },
    data () {
      return {
        tableLoading: false, // 表格获取的数据的loading
        tableCols: [
          {title: '到店时间', slot: 'arrival_time',align: 'center'},
          {title: '到店诉求', key: 'ask_type_text',align: 'center'},
          {title: '消费类型',render: ( h, { row } ) => h( 'span', {}, row.consume_type_text||'-' ),align: 'center'},
        ], // 消费订单明细的columns
        total: 0, // 总条数
        list: [], // 到店数据
        queryFormData: {
          page: 1,
          pageSize: 20,
        },
      }
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {
      // 获取消费订单明细
      this.getList()
    },
    methods: {
      onPageChange (page, pageSize) {
        this.queryFormData.page = page
        this.queryFormData.pageSize = pageSize
        this.getList()
      },
      // 获取消费订单明细
      getList () {
        this.tableLoading = true
        let params = {
          uid: this.$route.query.uid,
          ...this.queryFormData
        }
        this.$api.getArrivalList(params).then(data => {
          this.list = data.list
          this.total = data.total
        }).catch(error => {
          {}
        }).finally( () => {
          this.tableLoading = false
        } )
      }
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
/deep/ .ivu-table thead th {
  background-color: #f2f2f2 !important;
}
/deep/ .ivu-page {
  text-align: right !important;
}
::v-deep .ivu-table-body{
  scrollbar-width: none;//兼容火狐
  -ms-overflow-style: none;//兼容IE10
}
::v-deep .ivu-table-body::-webkit-scrollbar {
  display: none;
}
</style>
