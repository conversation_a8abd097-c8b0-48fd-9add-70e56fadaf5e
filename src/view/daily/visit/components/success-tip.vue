<template>
  <Modal
    :value="visible"
    :mask-closable="false"
    :before-close="closeModal"
    @on-cancel="closeModal"
    lock-scroll
    :title="title">
    <div class="content">
      <slot name="tip"></slot>
    </div>
    <div class="footer" slot="footer">
      <Button type="primary" @click="confirm">好的</Button>
    </div>
  </Modal>
</template>

<script>
  export default {
    name: "successTip",
    components: {

    },
    mixins: [],
    props: {
      title: {
        type: String,
        default: () => '随访计划已成功提交'
      },
      value: {
        type: Boolean,
        default: () => false
      }
    },
    data () {
      return {
      }
    },
    computed: {

    },
    watch: {
      value: {
        immediate: true,
        handler (val) {
          this.visible = val
        },
      }
    },
    created() {

    },
    mounted() {

    },
    methods: {
      // 关闭弹窗
      closeModal () {
        this.$emit('input', false)
      },
      confirm () {
        this.closeModal()
        this.$router.back()
      }
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
.width100 {
  width: 100% !important;
  max-width: 100% !important;
}
</style>