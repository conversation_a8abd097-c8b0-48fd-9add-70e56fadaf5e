<template>
  <div class="list-wrapper">
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" @click="addArrivalUser">新增用户到店</Button>
        </div>
        <Form
          inline
          :label-width="0"
          @submit.native.prevent
          @keyup.enter.native="onSearch"
          class="flex flex-item-between"
        >
          <Row :gutter="24">
            <Col span="24">
              <FormItem label="">
                <Input type="text" v-model="queryFormData.user_keyword" placeholder="姓名/注册名/手机号" />
              </FormItem>
              <FormItem label="">
                <DatePicker
                  type="daterange"
                  placeholder="日期范围"
                  :value="timeRange"
                  clearable
                  @on-change="times => handleTimeChange(times)"
                ></DatePicker>
              </FormItem>
              <FormItem label="">
                <Select v-model="queryFormData.user_type" placeholder="请选择到店类型">
                  <Option
                    v-for="(user_item, user_index) in userTypeDesc"
                    :key="user_index + 'user'"
                    :value="user_item.id"
                    >{{ user_item.desc }}</Option
                  >
                </Select>
              </FormItem>
              <FormItem>
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <Button type="default" class="mr10" @click="exportExcel" :loading="exportLoading">导出</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </template>
      <template slot-scope="{ row }" slot="arrival_time">
        {{ row.arrival_time | data_format('YYYY-MM-DD') }}
      </template>

      <template slot-scope="{ row }" slot="description">
        <div v-if="row.user_type === 'USER_TYPE_OLD'">
          <p class="flex"><span class="user-label">姓名：</span>{{ row.real_name || '-' }}</p>
          <p class="flex"><span class="user-label">注册名：</span>{{ row.nickname || '-' }}</p>
          <p class="flex"><span class="user-label">手机号：</span>{{ row.mobile || '-' }}</p>
        </div>
        <div v-else>
          <p class="flex"><span class="user-label">性别：</span>{{ row.sex_text }}</p>
          <p class="flex"><span class="user-label">年龄段：</span>{{ row.age_group_text || '-' }}</p>
          <p class="flex">
            <span class="user-label">外貌特征：</span><span>{{ row.exterior || '-' }}</span>
          </p>
        </div>
      </template>

      <template slot-scope="{ row }" slot="consume_type_text">
        {{ row.consume_type_text || '-' }}
      </template>

      <template slot-scope="{ row }" slot="remark">
        {{ row.remark || '-' }}
      </template>

      <template slot-scope="{ row }" slot="operator">
        {{ row.operator }} {{ row.role_name ? '·' + row.role_name : '' }}
      </template>

      <template slot-scope="{ row }" slot="action">
        <a @click="itemEdit(row)">编辑</a>
        <Dvd />
        <Dvd />
        <Poptip confirm title="确认删除?" @on-ok="deleteItem(row)">
          <a>删除</a>
        </Poptip>
      </template>
    </standard-table>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools

import io from '@/libs/io';
import search from '../../../mixins/search';
import StandardTable from "@/components/StandardTable/index.vue"; // Http request

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  user_keyword: '',
  st: '',
  et: '',
  user_type: '',
  r: '',
};

export default {
  name: 'list',
  components: {StandardTable},
  mixins: [search],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getArrivalList',
      userTypeDesc: [],
      tableCols: [
        { title: '到店日期', slot: 'arrival_time' },
        { title: '到店类型', key: 'user_type_text' },
        { title: '用户描述', slot: 'description', minWidth: 100 },
        { title: '到店述求', key: 'ask_type_text', minWidth: 40 },
        { title: '消费类型', slot: 'consume_type_text', minWidth: 40 },
        { title: '备注', slot: 'remark', minWidth: 100 },
        //todo 跟进人”字段有值的时候，取跟进人字段，没有值的时候，取登录人
        { title: '跟进人', slot: 'operator', minWidth: 80 },
        { title: '操作', slot: 'action', Width: 80, align: 'center' },
      ],
      tableLoading: false,

      list: [],
      total: 0,

      exportLoading: false, // 导出excel按钮的loading
    };
  },

  created() {
    this.getArrivalOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  methods: {
    // 新增用户到店
    addArrivalUser() {
      this.$router.push({
        path: '/daily/arrival/edit',
        query: {},
      });
    },

    // 编辑单挑表格数据
    itemEdit({ id } = row) {
      this.$router.push({
        path: '/daily/arrival/edit',
        query: {
          id,
        },
      });
    },
    // 删除单条表格数据
    deleteItem({ id } = row) {
      this.$api
        .delArrival({ id })
        .then(res => {
          this.submitQueryForm();
        })
        .catch(error => {
          {
          }
        });
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    // 导出excel
    exportExcel() {
      this.exportLoading = true;
      this.$api
        .getarrivalurl(this.queryFormData)
        .then(res => {
          this.download(res.url);
        })
        .finally(() => (this.exportLoading = false));
    },
    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },

    // api-获取options
    getArrivalOptions() {
      this.$api.getArrivalOptions().then(res => {
        this.userTypeDesc = S.descToArrHandle(res.userTypeDesc);
      });
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
};
</script>

<style lang="less">
.order_attrs_table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
}
.order_attrs_table td {
  padding: 5px 5px;
  border-color: transparent;
}
.margin-bottom10 {
  margin-bottom: 10px;
}
.user-label {
  display: inline-block;
  width: 70px;
  min-width: 70px;
  text-align: left;
  text-align: right;
  color: #aaa;
}
p {
  margin: 0;
}
</style>
