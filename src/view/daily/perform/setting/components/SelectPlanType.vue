<template>
  <Modal
    :value="visible"
    title="选择活动类型"
    width="860px"
    @on-ok="confirm"
    @on-cancel="cancel"
    @on-visible-change="visibleChange"
    :mask-closable="false"
  >
    <div>
      <div class="flex flex-item-around">
        <div
          class="activity-type"
          v-for="(item, index) in activityType"
          :key="index"
          @click="changeType(item.type)"
          :class="{ active: currentType === item.type }"
        >
          <div class="selected"></div>
          <div class="activity-title" :style="{ color: currentType === item.type ? '#155BD4' : '#333' }">
            {{ item.title }}
          </div>
          <div class="activity-content" :style="{ color: currentType === item.type ? '#155BD4' : '#999999' }">
            {{ item.content }}
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script>
import S from 'libs/util';
const initFormData = {
  code: '', //销售单编号；如果不填写，创建时系统会自动生成
};

export default {
  name: 'modal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      formData: { ...initFormData },
      activityType: [
        { title: '服务消耗提成', content: '消耗业绩=现金消耗业绩+消耗消耗业绩', type: '1' },
        { title: '商品销售提成', content: '销售业绩=现金销售业绩+消耗销售业绩', type: '2' },
        { title: '商品销售+服务消耗', content: '销售业绩=现金销售业绩+消耗销售业绩', type: '3' },
      ],
      currentType: '1',
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    cancel() {
      this.$emit('update:visible', false);
    },
    visibleChange(val) {
      if (!val) {
        this.currentType = '1'; // 重置
      }
    },
    changeType(val) {
      this.currentType = val;
    },
    confirm() {
      this.$router.push({
        path: '/organization/performance/setting/edit',
        query: { type: this.currentType },
      });
      this.$emit('update:visible', false);
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
.activity-type {
  position: relative;
  flex: 1;
  margin: 44px 10px;
  height: 180px;
  padding: 2px 16px;
  cursor: pointer;
  box-sizing: border-box;
  border: 1px solid #eee;
  border-radius: 10px;
  .activity-title {
    font-size: 18px;
    text-align: center;
    margin: 40px 0 22px;
    color: #333333;
  }
  .activity-content {
    color: #999999;
  }
  //&:hover{
  //  border: 2px solid #155BD4;
  //}
}
.active {
  border: 1px solid #155bd4;
  color: #155bd4;
  box-shadow: 0px 0px 0px 1px #155bd4;
  ::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 55px;
    height: 55px;
    background: url('../../../../../assets/image/select_logo.png');
    background-size: 100%;
  }
}
</style>
