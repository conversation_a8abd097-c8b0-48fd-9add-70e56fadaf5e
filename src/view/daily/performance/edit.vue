<!--  -->
<template>
  <div class="create-wrapper">
    <div class="content-box">
      <Form :label-width="120" label-colon>
        <div class="block-header"><span>用户信息</span></div>
        <Row>
          <Col :span="6">
            <FormItem label="姓名/手机号">
              <el-autocomplete
                style="width: 100%"
                ref="custom"
                :popper-append-to-body="false"
                popper-class="popper-class"
                :disabled="hasChosen"
                @blur="checkUid"
                class="inline-input"
                v-model="userInfo"
                :fetch-suggestions="queryChange"
                size="small"
                placeholder="请输入用户姓名/手机号"
                @select="optionChange"
              >
                <template slot-scope="{ item }">
                  <div v-if="!item.empty" style="white-space: pre-wrap">
                    <span>{{ item.mobile }}</span>
                    <span style="margin-left: 10px" class="h-ellipsis">{{ item.patient_name }}</span>
                    <span v-if="item.show_staging_mobile === '1'" style="font-size: 12px; color: #999; margin-left: 8px"
                      >暂存 {{ item.staging_mobile }}</span
                    >
                  </div>
                  <div class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>
                    <p class="flex flex-c">
                      <span>{{ userInfo }}</span>
                      <span class="tip">尚无该用户</span>
                    </p>
                    <a>创建用户</a>
                  </div>
                </template>
              </el-autocomplete>
            </FormItem>
          </Col>
          <Col :span="6">
            <FormItem label="性别">
              <Select v-model="formData.sex" placeholder="请选择用户性别" :disabled="hasChosen">
                <Option :value="sex_item.value" v-for="(sex_item, sex_index) in sexList" :key="sex_index + 'sex'">
                  {{ sex_item.label }}
                </Option>
              </Select>
            </FormItem>
          </Col>
          <Col :span="6">
            <FormItem label="用户类型">
              <Select v-model="formData.sex" placeholder="用户类型" disabled>
                <Option :value="sex_item.value" v-for="(sex_item, sex_index) in []" :key="sex_index + 'sex'">
                  {{ sex_item.label }}
                </Option>
              </Select>
            </FormItem>
          </Col>
          <Col :span="6">
            <FormItem label="来源">
              <Select v-model="formData.source" placeholder="请选择用户来源">
                <Option v-for="(item, status) in sourceList" :key="item.id" :value="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col :span="6">
            <FormItem label="等级">
              <Select v-model="formData.offline_level" placeholder="请选择用户等级">
                <Option v-for="(item, index) in levelList" :key="item" :value="item">{{ item }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <div class="block-header"><span>用户消费明细</span></div>
        <Row>
          <Col :span="24">
            <FormItem label="" :label-width="16">
              <Table stripe :columns="costColumn" :data="purchase_list">
                <template slot-scope="{ row, index }" slot="date">
                  <DatePicker
                    style="height: 30px; line-height: 30px"
                    :options="disabledTime"
                    placeholder="消费日期"
                    @on-change="date => changeDate(date, index, 'purchase_list')"
                    :value="purchase_list[index].date"
                  ></DatePicker>
                </template>
                <template slot-scope="{ row, index }" slot="cost_item">
                  <Select
                    v-model="purchase_list[index].item_type"
                    placeholder="消费项目"
                    @focus.native.capture="focusSelect(index + 'cost_item')"
                    :ref="index + 'cost_item'"
                  >
                    <Option v-for="item in pur_item_type_desc" :key="item.kw" :value="item.id">{{ item.desc }} </Option>
                  </Select>
                </template>
                <template slot-scope="{ row, index }" slot="cost_amount">
                  <InputNumber
                    v-model="purchase_list[index].money"
                    style="width: 100%"
                    :min="0"
                    placeholder="消费金额"
                    :active-change="false"
                    :precision="2"
                  ></InputNumber>
                </template>
                <template slot-scope="{ row, index }" slot="pay_type">
                  <Select
                    v-model="purchase_list[index].pay_type"
                    placeholder="支付方式"
                    @focus.native.capture="focusSelect(index + 'pay_type')"
                    :ref="index + 'pay_type'"
                  >
                    <Option v-for="item in pay_type_desc" :key="item.kw" :value="item.id">{{ item.desc }}</Option>
                  </Select>
                </template>
                <template slot-scope="{ row, index }" slot="associated_person">
                  <div style="line-height: 18px">
                    <div v-if="row.relate_staff_names.length">
                      <span v-for="(item, index) in row.relate_staff_names" :key="item + index">{{ item }}<br /></span>
                    </div>
                    <a @click="addAssociatedPerson(row, index, 'purchase_list')" v-else> 添加关联人</a>
                  </div>
                </template>
                <template slot-scope="{ row, index }" slot="action">
                  <a @click="addAssociatedPerson(row, index, 'purchase_list')">管理关联人员</a>
                  <a style="color: red; margin-left: 16px" @click="confirmDelete(index, 'purchase_list')">删除</a>
                </template>
              </Table>
              <div class="add-btn">
                <Button type="primary" @click="addCostItem">新增一笔消费明细</Button>
              </div>
            </FormItem>
          </Col>
        </Row>
        <div class="block-header"><span>用户消耗明细</span></div>
        <Row>
          <Col :span="24">
            <FormItem label="" :label-width="16">
              <Table stripe :columns="consumeColumn" :data="consumeList">
                <template slot-scope="{ row, index }" slot="date">
                  <DatePicker
                    :options="disabledTime"
                    placeholder="消费日期"
                    @on-change="date => changeDate(date, index, 'consumeList')"
                    :value="consumeList[index].date"
                  ></DatePicker>
                </template>
                <template slot-scope="{ row, index }" slot="service_item">
                  <Select
                    @focus.native.capture="focusSelect(index + 'service-item')"
                    :ref="index + 'service-item'"
                    v-model="consumeList[index].item_type"
                    placeholder="消耗项目"
                  >
                    <Option v-for="(desc, status) in con_item_type_desc" :key="desc.kw" :value="desc.id"
                      >{{ desc.desc }}
                    </Option>
                  </Select>
                </template>

                <template slot-scope="{ row, index }" slot="is_gift">
                  <Select
                    @focus.native.capture="focusSelect(index + 'is_gift')"
                    :ref="index + 'is_gift'"
                    v-model="consumeList[index].is_gift"
                    placeholder="是否赠送"
                    @on-change="selectChange($event, 'is_gift', index)"
                  >
                    <Option v-for="item in isGiveDesc" :key="item.value" :value="item.value">{{ item.label }}</Option>
                  </Select>
                </template>

                <template slot-scope="{ row, index }" slot="conversion_amount">
                  <InputNumber
                    :min="0"
                    v-model="consumeList[index].money"
                    style="width: 100%"
                    :disabled="row.is_gift == 1"
                    placeholder="消耗折算金额"
                    :active-change="false"
                    :precision="2"
                  ></InputNumber>
                </template>
                <template slot-scope="{ row, index }" slot="technician">
                  <Select
                    v-model="consumeList[index].artificer_id"
                    placeholder="技师"
                    @focus.native.capture="focusSelect(index + 'technician')"
                    :ref="index + 'technician'"
                  >
                    <Option v-for="item in artificers" :key="item.id" :value="item.id">{{ item.name }}</Option>
                  </Select>
                </template>
                <template slot-scope="{ row, index }" slot="commission_amount">
                  <InputNumber
                    :min="0"
                    placeholder="技师分佣金额"
                    style="width: 100%"
                    :active-change="false"
                    v-model="consumeList[index].commission"
                    :precision="2"
                  ></InputNumber>
                </template>
                <template slot-scope="{ row, index }" slot="associated_person">
                  <div style="line-height: 18px">
                    <div v-if="row.relate_staff_names.length">
                      <span v-for="(item, index) in row.relate_staff_names" :key="item + index">{{ item }}<br /></span>
                    </div>
                    <a @click="addAssociatedPerson(row, index, 'consumeList')" v-else> 添加关联人</a>
                  </div>
                </template>
                <template slot-scope="{ row, index }" slot="action">
                  <a @click="addAssociatedPerson(row, index, 'consumeList')">管理关联人员</a>
                  <a style="color: red; margin-left: 16px" @click="confirmDelete(index, 'consumeList')">删除</a>
                </template>
              </Table>
              <div class="add-btn">
                <Button type="primary" @click="addConsumeItem">新增一笔消耗明细</Button>
              </div>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>
    <div class="fixed-bottom-wrapper">
      <back-button>取消</back-button>
      <Button class="btnStyle" type="primary" :disabled="canSubmit" style="margin-left: 16px" @click="submit"
        >提交
      </Button>
    </div>
    <!-- 关联弹窗 -->
    <AssociatedPersonDia
      :clearCheckedStaffs="clearCheckedStaffs"
      :visible.sync="associatedPersonVisible"
      :add-person="addPerson"
      :staff-list="staff_list"
      :checked-staffs="checkedStaffs"
    ></AssociatedPersonDia>
    <!-- 创建用户 -->
    <!--    <CreateConsumerDia :showCreateInfo="optionChange" :source-list="sourceList" :visible.sync="consumerVisibleDia"-->
    <!--                       :level-list="levelList" :name='userInfo'></CreateConsumerDia>-->
    <!-- 创建用户 -->
    <create-user-modal
      :showCreateInfo="optionChange"
      :source-list="sourceList"
      :visible.sync="consumerVisibleDia"
      :level-list="levelList"
      :name="userInfo"
    ></create-user-modal>
  </div>
</template>

<script>
import AssociatedPersonDia from './components/AssociatedPersonDia';
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';

import S from '@/libs/util';
import moment from 'moment';

const init_form_data = {
  uid: '',
  source: '',
  sex: '',
};
export default {
  name: 'create',
  components: { AssociatedPersonDia, CreateUserModal },
  mixins: [],
  props: {},
  data() {
    return {
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      hasChosen: false,
      consumerVisibleDia: false, // 创建用户弹窗的标识
      statusDesc: [],
      costColumn: [
        { title: '日期', slot: 'date' },
        { title: '消费项目', slot: 'cost_item' },
        { title: '消费金额', slot: 'cost_amount' },
        { title: '支付方式', slot: 'pay_type' },
        { title: '消费关联人', slot: 'associated_person', align: 'center' },
        { title: '操作', slot: 'action', width: 140 },
      ],
      consumeColumn: [
        { title: '日期', slot: 'date' },
        { title: '消耗项目', slot: 'service_item' },
        { title: '是否赠送', slot: 'is_gift' },
        { title: '消耗折算金额', slot: 'conversion_amount' },
        { title: '技师', slot: 'technician' },
        { title: '技师分佣', slot: 'commission_amount' },
        { title: '其他关联人', slot: 'associated_person', align: 'center' },
        { title: '操作', slot: 'action', width: 140 },
      ],
      consumeList: [],
      purchase_list: [],
      associatedPersonVisible: false,
      editTable: 'cost', //当前要选择关联人是哪个表格
      formData: {
        ...init_form_data,
      },
      isGiveDesc: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
      sexList: [
        { label: '男', value: '1' },
        { label: '女', value: '2' },
      ],
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],
      userInfo: '',
      sourceList: [],
      pur_item_type_desc: [],
      pay_type_desc: [],
      con_item_type_desc: [],
      staff_list: [], //员工列表
      artificers: [], //技师列表
      checkedStaffs: [], //已选中的关联人员
      userList: [],
      searchTimes: 0,
    };
  },
  computed: {
    canSubmit() {
      return !this.purchase_list.length && !this.consumeList.length;
    },
  },
  watch: {},
  created() {
    // 获取选项信息
    this.getPerformOptions();
    this.getCreateInfo();
  },
  mounted() {},
  methods: {
    focusSelect(ref) {
      console.log('-> %cthis.$refs[ref] %o', 'font-size: 15px', this.$refs[ref]);
      setTimeout(() => {
        if (!this.$refs[ref].visible) {
          this.$refs[ref].toggleMenu();
        }
      }, 200);
    },
    checkUid() {
      console.log(this.formData);
      setTimeout(() => {
        if (!this.formData.uid && !this.consumerVisibleDia) {
          this.userInfo = '';
        }
      }, 200);
    },
    changeDate(date, index, tableType) {
      console.log('-> %cdate,index,tableType %o', 'font-size: 15px', date, index, tableType);
      this[tableType][index].date = date;
    },
    clearCheckedStaffs() {
      this.checkedStaffs = [];
    },
    getCreateInfo() {
      this.$api.getCreateinfo().then(res => {
        console.log('-> %cres %o', 'font-size: 15px', res);
        res.staff_list.map(item => {
          item.label = `${item.name} (${item.role})`;
        });
        this.staff_list = res.staff_list;
        this.artificers = res.artificers;
      });
    },
    getPerformOptions() {
      this.$api.getPerformanceOptions().then(res => {
        console.log('-> %cres %o', 'font-size: 15px', res);
        this.sourceList = S.descToArrHandle(res.user_source_desc);
        this.pur_item_type_desc = S.descToArrHandle(res.pur_item_type_desc);
        this.pay_type_desc = S.descToArrHandle(res.pay_type_desc);
        this.con_item_type_desc = S.descToArrHandle(res.con_item_type_desc);
        this.addConsumeItem();
        this.addCostItem();
      });
    },
    // 点击创建用户，显示弹窗
    creatConsumer() {
      this.consumerVisibleDia = true;
      this.$refs.custom.close();
    },
    addAssociatedPerson(row, index, type) {
      this.editIndex = index;
      this.editTable = type;
      this.checkedStaffs = this[type][index].relate_staff_ids;
      this.associatedPersonVisible = true;
    },
    //新增一项消费明细
    addCostItem() {
      this.purchase_list.push({
        date: moment(Date.now()).format('YYYY-MM-DD'),
        item_type: this.pur_item_type_desc[0].id,
        money: null,
        relate_staff_ids: [],
        relate_staff_names: '',
        pay_type: 'CASH',
      });
    },
    //新增一项消耗明细
    addConsumeItem() {
      this.consumeList.push({
        date: moment(Date.now()).format('YYYY-MM-DD'),
        item_type: this.con_item_type_desc[0].id,
        money: null,
        relate_staff_ids: [],
        is_gift: '0',
        relate_staff_names: '',
        pay_type: 'CASH',
        artificer_id: '',
        commission: null,
      });
    },
    //删除某一行
    confirmDelete(index, type) {
      this[type].splice(index, 1);
    },
    // 取消
    cancel() {
      this.$router.back();
    },
    // 下拉选择事件
    selectChange(e, kw, index) {
      this.consumeList[index][kw] = e;
      // 赠送情况下,服务折算金额为0,且不可设置
      if (kw === 'is_gift' && e == 1) {
        this.consumeList[index].money = 0;
      }
    },
    addPerson(person) {
      const checkedList = this.staff_list.filter(item => person.includes(item.id));
      const checkedListName = checkedList.map(item => item.label);
      const checkedListids = checkedList.map(item => item.id);
      this[this.editTable][this.editIndex].relate_staff_names = checkedListName;
      console.log('-> %ccheckedListName %o', 'font-size: 15px', checkedListName);
      this[this.editTable][this.editIndex].relate_staff_ids = checkedListids;
    },
    submit() {
      if (this.checkFormData()) {
        this.$api.createPerformance(this.handleFormData()).then(
          res => {
            console.log('-> %cres %o', 'font-size: 15px', res);
            this.$Message.success('新增门店业绩统计成功');
            this.$router.replace('/daily/perform/list');
          },
          err => this.$Message.error(err.errmsg)
        );
      }
    },
    //处理要提交的数据
    handleFormData() {
      const { uid, source, offline_level } = this.formData;
      const params = {
        uid,
        source,
        offline_level,
        purchase_list: this.purchase_list,
        consume_list: this.consumeList,
      };
      return params;
    },
    //校验数据是否合格
    checkFormData() {
      if (!this.formData.uid) {
        this.$Message.error('请选择用户');
        return false;
      }
      for (const item of this.purchase_list) {
        if (!item.date) {
          this.$Message.error('【用户消费明细】 请选择日期');
          return false;
        }
        if (!item.item_type) {
          this.$Message.error('【用户消费明细】 请选择消费项目');
          return false;
        }
        if (!item.money) {
          this.$Message.error('【消费明细】 消费金额不能为0');
          return false;
        }
      }
      for (const item of this.consumeList) {
        if (!item.date) {
          this.$Message.error('【用户服务消耗】 请选择日期');
          return false;
        }
        if (!item.item_type) {
          this.$Message.error('【用户服务消耗】 请选择消耗项目');
          return false;
        }
        if (item.is_gift == '0' && !item.money) {
          this.$Message.error('【用户服务消耗】 非赠送情况下，服务折算金额不能为0');
          return false;
        }
        // if(!item.artificer_id){
        //   this.$Message.error('【用户服务消耗】 请选择服务技师')
        //   return false
        // }
      }
      return true;
    },
    // 选中的用户手机号option变化触发
    optionChange(item) {
      console.log('-> %citem %o', 'font-size: 15px', item);
      this.userInfo = `${item.patient_name} ${item.mobile}`;
      this.formData.uid = item.uid;
      this.formData.source = item.source || '';
      this.formData.sex = item.sex || '';
      this.formData.offline_level = item.offline_level || '';
      this.hasChosen = true;
    },
    // 搜索词发生变化时触发
    queryChange(keyword, cb) {
      if (this.searchTimes && !keyword) {
        cb(this.userList);
      } else {
        this.getUserList(keyword, cb);
      }
    },

    // api-获取用户列表-用户手机号带出用户信息
    getUserList(search = '', cb) {
      let params = {
        page: 1,
        pageSize: 20,
        search,
      };
      this.searchTimes++;
      if (search) {
        this.searchTimes = 0;
      }
      this.$api.getUserList(params).then(
        res => {
          // 获取用户数据
          this.handleUserList(res.users, cb);
        },
        rej => this.$Message.error(rej.errmsg)
      );
    },
    // 处理用户数据
    handleUserList(data, cb) {
      this.userList = data;
      if (!data.length) {
        cb([{ empty: true }]);
        return [];
      }
      cb(data);
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
.content-box {
  padding-bottom: 40px;
}

.btn {
  margin: 20px auto;
  width: 300px;
  display: flex;
  justify-content: space-around;
}

.add-btn {
  margin-top: 14px;
}

p {
  margin: 0px;
}

.form-radio {
  margin-bottom: 14px;
}

.tip {
  font-size: 12px;
  color: #ccc;
}

::v-deep .el-popper {
  min-width: 300px !important;
}

::v-deep .el-autocomplete {
  .is-disabled {
    .el-input__inner {
      background: #f3f3f3;
      color: #ccc;
    }
  }

  .el-input__inner {
    border-color: #bbb;
    font-size: 12px;
    border-radius: 2px;
  }
}

.block-header {
  background: transparent;
  margin: 6px 0 !important;
}

::v-deep .ivu-input {
  border-color: #bbb !important;
}

::v-deep .ivu-input-number {
  border-color: #bbb !important;
}
::v-deep .popper-class {
  width: 400px !important;
}
</style>
