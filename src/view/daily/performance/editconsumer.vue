<!--  -->
<template>
  <div class="edit-wrapper">
    <div style="width: 100%">
      <Form :label-width="170" :rules="formRules" label-colon ref="formValidate" :model="formData">
        <div class="block-header"><span>用户信息</span></div>
        <Row>
          <Col :span="12">
            <FormItem label="姓名/手机号">
              <el-autocomplete
                style="width: 100%"
                ref="custom"
                :popper-append-to-body="false"
                popper-class="popper-class"
                :disabled="true"
                class="inline-input"
                v-model="userInfo"
                :fetch-suggestions="queryChange"
                size="small"
                placeholder="请输入用户姓名/手机号"
                @select="optionChange"
              >
                <template slot-scope="{ item }">
                  <div>
                    <div class="flex flex-item-v-center">
                      -->
                      <p>{{ item.mobile }}</p>
                      <p style="margin-left: 10px" class="h-ellipsis">{{ item.patient_name }}</p>
                    </div>
                  </div>
                  <!--                  <div v-if="!item.empty">-->
                  <!--                    <div class="flex flex-item-v-center">-->
                  <!--                      <p>{{ item.mobile }}</p>-->
                  <!--                      <p style="margin-left:10px;" class="h-ellipsis">{{ item.patient_name }}</p>-->
                  <!--                    </div>-->
                  <!--                  </div>-->
                  <!--                  <div class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>-->
                  <!--                    <p class="flex flex-c">-->
                  <!--                      <span>{{ userInfo }}</span>-->
                  <!--                      <span class="tip">尚无该用户</span>-->
                  <!--                    </p>-->
                  <!--                    <a>创建用户</a>-->
                  <!--                  </div>-->
                </template>
              </el-autocomplete>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem label="性别">
              <Select v-model="select_sex" placeholder="请选择用户性别" disabled>
                <Option :value="sex_item.value" v-for="(sex_item, sex_index) in sexList" :key="sex_index + 'sex'">
                  {{ sex_item.label }}
                </Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem label="用户类型">
              <Select placeholder="请选择用户类型" disabled>
                <Option :value="sex_item.value" v-for="(sex_item, sex_index) in []" :key="sex_index + 'sex'">
                  {{ sex_item.label }}
                </Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem label="用户级别">
              <Select v-model="formData.offline_level" placeholder="请选择用户等级">
                <Option v-for="(item, index) in levelList" :key="item" :value="item">{{ item }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem label="用户来源">
              <Select v-model="formData.source" placeholder="请选择用户来源">
                <Option v-for="(item, status) in sourceList" :key="item.id" :value="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <div class="block-header"><span>消费明细</span></div>
        <Row>
          <Col :span="12">
            <FormItem label="消费日期" prop="date">
              <DatePicker
                style="height: 30px; line-height: 30px; width: 100%"
                :options="disabledTime"
                placeholder="消费日期"
                @on-change="changeDate"
                :value="formData.date"
              ></DatePicker>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem label="消费项目" prop="item_type">
              <Select v-model="formData.item_type" placeholder="消费项目">
                <Option v-for="item in pur_item_type_desc" :key="item.kw" :value="item.id">{{ item.desc }} </Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem label="消费金额" prop="money">
              <InputNumber
                v-model="formData.money"
                :control="false"
                :active-change="false"
                placeholder="请输入消费金额"
                style="width: 100%"
                :min="0"
                :precision="2"
              >
              </InputNumber>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem label="付款类型" prop="pay_type">
              <Select v-model="formData.pay_type" placeholder="付款类型">
                <Option v-for="item in pay_type_desc" :key="item.kw" :value="item.id">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
        <Row>
          <Col :span="12">
            <FormItem label="消费相关人">
              <Select multiple :value="formData.relate_staff_ids" @on-change="changeRelateStaff">
                <Option v-for="item in staff_list" :value="item.id" :key="item.id"
                  >{{ item.name }} - {{ item.role }}
                </Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
      </Form>
    </div>
    <div class="fixed-bottom-wrapper">
      <Button class="btnStyle" @click="cancel">取消</Button>
      <Button class="btnStyle" type="primary" style="margin-left: 16px" @click="submitForm">提交</Button>
    </div>
    <!-- 创建用户 -->
    <!--    <CreateConsumerDia :showCreateInfo="optionChange" :source-list="sourceList" :visible.sync="consumerVisibleDia"-->
    <!--                       :level-list="levelList" :name='userInfo'></CreateConsumerDia>-->
  </div>
</template>

<script>
import S from '@/libs/util';
// import CreateConsumerDia from './components/CreateConsumerDia'
// import cloneDeep from 'lodash.clonedeep'

export default {
  name: 'editConsumer',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {
      formRules: {
        uid: [{ required: true, message: '请选择用户', trigger: 'change' }],
        date: [{ required: true, message: '请选择消费日期', trigger: 'blur' }],
        money: [{ required: true, type: 'number', min: 0, message: '请输入消费金额', trigger: 'change' }],
        pay_type: [{ required: true, message: '请选择付款类型', trigger: 'blur' }],
        item_type: [{ required: true, message: '请选择消费项目', trigger: 'blur' }],
      },
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      hasChosen: false,
      userInfo: '',
      formData: {
        date: '',
        offline_level: '',
        money: 0,
        uid: '',
        pay_type: '',
        item_type: '',
        type: 'PUR', //
        cas_token: '', //版本号
        relate_staff_ids: [], //关联人
        id: this.$route.query.id,
      },
      serviceItems: [],
      consumptionTypes: [],
      sexList: [
        { label: '男', value: '1' },
        { label: '女', value: '2' },
      ],
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],
      sourceList: [],
      pur_item_type_desc: [],
      pay_type_desc: [], //支付方式
      associatedPersons: [],
      artificers: [], //技师列表
      staff_list: [], //员工列表
      userList: [],
      searchTimes: 0,
      consumerVisibleDia: false,
      select_sex: '',
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getPerformOptions();
  },
  mounted() {},
  methods: {
    changeRelateStaff(ids) {
      console.log('-> %cids %o', 'font-size: 15px', ids);
      this.formData.relate_staff_ids = ids;
    },
    // 点击创建用户，显示弹窗
    creatConsumer() {
      this.consumerVisibleDia = true;
      this.$refs.custom.close();
    },
    getPerformanceInfo(id) {
      this.$api.getPerformanceInfo({ id }).then(res => {
        console.log('-> %cres %o', 'font-size: 15px', res);
        const userInfo = res.user_info;
        this.formData.cas_token = res.cas_token;
        this.userInfo = `${userInfo.user_name} ${userInfo.mobile}`;
        this.select_sex = userInfo.sex;
        this.formData.uid = userInfo.uid;
        this.formData.source = userInfo.source;
        this.formData.offline_level = userInfo.offline_level;
        this.formData.date = res.date;
        this.formData.item_type = res.item_type;
        this.formData.type = res.type;
        this.formData.money = Number(res.money);
        this.formData.pay_type = res.purchase_info.pay_type;
        this.formData.relate_staff_ids = res.relate_staff_list.map(item => item.id);
      });
    },
    changeDate(date) {
      this.formData.date = date;
    },
    // 取消
    cancel() {
      this.$router.replace({
        path: '/daily/perform/list',
        query: {
          type: 'PUR',
          componentName: 'statistics',
        },
      });
    },
    getPerformOptions() {
      this.$api
        .getPerformanceOptions()
        .then(res => {
          console.log('-> %cres %o', 'font-size: 15px', res);
          this.sourceList = S.descToArrHandle(res.user_source_desc);
          this.pur_item_type_desc = S.descToArrHandle(res.pur_item_type_desc);
          this.pay_type_desc = S.descToArrHandle(res.pay_type_desc);
          this.con_item_type_desc = S.descToArrHandle(res.con_item_type_desc);
          this.getCreateInfo();
        })
        .then(() => {
          const id = this.$route.query.id;
          id && this.getPerformanceInfo(id);
        });
    },
    getCreateInfo() {
      this.$api.getCreateinfo().then(res => {
        console.log('-> %cres %o', 'font-size: 15px', res);
        res.staff_list.map(item => {
          item.label = `${item.name} (${item.role})`;
        });
        this.staff_list = res.staff_list;
        this.artificers = res.artificers;
      });
    },
    // 选中的用户手机号option变化触发
    optionChange(item) {
      console.log('-> %citem %o', 'font-size: 15px', item);
      this.formData.uid = item.uid;
      this.formData.sex = item.sex;
      this.userInfo = `${item.patient_name}  ${item.mobile}`;
    },
    // 搜索词发生变化时触发
    queryChange(keyword, cb) {
      this.getUserList(keyword, cb);
    },
    // api-获取用户列表-用户手机号带出用户信息
    // api-获取用户列表-用户手机号带出用户信息
    getUserList(search = '', cb) {
      let params = {
        page: 1,
        pageSize: 20,
        search,
      };
      this.searchTimes++;
      if (search) {
        this.searchTimes = 0;
      }
      this.$api.getUserList(params).then(
        res => {
          // 获取用户数据
          this.handleUserList(res.users, cb);
        },
        rej => this.$Message.error(rej.errmsg)
      );
    },
    // 处理用户数据
    handleUserList(data, cb) {
      this.userList = data;
      if (!data.length) {
        cb([{ empty: true }]);
        return [];
      }
      cb(data);
    },
    submitForm() {
      console.log(21321);
      this.$refs['formValidate'].validate(valid => {
        if (valid) {
          console.log('-> %cvalid %o', 'font-size: 15px', valid);
          console.log('-> %cthis.formData %o', 'font-size: 15px', this.formData);
          this.$api.editPerformanceInfo(this.formData).then(
            res => {
              console.log(res);
              this.$Message.success('编辑消费明细成功');
              this.cancel();
            },
            err => this.$Message.error(err.errmsg)
          );
        } else {
          this.$Message.error('请完善表单信息');
        }
      });
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
::v-deep .el-autocomplete {
  .is-disabled {
    .el-input__inner {
      background: #f3f3f3;
      color: #ccc;
    }
  }

  .el-input__inner {
    border-color: #bbb;
    font-size: 12px;
    border-radius: 2px;
  }
}
</style>
