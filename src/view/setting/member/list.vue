<template>
  <div>
    <Tabs type="card" v-model="activeComponentName" @on-click="tabClick">
      <TabPane
        :label="item.tab_name"
        :name="item.componentName"
        v-for="(item, index) in computeTabList"
        :key="index"
      ></TabPane>
    </Tabs>

    <component :is="activeComponentName" :ref="activeComponentName" :extra-height="48"></component>
  </div>
</template>

<script>
import { isRstClinic, getPhysioName } from '../../../libs/runtime';
export default {
  name: 'list',
  components: {
    staff: () => import('./staff/list.vue'),
    physical: () => import('./physical/list.vue'),
    management: () => import('./management/list.vue'),
    classes: () => import('./classes/list.vue'),
  },
  mixins: [],
  data() {
    return {
      tab_list: [
        { tab_name: '员工管理', componentName: 'staff' },
        { tab_name: `${getPhysioName()}管理`, componentName: 'physical' },
        { tab_name: '排班管理', componentName: 'management' },
        { tab_name: '班次管理', componentName: 'classes' },
      ],
      activeComponentName: 'staff',
    };
  },
  computed: {
    computeTabList() {
      if (isRstClinic()) {
        return this.tab_list.filter(item => item.componentName !== 'physical');
      }
      return this.tab_list;
    },
  },
  watch: {},
  created() {
    console.log('list.vue created');

    this.init();
  },
  mounted() {},
  methods: {
    init() {
      if (this.$route.query.componentsName) {
        this.activeComponentName = this.$route.query.componentsName;
      } else {
        this.$route.query.componentsName = this.tab_list[0].componentName;
      }
    },
    tabClick(val) {
      if (this.$route.query.componentsName !== val) {
        this.activeComponentName = val;
        this.$route.query.componentsName = val;
      } else {
        this.$refs[this.activeComponentName]?.getsList();
        console.log('=>(list.vue:61) haha');
      }
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    if (to.query.componentsName) {
      this.activeComponentName = to.query.componentsName;
    }

    next();
  },
};
</script>

<style lang="less" scoped></style>
