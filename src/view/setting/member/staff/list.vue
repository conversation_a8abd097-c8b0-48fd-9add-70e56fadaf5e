<template>
  <div class="global-list-box">
    <standard-table
      class="global-table-style"
      :row-class-name="rowClassName"
      :loading="tableLoading"
      :columns="is_rst ? rstTableCols : tableCols"
      :data="list"
      :extra-height="48"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template slot="header">
        <!-- 搜索区域 -->
        <div
          class="global-list-search"
          ref="searchRefs"
          :style="{ height: expandStatus === 'expand' ? 'auto' : '52px' }"
        >
          <!-- 搜索条件区域 -->
          <div class="global-list-search-condition">
            <Form id="searchForm" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
              <FormItem label="">
                <Input type="text" v-model="queryFormData.keywords" placeholder="用户名/手机号/账号" clearable />
              </FormItem>
              <FormItem label="">
                <Select v-model="queryFormData.role_id" placeholder="请选择后台角色" clearable>
                  <Option value="">全部</Option>
                  <Option v-for="(item, index) in roles" :label="item.name" :value="item.id" :key="index">
                    <div class="flex flex-item-between">
                      <span>{{ item.name }}</span>
                      <span class="text-role">{{ item.source === 'SYSTEM' ? '系统内建' : '自定义' }}</span>
                    </div>
                  </Option>
                </Select>
              </FormItem>
              <FormItem label="">
                <Select v-model="queryFormData.status" placeholder="请选择状态" clearable>
                  <Option value="">全部</Option>
                  <Option v-for="(status, statusKey) in statusDesc" :value="statusKey" :key="statusKey"
                    >{{ status.desc }}
                  </Option>
                </Select>
              </FormItem>
            </Form>
          </div>
          <!-- 搜索条件功能区域 -->
          <div class="global-list-search-operate">
            <Button class="search-btn" type="primary" @click="onSearch">筛选</Button>
            <span class="list-reset-btn" @click="onResetSearch"
              ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
            >
            <div class="expand-or-collapse" v-if="isShowExpandDom" @click="expandCollapseEvent">
              <span>{{ expandStatus === 'expand' ? '收起筛选' : '更多筛选' }}</span>
              <img v-if="expandStatus === 'collapse'" src="@/assets/image/list/expand.png" />
              <img v-else src="@/assets/image/list/collapse.png" />
            </div>
          </div>
          <a v-if="isClinicToOpc" style="margin-left: auto; line-height: 32px" @click="() => (historyVisible = true)">
            查看历史
          </a>
        </div>

        <!-- 功能区域 -->
        <div class="global-list-function" ref="functionRefs">
          <Button type="primary" @click="onOpenAddModal()">
            <Icon type="md-add" size="16" />
            添加员工
          </Button>
        </div>
      </template>

      <template slot-scope="{ row }" slot="name">
        {{ row.name }}
      </template>

      <template slot-scope="{ row }" slot="mdp_role_name">
        {{ row.mdp_role_name || '-' }}
      </template>

      <template slot-scope="{ row }" slot="mdp_level_name">
        {{ row.mdp_level_name || '-' }}
      </template>

      <template slot-scope="{ row }" slot="mobile">
        {{ row.mobile }}
      </template>
      <template slot-scope="{ row }" slot="account">
        {{ row.account }}
      </template>
      <template slot-scope="{ row }" slot="weAppRole">
        {{ row.weapp_role_name || '-' }}
      </template>
      <template slot-scope="{ row }" slot="weapp_status">
        <div v-if="row.is_can_reserve == 1">
          <a @click="updateWeappStatus(row)"> {{ row.weapp_status == 'ON' ? '停止预约' : '开启预约' }} </a>
        </div>
        <div v-else>-</div>
      </template>

      <template slot-scope="{ row, index }" slot="sign">
        <div v-if="row.audit_status === '1'">
          <a v-show="!row.sign" @click="setSignaturePanel(row, index)">未设置</a>
          <div v-show="row.sign" @click="setSignaturePanel(row, index)" style="cursor: pointer">
            <img :src="row.sign" style="width: 76px; height: 30px" />
          </div>
        </div>
        <div v-else>-</div>
      </template>
      <template slot-scope="{ row }" slot="role">
        <div class="flex flex-wrap pt8" v-if="row.role_name.length">
          <div
            class="sys-tag"
            :class="{ 'custom-tag': item.source === 'CUSTOM' }"
            v-for="(item, index) in row.role_name"
            :key="index"
          >
            <Tooltip theme="light" trigger="hover" :content="item.source_text">
              <div>{{ item.name }}</div>
            </Tooltip>
          </div>
        </div>
        <div v-else>-</div>
      </template>
      <template slot-scope="{ row }" slot="auditStatus">
        <!-- 已认证 -->
        <div class="flex flex-item-align" v-if="row.audit_status === '1'">
          <img
            v-if="row.status === 'OFF'"
            style="width: 14px; height: 14px"
            src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0701/105048_91063.png"
          />
          <img
            v-else
            style="width: 14px; height: 14px"
            src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0626/160353_47936.png"
          />
          <p style="margin-left: 7px">{{ row.audit_status_text }}</p>
        </div>
        <!-- 审核中 -->
        <div class="flex flex-item-align" v-else-if="row.audit_status === '2'">
          <img
            v-if="row.status === 'OFF'"
            style="width: 14px; height: 14px"
            src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0701/105857_86928.png"
          />
          <img
            v-else
            style="width: 14px; height: 14px"
            src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0626/161037_99513.png"
          />
          <p style="margin-left: 7px">{{ row.audit_status_text }}</p>
        </div>

        <!-- 去认证 -->
        <div v-else>
          <div class="flex flex-item-align" style="cursor: pointer" @click="authentication(row)" v-if="row.is_doc == 1">
            <a>{{ row.audit_status_text }}</a>
            <img
              v-if="row.status === 'OFF'"
              style="width: 6px; height: 9px; margin-left: 7px"
              src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0701/110843_15473.png"
            />
            <img
              v-else
              style="width: 6px; height: 9px; margin-left: 7px"
              src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0626/161338_11101.png"
            />
          </div>
          <div v-else>{{ row.audit_status_text }}</div>
        </div>
      </template>
      <template slot-scope="{ row }" slot="enable_commission">
        <span>{{ row.enable_commission == '1' ? '参与' : '不参与' }}</span>
      </template>
      <template slot-scope="{ row }" slot="status">
        <span v-if="row.status == 'OFF'" class="text-danger">{{ statusDesc[row.status].desc }}</span>
        <span v-else>{{ statusDesc[row.status].desc }}</span>
      </template>
      <template slot-scope="{ row, index }" slot="operate">
        <OperationFolding :actions="getActions(row, index)" :row="row">
          <a @click="showChangeModel(row.id)">修改密码</a>
          <Divider type="vertical" />
          <a @click="onOpenAddModal(row.id)">编辑</a>
          <!-- :disabled="row.audit_status === '1'" -->
          <Divider type="vertical" />
          <Poptip
            v-if="!(row.source === 'SYSTEM' && row.role_id === '1')"
            confirm
            transfer
            :title="row.status == 'OFF' ? '确定启用?' : '确定停用？'"
            @on-ok="onStatus(row.id, row.status === 'OFF' ? 'ENABLED' : 'DISABLED')"
          >
            <a>{{ row.status === 'OFF' ? '启用' : '停用' }}</a>
          </Poptip>
          <Divider type="vertical" />
          <a @click="permissionPreview(row)">权限预览</a>
        </OperationFolding>
      </template>
    </standard-table>

    <!--     Modal -->
    <Modal
      v-model="addModal"
      :title="addModalTitle"
      :mask-closable="false"
      :width="460"
      :loading="addModalLoading"
      class="ks-add-modal"
      @on-visible-change="roleVisibleChange"
    >
      <KWidget label="姓名:" :labelWidth="85" required>
        <Input type="text" :disabled="isDisabled || rst_disabled" placeholder="请输入姓名" v-model="addFormData.name" />
        <!-- :disabled="(!isedit && systemDocRoid) || (addFormDataClick.audit_status === '1' && isedit)" -->
      </KWidget>
      <!-- v-if="!systemDocRoid || isedit" -->
      <KWidget label="手机号:" :labelWidth="85" required>
        <Input
          type="text"
          placeholder="请输入手机号"
          v-model="addFormData.mobile"
          @input="e => removeSpace(e, 'mobile')"
          :disabled="rst_disabled"
        />
      </KWidget>
      <KWidget label="登录账号:" :labelWidth="85" required>
        <Input
          type="text"
          placeholder="请输入登录账号"
          v-model="addFormData.account"
          autocomplete="off"
          @input="e => removeSpace(e, 'account')"
          :disabled="rst_disabled"
        />
      </KWidget>
      <KWidget label="登录密码:" :labelWidth="85" v-if="!isedit" required>
        <Input type="password" placeholder="请输入登录密码" v-model="addFormData.password" autocomplete="off" />
      </KWidget>
      <!-- <KWidget label="手机号:" :labelWidth="80" required v-if="systemDocRoid && !isedit"> -->
      <!--   <Select v-model="addFormData.physician_id" placeholder="请选择手机号" filterable @on-change="changeMobile"> -->
      <!--     <Option -->
      <!--       v-for="(item, key) in phoneList || []" -->
      <!--       :value="item.id" -->
      <!--       :key="key" -->
      <!--       :label="item.mobile" -->
      <!--       :disabled="item.is_relation === '1'" -->
      <!--     > -->
      <!--       <span>{{ item.mobile }}</span> -->
      <!--       <span class="rit-text">{{ item.name }}</span></Option -->
      <!--     > -->
      <!--   </Select> -->
      <!-- </KWidget> -->
      <KWidget label="证件类型:" :labelWidth="85" required>
        <Select
          :disabled="isDisabled || rst_disabled"
          v-model="addFormData.idcard_type_kw"
          clearable
          placeholder="请选择证件类型"
        >
          <Option v-for="(item, key) in idCardDesc" :value="item.id" :key="key">{{ item.desc }}</Option>
        </Select>
      </KWidget>
      <KWidget label="证件号:" :labelWidth="85" required>
        <Input
          :disabled="isDisabled || rst_disabled"
          type="text"
          placeholder="请输入证件号"
          v-model="addFormData.idcard"
          @on-change="replaceIdBrackets"
        />
      </KWidget>

      <KWidget label="后台角色:" :labelWidth="85" required>
        <Select
          multiple
          v-model="addFormData.role_ids"
          v-if="addModal"
          placeholder="请选择后台角色"
          @on-change="chnageRoles"
        >
          <Option
            v-for="item in computeRoles"
            :value="item.id"
            :key="item.id"
            :label="item.name"
            :disabled="roleMutex(item) || (item.id == doc_id && isDisabled) || mdp_disbaled(item)"
          >
            <div class="flex flex-item-between">
              <span>
                <span>{{ item.name }}</span>
                <span v-if="isShowMdpTip(item)" style="color: #ccc; margin: 0px 6px"
                  >(添加该角色员工请联系直营中心)</span
                >
              </span>
              <span class="text-role">{{ item.source === 'SYSTEM' ? '系统内建' : '自定义' }}</span>
            </div>
          </Option>
        </Select>
      </KWidget>

      <KWidget label="小程序权限:" :labelWidth="85">
        <Select v-model="addFormData.weapp_role" clearable placeholder="请选择对应权限">
          <Option v-for="(item, key) in weapp_roles" :value="item.id" :key="key">{{ item.desc }}</Option>
        </Select>
      </KWidget>
      <KWidget v-if="!is_rst" label="参与分佣:" :labelWidth="85" placeholder="请选择是否参与分佣">
        <Select v-model="addFormData.enable_commission">
          <Option v-for="(item, key) in commissions" :value="item.value" :key="key">{{ item.label }}</Option>
        </Select>
      </KWidget>
      <template slot="footer">
        <div>
          <Button type="default" @click="addModal = false">取消</Button>
          <Button :loading="addLoading" type="primary" @click="onAdd">确定</Button>
        </div>
      </template>
    </Modal>

    <Modal
      v-model="psdModel"
      title="修改密码"
      :mask-closable="false"
      :width="430"
      :loading="psdModalLoading"
      @on-visible-change="psdVisibleChange"
      class="ks-add-modal"
    >
      <KWidget label="姓名:" :labelWidth="70">
        <div style="line-height: 30px; font-size: 16px">{{ psdInfo.name }}</div>
      </KWidget>
      <KWidget label="新密码:" :labelWidth="70">
        <Input type="password" v-model="psdInfo.new_password" />
      </KWidget>
      <KWidget label="确认密码:" :labelWidth="70">
        <Input type="password" v-model="psdInfo.confirm_password" />
      </KWidget>
      <template slot="footer">
        <div>
          <Button type="default" @click="closepsdModal">取消</Button>
          <Button type="primary" @click="changePassWord">确定</Button>
        </div>
      </template>
    </Modal>
    <BindModal :confirmVisible.sync="bindVisible" :clickItem="clickItem" @ok="submitQueryForm()" />
    <signature-modal
      v-model="signModalVisible"
      :echo-img="current_row.sign"
      @getESignature="getESignature"
    ></signature-modal>
    <Picture
      v-show="false"
      ref="eSign"
      v-model="signature_img"
      :limit="1"
      is-query-detail
      style="pointer-events: none"
      @on-success="signSuccess($event)"
    ></Picture>

    <!-- 关联服务 -->
    <service-modal v-model="serviceVisible" :current_row="current_row"></service-modal>
    <staff-history-member
      :visible.sync="historyVisible"
      :options="{
        roles,
        statusDesc,
      }"
    />
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import config from '@/config';
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */
import cloneDeep from 'lodash.clonedeep';
import { validateIDCard } from '@/libs/validator';
import BindModal from './components/bindModal.vue';
import listCalc from '@/mixins/listCalc';
import signatureModal from './components/signatureModal';
import Picture from '@/components/upload/picture';
import {isClinicToOpc, isRstClinic} from '@/libs/runtime';
import StandardTable from '@/components/StandardTable/index.vue';
import serviceModal from './/components/serviceModal.vue';
import StaffHistoryMember from "@/view/setting/member/staff/historyMember.vue";
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  keywords: '',
  role_id: '',
  status: '',
  r: '',
};

let init_add_form_data = {
  id: '',
  name: '',
  mobile: '',
  physician_id: '',
  role_ids: '',
  account: '',
  password: '',
  enable_commission: '0',
  weapp_role: '',
  idcard: '', //身份证号
};

const ACTIONS = act => {
  S.log(act, 'act');
  return { ENABLED: '启用', DISABLED: '停用' }[act];
};

export default {
  name: 'list',
  components: {StaffHistoryMember, StandardTable, BindModal, signatureModal, Picture, serviceModal },
  mixins: [listCalc],

  data() {
    return {
      isClinicToOpc: isClinicToOpc(),
      reserve_height: 50,
      queryFormData: { ...init_query_form_data },
      tableCols: [
        { title: '姓名', slot: 'name', minWidth: 40 },
        { title: '账号', slot: 'account', minWidth: 40 },
        { title: '后台角色', slot: 'role', minWidth: 80 },
        { title: '资质认证状态', slot: 'auditStatus', minWidth: 40 },
        { title: '小程序权限', slot: 'weAppRole', minWidth: 40 },
        { title: '电子签名', slot: 'sign', minWidth: 40 },
        { title: '服务分佣', slot: 'enable_commission', minWidth: 40 },
        { title: '状态', slot: 'status', fixed: 'right', width: 60 },
        { title: '操作', slot: 'operate', fixed: 'right', minWidth: 100, align: 'left' },
      ],
      rstTableCols: [
        { title: '姓名', slot: 'name', minWidth: 40 },
        { title: 'MDP身份', slot: 'mdp_role_name', minWidth: 40 },
        { title: '职级', slot: 'mdp_level_name', minWidth: 40 },
        { title: '手机号', slot: 'mobile', minWidth: 40 },
        { title: '账号', slot: 'account', minWidth: 40 },
        { title: '后台角色', slot: 'role', minWidth: 80 },
        { title: '资质认证状态', slot: 'auditStatus', minWidth: 40 },
        { title: '小程序权限', slot: 'weAppRole', minWidth: 40 },
        { title: '电子签名', slot: 'sign', minWidth: 40 },
        { title: '小程序预约', slot: 'weapp_status', minWidth: 40 },

        // { title: '服务分佣', slot: 'enable_commission', minWidth: 40 },
        { title: '状态', slot: 'status', fixed: 'right', width: 60 },
        { title: '操作', slot: 'operate', fixed: 'right', minWidth: 100, align: 'left' },
      ],
      tableLoading: false,
      list: [],
      total: 0,
      statusDesc: {},
      roles: null,
      add_roles: {},
      edit_roles: [],
      member_roles: {}, // 用户角色的列表
      addModal: false,
      addModalTitle: '添加成员',
      addModalLoading: true,
      addFormData: cloneDeep(init_add_form_data),
      addFormDataClick: cloneDeep(init_add_form_data),
      isedit: true,
      psdModel: false,
      psdModalLoading: true,
      psdInfo: {
        id: '',
        name: '',
        new_password: '',
        confirm_password: '',
      },
      commissions: [
        { label: '是', value: '1' },
        { label: '否', value: '0' },
      ],
      weapp_roles: [],
      idCardDesc: [],
      bindVisible: false,
      clickItem: {},
      phoneList: [],
      echoInfo: {},
      addLoading: false,
      signModalVisible: false,
      signature_img: '',

      current_index: '',
      current_row: {},
      serviceVisible: false,
      mdp_role_ids: [], // mdp的id合集（包含非mdp身份）
      mdp_role_id: '', // mdp绑定的身份，

      historyVisible: false
    };
  },
  computed: {
    // 前提rst, 是否展示mdp角色的提示
    isShowMdpTip() {
      return item => {
        const id = item.id || '';
        return this.is_rst && this.mdp_role_ids.includes(id);
      };
    },
    is_rst() {
      return isRstClinic();
    },

    getActions() {
      return (row, index) => {
        return [
          {
            label: row.status === 'OFF' ? '启用' : '停用',
            confirmText: row.status === 'OFF' ? '确定启用?' : '确定停用？',
            handler: this.onStatus,
            params: {
              id: row.id,
              act: row.status === 'OFF' ? 'ENABLED' : 'DISABLED',
            },
            tagType: 'Poptip',
            isHidden: row.source === 'SYSTEM' && row.role_id === '1',
          },
          {
            label: '编辑',
            handler: this.onOpenAddModal,
            tagType: 'a',
            params: row.id,
          },
          {
            label: '设置排班',
            handler: this.setManagement,
            tagType: 'a',
            isHidden: !this.is_rst,
            params: row,
            index: index,
          },
          {
            label: '修改密码',
            handler: this.showChangeModel,
            tagType: 'a',
            params: row.id,
          },
          {
            label: '权限预览',
            handler: this.permissionPreview,
            tagType: 'a',
            params: row,
          },
          {
            label: '电子签名',
            handler: this.setSignaturePanel,
            tagType: 'a',
            isHidden: row.audit_status !== '1',
            params: row,
            index: index,
          },
          {
            label: '可提供服务',
            handler: this.seeService,
            tagType: 'a',
            isHidden: !this.is_rst,
            params: row,
            index: index,
          },
        ];
      };
    },
    // 获取医生的id
    doc_id() {
      let id = '';
      this.roles.some(item => {
        if (item.name === '医生') {
          id = item.id;
          return true;
        }
      });
      return id;
    },
    // 获取药剂师的id
    yjs_id() {
      let id = '';
      this.roles.some(item => {
        if (item.name === '药剂师') {
          id = item.id;
          return true;
        }
      });
      return id;
    },
    /**
     * rst诊所，编辑时，mdp的规则：绑定的系统内建(mdp角色，包括非mdp身份)的角色不允许删除，
     * 绑定了mdp身份或者选择了自定义，都不允许在勾选mdp（包含非mdp身份）的角色（权限）
     * */
    mdp_disbaled() {
      return item => {
        if (!this.is_rst) return false;
        // 创建时，mdp都不允许创建
        if (!this.isedit) {
          return this.mdp_role_ids?.includes(item.id);
        } else if (this.isedit && this.mdp_role_ids?.includes(item.id)) {
          // 编辑时规则，绑定的是mdp身份, 不允许勾选
          return true;
        } else if (Number(this.mdp_role_id || 0) === 0 && this.mdp_role_ids?.includes(item.id)) {
          // 编辑时，绑定的自定义的，mdp的不允许勾选
          return true;
        } else {
          return false;
        }
      };
    },
    /**
     * rst某一类禁用规则
     * mdp身份(包括非mdp)绑定时，某一类表单禁止输入
     * 绑定自定义时，不做限制
     * mdp_role_id: 绑定定身份id
     * mdp_role_ids: mdp身份合集
     * */
    rst_disabled() {
      return this.isedit && this.is_rst && this.mdp_role_ids.includes(this.mdp_role_id);
    },
    // 1: 管理员不允许删除，2:内建的医生和药剂师不能叠加
    roleMutex() {
      return role_item => {
        let role_ids = this.addFormData.role_ids;
        let is_exist_doc = role_ids.indexOf(this.doc_id);
        let is_exist_yjs = role_ids.indexOf(this.yjs_id);
        if (role_item.id === '1') {
          return true;
        }
        // 当前是医生角色，并且已经选择过药剂师
        if (role_item.id == this.doc_id && is_exist_yjs != -1) {
          return true;
        }

        // 当前是药剂师角色，并且已经选择过医生
        if (role_item.id == this.yjs_id && is_exist_doc != -1) {
          return true;
        }

        return false;
      };
    },
    // 编辑时，已经认证过的医生不可以移除,
    isDisabled() {
      if (!this.is_rst && this.addFormData.id && this.echoInfo.audit_status == '1') {
        return true;
      } else {
        return false;
      }
    },
    systemDocRoidClick() {
      if (this.roles) {
        let findItem = null;
        for (const key in this.roles) {
          if (this.roles[key].id === this.addFormDataClick.role_id) {
            findItem = this.roles[key];
          }
        }
        // let findItem = this.roles.find(item => item.id === this.addFormData.role_id);
        return findItem?.source === 'SYSTEM' && findItem?.name === '医生';
      }
    },

    // systemDocRoid() {
    //   console.log(this.roles);
    //   if (this.roles) {
    //     let findItem = null;
    //     for (const key in this.roles) {
    //       if (this.roles[key].id === this.addFormData.role_id) {
    //         findItem = this.roles[key];
    //       }
    //     }
    //     return findItem?.source === 'SYSTEM' && findItem?.name === '医生';
    //   }
    // }

    // 榕树堂不能新增理疗师，
    computeRoles() {
      // 新版理疗师放出来，禁用
      // if (isRstClinic()) {
      //   return this.roles.filter(item => item.id !== '2376');
      // }
      return this.roles;
    },
  },

  watch: {},

  created() {
    this.getOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  methods: {
    setManagement(row) {
      this.$router.push({
        path: '/setting/member/list',
        query: {
          componentsName: 'management',
        },
      });
    },
    seeService(row) {
      this.current_row = row;
      this.serviceVisible = true;
    },
    updateWeappStatus(row) {
      this.$api
        .postWeappstatus({
          id: row.id,
          weapp_status: row.weapp_status == 'ON' ? 'OFF' : 'ON',
        })
        .then(() => {
          this.getsList();
        });
    },

    setSignaturePanel(row, index) {
      this.current_index = index;
      this.current_row = row;
      this.signModalVisible = true;
    },
    signSuccess(imgUrl) {
      let id = this.list[this.current_index].id;
      this.getSavememberdocsign(id, imgUrl);
    },
    getESignature(file) {
      this.signature_img = '';
      if (file) {
        this.$refs.eSign.$refs['qn-upload']?.onBeforeUpload(file).then(() => {
          this.$refs.eSign?.$refs['qn-upload']?.$children[0]?.post(file);
        });
      } else {
        this.getSavememberdocsign(this.current_row.id, '');
      }
    },
    // 给表格行设置样式
    rowClassName(row, index) {
      return row.status === 'OFF' ? 'del-cell' : '';
    },
    // 去认证
    authentication(row) {
      let query = {
        is_quick: '1',
        name: row.name,
        mobile: row.mobile,
      };
      console.log('=>(list.vue:726) (Object.keys(row.mdp_info || {}).length', Object.keys(row.mdp_info || {}).length);
      if (Object.keys(row.mdp_info || {}).length) {
        let mdp_info = row.mdp_info || {};
        query = {
          ...query,
          avatar: mdp_info.avatar,
          desc: mdp_info.desc,
          id_card_type: mdp_info.id_card_type,
          idcard: mdp_info.idcard,
        };
      }
      this.$router.push({
        path: '/setting/physician/detail',
        query: query,
      });
    },
    permissionPreview(row) {
      this.$router.push({
        path: '/setting/member/staff/detail',
        query: {
          id: row.id,
        },
      });
    },
    removeSpace(e, key) {
      this.$nextTick(() => {
        this.addFormData[key] = e.replace(/\s+/g, '');
      });
    },
    chnageRoles(e) {
      if (this.isedit && this.systemDocRoidClick) {
        // this.addFormData = { ...cloneDeep(init_add_form_data), role_id: e };
      } else {
        // this.addFormData.name = '';
        // this.addFormData.idcard = '';
        if (this.systemDocRoid) {
          // this.addFormData.name = '';
          // this.addFormData.idcard = '';
        }
      }
    },
    changeMobile(e) {
      console.log(e);
      let mobildItem = this.phoneList.find(item => item.id === e);
      console.log(mobildItem);

      this.addFormData.physician_id = mobildItem.id;
      this.addFormData.mobile = mobildItem.mobile;
      this.addFormData.name = mobildItem.name;
      this.addFormData.idcard = mobildItem.id_card;
    },
    replaceIdBrackets() {
      // 将中文括号替换为英文括号
      this.addFormData.idcard = this.addFormData.idcard.replace(/（/g, '(').replace(/）/g, ')');
    },
    getPhoneList() {
      this.$api.getSeetingPhysicianList({ status: '2', is_relation: '1' }).then(res => {
        this.phoneList = res.list;
      });
    },
    getOptions() {
      this.$api.getMemberOptions().then(res => {
        this.weapp_roles = S.descToArrHandle(res.weappRoleDesc);
        this.idCardDesc = S.descToArrHandle(res.idCardDesc);
        this.submitQueryForm(true);
      });
    },
    psdVisibleChange(visible) {
      console.log('-> visible', visible);
      if (!visible) {
        for (const psdInfoKey in this.psdInfo) {
          this.psdInfo[psdInfoKey] = '';
        }
      }
    },
    roleVisibleChange(visible) {
      if (!visible) {
        this.addFormData = { ...init_add_form_data };
        this.roles = this.add_roles;
      }
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    showChangeModel(id) {
      io.get('clinic/member.info', { data: { id } }).then(res => {
        this.psdInfo.id = res.member.id;
        this.psdInfo.name = res.member.name;
        this.psdModel = true;
      });
    },
    changePassWord() {
      if (this.psdInfo.new_password.trim() == '' || this.psdInfo.confirm_password.trim() == '') {
        this.$Message.error('请填写完信息');
        this.psdModalLoading = false;
        return;
      }
      if (this.psdInfo.new_password != this.psdInfo.confirm_password) {
        this.$Message.error('两次密码需填写一致');
        this.psdModalLoading = false;
        return;
      }
      let query = {
        id: this.psdInfo.id,
        new_password: S.encrypt(
          JSON.stringify({
            password: this.psdInfo.new_password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        confirm_password: S.encrypt(
          JSON.stringify({
            password: this.psdInfo.confirm_password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        version: config.cryptoVersion,
      };
      this.$api
        .getChgpass(query)
        .then(
          res => {
            this.psdModel = false;
            this.$Message.success('修改密码成功');
            this.psdInfo.new_password = '';
            this.psdInfo.confirm_password = '';
          },
          err => {
            {
            }
            return;
          }
        )
        .catch(e => {});
    },
    closepsdModal() {
      this.psdModel = false;
      this.psdInfo.new_password = '';
      this.psdInfo.confirm_password = '';
    },
    onOpenAddModal: function (id) {
      this.getPhoneList();
      if (!id) {
        this.addModalTitle = '添加员工';
        this.isedit = false;
        this.addFormData = { ...init_add_form_data };
      } else {
        this.addModalTitle = '编辑员工';
        this.isedit = true;
        io.get('clinic/member.info', { data: { id } })
          .then(data => {
            this.echoInfo = data.member;
            this.addFormDataClick = cloneDeep(data.member);
            this.addFormData.id = data.member.id;
            this.edit_roles = data.all_roles;
            this.roles = data.all_roles;
            this.mdp_role_ids = data.mdp_role_ids;
            this.mdp_role_id = data.member.mdp_role_id;
            this.addFormData.name = data.member.name;
            this.addFormData.idcard = data.member.idcard;
            this.addFormData.mobile = data.member.mobile;
            this.addFormData.idcard_type_kw = data.member.idcard_type_kw;
            this.addFormData.role_ids = data.member.role_ids;
            this.addFormData.account = data.member.account;
            this.addFormData.password = '';
            this.addFormData.enable_commission = data.member.enable_commission;
            this.addFormData.weapp_role = data.member.weapp_role;
          })
          .catch(error => {
            console.log('-> error', error);
            {
            }
          });
      }
      this.addModal = true;
    },

    onAdd: function () {
      let formData = { ...this.addFormData };

      if (!formData.name.trim()) {
        this.$Message.error('请填写员工姓名');
        this.addModalLoading = false;
        return;
      }

      let telreg = this.regRole(this.addFormData.mobile);
      if (!telreg) {
        this.addModalLoading = false;
        this.$Message.error('请输入正确的号码');
        return;
      }

      if (!formData.account.trim()) {
        this.$Message.error('请填写员工登录账号');
        this.addModalLoading = false;
        return;
      }

      if (!this.isedit && !formData.password) {
        this.$Message.error('请填写登录密码');
        this.addModalLoading = false;
        return;
      }

      if (!formData.idcard_type_kw) {
        this.$Message.error('请选择身份证类型');
        this.addModalLoading = false;
        return;
      }
      // 身份证类型
      console.log(validateIDCard(formData.idcard), 'first');
      const gatIDCardValidate = /^8[123]0000(?:19|20)\d{2}(?:0[1-9]|1[0-2])(?:0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;
      console.log(gatIDCardValidate.test(formData.idcard), 'second');
      if (!formData.idcard) {
        this.$Message.error('请输入身份证号码');
        this.addModalLoading = false;
        return;
      }
      if (!validateIDCard(formData.idcard) && !gatIDCardValidate.test(formData.idcard)) {
        this.$Message.error('身份证号码格式不正确');
        this.addModalLoading = false;
        return;
      }

      if (formData.role_ids.length == 0) {
        this.$Message.error('请选择后台角色');
        this.addModalLoading = false;
        return;
      }

      let params = {
        ...formData,
        password: S.encrypt(
          JSON.stringify({
            password: this.addFormData.password,
            expired_time: Date.parse(new Date()) / 1000,
          })
        ),
        version: config.cryptoVersion,
      };
      if (this.isedit) {
        // 如果是编辑，password不要传
        this.$delete(params, 'password');
      }
      this.addLoading = true;
      io.post('clinic/member.save', params)
        .then(() => {
          this.$Message.success('保存成功');
          this.addModalLoading = false;
          this.addLoading = false;
          this.addModal = false;
          this.submitQueryForm();
        })
        .catch(error => {
          this.addModalLoading = false;
          this.addLoading = false;
        });
    },

    onStatus: function (params) {
      io.post('clinic/member.status', params)
        .then(() => {
          this.$Message.success(ACTIONS(params.act) + '成功');
          this.submitQueryForm();
        })
        .catch(error => {});
    },

    getsList: function () {
      console.log('=>(list.vue:864) getsListgetsListgetsList');
      this.tableLoading = true;
      io.get('clinic/member.list', { data: this.queryFormData }).then(data => {
        let list = this.handleList(data.members);
        this.list = list;
        this.total = data.total;
        this.statusDesc = data.statusDesc;
        this.member_roles = data.roles;
        this.roles = data.all_roles;
        this.add_roles = data.all_roles;
        this.mdp_role_ids = data.mdp_role_ids;
        this.tableLoading = false;
      });
    },
    // acceBind(row) {
    //   console.log(row);
    //   this.clickItem = row;
    //   this.bindVisible = true;
    // },

    handleList: function (list) {
      return list;
    },

    regRole(tel) {
      let flag;
      let reg = /^1[3456789]\d{9}$/;
      flag = reg.test(tel);
      return flag;
    },

    getSavememberdocsign(id, sign) {
      let params = {
        id,
        sign,
      };
      this.$api.getSavememberdocsign(params).then(res => {
        this.$Message.success('签名设置成功');
        this.getsList();
      });
    },
    submitQueryForm(replace) {
      console.log('为啥不执行', replace);
      this.queryFormData.r = S.random(6); // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      let searchObj = {};
      for (const searchKey in this.queryFormData) {
        if (this.queryFormData[searchKey] || this.queryFormData[searchKey] === 0) {
          searchObj[searchKey] = this.queryFormData[searchKey];
        }
      }
      if (replace) {
        this.$router.replace({ query: searchObj });
      } else {
        this.$router.push({ query: searchObj });
      }
      this.getsList();
    },
  },
};
</script>

<style lang="less" scoped>
.text-role {
  color: #8c8c8c;
  font-size: 12px;
  padding-right: 16px;
}

.rit-text {
  float: right;
  color: #aaa;
  width: 170px;
  text-align: right;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.sys-tag {
  cursor: pointer;
  padding: 1px 8px;
  width: fit-content;
  background: #3088ff;
  border-radius: 3px;
  font-weight: 400;
  font-size: 12px;
  color: #ffffff;
  //line-height: 20px;
  margin-right: 8px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;

  &:last-child {
    margin-right: 0px;
  }
}

.custom-tag {
  cursor: pointer;
  padding: 1px 8px;
  width: fit-content;
  background: rgba(48, 136, 255, 0.05) !important;
  color: #3088ff !important;
  border-radius: 3px;
  border: 1px solid #3088ff;
}

.pt8 {
  padding-top: 8px;
}

p {
  margin-bottom: 0px;
}

::v-deep .ivu-table .del-cell td {
  color: #c0c4cc !important;

  a,
  p,
  div,
  span {
    color: #c0c4cc !important;
  }

  .sys-tag {
    background: #c0c4cc !important;

    div {
      color: #fff !important;
    }
  }

  .custom-tag {
    background: rgba(48, 136, 255, 0.05) !important;
    border: 1px solid #c0c4cc;

    div {
      color: #c0c4cc !important;
    }
  }
}

.custom-picture-radio {
  padding-top: 10px;

  .picture-list .picture-list__pic {
    width: 76px;
    height: 30px;
  }
}
</style>
