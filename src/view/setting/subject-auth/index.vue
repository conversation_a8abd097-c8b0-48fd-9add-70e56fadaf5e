<template>
  <div class="auth-wrap" v-if="pageLoad">
    <h3 class="page-title">主体认证</h3>
    <div :class="['auth-box-item', 'is-active']">
      <div class="box-left">
        <div class="step-logo">01</div>
        <div class="auth-desc">
          <h4 class="desc-title">填写基础资料</h4>
          <div class="desc-text">填写企业资质证照、法人信息及银行账户等信息</div>
        </div>
      </div>
      <div class="box-right">
        <Button type="primary" @click="writeAuth('subject-auth')" class="auth-btn" v-if="baseStatus === 'init'"
          >填写
        </Button>
        <div class="flex-c flex-item-center" v-else>
          <div class="flex flex-item-align mb-4">
            <img class="mr-4" src="@/assets/image/auth/auth-succeed-icon.png" width="20" height="20" alt="" />
            <span style="color: #45b279">已提交</span>
          </div>
          <a @click="writeAuth('subject-auth', false, 0)">查看资料</a>
        </div>
      </div>
    </div>
    <div :class="['auth-box-item', validateStatus !== 'init' ? 'is-active' : '']" v-if="!showPaymentAuth">
      <div class="box-left">
        <div class="step-logo">02</div>
        <div class="auth-desc">
          <h4 class="desc-title">账户汇款验证</h4>
          <div class="desc-text">正确填写向您银行账户汇入的确认金额的数目，已验证账户</div>
        </div>
      </div>
      <div class="box-right">
        <div class="flex flex-item-align" v-if="validateStatus === 'init' || validateStatus === 'processing'">
          <span class="mr-10 remit-text" style="color: #aaaaaa" v-if="baseStatus === 'succeed'">等待汇款验证中</span>
          <Button
            class="auth-btn"
            type="primary"
            :disabled="validateStatus !== 'processing'"
            @click="writeAuth('remittance-auth')"
            >汇款验证
          </Button>
        </div>
        <div class="flex-c" v-if="validateStatus === 'failed'">
          <div class="flex flex-item-align mb-10 flex-item-end">
            <img class="mr-4" src="@/assets/image/auth/auth-failed-icon.png" width="20" height="20" alt="" />
            <span style="color: #e83b3b">验证失败</span>
          </div>
          <div class="flex flex-item-align">
            <span class="mr-10 remit-text">驳回原因：{{ reason }}</span>
            <Button class="auth-btn" type="primary" @click="writeAuth('subject-auth', true)">重新认证</Button>
          </div>
        </div>

        <div class="flex-c" v-if="validateStatus === 'limit'">
          <div class="flex flex-item-align mb-10 flex-item-end">
            <img class="mr-4" src="@/assets/image/auth/auth-failed-icon.png" width="20" height="20" alt="" />
            <span style="color: #e83b3b">验证失败</span>
          </div>
          <div class="flex flex-item-align">
            <span class="mr-10 remit-text">连续3次金额验证错误，请在{{ validate_form.deadline }}后重新提交认证</span>
            <Button class="auth-btn" type="primary" disabled>重新认证</Button>
          </div>
        </div>

        <div class="flex flex-item-align" v-if="validateStatus === 'succeed'">
          <img class="mr-4" src="@/assets/image/auth/auth-succeed-icon.png" width="20" height="20" alt="" />
          <span style="color: #45b279"> 已验证 </span>
        </div>
      </div>
    </div>
    <div :class="['auth-box-item', auditStatus ? 'is-active' : '']" v-if="baseStatus === 'submitted'">
      <div class="box-left">
        <div class="step-logo">{{ showPaymentAuth ? '02' : '03' }}</div>
        <div class="auth-desc">
          <h4 class="desc-title">银联审核</h4>
          <div class="desc-text">{{ subjectFormData.company_name }}</div>
        </div>
      </div>
      <div class="box-right" v-if="auditStatus === 'failed'">
        <div class="flex-c flex-item-center">
          <div class="flex flex-item-align mb-4">
            <img class="mr-4" src="@/assets/image/auth/auth-failed-icon.png" width="20" height="20" alt="" />
            <span style="color: #e83b3b">审核失败</span>
          </div>
          <a @click="writeAuth('subject-auth', false, 2)">{{ auditStatus === 'failed' ? '查看原因' : '查看资料' }}</a>
        </div>
      </div>
      <div class="box-right" v-if="auditStatus === 'processing'">
        <div class="flex-c flex-item-center">
          <div class="flex flex-item-align mb-4">
            <img class="mr-4" src="@/assets/image/auth/auth-processing-icon.png" width="20" height="20" alt="" />
            <span style="color: #f8a44f">审核中</span>
          </div>
          <div></div>
        </div>
      </div>
      <div class="box-right" v-if="auditStatus === 'succeed'">
        <div class="flex-c flex-item-center">
          <div class="flex flex-item-align mb-4">
            <img class="mr-4" src="@/assets/image/auth/auth-succeed-icon.png" width="20" height="20" alt="" />
            <span style="color: #45b279">认证成功</span>
          </div>
          <div></div>
        </div>
      </div>
    </div>

    <div class="table-wrapper mt-30" v-if="!!authRecords.length">
      <Table :columns="authColumns" :data="authRecords">
        <template v-slot:validate_status_desc="{ row, index }">
          <span>{{ row.validate_status_desc }}</span>
          <Tooltip :content="getTipText(row.validate_status)" v-if="!!getTipText(row.validate_status)">
            <svg-icon iconClass="fill-question" color="#999" class="cursor ml-4" size="16"></svg-icon>
          </Tooltip>
        </template>
        <template v-slot:action="{ row, index }">
          <div class="flex-inline">
            <div v-if="index === 0" class="flex-inline">
              <Tooltip
                v-if="row.into_status === 'succeeded' || row.audit_status === 'processing'"
                :disabled="row.into_status !== 'processing'"
                content="当前已存在进行中的认证"
                class="mr-12"
              >
                <a @click="reAuth(row, true)" :disabled="row.into_status === 'processing'">重新认证</a>
              </Tooltip>
              <a
                class="mr-12"
                v-if="row.apply_status?.audit === 'failed' || row.apply_status?.validate === 'failed'"
                @click="listCheckReason(row)"
                >查看原因</a
              >
              <a class="mr-12" v-if="row.apply_status?.validate === 'processing'" @click="remittAuth(row)">汇款验证</a>
            </div>
            <a @click="reAuth(row)">认证信息</a>
          </div>
        </template>
      </Table>
    </div>
    <auth-status-dialog
      v-model="dialogVisible"
      is-from-list
      :audit-status="baseStatus"
      :reason="reason"
      @on-close="dialogVisible = false"
      :out_request_no="out_request_no"
    ></auth-status-dialog>
  </div>
  <div v-else>
    <Spin fix></Spin>
  </div>
</template>

<script>
import AuthStatusDialog from './components/AuthStatusDialog.vue';
import S from '@/libs/util';

export default {
  name: 'SubjectAuth',
  components: {
    AuthStatusDialog,
  },
  data() {
    return {
      baseStatus: '', // 基础资料审核状态
      validateStatus: '', // 汇款验证状态
      auditStatus: '', //审核状态
      validate_form: {
        deadline: '',
      },
      dialogVisible: false,
      reason: '',
      subjectFormData: {
        organization_type: '',
        settle_acct_type: '',
        company_name: '',
      },
      pageLoad: false,
      authColumns: [
        { title: '认证主体', key: 'name' },
        { title: '认证状态', key: 'into_status_desc' },
        { title: '资料审核', key: 'audit_status_desc' },
        { title: '汇款验证', key: 'validate_status_desc', slot: 'validate_status_desc' },
        { title: '提交时间', key: 'create_at', width: '220' },
        {
          title: '审核通过时间',
          key: 'creator_name',
          width: '220',
          render: (h, { row }) => h('span', {}, row.audit_at || '-'),
        },
        { title: '操作', slot: 'action', width: 200 },
      ],
      authRecords: [], //认证记录
      out_request_no: '',
    };
  },
  computed: {
    getAuthStatusText() {
      const statusMap = {
        init: {
          text: '待提交',
          imgSrc: require('@/assets/image/auth/auth-processing-icon.png'),
          color: '#F8A44F',
        },
        submitted: {
          text: '资料已提交',
          imgSrc: require('@/assets/image/auth/auth-succeed-icon.png'),
          color: '#45B279',
        },
        // failed: {
        //   text: '审核失败',
        //   imgSrc: require('@/assets/image/auth/auth-failed-icon.png'),
        //   color: '#E83B3B'
        // }
      };
      return key => {
        return this.baseStatus && statusMap[this.baseStatus][key];
      };
    },
    showPaymentAuth() {
      return this.subjectFormData.organization_type === '2' && this.subjectFormData.settle_acct_type === '1';
    },
    showAuditStep() {
      return this.subjectFormData.organization_type === '2' && this.subjectFormData.settle_acct_type === '2';
    },
    getTipText() {
      return status => {
        switch (status) {
          case 'limit':
            return '汇款验证连续输错3次，请联系平台解决';
          case 'checking':
            return '银联已进行汇款验证打款，自行去账户查看打款金额';
          case 'wait':
            return '等待银联进行汇款';
          default:
            return '';
        }
      };
    },
  },
  created() {
    if (S.isServeRun) {
      this.init();
    }
  },
  activated() {
    this.init();
  },
  methods: {
    listCheckReason(row) {
      console.log('-> %c row  ===    %o', 'font-size: 15px;color: #fa8c16 ;', row);
      this.out_request_no = row.out_request_no;
      this.reason = row.reason;
      this.dialogVisible = true;
    },
    remittAuth(row) {
      this.$router.push({
        path: '/setting/subject-auth/detail',
        query: {
          step: 'remittance-auth',
          out_request_no: row.out_request_no,
        },
      });
    },
    init() {
      this.out_request_no = '';
      this.getAuthDetail();
      this.getRecordList();
    },
    getRecordList() {
      this.$api
        .getAuthRecords({
          sys_code: 'RSJYZT',
        })
        .then(res => {
          console.log(res);
          this.authRecords = res;
        });
    },
    writeAuth(step, isReAuth, stepIndex) {
      if (this.auditStatus === 'failed' && stepIndex === 2) {
        this.dialogVisible = true;
        return;
      }
      this.out_request_no = '';
      const query = {
        step,
      };
      if (step === 'subject-auth') {
        query.status = isReAuth ? this.validateStatus : this.baseStatus;
      }
      this.$router.push({
        path: '/setting/subject-auth/detail',
        query,
      });
    },
    reAuth(row, isReAuth) {
      console.log('-> %c row  ===    %o', 'font-size: 15px;color: #fa8c16 ;', row);
      const query = {
        step: 'subject-auth',
        status: row.apply_status?.validate,
        out_request_no: row.out_request_no,
      };
      isReAuth && (query.isReAuth = '1');
      this.$router.push({
        path: '/setting/subject-auth/detail',
        query,
      });
    },
    getAuthDetail() {
      this.$api
        .getAuthenticationInfo({ sys_code: 'RSJYZT' })
        .then(res => {
          this.baseStatus = res.base_status;
          this.validateStatus = res.validate_status;
          this.auditStatus = res.audit_status;
          this.reason = res.detail.reason;
          this.validate_form = res.detail?.validate_form;
          this.subjectFormData.organization_type = res.detail.organization_type;
          this.subjectFormData.settle_acct_type = res.detail.settle_acct.type;
          this.subjectFormData.company_name = res.detail.business_license.company_name;
        })
        .finally(() => {
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
          this.pageLoad = true;
        });
    },
  },
};
</script>

<style scoped lang="less">
.auth-wrap {
  padding: 8px;

  .auth-btn {
    width: 120px;
  }

  .page-title {
    font-size: 15px;
    font-weight: 500;
    color: #333333;
    line-height: 19px;
  }

  .auth-box-item {
    margin-top: 16px;
    background: #fafafa;
    border-radius: 4px;
    padding: 24px 20px 24px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .box-left {
      display: flex;

      .step-logo {
        font-size: 34px;
        font-family: OPPOSans-H, OPPOSans;
        font-weight: normal;
        color: #c5c9d7;
        line-height: 42px;
        margin-right: 14px;
        letter-spacing: 3px;
      }

      .auth-desc {
        .desc-title {
          margin-bottom: 8px;
          font-size: 13px;
          font-weight: 500;
          color: #333333;
          line-height: 17px;
        }

        .desc-text {
          color: #666666;
          line-height: 16px;
        }
      }
    }

    .box-right {
      .ivu-btn-primary[disabled] {
        background: #cccccc;
        color: #ffffff;
      }
    }
  }

  .is-active {
    .step-logo {
      color: #155bd4 !important;
    }
  }

  .remit-text {
    //color: #aaaaaa;
    color: #e83b3b;
  }
}

.table-wrapper {
}
</style>
