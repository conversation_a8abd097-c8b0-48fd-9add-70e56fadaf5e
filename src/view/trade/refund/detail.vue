<template>
  <div class="detail-wrapper">
    <div class="block-header"><span>基础信息</span></div>

    <div class="flex">
      <p class="label">退款编号：</p>
      <p class="text">{{ echoData.refund_no }}</p>
    </div>

    <div class="flex">
      <p class="label">状态：</p>
      <p class="text desc">
        <span>{{ echoData.status_desc }}</span>
        <span class="ml10" v-if="echoData.audit_data && echoData.audit_data.description"
          >(驳回原因：{{ echoData.audit_data && echoData.audit_data.description }})</span
        >
      </p>
    </div>

    <div class="flex">
      <p class="label">来源：</p>
      <p class="text">{{ echoData.source }}</p>
    </div>

    <div class="flex">
      <p class="label">申请人：</p>
      <p class="text">
        <KLink :to="{ path: '/user/detail', query: { uid: echoData.user && echoData.user.uid } }" target="_blank">
          <span>{{ echoData.user && echoData.user.name }}</span>
          <span v-if="echoData.user && echoData.user.nickname">({{ echoData.user && echoData.user.nickname }})</span>
          <span class="ml10">{{ echoData.user && echoData.user.mobile }}</span>
        </KLink>
      </p>
    </div>

    <div class="flex">
      <p class="label">申请时间：</p>
      <p class="text">{{ echoData.create_time }}</p>
    </div>

    <div class="flex" v-if="echoData.status !== 'WAIT_AUDIT'">
      <p class="label">审核信息：</p>
      <div>
        <p class="text">
          <span>{{ echoData.audit_data && echoData.audit_data.name }}</span>
          <span v-if="echoData.audit_data && echoData.audit_data.role"
            >({{ echoData.audit_data && echoData.audit_data.role }})</span
          >
          <span class="ml10">{{ echoData.audit_data && echoData.audit_data.time }}</span>
        </p>
      </div>
    </div>

    <div class="flex" v-if="echoData.status == 'FINISHED'">
      <p class="label">退款时间：</p>
      <p class="text">{{ echoData.create_time }}</p>
    </div>

    <div class="block-header"><span>储值退款明细</span></div>

    <div class="flex">
      <p class="label">实际退款金额：</p>
      <p class="text">{{ echoData.apply_money ? `￥${echoData.apply_money}` : '-' }}</p>
    </div>

    <div class="flex">
      <p class="label">储值退款金额：</p>
      <div>
        <p class="text">
          <span>{{ echoData.total_money ? `￥${echoData.total_money}` : '-' }}</span>
          <span v-if="echoData.wallet_type === 'USER_RECHARGE'">
            <span class="ml10 warn" v-if="echoData.account_money">其中实充需退款{{ echoData.account_money }}元</span
            ><span class="warn" v-if="echoData.given_money">，赠送需退款{{ echoData.given_money }}元</span>
          </span>
        </p>
      </div>
    </div>

    <div class="flex">
      <p class="label">退款方式：</p>
      <p class="text">{{ echoData.method }}</p>
    </div>

    <div class="flex">
      <p class="label">退款凭证：</p>
      <p class="text">
        <viewer :images="evidence_img" v-if="evidence_img?.length" class="flex flex-item-center">
          <p v-if="evidence_img?.length" class="flex flex-item-center">
            <img
              style="width: 60px; height: auto"
              class="mr10 cursor hover-f"
              v-for="(item, index) in evidence_img"
              :key="'img' + index"
              :src="item | imageStyle"
            />
          </p>
        </viewer>
        <span v-else>-</span>
      </p>
    </div>

    <div class="flex mt10">
      <p class="label">退款说明：</p>
      <p class="text wrap desc">{{ (echoData.attach && echoData.attach.description) || '-' }}</p>
    </div>

    <div style="height: 35px">
      <div class="fixed-bottom-wrapper">
        <!--        <Button @click="back">返回</Button>-->
        <back-button></back-button>
        <Button @click="refuseModalVisible = true" class="ml10" type="error" v-if="echoData.status == 'WAIT_AUDIT'">
          审核驳回
        </Button>
        <Button
          @click="pass()"
          class="ml10"
          type="primary"
          :loading="passLoading"
          v-if="echoData.status == 'WAIT_AUDIT'"
          >审核通过
        </Button>
      </div>
    </div>

    <!-- 审核弹窗 -->
    <Modal v-model="refuseModalVisible" :mask-closable="false" title="审核驳回">
      <div slot="footer">
        <Button @click="refuseModalVisible = false">取消</Button>
        <Button type="primary" @click="submitRefuseReason('rejected')" :loading="confirmLoading">确定</Button>
      </div>
      <div style="width: 100%">
        <p class="label">请输入驳回原因：</p>
        <Input
          v-model.trim="description"
          :autosize="{ minRows: 6, maxRows: 6 }"
          class="mt10"
          style="max-width: 1000px"
          type="textarea"
        />
      </div>
    </Modal>

    <ConfirmModal
      content="通过审核"
      contentText="确定要通过审核吗？"
      :confirmVisible.sync="confirmVisible"
      @ok="confirmFn"
    ></ConfirmModal>
  </div>
</template>

<script>
import ConfirmModal from '@/components/confirmModal/confirmModal';

export default {
  name: 'detail',
  components: {
    ConfirmModal,
  },
  mixins: [],
  props: {},
  data() {
    return {
      echoData: {}, // 回显数据
      evidence_img: [], //退款凭证
      passLoading: false, // 审核通过
      confirmVisible: false,

      // 审核弹窗
      refuseModalVisible: false,
      confirmLoading: false, // 审核loading
      description: null,
    };
  },
  computed: {},
  watch: {
    refuseModalVisible(val) {
      if (!val) {
        this.description = null;
      }
    },
  },
  created() {
    this.init();
  },
  mounted() {},
  methods: {
    init() {
      if (this.$route.query.id) {
        this.getRRefundDetail();
      }
    },
    // 返回
    back() {
      this.$router.push('/trade/refund/list');
    },

    // 审核驳回
    submitRefuseReason(type) {
      const { description } = this;
      if (!description) {
        this.$Message.error('请填写驳回原因');
        return false;
      }
      this.confirmLoading = true;
      this.getRRefundAudit(type);
    },

    // 审核通过
    pass() {
      this.confirmVisible = true;
    },

    confirmFn(type) {
      this.getRRefundAudit('success');
    },

    // 获取详情
    getRRefundDetail() {
      let params = {
        id: this.$route.query.id,
      };
      this.$api.getRRefundDetail(params).then(res => {
        this.echoData = res;
        this.evidence_img = res.attach && res.attach.evidence_img;
      });
    },

    // 审核驳回
    getRRefundAudit(type) {
      const { id } = this.echoData;
      const { description } = this;
      let params = {
        id,
        description: type === 'success' ? '' : description,
        audit_type: type,
      };
      this.$api
        .getRRefundAudit(params)
        .then(
          res => {
            console.log('type', type);
            if (type === 'success') {
              this.$Message.success('储值退款申请已通过，请等待系统退款');
            } else {
              this.$Message.success('储值退款申请已驳回');
            }
            this.refuseModalVisible = false;
            this.confirmVisible = false;
            this.$router.push('/trade/refund/list');
          },
          rej => {
            this.$$Message.error(rej.errmsg);
          }
        )
        .finally(() => {
          this.confirmLoading = false;
          this.passLoading = false;
        });
    },
  },
  filters: {},
};
</script>

<style lang="less" scoped>
.detail-wrapper {
  .label {
    width: 100px;
    text-align: right;
    margin-bottom: 16px;
    font-size: 12px;
    color: #000;
  }
}

.warn {
  color: #ff9900;
}

.mt10 {
  margin-top: 10px;
}

.mr10 {
  margin-right: 10px;
}

.cursor {
  cursor: pointer;
}

.hover-f {
  &:hover {
    transform: scale(1.1);
  }
}

.wrap {
  white-space: pre-wrap;
}

.desc {
  min-width: 200px;
  width: 40%;
}
</style>
