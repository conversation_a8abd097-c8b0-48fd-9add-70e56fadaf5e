<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <Col>
              <FormItem label="" :label-width="0">
                <Input type="text" v-model="queryFormData.user_keyword" placeholder="姓名/注册名/手机号" />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="">
                <Input type="text" v-model="queryFormData.refund_no" placeholder="退款编号" />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="">
                <DatePicker
                  type="daterange"
                  v-model="timeRange"
                  @on-change="times => handleTimeChange(times)"
                  placeholder="申请时间"
                ></DatePicker>
              </FormItem>
            </Col>
            <Col>
              <FormItem label="">
                <DatePicker
                  type="daterange"
                  v-model="finishTimeRange"
                  @on-change="times => handleTimeChange(times, 'finish_st', 'finish_et')"
                  placeholder="完成时间"
                  :options="disabledOptions"
                ></DatePicker>
              </FormItem>
            </Col>
            <Col>
              <FormItem label="">
                <Select v-model="queryFormData.status" placeholder="退款状态">
                  <Option value="">全部</Option>
                  <Option v-for="item in statusDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col>
              <FormItem label="">
                <Select v-model="queryFormData.source" placeholder="订单来源">
                  <Option value="">全部来源</Option>
                  <Option v-for="source in sourceDesc" :key="source.id" :value="source.id">{{ source.desc }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col v-if="isRstClinic">
              <FormItem label="">
                <Select v-model="queryFormData.wallet_type" placeholder="订单类型">
                  <Option v-for="source in walletTypeDesc" :key="source.id" :value="source.id">{{
                    source.desc
                  }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col>
              <FormItem>
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>

        <div class="panel-nav flex flex-item-between" v-if="Object.keys(statusDesc).length">
          <div>
            <a class="nav" :class="{ active: !queryFormData.status }" @click.prevent.capture="onStatusChange('')">
              全部
              <!-- <Tag color="default">{{ statusTotal.TOTAL }}</Tag> -->
            </a>
            <a
              class="nav"
              :class="{ active: $route.query.status == item.id }"
              v-for="item in statusDesc"
              :key="item.id"
              @click.prevent.capture="onStatusChange(item.id)"
            >
              {{ item.desc }}
              <Tag color="warning" v-if="item.id === 'WAIT_AUDIT'">
                {{ statusTotal['WAIT_AUDIT'] }}
              </Tag>
            </a>
          </div>
        </div>
      </template>
      <template slot-scope="{ row }" slot="info">
        姓名：{{ row.real_name || '-' }}<br />
        注册名：
        <KLink :to="{ path: '/user/detail', query: { uid: row.uid } }" target="_blank">{{ row.nickname || '-' }}</KLink>
        <br />
        手机号：{{ row.user_mobile }}<br />
      </template>

      <!-- 实际退款金额 -->
      <template slot-scope="{ row }" slot="apply_money">
        <p>{{ row.apply_money ? `￥${row.apply_money}` : '-' }}</p>
      </template>

      <!-- 储值退款金额 -->
      <template slot-scope="{ row }" slot="total_money">
        <p>{{ row.total_money ? `￥${row.total_money}` : '-' }}</p>
      </template>

      <!-- 实充退款 -->
      <template slot-scope="{ row }" slot="account_money">
        <p>{{ row.account_money ? `￥${row.account_money}` : '-' }}</p>
      </template>

      <!-- 赠送退款 -->
      <template slot-scope="{ row }" slot="given_money">
        <p>{{ row.given_money ? `￥${row.given_money}` : '-' }}</p>
      </template>

      <!-- 申请时间 -->
      <template slot-scope="{ row }" slot="create_time">
        <p>{{ row.create_time | data_format }}</p>
      </template>

      <template slot-scope="{ row }" slot="operate">
        <a @click="examine(row)" v-if="row.status == 'WAIT_AUDIT'">审核</a>
        <a class="ml10" @click="detail(row)">详情</a>
      </template>
    </standard-table>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import search from '@/mixins/search';
import StandardTable from '@/components/StandardTable/index.vue';
import { isRstClinic } from '@/libs/runtime';
/* eslint-disable */

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  user_keyword: '', // 用户姓名/昵称/手机号
  refund_no: '', // 退款编号
  st: '', // 申请开始时间
  et: '', // 申请结束时间
  status: '', // 退款状态
  source: '', // 来源
  wallet_type: '',
  r: '',
};
const init_relate_form_data = {
  editOrderId: '',
  relateVisible: false,
  relateStaffIds: [],
  classType: '',
};
export default {
  name: 'list',
  components: { StandardTable },
  mixins: [search],
  data() {
    return {
      relatedModalFormData: {
        ...init_relate_form_data,
      },
      apiName: 'getRRefundList',
      downloadApiName: 'downloadOrderExcel',
      queryFormData: { ...init_query_form_data },

      timeRange: [], // 申请时间
      finishTimeRange: [], // 完成时间
      tableCols: [
        { title: '退款编号', key: 'refund_no', width: 120 },
        { title: '用户信息', slot: 'info', minWidth: 120 },
        { title: '来源', key: 'source' },
        { title: '实际退款金额', slot: 'apply_money' },
        { title: '储值退款金额', slot: 'total_money' },
        { title: '实充退款', slot: 'account_money' },
        { title: '赠送退款', slot: 'given_money' },
        { title: '退款方式', key: 'method' },
        { title: '状态', key: 'status_desc' },
        { title: '申请时间', slot: 'create_time', width: 140 },
        { title: '完成时间', key: 'finished_time_format', width: 140 },
        { title: '操作', slot: 'operate', width: 120, align: 'center' },
      ],
      tableLoading: false,
      total: 0,
      statusTotal: {},

      list: [],
      sourceDesc: [], // 来源枚举
      statusDesc: [], // 退款状态枚举
      disabledOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      isRstClinic: isRstClinic(),
      walletTypeDesc: [],
    };
  },

  created() {
    this.getRefundOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
    if (this.isRstClinic) {
      let newCol = { title: '订单类型', key: 'wallet_type_desc' };
      this.tableCols.splice(2, 0, newCol);
    }
  },

  methods: {
    changeTime(times) {
      this.queryFormData.st = times[0];
      this.queryFormData.et = times[1];
    },

    // 审核
    examine(row) {
      this.detail(row);
    },

    // 详情
    detail({ id }) {
      this.$router.push({
        path: '/trade/refund/detail',
        query: { id },
      });
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.timeRange = [];
      this.finishTimeRange = [];
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },

    // 退款单选项描述
    getRefundOptions() {
      this.$api.getRefundOptions().then(res => {
        this.statusDesc = S.descToArrHandle(res.statusDesc);
        this.sourceDesc = S.descToArrHandle(res.sourceDesc);
        this.walletTypeDesc = S.descToArrHandle(res.walletTypeDesc);
      });
    },

    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.list = data.orders;
          this.total = data.total;
          this.statusTotal = data.statusTotal;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getTimeRange('finish_st', 'finish_et', 'finishTimeRange');
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
.ml10 {
  margin-left: 10px;
}

p {
  margin: 0;
}
</style>
