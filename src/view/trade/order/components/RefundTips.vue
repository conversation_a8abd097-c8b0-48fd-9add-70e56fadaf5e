<template>
  <Modal v-model="show" :mask-closable="false" :closable="false" width="400" class-name="vertical-center-modal">
    <div class="refund-tips">
      <div class="tips-content">
        <div class="icon"></div>
        <div class="message">
          <template v-if="$slots.default">
            <slot></slot>
          </template>
          <template v-else>
            <div class="flex" style="justify-content: center">
              <Icon type="ios-alert" class="mr-12" size="24" color="#faad14" />
              <div>
                <div>当日全额退款，退款将在当日到账；</div>
                <div>当日部分退款，将在T+1日工作日后才能到账；</div>
                <div>次日部分退款，退款将在当日到账；</div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
    <div class="action-buttons" slot="footer">
      <Button @click="handleCancel">取消</Button>
      <Button @click="handleContinue" type="primary">继续</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'RefundTips',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      },
    },
  },
  methods: {
    handleContinue() {
      this.$emit('continue');
      this.show = false;
    },
    handleCancel() {
      this.$emit('cancel');
      this.show = false;
    },
  },
};
</script>

<style lang="less" scoped>
.refund-tips {
  padding: 16px 0;

  .tips-content {
    .icon {
      margin-bottom: 16px;
    }

    .message {
      color: #333;
      font-size: 14px;
      line-height: 21px;
    }
  }
}

.action-buttons {
  text-align: right;
  button + button {
    margin-left: 8px;
  }
}

/deep/ .vertical-center-modal {
  display: flex;
  align-items: center;
  justify-content: center;

  .ivu-modal {
    top: 0;
  }
}
</style>
