<!--  -->
<template>
  <div class="pay-wrapper">
    <Modal
      :value="payVisible"
      width="800"
      title="收款"
      @on-visible-change="visibleChange"
      :mask-closable='false'>
      <div class="pay-content">
        <div class="flex">
          <div class="pay-item">
            <p class="pay-title">应收金额</p>
            <p class="wait-pay-money">￥{{ orderInfo.ori_payment }} <span class="ori-price" v-show="pay_activeId===3&&is_recharge_buy==='yes'&&getDiscountFee">￥ {{
                receivable_fee
              }}</span></p>
          </div>
        </div>
        <div class="pay-item">
          <p class="pay-title">优惠金额</p>
          <p class="wait-pay-money">
            <InputNumber :max="Number(orderInfo.ori_payment)" :min="0" placeholder="请输入优惠金额" style="width: 200px"
                         v-model="formData.extra_discount" @on-change="changeDiscountFee"/>
          </p>
        </div>
        <div class="pay-item">
          <p class="pay-title">实收金额</p>
          <p class="wait-pay-money red">￥{{ collected_amount }}</p>
        </div>


        <p class="pay-title">备注</p>
        <Input v-model="formData.remark" type="textarea" :autosize="{ minRows: 1, maxRows: 5}" placeholder="请输入备注"
               style="width: 200px;margin-bottom: 30px;"/>
        <p class="pay-title">收款方式</p>
        <div class="pay-way">
          <div class="pay-cash" @click="choosePayWay('CASH')">
            <div :class="['pay-way-item',pay_activeId === 1?'pay-active':'pay-card']">
              <div class="pay-icon">
                <i class="el-icon-success"></i>
              </div>
              <i class="iconfont icon-qianbao"></i><span class="pay-text">现金支付</span>
            </div>
          </div>
          <div class="pay-weixin" @click="choosePayWay('WXCODE')">
            <div :class="['pay-way-item',pay_activeId === 2?'pay-active':'']">
              <div class="pay-icon">
                <i class="el-icon-success"></i>
              </div>
              <i class="iconfont icon-weixinzhifuchenggong"></i><span class="pay-text">微信支付</span>
            </div>
          </div>

          <div class="pay-stored" @click="choosePayWay('STORED')">
            <Tooltip placement="top" :content="promptCopyText" :disabled="!canRechargeBuy" :max-width="200">
              <div :class="['pay-way-item',pay_activeId === 3?'pay-active':'',canRechargeBuy?'store-disable':'']">
                <div class="pay-icon">
                  <i class="el-icon-success"></i>
                </div>
                <div class="pay-discount" v-if="getDiscountFee!=0">
                  减¥{{ getDiscountFee }}
                </div>
                <div class="stored-info">
                  <span class="pay-text">储值余额支付</span>
                  <span class="stored-balance">￥{{ balance }}</span> <br/>
                  <span class="text-error" v-if="isInsufficientBalance">余额不足</span>
                </div>
              </div>
            </Tooltip>

          </div>
        </div>
      </div>
      <!-- footer -->
      <div slot="footer" style="text-align:center">
        <Button type="primary" class="pay-btn" @click="confirmPay" :loading='payLoading'>确认支付</Button>
      </div>
    </Modal>
    <Modal
      v-model="codeVisible"
      width="800"
      title="收款码"
      @on-visible-change="codeVisibleChange"
      :mask-closable='false'>
      <div class="pay-content flex flex-c flex-item-center">
        <p class="pay-money">待支付金额:<span class="money">￥{{ wxpayment_fee }}</span></p>
        <div class="pay-qr">
          <div id="qrCode" ref="qrCodeDiv"></div>
        </div>
        <!-- <img :src="codeUrl" alt=""> -->
        <span class="scan">用户扫描二维码付款</span>
      </div>
      <!-- footer -->
      <div slot="footer" style="text-align:center">
        <Button type="primary" class="pay-btn" @click="payLater" :loading="payLaterLoading">稍后支付</Button>
      </div>
    </Modal>
    <!-- 支付成功 -->
    <SuccessPayDialog v-model="successVisible" :successData="successData"
                      v-if="successVisible"></SuccessPayDialog>
  </div>
</template>

<script>
import QRCode from 'qrcodejs2'
import SuccessPayDialog from './SuccessPayDialog.vue'
import util from '@/libs/util'

export default {
  name: 'pay',
  components: {
    SuccessPayDialog
  },
  mixins: [],
  props: {
    payVisible: {
      type: Boolean,
      default: false,
    },
    order_id: {
      type: String,
      default: ''
    },
    isCreatePay:{
      type:Boolean,
      default:false
    },
    // balance: {
    //   type: String,
    //   default: ''
    // }
  },
  data() {
    return {
      balance: '',
      codeUrl: '', // 二维码收款地址
      codeVisible: false, // 收款码弹窗显示标识
      timer: null,
      payment_fee: '', // 待收款金额
      wxpayment_fee: '',// 微信扫码待收款金额,正常情况下与payment_fee数值一样
      stored_total_fee: '', // 储值金额应当支付的金额
      pay_activeId: 1, //支付方式,1为现金，2为微信
      successVisible: false, // 支付成功弹窗显示标识
      successData: {}, // 支付成功返回的数据
      payLoading: false, // 确认支付的loading
      isCreate: true, // true代表创建详情页
      formData: {
        remark: '', // 备注
        extra_discount: null
      },
      orderInfo: {
        ori_payment: '',
        stored_total_fee: '',
      },
      receivable_fee: '', // 应收金额
      is_recharge_buy: 'no', // 是否是充值购买
      promptCopy: ['储值余额不足','商品购买清单中包含不支持使用储值余额支付的商品'],
      promptCopyText: '',
      isWXPayProgress: false, // 微信支付是否正在进行中
      payLaterLoading: false, // 稍后支付的loading
    }
  },
  computed: {
    // 实收金额
    collected_amount() {
      const { ori_payment } = this.orderInfo
      const { extra_discount } = this.formData
      return util.mathSub( Number( ori_payment ), Number( extra_discount ) )

    },
    isInsufficientBalance() {
      return this.balanceAmount > Number( this.balance )
    },
    canRechargeBuy(){
      return this.balanceAmount > Number( this.balance )||this.is_recharge_buy=='no'
    },
    balanceAmount() {
      const { extra_discount } = this.formData
      return util.mathSub( this.stored_total_fee, Number( extra_discount ) )
    },
    getDiscountFee() {
      if ( this.is_recharge_buy === 'no' ) {
        return 0
      } else {
        return util.mathSub( this.receivable_fee, this.orderInfo.stored_total_fee )
      }
    }
  },
  watch: {
    payVisible( val ) {
      if ( !val ) {
        this.remark = ''
        this.pay_activeId = 1
      }
    },
    order_id( val ) {
      if ( val ) {
        this.getOrderInfo()
      }
    },
  },
  created() {

  },
  mounted() {
    if ( this.$router.history.current.path === '/trade/order/create' ) {
      this.isCreate = true
    } else {
      this.isCreate = false
    }
  },
  methods: {
    detailPayClose(){
      console.log(21321)
      if(this.isCreatePay&&!this.isWXPayProgress){
        this.$router.replace('/trade/order/list')
      }
    },
    changeDiscountFee( val ) {
      console.log( '-> %c val  === %o ', 'font-size: 15px', val )
      if ( this.pay_activeId === 3 && this.isInsufficientBalance ) {
        this.orderInfo.origin_payment = this.receivable_fee
        this.choosePayWay( 'CASH' )
      }
    },
    // 收款弹窗关闭,清理定时器
    codeVisibleChange( val ) {
      if ( !val ) {
        const { isCreate } = this
        this.codeVisible = false
        clearInterval( this.timer )
        this.timer = null
        if ( isCreate ) {
          this.$router.push( {
            path: '/trade/order/list'
          } )
        }

      }
    },
    // 切换支付方式
    choosePayWay( type ) {
      console.log( '-> %c type  === %o ', 'font-size: 15px', type )
      if ( type === 'CASH' ) {
        this.pay_activeId = 1
        this.orderInfo.ori_payment = this.receivable_fee
      } else if ( type === 'WXCODE' ) {
        this.pay_activeId = 2
        this.orderInfo.ori_payment = this.receivable_fee
      } else if ( type === 'STORED' ) {
        if ( Number( this.balance ) < this.balanceAmount ||this.is_recharge_buy =='no') {
          return
        } else {
          this.pay_activeId = 3
          this.orderInfo.ori_payment = this.stored_total_fee
          if(this.orderInfo.ori_payment<=this.formData.extra_discount){
            this.formData.extra_discount = this.orderInfo.ori_payment
          }

        }
      }
    },
    // 确认支付按钮事件
    confirmPay() {
      this.orderPayShop()
    },
    // 确认支付
    orderPayConfirm() {
      let params = {
        order_id: this.order_id,
      }
      this.$api.orderPayConfirm( params ).then( res => {
        this.emitClose()
        this.successVisible = true
        this.successData = res
      }, rej => this.$Message.error( rej.errmsg ) )
    },
    // 提交预付款
    orderPayShop() {
      // if ( this.pay_activeId === 3 && this.is_recharge_buy !== 'yes' ) {
      //   this.$Message.error( '商品购买清单中包含不支持使用储值余额支付的商品，请修改收款方式或调整购买商品' )
      //   return
      // }
      if(this.collected_amount<=0){
        this.$Message.error( '实收金额不能小于0.01元' )
        return
      }
      const { pay_activeId } = this
      console.log( '-> %c pay_activeId  === %o ', 'font-size: 15px', pay_activeId )
      this.isWXPayProgress = false
      let pay_platform = ''
      if ( pay_activeId === 1 ) {
        pay_platform = 'cash'
      } else if ( pay_activeId === 2 ) {
        this.isWXPayProgress = true
        pay_platform = 'wxcode'
      } else if ( pay_activeId === 3 ) {
        pay_platform = 'user_recharge'
      }
      let params = {
        order_id: this.order_id,
        pay_platform,
        ...this.formData
      }
      this.payLoading = true
      this.$api.orderPayShop( params ).then( res => {
        this.codeUrl = res.pay_params.code_url
        this.wxpayment_fee = res.payment_fee
        if ( pay_activeId == 1 || pay_activeId == 3 ) {
          // 现金支付
          this.orderPayConfirm()
        }
        if ( pay_activeId == 2 ) {
          // 微信支付
          this.waitPay()
        }

      }, rej => this.$Message.error( rej.errmsg ) ).finally( () => {
        this.payLoading = false
      } )
    },
    waitPay() {
      this.emitClose()
      this.codeVisible = true
      setTimeout( () => {
        this.creatQrCode()
        console.log("-> %c this.order_id  === %o", "font-size: 15px;color: green;", this.order_id)
        this.timer = setInterval( () => {
          this.$api.getPaystate( { order_id: this.order_id } ).then( res => {
            if ( res.has_pay === '1' ) {
              clearInterval( this.timer )
              this.timer = null
              this.codeVisible = false
              this.successVisible = true
              // 确认支付获取支付成功信息
              this.orderPayConfirm()
            }
          } )
        }, 1000 )
      }, 300 )
    },
    //生成支二维码
    creatQrCode() {
      this.$refs.qrCodeDiv.innerHTML = ''
      new QRCode( this.$refs.qrCodeDiv, {
        text: this.codeUrl,
        width: 178,
        height: 178,
        colorDark: '#333333', //二维码颜色
        colorLight: '#ffffff', //二维码背景色
        correctLevel: QRCode.CorrectLevel.L,//容错率，L/M/H
      } )

    },
    // 监测支付弹窗的visible
    visibleChange( val ) {
      if ( !val ) {
        this.emitClose()
        // setTimeout(()=>{
        //   this.detailPayClose()
        // },200)
      }
    },
    // 关闭支付弹窗
    emitClose() {
      this.$emit( 'update:payVisible', false )
      clearInterval( this.timer )
      this.timer = null
    },
    // 稍后支付
    payLater() {
      this.payLaterLoading = true
      const { isCreate } = this
      this.codeVisible = false
      clearInterval( this.timer )
      this.timer = null
      if ( isCreate ) {
        this.$router.push( {
          path: '/trade/order/list'
        } )
      }
      this.payLaterLoading = false
    },
    // 获取订单信息
    getOrderInfo() {
      this.$api.getOrderInfo( { order_id: this.order_id } ).then( res => {
        this.orderInfo = res.order
        this.receivable_fee = res.order.ori_payment
        this.balance = res.user_wallet.total_money
        this.formData.extra_discount = Number( res.order.extra_discount )
        this.is_recharge_buy = res.order.is_recharge_buy
        if(this.is_recharge_buy==='no'){
          this.promptCopyText = this.promptCopy[1]
        }else {
          this.promptCopyText = this.promptCopy[0]
        }
        this.stored_total_fee = res.order.stored_total_fee
      }, rej => this.$Message.error( rej.errmsg ) )
    }
  },
  filters: {}
}
</script>
<style lang="less" scoped>
// 收款弹窗
.pay-way {
  margin-top: 26px;
  display: flex;

  .pay-way-item {
    width: 100px;
    height: 70px;
    border-radius: 4px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    .pay-icon {
      position: absolute;
      border-radius: 50%;
      border: 2px solid #FFFFFF;
      background: #FFFFFF;
      right: 4px;
      top: -16px;
      display: none;

      .el-icon-success {
        font-size: 24px;
        color: #F68A42;
      }
    }

    .pay-text {
      font-size: 13px;
      line-height: 23px;
      color: #999999;
    }
  }

  .pay-cash {
    margin-right: 30px;
    background: rgba(255, 255, 255, 0.5);

    .icon-qianbao {
      font-size: 22px;
      color: #999999;
      margin-right: 10px;
    }

    .pay-way-item {
      border: 1px solid #F0F0F0;
    }

    .pay-active {
      border: 1px solid #AE853D;
      background: #C7A363;

      .pay-icon {
        display: block;

        .el-icon-success {
          color: #C7A363;
        }
      }

      .icon-qianbao {
        color: #FFFFFF;
      }

      .pay-text {
        color: #FFFFFF;
      }
    }
  }

  .pay-weixin {
    margin-right: 30px;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.5);

    .icon-weixinzhifuchenggong {
      font-size: 28px;
      color: #999999;
      margin-right: 6px;
    }

    .pay-way-item {
      border: 1px solid rgba(112, 112, 112, 0.1);
    }

    .pay-active {
      background: #46BB36;
      border: 1px solid rgba(112, 112, 112, 0.10196078431372549);

      .pay-icon {
        display: block;

        .el-icon-success {
          color: #46BB36;
        }
      }

      .icon-weixinzhifuchenggong {
        color: #FFFFFF;
      }

      .pay-text {
        color: #FFFFFF;
      }
    }
  }
}
</style>
<style lang="less" scoped>
.pay-btn {
  padding: 0 40px;
  border-radius: 4px;
}

.pay-content {
  margin: 0 auto;
  width: 240px;

  .wait-pay-money {
    font-size: 20px;
    line-height: 34px;
    color: #000;
    margin-bottom: 20px;

    .ori-price {
      font-size: 14px;
      color: #999999;
      line-height: 16px;
      text-decoration: line-through;
      margin-left: 8px;
    }
  }

  .pay-title {
    font-weight: 400;
    line-height: 24px;
    color: #666;
    font-size: 13px;
  }

  .pay-money {
    font-size: 16px;

    .money {
      color: rgb(233, 98, 8);
      font-weight: 600;
      font-size: 20px;
    }
  }

  .pay-qr {
    margin-top: 8px;
    width: 178px;
    height: 178px;
  }

  .scan {
    padding: 8px 12px;
    margin-top: 16px;
    font-size: 13px;
    background: rgba(54, 162, 31, .09);
    border-radius: 16px;
    color: #36a21f;
  }
}

</style>
<style lang="less" scoped>
/deep/ .ivu-modal-header-inner {
  font-size: 18px;
}

/deep/ .ivu-modal-header-inner:before {
  display: inline-block;
  content: "";
  width: 2px;
  height: 12px;
  background: #1157e5;
  margin-right: 6px;
}

/deep/ textarea.ivu-input {
  // height: 36px;
}

.ml10 {
  margin-left: 10px;
}

.red {
  color: red !important;
}

.pay-stored {
  margin-right: 30px;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0);
  border: 1px solid #F0F0F0;

  .pay-discount {
    background: linear-gradient(127deg, #FF9E5A 0%, #F36566 100%);
    border-radius: 6px 0px 6px 0px;
    position: absolute;
    top: -10px;
    left: -1px;
    font-size: 12px;
    font-weight: 300;
    color: #FFFFFF;
    line-height: 12px;
    padding: 3px 8px;
  }

  .store-disable {
    background: #F6F6F6;
    cursor: not-allowed;

    .pay-discount {
      background: #C8C8C8;
    }

    .pay-text, .stored-balance {
      color: #C8C8C8;
    }

    .text-error {
      color: #E5634B;
    }
  }

  .pay-text {
    margin-top: -10px;
  }

  .stored-info {
    margin-left: 10px;
  }

  .stored-balance {
    font-size: 16px;
    font-weight: 500;
    color: #000000;
    line-height: 16px;
  }

  .pay-active {
    background: #155BD3;
    border: 1px solid rgba(112, 112, 112, 0.10196078431372549);

    .pay-icon {
      display: block;

      .el-icon-success {
        color: #155BD3;
      }
    }

    .icon-weixinzhifuchenggong {
      color: #FFFFFF;
    }

    .pay-text {
      color: #FFFFFF;
    }

    .stored-balance {
      color: #FFFFFF;
    }
  }
}
</style>
