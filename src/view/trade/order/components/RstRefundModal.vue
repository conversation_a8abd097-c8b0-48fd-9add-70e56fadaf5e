<template>
  <Modal
    :value="value"
    :mask-closable="false"
    class-name="custom-pay-tip-modal"
    :title="title"
    width="1180px"
    :closable="false"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <Alert v-if="showAlert" type="warning" show-icon>
        针对已核销的服务如果在售后期内继续退款，需要在卡券信息先撤回核销后才能进行退款，不撤回核销默认无需退款；已核销且不再售后期内的服务无法再进行退款
      </Alert>
      <h4 class="table-title">售后商品明细：</h4>
      <Table :columns="getColumns" :data="goodsList" max-height="200">
        <template v-slot:service_status="{ row, index }">
          {{ row.service_status || '-' }}
        </template>
        <template v-slot:refund_amount="{ row, index }">
          <InputNumber
            v-model="goodsList[index].refund_amount"
            :active-change="false"
            @on-change="v => changeRefundNum(v, index)"
            :disabled="row.goods_type !== '40' || is_ry_order == '1'"
            :min="0"
            :max="+row.amount"
            :precision="0"
          />
        </template>
      </Table>
      <template v-if="cardList.length > 0">
        <h4 class="table-title mt-16">卡券信息</h4>
        <Table :columns="virtualColumns" :data="cardList" max-height="200">
          <template slot-scope="{ row, index }" slot="used_time">
            {{ row.used_time | data_format }}
          </template>
          <template slot-scope="{ row, index }" slot="action">
            <Poptip v-model="row.popVisible" v-if="row.is_can_cancel_checkin == 1">
              <div slot="content" class="withdraw-verification">
                <div class="withdraw-verification-content">
                  <div class="withdraw-verification-title">
                    <img src="@/assets/image/warning.png" />
                    <div>确认撤销该次核销码?</div>
                  </div>
                  <div class="withdraw-verification-desc" v-if="row.is_out_sales_card === '1'">
                    注：&nbsp;&nbsp;撤销核销只能撤回我们系统上核销，不能撤回美团/抖音后台上的核销记录，若想撤回美团/抖音的核销记录，需在美团/抖音的后台操作。
                  </div>
                </div>
                <div class="withdraw-verification-footer">
                  <Button size="small" @click="confirmRevoke(row, true)">取消</Button>
                  <Button type="primary" size="small" @click="confirmRevoke(row)">确定</Button>
                </div>
              </div>
              <a @click.stop="$set(row, 'popVisible', true)">撤回核销</a>
            </Poptip>
            <span v-else>-</span>
          </template>
        </Table>
      </template>

      <k-widget label="退款方式" required :label-width="70" class="mt-20 mb-20" placeholder="请选择退款方式">
        <Select v-model="formData.pay_platform" style="width: 280px">
          <Option
            :value="item.kw"
            :label="item.refund_desc"
            :key="item.kw"
            v-for="item in offlineRefundPayPlatformDesc"
          ></Option>
        </Select>
      </k-widget>
      <k-widget label="售后原因" :label-width="70">
        <Input
          v-model="formData.refund_remark"
          type="textarea"
          style="width: 80%"
          placeholder="请输入售后原因"
          :autosize="{
            minRows: 2,
            maxRows: 3,
          }"
        />
      </k-widget>
    </div>
    <div slot="footer">
      <Button @click="cancelHandler">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirmHandler">确认退款</Button>
    </div>
    <!--    <RefundTips v-model="tipsVisible" @continue="continueHandler" />-->
    <!-- 是否清除业绩 -->
    <is-clear-performance-modal
      v-model="clearPerformanceVisible"
      @change="cleanDivideChange"
    ></is-clear-performance-modal>
  </Modal>
</template>

<script>
import { $operator } from '@/libs/operation';
import io from '@/libs/io';
// import RefundTips from './RefundTips.vue';
import IsClearPerformanceModal from './IsClearPerformanceModal.vue';

export default {
  name: 'RstRefundModal',
  components: {
    // RefundTips,
    IsClearPerformanceModal,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '商品售后',
    },
    refundGoodsId: {
      type: String,
      default: '',
    },
    is_origin_pay: {
      type: Boolean,
      default: false,
    },
    isRstClinic: {
      type: Boolean,
      default: false,
    },
    isRefundAll: {
      type: Boolean,
      default: false,
    },
    is_ry_order: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      confirmLoading: false,
      baseColumns: [
        { title: '商品编号', key: 'goods_id', align: 'center', fixed: 'left' },
        { title: '商品名称', key: 'goods_name', align: 'center', fixed: 'left' },
        {
          title: '商品状态',
          key: 'goods_status_text',
          align: 'center',
          goodsType: ['10', '40'],
          refundAllHidden: true,
        },
        { title: '通兑券状态', key: 'exchange_status', align: 'center', goodsType: '25', refundAllHidden: true },
        { title: '服务状态', slot: 'service_status', align: 'center', goodsType: ['15', '30'], refundAllHidden: true },
        { title: '售后类型', key: 'refund_type', align: 'center', goodsType: ['10', '40'], refundAllHidden: true },
        {
          title: '单价',
          key: 'price',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.price ? '￥' + Number(row.price || 0)?.toFixed(2) : '-'),
        },
        { title: '商品数量', key: 'ori_amount', align: 'center' },
        {
          title: '商品总价',
          key: 'ori_money',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.price ? '￥' + Number(row.ori_money || 0)?.toFixed(2) : '-'),
        },
        { title: '已退数量', key: 'has_refund_amount', align: 'center' },
        {
          title: '退款在途数量',
          key: 'refund_in_amount',
          align: 'center',
        },
        {
          title: '已退金额',
          key: 'amount',
          align: 'center',
          render: (h, { row }) =>
            h('span', {}, row.has_refund_money ? '￥' + Number(row.has_refund_money || 0)?.toFixed(2) : '-'),
        },
        {
          title: '退款在途金额',
          key: 'amount',
          align: 'center',
          render: (h, { row }) =>
            h('span', {}, row.refund_in_money ? '￥' + Number(row.refund_in_money || 0)?.toFixed(2) : '-'),
        },
        { title: '可退数量', key: 'amount', align: 'center' },
        {
          title: '可退金额',
          key: 'money',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.money ? '￥' + Number(row.money || 0)?.toFixed(2) : '-'),
        },
        { title: '退款数量', slot: 'refund_amount', align: 'center', width: 120 },
        {
          title: '退款金额',
          key: 'refund_money',
          align: 'center',
          render: (h, { row }) =>
            h('span', {}, row.refund_money ? '￥' + Number(row.refund_money || 0)?.toFixed(2) : '-'),
        },
      ],
      virtualColumns: [
        { title: '商品编号', key: 'goods_id', align: 'center' },
        { title: '商品名称', key: 'goods_name', align: 'center' },
        { title: '服务名称', key: 'service_name', align: 'center' },
        { title: '单价', key: 'price', align: 'center' },
        { title: '状态', key: 'status_desc', align: 'center' },
        { title: '核销时间', slot: 'used_time', align: 'center' },
        { title: '操作', slot: 'action', align: 'center' },
      ],
      goodsList: [],
      formData: {
        pay_platform: 'refund_old_way',
        refund_remark: '',
      },
      canRefund: false,
      is_all_refund: '',
      offlineRefundPayPlatformDesc: {
        refund_old_way: { refund_desc: '原路返回', kw: 'refund_old_way' },
      },
      goodsType: '',
      // tipsVisible: false,
      clearPerformanceVisible: false,
      revokeId: '',
      is_clean_divide: '', // 是否清除业绩 1:清除 0：保留
      cardList: [],
    };
  },
  watch: {
    value(value) {
      if (value) {
        this.getRefundGoodsInfo();
        !this.is_origin_pay && this.getOptions();
      } else {
        this.goodsList = [];
      }
    },
  },
  computed: {
    // 是否展示清除业绩弹窗
    isShowClearPerformance() {
      return this.goodsList.findIndex(item => item.service_is_part_used === '1') > -1;
    },
    showAlert() {
      return this.goodsList.findIndex(item => item.goods_type === '30') > -1;
    },
    getColumns() {
      let res = this.baseColumns.filter(item => {
        if (this.isRefundAll) {
          return !item.refundAllHidden;
        }
        // if (this.isRstClinic) {
        //   return item.goodsType !== '25';
        // }
        return (
          !item.goodsType ||
          item.goodsType === this.goodsType ||
          (Array.isArray(item.goodsType) && item.goodsType.includes(this.goodsType))
        );
      });
      return res;
    },
  },
  created() {},
  methods: {
    confirmRevoke(row, isCancel = false) {
      this.$set(row, 'popVisible', false);
      if (isCancel) return;
      this.clearPerformanceVisible = true;
      this.revokeId = row.card_id;
    },
    cleanDivideChange(val) {
      this.is_clean_divide = val;
      this.$api.cancelCheckIn({ id: this.revokeId, is_clean_divide: val }).then(res => {
        this.$Message.success('撤销成功');
        this.getRefundGoodsInfo();
        this.$emit('reloadDetail');
      });
      // this.submitRefund();
    },
    showTips() {
      this.showTips = true;
    },
    continueHandler() {
      this.submitRefund();
    },
    changeVisible(v) {
      if (!v) {
        this.formData = {
          pay_platform: 'refund_old_way',
          refund_remark: '',
        };
      }
    },
    changeRefundNum(v, index) {
      const max = this.goodsList[index].money || 0;
      const calcAmount = $operator.multiply(this.goodsList[index].price, v || 0);
      this.goodsList[index].refund_money = calcAmount >= Number(max) ? max : calcAmount;
    },
    submitRefund() {
      this.confirmLoading = true;
      let refund_order_attrs = {};
      for (const goods of this.goodsList) {
        refund_order_attrs[goods.id] = {
          id: goods.id,
          refund_amount: goods.refund_amount,
          refund_money: goods.refund_money,
        };
      }
      const formData = {
        orderId: this.$route.query.orderid,
        ...this.formData,
        refund_order_attrs,
        // is_clean_divide: this.is_clean_divide,
      };
      console.log('🚀 ~ submitRefund ~ formData=>', formData);
      io.post('clinic/order.order.refund', formData)
        .then(
          () => {
            this.$Message.success('提交成功');
            this.$emit('reloadDetail');
          },
          error => {}
        )
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    confirmHandler() {
      // 重置
      this.is_clean_divide = '';
      if (!this.formData.pay_platform) {
        this.$Message.error('请选择退款方式');
        return;
      }
      let flag = this.goodsList.every(item => !item.refund_amount && !Number(item.refund_money));
      if (flag) {
        this.$Message.error('退款金额不能为0');
        return;
      }
      // for (const goods of this.goodsList) {
      //   if (!goods.refund_amount) {
      //     this.$Message.error('请输入退款数量');
      //     return;
      //   }
      // }
      // const isAllRefund =
      //   this.is_all_refund === '1' && this.goodsList.every(item => item.refund_amount === item.amount);
      // if (this.isRstClinic && !isAllRefund) {
      //   this.tipsVisible = true;
      //   return;
      // }
      // if (this.isShowClearPerformance && !this.is_clean_divide) {
      //   this.clearPerformanceVisible = true;
      //   return;
      // }
      this.submitRefund();
    },
    cancelHandler() {
      this.closeModal();
    },
    closeModal() {
      this.$emit('input', false);
    },
    getRefundGoodsInfo() {
      const refundApi = this.isRstClinic ? 'getRstRefundGoods' : 'getDirectRefundGoods';
      this.$api[refundApi]({
        orderid: this.$route.query.orderid,
        is_all_refund: this.refundGoodsId ? '0' : '1',
      }).then(res => {
        console.log('%c=>(RefundModal.vue:312) res', 'color: #ECA233;font-size: 16px;', res);
        this.orderType = res.order_type;
        this.is_all_refund = res.is_all_refund;
        this.canRefund = res.is_refund === '1';
        let refundGoods = Object.values(res.data).filter(item => {
          if (!this.refundGoodsId) return true;
          const symbolIndex = item.id.indexOf('#');
          const realGoodsId = symbolIndex > -1 ? item.id.slice(0, symbolIndex) : item.id;
          return realGoodsId === this.refundGoodsId;
        });
        this.goodsType = refundGoods[0].goods_type;
        this.goodsList = refundGoods.map(item => {
          return {
            ...item,
            refund_money: item.money,
            refund_amount: +item.amount,
          };
        });
        console.log(
          '%c=>(RstRefundModal.vue:357) this.refundGoodsId',
          'color: #ECA233;font-size: 16px;',
          this.refundGoodsId
        );
        this.cardList = res.card_list
          .filter(item => {
            if (!this.refundGoodsId) return true;
            return item.order_attr_id === this.refundGoodsId;
          })
          ?.map(item => ({ ...item, popVisible: false }));
      });
    },
    getOptions() {
      this.$api.getOrderOptions().then(res => {
        this.offlineRefundPayPlatformDesc = res.offlineRefundPayPlatformDesc;
        this.formData.pay_platform = '';
      });
    },
  },
};
</script>

<style scoped lang="less">
.content {
  padding-bottom: 10px;

  .table-title {
    font-size: 16px;
    margin-bottom: 12px;
  }

  ::v-deep .ivu-input-wrapper,
  .ivu-select {
    max-width: none;
  }
}
.withdraw-verification {
  max-width: 300px;
  padding: 12px 16px;
  .withdraw-verification-content {
    width: 100%;
    .withdraw-verification-title {
      width: 100%;
      display: flex;
      align-items: center;
      img {
        width: 14px;
        height: 14px;
        margin-right: 8px;
      }
      > div {
        font-size: 13px;
        color: #515a6e;
        line-height: 22px;
      }
    }
    .withdraw-verification-desc {
      margin-top: 6px;
      padding-left: 22px;
      font-size: 11px;
      color: #60667a;
      line-height: 1.5;
      white-space: normal;
    }
  }
  .withdraw-verification-footer {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 8px;
    > button {
      margin: 0 4px;
    }
  }
}
</style>
