<template>
  <Modal
    :value="value"
    :title="title"
    width="800px"
    @on-visible-change="changeVisible"
    footer-hide
    class="modal-wrapper"
  >
    <Form inline :label-width="40" :label-colon="true">
      <Row>
        <FormItem label="类型"> 医保 </FormItem>

        <FormItem label="金额" :label-width="100">{{ total_fee }}</FormItem>
      </Row>
    </Form>

    <Table :data="list" :columns="modalColumns" :height="360">
      <!-- 规格 -->
      <template slot-scope="{ row }" slot="spec">
        <div>{{ row.spec || '-' }}</div>
      </template>

      <!-- 单位 -->
      <template slot-scope="{ row }" slot="unit">
        <div>{{ row.unit || '-' }}</div>
      </template>

      <!-- 数量 -->
      <template slot-scope="{ row }" slot="num">
        <div>{{ row.num || '-' }}</div>
      </template>

      <!-- 单价 -->
      <template slot-scope="{ row }" slot="price">
        <div>{{ row.price ? `￥${row.price}` : '-' }}</div>
      </template>
    </Table>
  </Modal>
</template>

<script>
export default {
  name: 'modal',

  components: {},

  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '医保订单明细',
    },
    order_id: {
      type: String,
      default: '',
    },
    order_code: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      modalColumns: [
        { title: '序号', type: 'index', align: 'center' },
        { title: '名称', key: 'name', align: 'center' },
        { title: '数量', key: 'quantity', align: 'center' },
        { title: '单位', key: 'unit_name', align: 'center' },
      ],

      list: [],
      tableLoading: false,
      total: 0,
      total_fee: 0,
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    getYBOrderDetail() {
      let params = { order_id: this.order_id };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getYBOrderDetail(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          this.list = res.list;
          this.total_fee = res.total_fee;
        })
        .catch(err => {});
    },

    /**
     * 表格回归顶部
     * */
    scrollTop() {
      let overflowY = this.$el.getElementsByClassName('ivu-table-body')[0];
      if (!overflowY) {
        return;
      }
      overflowY.scrollTop = 0;
    },

    /**
     * 关闭弹窗
     * */
    closeModal() {
      this.scrollTop();
      this.$emit('input', false);
    },

    /**
     * 弹窗变化
     * */
    changeVisible(flag) {
      if (flag) {
        this.getYBOrderDetail();
      } else {
        this.closeModal();
      }
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 500px;
  overflow-y: auto;
}
.cursor {
  cursor: pointer;
}
.mb10 {
  margin-bottom: 10px;
}
.no-wrap {
  display: flex;
  flex-wrap: wrap;
}
</style>
