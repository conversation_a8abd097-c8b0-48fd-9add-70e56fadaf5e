<template>
  <div class="create-order-wrapper">
    <div class="goods-box-wrapper">
      <div class="title">下单用户</div>
      <div class="search-box" ref="searchBoxRef">
        <div class="current-user" v-if="current_user_info.uid">
          <div class="current-user-left">
            <div
              class="avatar-box"
              :class="{ 'vip-avatar-box': current_user_info?.vip_info?.length > 0 }"
              :style="{ borderColor: getVipBorderColor(current_user_info.vip_info) }"
            >
              <img
                v-if="current_user_info?.vip_info?.length"
                class="vip-icon"
                :src="getVipIcon(current_user_info.vip_info)"
              />
              <img
                class="avatar"
                :src="
                  current_user_info.avatar
                    | imageStyle('B.w300', 'https://img-sn-i01s-cdn.rsjxx.com/image/2024/1218/095042_17701.png-B.w300')
                "
              />
            </div>

            <div class="user-info">
              <div class="user-info-top">
                <div class="user-info-name">{{ current_user_info.real_name }}</div>
                <div class="user-info-sex">
                  <span>{{ current_user_info.sex_text }}</span>
                  <span v-if="current_user_info.sex_text && current_user_info.age">｜</span>
                  <span>{{ current_user_info.age ? `${current_user_info.age}岁` : current_user_info.age }}</span>
                </div>
              </div>
              <div class="user-info-mobile-box">
                <div class="info-mobile">{{ current_user_info.mobile }}</div>
                <div class="info-stage-mobile" v-if="current_user_info.show_staging_mobile == '1'">
                  <span class="stage-tag">暂存</span>
                  <span class="stage-mobile">{{ current_user_info.staging_mobile }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="delete-user-icon-box">
            <Tooltip content="移除" placement="top">
              <img
                class="delete-user-icon"
                @click="deleteUserInfo"
                src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0722/102709_78163.png"
              />
            </Tooltip>
          </div>
        </div>

        <el-autocomplete
          v-else
          class="custom-user-autocomplete"
          ref="custom"
          v-model="nickname"
          :debounce="600"
          :popper-append-to-body="false"
          :fetch-suggestions="querySearchAsync"
          :trigger-on-focus="true"
          @blur="blur"
          placeholder="输入用户姓名、手机号搜索"
          @select="handleSelect"
        >
          <template slot-scope="{ item }">
            <div class="autocomplete" v-if="!item.empty" style="white-space: pre-wrap">
              <div
                class="avatar-box"
                :class="{ 'vip-avatar-box': item.vip_info.length > 0 }"
                :style="{ borderColor: getVipBorderColor(item.vip_info) }"
              >
                <img v-if="item.vip_info.length > 0" class="vip-icon" :src="getVipIcon(item.vip_info)" />
                <img
                  class="avatar-icon"
                  :src="
                    item.avatar
                      | imageStyle(
                        'B.w300',
                        'https://img-sn-i01s-cdn.rsjxx.com/image/2024/1218/095042_17701.png-B.w300'
                      )
                  "
                />
              </div>
              <div class="info-content">
                <span class="name">{{ item.patient_name }}</span>
                <span class="info">
                  <span>{{ item.sex_text }}</span>
                  <span v-if="item.age && item.sex_text"> | </span>
                  <span>{{ item.age ? `${item.age}岁` : '' }}</span>
                </span>
                <span class="mobile">{{ item.mobile }}</span>
                <span class="stage-mobile-box" v-if="item.show_staging_mobile === '1'">
                  <span class="stage-icon">暂存</span>
                  <span class="stage-mobile">{{ item.staging_mobile }}</span>
                </span>
              </div>
            </div>
            <div class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>
              <p class="flex flex-c">
                <span>{{ nickname }}</span>
                <span class="tip">尚无该用户</span>
              </p>
              <a>创建用户</a>
            </div>
          </template>
        </el-autocomplete>
      </div>
      <div class="clinic-goods-area" :style="getGoodsBoxStyle">
        <div>
          <div class="title">诊所售卖</div>
          <div class="goods-tab-box">
            <Tabs v-model="tabActive" @on-click="tabClick">
              <TabPane
                v-for="item in tabList"
                :label="h => tabPaneLabel(h, item)"
                :name="item.name"
                :key="item.name"
                :index="item.sort"
              >
              </TabPane>
            </Tabs>
            <div class="search-goods-box">
              <Input
                v-model="search_keyword"
                placeholder="搜索输入商品/服务名称"
                prefix="ios-search"
                @on-change="searchChange"
                clearable
                autocomplete="off"
                style="width: 180px"
              />
              <div style="position: fixed; left: -200px; top: -200px">
                <Input type="text" name="username"></Input>
              </div>
            </div>
          </div>
        </div>

        <!-- 所有商品菜单 -->
        <div class="goods-box" ref="goodsArea">
          <div
            class="goods-box-nav hidden-scroll"
            ref="navScrollRef"
            v-if="!search_keyword && tabActive !== 'plat' && tabActive !== 'com'"
          >
            <div
              class="goods-box-nav-item"
              :class="{
                'goods-box-nav-item--active': index == goodsTabActive,
                'goods-box-nav-item--disabled': item.name === 'self' && !current_user_info.uid,
              }"
              v-for="(item, index) in goodsTabList"
              :key="index"
              :ref="'nav' + index"
              @click="item.name === 'self' && !current_user_info.uid ? '' : goodsTabChange(index)"
            >
              {{ item.label }}
            </div>
          </div>
          <div class="goods-box-area">
            <div class="goods-box-area-title" v-if="tabActive !== 'recently' && tabActive !== 'service'">
              <div class="goods-area-num" v-if="search_keyword">共{{ total }}个搜索结果</div>
              <div class="goods-area-num" v-else>共{{ total }}个，其中{{ sale_total }}个可售</div>
              <div class="goods-area-check" v-if="!search_keyword && tabActive === 'goods'">
                <Checkbox @on-change="isShowChange" v-model="isShowGoods">仅显示可售商品</Checkbox>
              </div>
            </div>
            <!-- 类型商品数据 -->
            <Scroll
              :on-reach-bottom="total <= getCurrentListLength || tabActive === 'service' ? null : handleReachBottom"
              :distance-to-edge="4"
              :loading-text="loadingText"
              class="goods-source-box"
              :height="scrollHeight"
            >
              <template v-if="tabActive === 'recently' && (goods_list.length || loading)">
                <div v-for="(recent_item, recent_index) in goods_list" :key="'recent' + recent_index">
                  <div class="recent_time">{{ recent_item.sales_date }}</div>
                  <div
                    class="goods-source-item"
                    :class="{ 'no-add-item': !isCanAdd(item), 'has-add-item': isHadAdd(item) }"
                    v-for="(item, index) in recent_item.goods_sales_info"
                    :key="'goods' + index"
                    @click="checkedGoodsSource(item)"
                  >
                    <div class="source-item-left">
                      <img
                        @click.stop="GoodsNameJump(item.goods)"
                        class="goods-img"
                        :src="item.goods?.main_img | imageStyle"
                      />
                      <div class="source-item-l-info">
                        <div class="source-item-name">
                          <div class="name ecs ecs-2">{{ item.goods?.name }}</div>
                          <Tooltip
                            v-for="channel in getOutChannelList(item.goods)"
                            :key="channel.channel"
                            placement="top"
                            :content="channel.tooltip"
                          >
                            <img class="douyin-icon" :src="channel.icon" :alt="channel.name" />
                          </Tooltip>
                        </div>
                        <div class="source-item-price-box">
                          <div class="base-price">
                            ¥{{ Number(item?.goods?.price_section?.min)?.toFixed(2)
                            }}<span class="section-icon" v-if="isHasSection(item?.goods?.price_section)">起</span>
                          </div>
                          <div class="vip-price" v-if="is_rst && isVip">
                            <Tooltip theme="light" placement="top">
                              <div slot="content">
                                会员专享
                                <span style="color: red">
                                  ¥{{ Number(item?.goods?.vip_price_section?.min || 0).toFixed(2) }}</span
                                >
                              </div>
                              ¥{{ Number(item?.goods?.vip_price_section?.min || 0).toFixed(2) }}
                              <span class="section-icon" v-if="isHasSection(item?.vip_price_section)">起</span>
                              <img
                                src="https://img-sn01.rsjxx.com/image/2025/0616/171400_99926.png"
                                alt=""
                                class="rst-vip-icon"
                              />
                            </Tooltip>
                          </div>
                          <div class="not-store-price-tag" v-if="item.goods.is_recharge_buy === 'no'">
                            不可使用储值支付
                          </div>
                          <template v-if="isShowStorePrice(item.goods)">
                            <div class="store-price">
                              ¥{{ isShowStorePrice(item.goods)
                              }}<span class="section-icon" v-if="isHasSection(item?.goods?.stored_price_section)"
                                >起</span
                              >
                            </div>
                            <div class="store-price-tag">储</div>
                          </template>
                        </div>
                      </div>
                    </div>

                    <div class="source-item-right">
                      <div class="source-item-r-type">
                        <div class="r-type-tag">{{ item.goods?.goods_type_mini_text }}</div>
                        <div class="r-type-tag">{{ item.goods?.source_platform_text }}</div>
                        <div class="r-type-tag">销售{{ item.total_sales_count }}件</div>
                      </div>
                      <div class="source-item-r-stock">
                        <div class="current-stock" v-if="item.goods?.stock > 0">当前库存：{{ item.goods?.stock }}</div>
                        <div class="no-stock" v-else>无库存</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="pr10" v-if="!loading">
                  <Divider class="no-data-divider" v-if="isShowDivider" plain>没有更多数据</Divider>
                </div>
              </template>
              <service-content
                ref="serviceRef"
                v-else-if="tabActive === 'service' && (serviceList.length || loading)"
                :list="serviceList"
                :scrollTop="scrollTop"
                :add_goods="add_goods"
                :is_rst="is_rst"
                :is_vip="isVip"
                @addService="addService"
                @getIndex="getIndex"
              ></service-content>
              <template v-else-if="tabActive !== 'recently' && (goods_list.length || loading)">
                <div
                  class="goods-source-item"
                  :class="{ 'no-add-item': !isCanAdd(item), 'has-add-item': isHadAdd(item) }"
                  v-for="(item, index) in goods_list"
                  :key="'goods' + index"
                  @click="checkedGoodsSource(item)"
                >
                  <div class="source-item-left">
                    <img class="goods-img" @click.stop="GoodsNameJump(item)" :src="item.main_img | imageStyle" />
                    <div class="source-item-l-info">
                      <div class="source-item-name">
                        <div class="name ecs ecs-2">{{ item.name }}</div>
                        <Tooltip
                          v-for="channel in getOutChannelList(item)"
                          :key="channel.channel"
                          placement="top"
                          :content="channel.tooltip"
                        >
                          <img class="douyin-icon" :src="channel.icon" :alt="channel.name" />
                        </Tooltip>
                      </div>
                      <div class="source-item-price-box">
                        <div class="base-price">
                          ¥{{ Number(item?.price_section?.min || 0).toFixed(2) }}
                          <span class="section-icon" v-if="isHasSection(item?.price_section)">起</span>
                        </div>
                        <div class="vip-price" v-if="is_rst && isVip">
                          <Tooltip theme="light" placement="top">
                            <div slot="content">
                              会员专享
                              <span style="color: red">
                                ¥{{ Number(item?.vip_price_section?.min || 0).toFixed(2) }}</span
                              >
                            </div>
                            ¥{{ Number(item?.vip_price_section?.min || 0).toFixed(2) }}
                            <span class="section-icon" v-if="isHasSection(item?.vip_price_section)">起</span>
                            <img
                              src="https://img-sn01.rsjxx.com/image/2025/0616/171400_99926.png"
                              alt=""
                              class="rst-vip-icon"
                            />
                          </Tooltip>
                        </div>
                        <div class="not-store-price-tag" v-if="item.is_recharge_buy === 'no'">不可使用储值支付</div>
                        <template v-if="isShowStorePrice(item) && !is_rst">
                          <div class="store-price">
                            ¥{{ isShowStorePrice(item) }}
                            <span class="section-icon" v-if="isHasSection(item?.stored_price_section)">起</span>
                          </div>
                          <div class="store-price-tag">储</div>
                        </template>
                      </div>
                    </div>
                  </div>

                  <div class="source-item-right">
                    <div class="source-item-r-type">
                      <div class="r-type-tag">{{ item.goods_type_mini_text }}</div>
                      <div class="r-type-tag">{{ item.source_platform_text }}</div>
                    </div>
                    <div class="source-goods-template" @click="jumpToTemplate(item)" v-if="isGoodsTemplateOff(item)">
                      商品模板未上架
                    </div>

                    <div class="source-item-r-stock" v-else>
                      <div v-if="isActivity">
                        <div class="no-stock" v-if="item.goods.show_status === '1'">商品未上架</div>
                        <div class="no-stock" v-else-if="item.goods.show_status === '2'">商品不在售卖范围内</div>
                        <div class="no-stock" v-else-if="item.goods.show_status === '3' || item.goods.stock == 0">
                          无库存
                        </div>
                        <div class="current-stock" v-else>当前库存：{{ item.goods.stock }}</div>
                      </div>

                      <div v-else>
                        <div class="current-stock" v-if="item.stock > 0">当前库存：{{ item.stock }}</div>
                        <div class="no-stock" v-else>无库存</div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="pr10" v-if="!loading">
                  <Divider class="no-data-divider" v-if="isShowDivider" plain>没有更多数据</Divider>
                </div>
              </template>
              <div class="empty-img-box" v-else>
                <img class="empty-img" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0722/180054_55080.png" />
              </div>
            </Scroll>
          </div>
        </div>
      </div>
    </div>
    <div class="car-box-wrapper" ref="carBoxRef">
      <div class="car-top">
        <div class="title">消费明细（{{ add_goods.length }}）</div>
        <Poptip confirm width="180" title="确定清空所有商品?" @on-ok="clearAllGoods">
          <div class="car-clear-all">清空</div>
        </Poptip>
      </div>

      <Scroll class="car-item-box" :height="carScrollHeight">
        <div class="car-item-box-content" ref="carScrollRef">
          <div class="car-item-box-goods" v-if="add_goods.length">
            <div class="car-item" v-for="(item, index) in add_goods" :key="index">
              <div class="delete-icon-box">
                <Tooltip content="移除" placement="top">
                  <img
                    class="delete-icon"
                    @click="deleteAddGoods(index)"
                    src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0722/102709_78163.png"
                  />
                </Tooltip>
              </div>
              <div class="car-item-left">
                <div class="car-item-l-name-box">
                  <div class="car-item-l-name ecs ecs-2">{{ item.name }}</div>
                  <div class="car-item-l-tag-box">
                    <div class="car-item-l-tag" v-if="item.serv_type_text">{{ item.serv_type_text }}</div>
                    <div class="car-item-l-tag" v-if="item.goods_type_mini_text">{{ item.goods_type_mini_text }}</div>
                    <div class="car-item-l-tag" v-if="item.source_platform_text">{{ item.source_platform_text }}</div>
                  </div>
                </div>
                <img
                  v-if="item.is_service"
                  class="service-tag"
                  src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0919/150829_59582.png"
                />
                <div v-else class="car-item-l-info ecs ecs-2">
                  {{ item.spec }}
                </div>
              </div>

              <div class="car-item-right">
                <div class="car-item-count">
                  <InputNumber
                    class="custom-input-number"
                    :min="1"
                    v-model="item.num"
                    :precision="0"
                    controls-outside
                    autocomplete="off"
                  />
                </div>

                <div class="car-item-price-box">
                  <div class="car-item-price">
                    <span class="price-icon">¥</span>
                    <span>{{ calcCarPirce(item.num, item.price) }}</span>
                  </div>
                  <div class="car-item-vip-price" v-if="is_rst && isVip">
                    <Tooltip theme="light" placement="top">
                      <div slot="content">
                        会员专享
                        <span style="color: red"> ¥{{ calcCarPirce(item.num, item.vip_price) }}</span>
                      </div>
                      <span class="price-icon">¥</span>
                      <span>{{ calcCarPirce(item.num, item.vip_price) }}</span>
                      <img
                        src="https://img-sn01.rsjxx.com/image/2025/0616/171400_99926.png"
                        alt=""
                        class="rst-vip-icon"
                      />
                    </Tooltip>
                  </div>
                  <div
                    class="car-item-store-price"
                    v-if="item.is_recharge_buy == 'yes' && item.stored_price && !is_rst"
                  >
                    <span class="price-icon">¥</span>
                    <span>{{ calcCarPirce(item.num, item.stored_price) }}</span>
                    <div class="store-price-tag">储</div>
                  </div>
                  <div v-if="item.is_recharge_buy === 'no'">
                    <div class="not-store-price-tag">不可使用储值支付</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="empty-img-box car-item-box-goods">
            <img class="empty-img" src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/0722/180054_55080.png" />
          </div>
          <div class="customer-box" ref="customerRef">
            <Form :label-width="74" :model="formData" ref="formData" :rules="formDataRules">
              <FormItem label="参与活动:" v-if="coupon_pay === '1' && !is_rst">
                <Select
                  style="width: 50%; padding-right: 5px"
                  transfer
                  transfer-class-name="activie_select"
                  v-model="activie_ticket"
                  clearable
                  :not-found-text="activeLoading ? '' : '无匹配数据'"
                  :loading="activeLoading"
                  @on-open-change="onActiveOpenChange($event, true)"
                >
                  <Option v-for="item in activie_ticket_list" :value="item.id" :label="item.name" :key="item.id">
                    <div class="activie_options">
                      <div class="activie_info">
                        <div class="activie_name">{{ item.name }}</div>
                        <div class="activie_tag">{{ item.goods_type_desc }}</div>
                      </div>
                      <div class="active-desc">购买【{{ item.goods_name }}】可获得优惠券</div>
                    </div>
                  </Option>
                </Select>
                <div v-if="activie_ticket" class="active-desc">{{ active_desc }}</div>
              </FormItem>

              <FormItem label="配送方式:" v-if="!is_rst">
                <RadioGroup v-model="distributionType">
                  <Radio label="0">无需配送</Radio>
                  <Radio label="1" :disabled="!isNeedLogistics">快递发货</Radio>
                </RadioGroup>
              </FormItem>
              <div v-if="distributionType === '1'">
                <FormItem label="收件姓名:" class="mb15" :prop="distributionType === '1' ? 'name' : ''">
                  <Input
                    style="width: 50%; padding-right: 5px"
                    v-model="formData.name"
                    placeholder="收件人姓名"
                  ></Input>
                </FormItem>

                <FormItem label="收件手机:" class="mb15" :prop="distributionType === '1' ? 'mobile' : ''">
                  <Input
                    style="width: 50%; padding-right: 5px"
                    v-model.trim="formData.mobile"
                    placeholder="收件人手机号"
                    :maxlength="11"
                  />
                </FormItem>

                <FormItem label="收件地址" class="mb15" :prop="distributionType === '1' ? 'selectedAddress' : ''">
                  <div class="flex">
                    <div class="address flex-1">
                      <el-cascader
                        v-model="formData.selectedAddress"
                        :options="options"
                        clearable
                        placeholder="省市区"
                        size="small"
                        popper-class="address-com"
                        style="width: 100%"
                        @change="regionChange"
                        class="address-input"
                      >
                      </el-cascader>
                    </div>
                    <div class="flex-1 ml10">
                      <Input v-model.trim="formData.consignee_info.address.detail" placeholder="详细地址"></Input>
                    </div>
                  </div>
                </FormItem>
              </div>
            </Form>
          </div>
        </div>
      </Scroll>

      <div class="fixed-btn-box" ref="fixedBtnRef">
        <div class="fixed-btn-left">
          <div class="fixed-btn-num">
            <span class="num">共{{ addGoodsCalc.num }}件，</span>总计：
          </div>
          <div class="fixed-btn-price">
            <div class="fixed-total-price">
              <span>¥</span>
              <span class="store-price">{{ Number(addGoodsCalc.price || 0).toFixed(2) }}</span>
            </div>
            <div class="fixed-vip-price" v-if="is_rst && isVip">
              <Tooltip theme="light" placement="top">
                <div slot="content">
                  会员专享
                  <span style="color: red"> ¥{{ Number(addGoodsCalc.vip_price || 0).toFixed(2) }}</span>
                </div>
                <span class="price-icon">¥</span>
                <span>{{ Number(addGoodsCalc.vip_price || 0).toFixed(2) }}</span>
                <img src="https://img-sn01.rsjxx.com/image/2025/0616/171400_99926.png" alt="" class="rst-vip-icon" />
              </Tooltip>
            </div>
            <div class="fixed-store-price" v-if="isHasRechargeBuyCalc && !is_rst">
              <span>¥</span>
              <span class="store-price">{{ Number(addGoodsCalc.stored_price || 0).toFixed(2) }}</span>
              <div class="store-price-tag">储</div>
            </div>
          </div>
        </div>
        <!--        <div class="vip-tips flex-1 ml-12" style="color: red" v-if="is_980_Vip && is_rst">-->
        <!--          注：980专享服务需要选择980会员优惠活动-->
        <!--        </div>-->
        <div class="fixed-btn-right">
          <Button type="default" @click="cancel">取消</Button>
          <Button type="primary" style="margin-left: 10px" :loading="submitLoading" @click="submit">确认收款</Button>
        </div>
      </div>
    </div>
    <!-- 具有多个sku的商品时，弹窗  -->
    <EntityOrderDialog
      :visible.sync="entityOrderVisible"
      :isActivity="isActivity"
      v-model="entityOrderObject"
      @checkedList="checkedList"
      @close="closeEntityOrderDialog"
      :is_rst="is_rst"
    ></EntityOrderDialog>

    <!-- 创建用户 -->
    <create-user-modal
      :showCreateInfo="optionChange"
      :source-list="sourceList"
      :visible.sync="consumerVisibleDia"
      :level-list="levelList"
      :name="creatName"
    ></create-user-modal>

    <!-- v2版本支持优惠券的支付弹窗 -->
    <k-pay-dialog v-model="payVisible" :order_id="order_id" :is_rst="is_rst"></k-pay-dialog>
    <rst-pay-dialog v-model="rstPayVisible" :order-id="order_id"></rst-pay-dialog>
    <choose-tip-modal v-model="chooseTipVisible" @success="confirmSubmit"></choose-tip-modal>

    <!--未通过主体验证 拦截收款弹窗-->
    <auth-warning-modal ref="authWarningRef"></auth-warning-modal>
  </div>
</template>

<script>
import { getClinicid } from '@/libs/runtime';
import S from '@/libs/util';
import { $operator } from '@/libs/operation';
import { CodeToText, regionData, TextToCode } from '@/libs/chinaMap';
import cloneDeep from 'lodash.clonedeep';
import EntityOrderDialog from './components/entity-order-dialog.vue';
// import PayModal from './components/payModal.vue';
import ChooseTipModal from './components/ChooseTipModal.vue';
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';
import debounce from 'lodash.debounce';
import KPayDialog from '@/components/k-pay-dialog/index.vue';
import serviceContent from './components/serviceContent.vue';
import { isRstClinic } from '@/libs/runtime';
import RstPayDialog from '@/components/k-pay-dialog/rst-pay-dialog.vue';
import AuthWarningModal from '@/components/AuthWarning/AuthWarningModal.vue';

const init_form_data = {
  name: '', // 收件姓名
  mobile: '', // 收件手机
  selectedAddress: [], // 收件地址
  consignee_info: {
    consignee: '',
    mobile: '',
    address: {
      prov: {
        name: '',
        code: '',
      },
      city: {
        name: '',
        code: '',
      },
      county: {
        name: '',
        code: '',
      },
      detail: '',
    },
  },
};

const regRole = tel => {
  let flag;
  let reg = /^1[3456789]\d{9}$/;
  flag = reg.test(tel);
  return flag;
};
const phoneValidate = (rule, value, callback) => {
  if (!value) {
    callback(new Error('请输入收件人手机号'));
  }
  if (!regRole(value)) {
    callback(new Error('请输入正确的手机号'));
  } else {
    callback();
  }
};

const addressValidate = (rule, value, callback) => {
  let detail = rule.detail();
  if (value[0] == undefined) {
    callback(new Error('请选择收件地址'));
  } else if (detail === '') {
    callback(new Error('请输入详细地址'));
  } else {
    callback();
  }
};

export default {
  name: '',
  components: {
    AuthWarningModal,
    KPayDialog,
    ChooseTipModal,
    EntityOrderDialog,
    CreateUserModal,
    serviceContent,
    RstPayDialog,
  },
  mixins: [],
  data() {
    return {
      // 外部渠道配置
      outChannelConfig: {
        1: {
          name: '美团',
          icon: 'https://static.rsjxx.com/image/2025/0619/144358_21725.png',
        },
        2: {
          name: '抖音',
          icon: 'https://static.rsjxx.com/image/2025/0619/144358_49841.png',
        },
      },
      // 保持向后兼容
      meituan: 'https://static.rsjxx.com/image/2025/0619/144358_21725.png',
      douyin: 'https://static.rsjxx.com/image/2025/0619/144358_49841.png',
      loading: false,
      isRstClinic,
      page: 1,
      pageSize: 20,
      tabPaneLabel: (h, item) => {
        return h('div', { style: { position: 'relative' } }, [
          h('div', item.label),
          item.img
            ? h('img', {
                attrs: {
                  src: item.img,
                },
                style: {
                  width: '20px',
                  height: '12px',
                  position: 'absolute',
                  right: '-19px',
                  top: '-6px',
                },
              })
            : '',
        ]);
      },
      tabList: [
        { label: '商品', name: 'goods', sort: 1 },
        { label: '服务', name: 'service', sort: 2 },
        { label: '最近售卖', name: 'recently', sort: 3 },
      ],
      initGoodsTabList: [
        { label: '实物', name: 'physical', type: '10' },
        { label: '虚拟', name: 'virtual', type: '15' },
        { label: '套餐', name: 'package', type: '20' },
        { label: '通兑券', name: 'coupons', type: '25' },
        // 榕树堂
        { label: '实物', name: 'coupons', type: '40' },
        { label: '虚拟', name: 'coupons', type: '30' },
        { label: '套餐', name: 'coupons', type: '35' },
        { label: '通兑券', name: 'coupons', type: '45' },
      ],
      init_goods_tab_list: [
        { label: '实物', name: 'physical', type: '10' },
        { label: '虚拟', name: 'virtual', type: '15' },
        { label: '套餐', name: 'package', type: '20' },
        { label: '通兑券', name: 'coupons', type: '25' },
        // 榕树堂
        { label: '实物', name: 'coupons', type: '40' },
        { label: '虚拟', name: 'coupons', type: '30' },
        { label: '套餐', name: 'coupons', type: '35' },
        { label: '通兑券', name: 'coupons', type: '45' },
      ],
      sale_tab_list: [
        { label: '全部', name: 'all' },
        { label: '本人', name: 'self' },
      ],
      service_tab_list: [],
      tabActive: 'goods',
      options: regionData,
      userList: [], // 用户数据
      mobile: '', // 用户手机号
      nickname: '', //用户姓名
      uid: '', // 选中的用户,如果为自建用户，则为空
      userAddress: {}, // 获取用户的收获地址

      // 创建用户
      name: '',
      creatName: '',
      consumerVisibleDia: false, // 创建用户的弹窗
      sourceList: [],
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],

      // 搜索商品
      goods: '',

      // 参与活动
      activie_ticket_list: [],
      activie_ticket: '',
      activeLoading: false,

      // 未选择活动提示
      chooseTipVisible: false,

      coupon_pay: '1', // 1：可用使用优惠，2：不可以使用优惠

      scrollHeight: 300,
      carScrollHeight: 300,
      goodsTabActive: 0,

      formData: { ...init_form_data },
      formDataRules: {
        name: [{ required: true, message: '请输入收件人姓名', trigger: 'change' }],
        mobile: [{ required: true, validator: phoneValidate, trigger: 'change' }],
        selectedAddress: [
          {
            required: true,
            validator: addressValidate,
            detail: () => {
              return this.formData.consignee_info.address.detail; /*为外部提供值*/
            },
            trigger: 'blur',
          },
        ],
      }, // 表单校验
      distributionType: '0', // 配送方式
      add_goods: [], // 表格加入的商品
      isShowGoods: true,

      current_user_info: {}, // 当前用户

      car_lsit: [], // 选中的商品的
      goods_list: [], // 服务商品list
      payVisible: false, // 支付弹窗显示的标识
      rstPayVisible: false, // 收款弹窗显示的标识
      entityOrderVisible: false, // 获取实体订单的弹窗，一个spu对应多个sku时，显示弹窗
      entityOrderObject: {}, // 一个spu对应的多个sku
      order_id: '', // 订单id,
      total: 0,
      sale_total: 0,
      search_keyword: '',
      serviceList: [],
      scrollTop: 0,
      submitLoading: false,
    };
  },
  computed: {
    // 获取外部渠道信息列表（用于循环渲染）
    getOutChannelList() {
      return item => {
        const outSalesGoodsNames = item?.goods?.out_sales_goods_names || item?.out_sales_goods_names;
        if (!outSalesGoodsNames) return [];

        return outSalesGoodsNames
          .filter(goods => this.outChannelConfig[goods.out_sales_channel])
          .map(goods => ({
            channel: goods.out_sales_channel,
            name: this.outChannelConfig[goods.out_sales_channel].name,
            icon: this.outChannelConfig[goods.out_sales_channel].icon,
            tooltip: goods.out_goods_name,
          }));
      };
    },

    // 向后兼容
    is_rst() {
      return isRstClinic();
    },
    getVipIcon() {
      return vipInfo => {
        // 980会员与榕粉会员合并使用相同icon
        let isVip = vipInfo?.findIndex(item => item.user_type === '1' || item.user_type === '4') > -1;
        if (isVip) {
          return require(`@/assets/image/order/vip_980.png`);
        }
        let is9800Vip = vipInfo?.findIndex(item => item.user_type === '3') > -1;
        if (is9800Vip) {
          return require(`@/assets/image/order/vip_9800.png`);
        }

        return '';
      };
    },
    getVipBorderColor() {
      return vipInfo => {
        return vipInfo?.findIndex(item => item.user_type === '3') > -1 ? '#FAD88F' : '#B0C3DD';
      };
    },
    is_980_Vip() {
      return this.current_user_info.vip_info?.findIndex(item => item.user_type === '1') > -1;
    },
    // 980会员 || 298榕粉属于会员
    isVip() {
      return this.current_user_info.vip_info?.findIndex(item => item.user_type === '1' || item.user_type === '4') > -1;
    },
    // 当前数据的长度
    getCurrentListLength() {
      return this.goods_list.length;
      // 最近售卖取的是最外层天为维度，如果以goods_sales_info为维度，则用下面的逻辑
      // if (this.tabActive == 'recently') {
      //   let recently_length = 0;
      //   this.goods_list.forEach(item => {
      //     recently_length = recently_length + item?.goods_sales_info?.length;
      //   });
      //   return recently_length;
      // } else {
      //   return this.goods_list.length;
      // }
    },
    // 是否展示没有更多数据
    isShowDivider() {
      return this.getCurrentListLength >= this.total && this.total != 0;
    },
    calcCarPirce() {
      return (num, price) => {
        return $operator.multiply(Number(num || 0), Number(price || 0))?.toFixed(2);
      };
    },
    loadingText() {
      return this.total > this.getCurrentListLength ? '' : '';
    },
    // 暂时留着，当选择用户盒子高度不一致时，可以利用当前方法调整高度
    getGoodsBoxStyle() {
      return {
        height: this.current_user_info.uid ? 'calc(100vh - 230px)' : 'calc(100vh - 230px)',
      };
    },
    isHasRechargeBuyCalc() {
      let flag = false;
      if (this.add_goods.length) {
        flag = this.add_goods.every(item => item.is_recharge_buy === 'yes' || item.is_service);
      }
      return flag;
    },
    // 商品总计算
    addGoodsCalc() {
      let num = 0;
      let price = 0;
      let vip_price = 0;
      let stored_price = 0;
      this.add_goods.forEach(item => {
        num = $operator.add(num, Number(item.num || 0));

        let item_price = $operator.multiply(Number(item.price || 0), Number(item.num || 0));
        price = $operator.add(price, item_price);
        console.log('=>(createNew.vue:824) item.vip_price', item.vip_price);
        let item_vip_price = $operator.multiply(Number(item.vip_price || 0), Number(item.num || 0));
        vip_price = $operator.add(vip_price, item_vip_price);

        let current_stored_price =
          (item.is_recharge_buy == 'yes' && item.stored_price == 0) || item.is_service ? item.price : item.stored_price;
        let item_stored_price = $operator.multiply(Number(current_stored_price || 0), Number(item.num || 0));
        stored_price = $operator.add(stored_price, item_stored_price);
      });
      return {
        num,
        price,
        vip_price,
        stored_price,
      };
    },
    // 是否是活动
    isActivity() {
      if (this.tabActive === 'plat' || this.tabActive === 'com') {
        return true;
      } else {
        return false;
      }
    },
    // 是否是活动的未上架的模版
    isGoodsTemplateOff() {
      return item => {
        if (this.tabActive === 'plat' || this.tabActive === 'com') {
          if (Object.keys(item.goods || {}).length == 0) {
            return true;
          } else {
            return false;
          }
        }
        return false;
      };
    },
    // 诊所商品储值价
    isShowStorePrice() {
      return item => {
        if (item.is_recharge_buy === 'yes' && item.stored_price_section?.min > 0) {
          return Number(item.stored_price_section?.min || 0)?.toFixed(2);
        } else {
          return '';
        }
      };
    },
    // 是否存在价格区间(是否展示起),只有存在区间才展示
    isHasSection() {
      return section_obj => {
        if (section_obj?.min && section_obj?.max) {
          return true;
        } else {
          return false;
        }
      };
    },
    // 展示的活动描述
    active_desc() {
      let chooseActiveItem = this.activie_ticket_list.find(item => item.id === this.activie_ticket);
      return chooseActiveItem?.goods_name ? `购买【${chooseActiveItem?.goods_name}】可获得优惠券` : '';
    },
    goods_type() {
      return this.goodsTabList[this.goodsTabActive].type;
    },
    // 是否展示物流
    isNeedLogistics() {
      let flag = false;
      this.add_goods.some(item => {
        if (item.goods_type === '10') {
          flag = true;
          return true;
        }

        // 如果是套餐，套餐包含实物，可以勾选
        if (item.goods_type === '20') {
          item.tc_infos?.some(tc_item => {
            if (tc_item.goods_type === '10') {
              flag = true;
              return true;
            }
          });
        }
      });
      if (!flag) {
        this.distributionType = '0';
      }
      return flag;
    },
    // 显示商品类型
    goodsTabList() {
      if (this.tabActive === 'goods' && isRstClinic()) {
        return this.initGoodsTabList.filter(item => ['30', '35', '40', '45'].includes(item.type));
      }
      if (this.tabActive === 'goods' && !isRstClinic()) {
        return this.initGoodsTabList.filter(item => ['10', '15', '20', '25'].includes(item.type));
      }
      return this.initGoodsTabList;
    },
    // 显示商品类型
    goods_tab_list() {
      if (isRstClinic()) {
        console.log(this.init_goods_tab_list.filter(item => ['30', '35', '40', '45'].includes(item.type)));

        return this.init_goods_tab_list.filter(item => ['30', '35', '40', '45'].includes(item.type));
      }
      return this.init_goods_tab_list.filter(item => ['10', '15', '20', '25'].includes(item.type));
    },
  },
  watch: {
    activie_ticket() {
      this.getCarScrollHeight();
    },
    distributionType(val) {
      if (val == 1) {
        this.scrollToValidFieldPosition();
      }
      // this.getCarScrollHeight();
    },
    search_keyword(val) {
      if (val) {
        // this.isShowGoods = true;
      }
    },
  },
  created() {
    // rst默认不勾选
    if (this.is_rst) {
      this.isShowGoods = false;
    }
  },
  mounted() {
    this.getActivityCheckclinic();
    this.getArrivalOptions();
    this.goodsTabChange(this.goodsTabActive);
    this.getActivitylist();
    this.getActivitycomlist();
    this.getScrolllHeight();
    this.getCarScrollHeight();
    window.addEventListener('resize', this.getScrolllHeight);
    window.addEventListener('resize', this.getCarScrollHeight);

    this.$nextTick(() => {
      // 注册监听服务的滚动
      let overflowY = this.$el.getElementsByClassName('ivu-scroll-container')[0];
      overflowY.addEventListener('scroll', this.serviceScroll);
    });
    const uid = this.$route.query.uid;
    if (uid) {
      this.handleDefaultUID(uid);
    }
  },
  methods: {
    // 选中指定的用户
    handleDefaultUID(uid = '') {
      this.getUserList({ uid }).then(res => {
        this.handleSelect(res?.users[0] || {});
      });
    },
    getIndex(index) {
      if (this.tabActive === 'service') {
        this.goodsTabActive = index;
        let ref = `nav${index}`;
        this.$refs[ref][0].scrollIntoView({ behavior: 'smooth' });
      }
    },
    serviceScroll() {
      if (this.tabActive === 'service') {
        let overflowY = this.$el.getElementsByClassName('ivu-scroll-container')[0];
        this.scrollTop = overflowY.scrollTop;
        this.$refs.serviceRef.handleNodeScroll();
      }
    },

    // 选中的服务
    addService(item) {
      this.addGoods(item, true);
    },
    cancel() {
      const { from } = this.$route.query;
      if (from) {
        this.$router.push({
          path: from,
        });
        return;
      }
      this.$router.push({
        path: '/trade/order/list',
      });
    },
    // 活动的商品名称支持跳转
    GoodsNameJump(item) {
      let routeUrl = {};
      if (this.isActivity) {
        if (Object.keys(item.goods || {}).length) {
          routeUrl = this.$router.resolve({
            path: '/goods/item/edit',
            query: {
              id: item.goods.id,
            },
          });
        } else {
          routeUrl = this.$router.resolve({
            path: '/goods/warehouse/list',
            query: {
              name: item.name,
            },
          });
        }
      } else {
        routeUrl = this.$router.resolve({
          path: '/goods/item/edit',
          query: {
            id: item.id,
          },
        });
      }
      window.open(routeUrl.href, '_blank');
    },
    // 跳转模板
    jumpToTemplate(item) {
      this.$router.push({
        path: '/goods/warehouse/list',
        query: {
          name: item.name,
        },
      });
    },
    closeEntityOrderDialog() {
      this.entityOrderObject = {};
    },
    tabClick(val) {
      this.getScrolllHeight();
      this.goods_list = [];
      this.page = 1;
      this.pageSize = 20;
      this.goodsTabActive = 0;
      // 最近售卖
      if (val === 'recently') {
        this.initGoodsTabList = this.sale_tab_list;
        this.getSalesList();
      } else if (val === 'service') {
        this.getSalesgoodsservice();
      } else if (val === 'goods') {
        this.initGoodsTabList = this.goods_tab_list;
        this.getCureRecordList();
      } else if (val === 'plat') {
        this.getActivitylist();
      } else if (val === 'com') {
        this.getActivitycomlist();
      }
    },
    searchChange: debounce(function () {
      this.goods_list = [];
      this.page = 1;
      this.total = 0;
      this.sale_total = 0;
      if (this.tabActive === 'goods') {
        this.getCureRecordList('1');
      } else if (this.tabActive === 'service') {
        this.getSalesgoodsservice();
      } else if (this.tabActive === 'recently') {
        this.getSalesList();
      } else if (this.tabActive === 'plat') {
        this.getActivitylist();
      } else if (this.tabActive === 'com') {
        this.getActivitycomlist();
      }
    }, 200),

    isShowChange() {
      this.page = 1;
      this.pageSize = 20;
      this.goods_list = [];

      if (this.tabActive === 'goods') {
        this.getCureRecordList('1');
      } else if (this.tabActive === 'recently') {
        this.getSalesList();
      } else if (this.tabActive === 'plat') {
        this.getActivitylist();
      } else if (this.tabActive === 'com') {
        this.getActivitycomlist();
      }
    },
    // 提交之前对页面必填数据判断
    isSubmit() {
      if (!this.current_user_info.uid) {
        this.$Message.error('请选择下单用户');
        return false;
      }

      if (!this.add_goods.length) {
        this.$Message.error('购买商品不可为空');
        return false;
      }

      let flag = this.add_goods.some(item => {
        if (item.num === 0 || item.num === null) {
          this.$Message.error(`${item.name}的数量最小为1`);
          return true;
        }
      });

      if (flag) {
        return false;
      }
      return true;
    },
    // 消费明细区域滚动到配送提示区域
    scrollToValidFieldPosition() {
      this.$nextTick(() => {
        let overflowY = this.$el.getElementsByClassName('ivu-scroll-container')[1];
        let contentOverflowY = this.$el.getElementsByClassName('ivu-scroll-content')[1];
        let offsetHeight = contentOverflowY?.offsetHeight;
        if (!overflowY) {
          return;
        }
        overflowY.scrollTo({ top: offsetHeight, behavior: 'smooth' });
      });
    },
    submit() {
      if (this.isSubmit()) {
        this.submitLoading = true;
        this.$refs['formData'].validate(valid => {
          if (valid) {
            if (!this.activie_ticket && this.coupon_pay === '1') {
              this.activeChange(true, true).then(list => {
                // 如果检测到有可以参与赠券活动的商品，但没有选择参与，则弹窗提醒
                if (list.length && !this.activie_ticket) {
                  this.chooseTipVisible = true;
                } else {
                  this.confirmSubmit();
                }
              });
            } else {
              this.confirmSubmit();
            }
          } else {
            this.scrollToValidFieldPosition();
            this.submitLoading = false;
          }
        });
      }
    },
    deleteAddGoods(index) {
      this.add_goods.splice(index, 1);
      this.activeChange(true);
    },

    confirmSubmit: debounce(function () {
      // 支付前进行主体认证校验 只对仅数字化诊所生效
      this.submitLoading = true;

      this.$refs.authWarningRef
        .verify()
        .then(res => {
          console.log('🚀 ~ file:createNew method: line:1127 -----', res);
          let params = {
            version: 2,
            mobile: this.mobile,
            name: this.nickname,
            uid: this.uid,
            listof: this.handleListof().listof,
            service_of: this.handleListof().serviceof,
            need_address: this.distributionType, // 是否需要配送
            consignee_address: this.getJsonAddress(),
            activity_id: this.activie_ticket, // 参与活动id
          };
          this.$api
            .orderCreate(params)
            .then(res => {
              this.order_id = res.id;
              this.uid = res.uid;
              if (this.is_rst) {
                this.rstPayVisible = true;
              } else {
                this.payVisible = true;
              }
            })
            .finally(() => {
              this.submitLoading = false;
            });
        })
        .catch(res => {
          console.log('🚀 ~ createNew.vue:1153 ~ submit ~ res=>', res);
        });
    }, 400),
    // 组合JSON收件地址，包括收件人，手机号码
    getJsonAddress() {
      let address = {
        consignee: this.formData.name,
        mobile: this.formData.mobile,
        location: {
          province: this.formData.consignee_info.address.prov,
          city: this.formData.consignee_info.address.city,
          county: this.formData.consignee_info.address.county,
          other: this.formData.consignee_info.address.detail,
        },
      };
      return JSON.stringify(address);
    },
    // 处理要提交的商品数据-listof, 服务数据-serviceof
    handleListof() {
      let listof = {};
      let serviceof = {};
      this.add_goods.forEach(item => {
        if (item.is_service) {
          serviceof = { ...serviceof, ...{ [item.id]: item.num } };
        } else {
          if (['15', '20', '25', '30', '35'].includes(item.goods_type)) {
            listof = { ...listof, ...{ [item.goods_attr_ids[0]]: item.num } };
          } else {
            listof = { ...listof, ...{ [item.id]: item.num } };
          }
        }
      });
      return {
        listof: JSON.stringify(listof),
        serviceof: JSON.stringify(serviceof),
      };
    },

    checkedList(list, name, type = '') {
      list.forEach(item => {
        if (!this.isHasGood(item.id)) {
          let obj = {
            ...item,
            num: 1,
            name: name,
            spec: item.spec,
          };
          if (type === 'entity_modal') {
            obj = {
              ...obj,
              goods_type: item.item_obj.goods_type,
              is_recharge_buy: item.item_obj.is_recharge_buy,
              source_platform_text: item.item_obj.source_platform_text,
              goods_type_mini_text: item.item_obj.goods_type_mini_text,
            };
          }
          this.add_goods.unshift(obj);
          this.activeChange(true);
        } else {
          this.$Message.error(`${name}[${item.spec}]已经存在,不可重复添加`);
        }
      });
    },
    // 已经添加
    isHadAdd(item) {
      let isAdd = false;
      if (this.isCanAdd(item)) {
        let current_item = this.tabActive == 'goods' ? item : item.goods;
        this.add_goods.some(add_item => {
          if (add_item.goods_type == '15' || add_item.goods_type == '25' || add_item.goods_type == '20') {
            if (current_item?.goods_attr_ids[0] == add_item?.goods_attr_ids[0]) {
              isAdd = true;
              return true;
            }
          } else {
            // ids合集里面存在number和string类型，两种都做判断
            if (
              current_item.goods_attr_ids.includes(Number(add_item?.id)) ||
              current_item.goods_attr_ids.includes(String(add_item?.id))
            ) {
              isAdd = true;
              return true;
            }
          }
        });
      }
      return isAdd;
    },
    // 不可添加
    isCanAdd(item) {
      // 模板和无库存的不可点击
      if (this.isGoodsTemplateOff(item)) {
        return false;
      }

      // 所有商品，最近售卖无库存阻拦
      if (this.tabActive == 'goods' && Number(item.stock) == 0) {
        return false;
      }

      // 建单活动无库存阻拦
      if (this.tabActive == 'recently' && item.goods.stock == 0) {
        return false;
      }

      if (this.isActivity && item.goods.show_status != 4) {
        return false;
      }

      return true;
    },
    checkedGoodsSource(item) {
      if (this.isCanAdd(item)) {
        // 最近售卖的商品数据格式
        let item_obj = this.tabActive === 'recently' ? item.goods : item;
        this.goodsOptionChange(item_obj);
      }
    },
    deleteUserInfo() {
      this.current_user_info = {};
      this.name = '';
      this.mobile = '';
      this.nickname = '';
      this.uid = '';
      this.balance = '';
      this.getScrolllHeight();

      if (this.initGoodsTabList[this.goodsTabActive]?.name === 'self') {
        this.goodsTabActive = 0;
        this.goodsTabChange(this.goodsTabActive);
      }
    },
    clearAllGoods() {
      this.add_goods = [];
      this.activeChange(true);
    },
    // 获取已选商品的id合集
    getGoodsIds() {
      let ids = [];
      let id_key = {
        10: 'goodsid',
        15: 'id',
        25: 'id',
        20: 'id',
      };
      this.add_goods.forEach(good_item => {
        if (id_key[good_item.goods_type]) {
          ids.push(good_item[id_key[good_item.goods_type]]);
        }
      });
      return ids || [];
    },
    onActiveOpenChange(val, isSelect = false) {
      if (val && !this.activie_ticket) {
        this.activeChange(true, isSelect);
      }
    },
    activeChange(val, isSelect = false) {
      if (val && this.coupon_pay === '1') {
        let params = {
          goods_ids: this.getGoodsIds(),
        };
        this.activeLoading = true;
        return this.$api
          .getGoodsBindactivity(params)
          .then(res => {
            this.activie_ticket_list = res.list;
            if (!isSelect) {
              if (this.activie_ticket) {
                let isHasIndex = this.activie_ticket_list.findIndex(item => item.id === this.activie_ticket);
                if (isHasIndex < 0) {
                  this.activie_ticket = '';
                }
              } else {
                if (this.activie_ticket_list?.length) {
                  this.activie_ticket = this.activie_ticket_list[0]?.id;
                }
              }
            }
            return new Promise(resolve => resolve(res.list));
          })
          .finally(() => (this.activeLoading = false));
      }
    },
    // 地址
    regionChange(address) {
      if (address.length) {
        const prov = {
          name: CodeToText[address[0]],
          code: address[0],
        };
        const city = {
          name: CodeToText[address[1]],
          code: address[1],
        };
        const county = {
          name: CodeToText[address[2]],
          code: address[2],
        };
        this.formData.consignee_info.address.prov = prov;
        this.formData.consignee_info.address.city = city;
        this.formData.consignee_info.address.county = county;
      } else {
        this.formData.consignee_info.address = {
          prov: {
            name: '',
            code: '',
          },
          city: {
            name: '',
            code: '',
          },
          county: {
            name: '',
            code: '',
          },
          detail: '',
        };
      }

      // 手动触发校验
      // this.$refs.formData.validateField('selectedAddress')
    },
    scrollEvent() {
      let ref = this.initGoodsTabList[this.goodsTabActive].name;
      this.$refs.serviceRef.scrollView(ref);
    },
    goodsTabChange(index) {
      this.goodsTabActive = index;
      if (this.tabActive === 'service') {
        this.scrollEvent();
        return;
      }
      this.goods_list = [];
      this.page = 1;
      this.pageSize = 20;
      if (this.tabActive === 'goods') {
        this.getCureRecordList();
      } else if (this.tabActive === 'service') {
        this.getSalesgoodsservice();
      } else if (this.tabActive === 'recently') {
        this.getSalesList();
      }
    },
    getScrolllHeight() {
      this.$nextTick(() => {
        let other_distance = this.tabActive !== 'recently' ? 60 : 34;
        this.scrollHeight = this.$refs.goodsArea.offsetHeight - other_distance;
      });
    },
    getCarScrollHeight() {
      this.$nextTick(() => {
        this.carScrollHeight =
          this.$refs.carBoxRef.offsetHeight -
          // this.$refs.customerRef.offsetHeight -
          this.$refs.fixedBtnRef.offsetHeight -
          40;
      });
    },
    handleReachBottom() {
      setTimeout(() => {
        let current_length = 0;
        current_length = this.goods_list.length;
        // 最近售卖取的是最外层天为维度，如果以goods_sales_info为维度，则用下面的逻辑
        // if (this.tabActive == 'recently') {
        //   this.goods_list.forEach(item => {
        //     current_length = current_length + item?.goods_sales_info?.length;
        //   });
        // } else {
        //   current_length = this.goods_list.length;
        // }
        if (this.total > current_length) {
          this.page = this.page + 1;
          if (this.tabActive === 'goods') {
            this.getCureRecordList();
          } else if (this.tabActive === 'recently') {
            return this.getSalesList();
          }
        }
      }, 600);
    },
    /**
     * @description:远程搜索用户信息
     * */
    querySearchAsync(keyword, cb) {
      this.creatName = cloneDeep(keyword);
      if (this.searchTimes && !keyword) {
        cb(this.userList);
      } else {
        let copyName = this.name || 'none';
        if (keyword !== copyName) {
          this.getUserList({ search: keyword }, cb);
        } else {
          cb(this.userList);
        }
        this.getUserList({ search: keyword }, cb);
      }
    },

    // 点击创建用户，显示弹窗
    creatConsumer(val) {
      this.consumerVisibleDia = true;
      this.$refs.custom.close();
    },

    // 创建用户返回的数据
    optionChange(item) {
      this.balance = '';
      this.name = item.patient_name;
      this.mobile = item.mobile;
      this.nickname = item.patient_name;
      this.uid = item.uid;
      this.current_user_info = {
        balance: '',
        nickname: this.nickname,
        ...item,
      };
    },
    handleSelect(item) {
      this.current_user_info = item;
      this.name = item.patient_name;
      this.mobile = item.mobile;
      this.nickname = item.patient_name;
      this.uid = item.uid;
      this.balance = item.recharge_money;
      this.getUserAddress();
      setTimeout(() => {
        this.getScrolllHeight();
      }, 2000);
    },
    // 当搜索的人不存在时,失焦清除绑定数据,不允许自建
    blur() {
      setTimeout(() => {
        if (!this.name) {
          if (!this.consumerVisibleDia) {
            this.nickname = '';
            this.$refs.custom?.getData();
          }
        } else {
          this.nickname = this.name;
        }
      }, 200);
    },

    // 商品options选中
    goodsOptionChange(item) {
      const { goods_type } = item;

      // 如果你此时选择的时实体商品订单，一个spu对应多个sku的情况下，要出现弹窗手动选择
      if (goods_type == '10' || goods_type == '40' || goods_type == '45') {
        let attrs = this.isActivity ? item.goods.attrs : item.attrs;
        // 一个spu对应多个sku,在弹框内部选择
        if (Object.keys(attrs).length > 1) {
          this.entityOrderVisible = true;
          this.entityOrderObject = item;
        } else {
          // 如果一个spu对应一个sku，直接添加
          let _attrs = [];
          Object.keys(attrs).forEach(key => {
            _attrs.push({
              ...attrs[key],
              goods_type: item.goods_type,
              is_recharge_buy: item.is_recharge_buy,
              source_platform_text: item.source_platform_text,
              goods_type_mini_text: item.goods_type_mini_text,
            });
          });
          this.checkedList(_attrs, item.name);
        }
      } else {
        let current_item = this.isActivity ? item.goods : item;
        this.addGoods(current_item);
      }
    },

    getUserList({ search = '', uid = '' }, cb) {
      return new Promise(resolve => {
        let params = {
          page: 1,
          pageSize: 20,
          search,
          uid: search ? '' : uid,
        };
        this.searchTimes++;
        if (search) {
          this.searchTimes = 0;
        }
        this.$api.getUserList(params).then(res => {
          resolve(res);
          // 获取用户数据
          this.handleUserList(res.users, cb);
        });
      });
    },
    // 处理用户数据
    handleUserList(data, cb) {
      this.userList = data;
      if (!data.length) {
        cb([{ empty: true }]);
        return [];
      }
      typeof cb === 'function' && cb(data);
    },

    // 获取接口返回的地址，转换成当前的地址
    handleAddress({ consignee, mobile, location }) {
      if (!Object.keys(location).length) return;
      // 有默认地址
      if (location.province.name) {
        this.formData.name = consignee;
        this.formData.mobile = mobile;
        this.formData.consignee_info.address.prov = location.province;
        this.formData.consignee_info.address.city = location.city;
        this.formData.consignee_info.address.county = location.county;

        // 澳门特别行政区地区直接返回空数组，小程序地址过于老旧
        if (location.province.name === '澳门特别行政区') return [];
        this.formData.consignee_info.address.detail = location.other;
        this.formData.selectedAddress = [
          TextToCode[location.province.name].code,
          TextToCode[location.province.name][location.city.name]?.code,
          TextToCode[location.province.name][location.city.name][location.county.name]?.code,
        ];
        if (this.distributionType === '1') {
          this.$refs.formData.validateField('selectedAddress');
        }
      }
    },

    // api-获取服务商品列表, ignore_type: 1 忽略类型
    getCureRecordList(ignore_type = '') {
      const { goods_type } = this;
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        goods_type: this.search_keyword ? '' : goods_type,
        ignore_type,
        status: 200,
        check_stock: this.isShowGoods ? 1 : 0,
        xn_scope: goods_type == '15' ? '1,9' : '',
        shelf_scope: '1,9',
        name: this.search_keyword,
        filter_pms_self_goods: '1',
      };
      this.loading = true;
      this.$api
        .getCureRecordList(params)
        .then(res => {
          if (this.tabActive === 'goods') {
            this.goods_list = this.page == 1 ? res.goods_items : this.goods_list.concat(res.goods_items);
            this.total = res.total;
            this.sale_total = res.sale_total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    getSalesList() {
      let type = this.initGoodsTabList[this.goodsTabActive].name;
      let params = {
        page: this.page,
        pageSize: this.pageSize,
        keywords: this.search_keyword,
        is_self: type === 'self' && !this.search_keyword ? 1 : 0,
        uid: type === 'self' && !this.search_keyword ? this.current_user_info.uid : '',
      };
      this.loading = true;
      this.$api
        .getSalesList(params)
        .then(res => {
          if (this.tabActive === 'recently') {
            this.goods_list = this.page == 1 ? res.list : this.goods_list.concat(res.list);
            this.total = res.total;
            this.sale_total = res.sale_total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },

    //  获取服务列表
    getSalesgoodsservice() {
      let params = {
        name: this.search_keyword,
      };
      this.loading = true;
      this.$api
        .getSalesgoodsservice(params)
        .then(res => {
          this.serviceList = res;
          this.service_tab_list = this.handleTabList(res);
          this.initGoodsTabList = this.service_tab_list;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 处理服务的左侧菜单数据
    handleTabList(res = []) {
      let list = [];
      res &&
        res.forEach(item => {
          list.push({
            label: item.serv_type_text,
            name: item.serv_type,
          });
        });
      return list || [];
    },

    addGoods(item, is_service = false) {
      if (!this.isHasGood(item.id, is_service)) {
        this.add_goods.unshift({
          ...item,
          num: 1,
        });
        if (!is_service) {
          this.activeChange(true);
        }
      } else {
        this.$Message.error(`${item.name}已经存在,不可重复添加`);
      }
    },

    // 商品列表是否已经有该商品
    isHasGood(id, is_service = false) {
      return this.add_goods.some(item => {
        // 服务类型
        if (is_service) {
          return item.is_service && item.id == id;
        } else {
          return item.id == id;
        }
      });
    },

    // api-获取用户收件地址
    getUserAddress() {
      let params = {
        uid: this.userList.length > 0 ? this.uid : '',
      };
      this.$api.getUserAddress(params).then(res => {
        this.userAddress = res.location;
        // 处理地址
        this.handleAddress(res);
      });
    },

    // 获取平台活动列表
    getActivitylist() {
      let params = {
        keywords: this.search_keyword,
        // check_stock: this.isShowGoods ? 1 : 0, // 仅显示可售商品，活动不展示
      };
      this.$api.getActivitylist(params).then(res => {
        let index = this.tabList.findIndex(item => item.name === 'plat');
        if (index == -1) {
          this.tabList.push({
            label: res.title,
            img: res.image,
            name: 'plat',
            sort: 4,
          });
        } else {
          this.tabList[index] = {
            label: res.title,
            img: res.image,
            name: 'plat',
            sort: 4,
          };
        }
        if (this.tabActive === 'plat') {
          this.goods_list = res.list || [];
          this.total = res.total || 0;
          this.sale_total = res.can_sale_num || 0;
          // !this.isShowGoods &&
          if (!this.search_keyword && (res.list?.length == 0 || res.list === undefined)) {
            this.tabActive = 'goods';
            this.initGoodsTabList = this.goods_tab_list;
            this.tabList.splice(index, 1);
            this.goodsTabChange(this.goodsTabActive);
          }
        }
      });
    },

    // 获取省公司活动列表
    getActivitycomlist() {
      let params = {
        keywords: this.search_keyword,
        // check_stock: this.isShowGoods ? 1 : 0, // 仅显示可售商品，活动不展示
      };
      this.$api.getActivitycomlist(params).then(res => {
        let index = this.tabList.findIndex(item => item.name === 'com');
        if (index == -1) {
          this.tabList.push({
            label: res.title,
            img: res.image,
            name: 'com',
            sort: 5,
          });
        } else {
          this.tabList[index] = {
            label: res.title,
            img: res.image,
            name: 'com',
            sort: 5,
          };
        }
        if (this.tabActive === 'com') {
          this.goods_list = res.list || [];
          this.total = res.total || 0;
          this.sale_total = res.can_sale_num || 0;
          // !this.isShowGoods &&
          if (!this.search_keyword && (res.list?.length == 0 || res.list === undefined)) {
            this.tabActive = 'goods';
            this.initGoodsTabList = this.goods_tab_list;
            this.tabList.splice(index, 1);
            this.goodsTabChange(this.goodsTabActive);
          }
        }
      });
    },

    // api-获取options
    getArrivalOptions() {
      this.$api.getArrivalOptions().then(res => {
        // 用户来源
        this.sourceList = S.descToArrHandle(res.userFromDesc);
      });
    },

    // 检测诊所是否使用优惠券支付
    getActivityCheckclinic() {
      let params = {
        clinic_id: getClinicid(),
      };
      this.$api.getActivityCheckclinic(params).then(res => {
        this.coupon_pay = res.coupon_pay;
      });
    },
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.getScrolllHeight);
    window.removeEventListener('resize', this.getCarScrollHeight);
    let overflowY = this.$el.getElementsByClassName('ivu-scroll-container')[0];
    if (overflowY) {
      overflowY.addEventListener('scroll', this.serviceScroll);
    }
  },
};
</script>
<style lang="less" scoped>
@import url('./style/create.less');

.goods-box-wrapper {
  min-width: 640px;
}
.source-item-name {
  display: flex;
}
.source-item-name .name {
  width: fit-content;
}
.douyin-icon {
  width: 16px;
  height: 16px;
  object-fit: cover;
  margin-left: 4px;
  margin-top: -2px;
}
</style>
