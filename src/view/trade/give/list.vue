<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" @click="createOrder">创建订单</Button>
        </div>
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <Col>
              <FormItem label="">
                <Input type="text" v-model="queryFormData.user_keyword" placeholder="姓名/注册名/手机号" />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="">
                <Input type="text" v-model="queryFormData.out_trade_no" placeholder="订单编号" />
              </FormItem>
            </Col>
            <Col>
              <FormItem label="" placeholder="完成时间">
                <DatePicker
                  type="daterange"
                  v-model="orderTime"
                  @on-change="times => handleTimeChange(times, 'finish_st', 'finish_et')"
                  placeholder="完成时间"
                ></DatePicker>
              </FormItem>
            </Col>
            <Col>
              <FormItem label="">
                <Select v-model="queryFormData.source" placeholder="订单来源">
                  <Option value="">全部来源</Option>
                  <Option v-for="item in sourceDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                </Select>
              </FormItem>
            </Col>

            <Col>
              <FormItem label="">
                <Select v-model="queryFormData.relate_staff_id" placeholder="关联人">
                  <Option value="">全部</Option>
                  <Option
                    v-for="staff in staff_list"
                    :key="staff.id"
                    :value="staff.id"
                    :label="staff.role ? `${staff.name}·${staff.role}` : staff.name"
                  >
                    <div class="flex flex-item-between">
                      <span>{{ staff.name }}</span>
                      <span style="font-size: 12px; color: #999999">{{ staff.role }}</span>
                    </div>
                  </Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col v-if="!isRst">
              <FormItem label="">
                <Input type="text" v-model="queryFormData.goods_name" placeholder="赠送商品" />
              </FormItem>
            </Col>

            <Col v-if="!isRst">
              <FormItem label="">
                <Select filterable v-model="queryFormData.activity_id" placeholder="关联储值活动">
                  <Option v-for="item in activitylist" :key="item.id" :value="item.id">{{ item.name }}</Option>
                </Select>
              </FormItem>
            </Col>

            <Col>
              <FormItem>
                <Select v-model="queryFormData.wallet_type" placeholder="订单类型" v-if="isRst">
                  <Option v-for="item in walletTypeDesc" :key="item.id" :value="item.id">{{ item.desc }}</Option>
                </Select>
              </FormItem>
              <FormItem :label-width="0">
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <Button type="default" class="mr10" @click="downloadExcel(queryFormData)">导出</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>

        <!-- <div class="panel-nav flex flex-item-between" v-if="Object.keys(statusDesc).length"> -->
        <!-- <div>
            <a class="nav" :class="{active: !queryFormData.status}" @click.prevent.capture="onStatusChange('')">
              全部
              <Tag color="default">{{ statusTotal.TOTAL }}</Tag>
            </a>
            <a class="nav" :class="{active: $route.query.status == status}" v-for="(desc, status) in statusDesc"
               :key="status" @click.prevent.capture="onStatusChange(status)">
              {{ desc.desc }}
              <Tag :color="status==2000?'error':'default'">
                {{ statusTotal[desc.kw.slice( 7, 20 )] || statusTotal[desc.kw.slice( 11, 20 )] || '0' }}
              </Tag>
            </a>
          </div> -->
        <!-- </div> -->
      </template>
      <template slot-scope="{ row }" slot="out_trade_no">
        订单号：{{ row.out_trade_no }}<br />
        交易号：{{ row.transaction_no }}<br />
      </template>

      <template slot-scope="{ row }" slot="customer">
        注册名：
        <KLink :to="{ path: '/user/detail', query: { uid: row.uid } }" target="_blank">{{ row.nickname || '-' }}</KLink>
        <br />
        姓名：{{ row.real_name || '-' }}<br />
        手机号：{{ row.user_mobile }}<br />
        <div v-if="!isRst">关联人：{{ row.relate_staff_text || '-' }}<br /></div>
      </template>

      <template slot-scope="{ row }" slot="price">
        <div>
          <p>实充金额：{{ row.total_fee ? `￥${Number(row.total_fee || 0).toFixed(2)}` : '-' }}</p>
          <p v-if="!isRst">赠送金额：{{ row.given_fee ? `￥${Number(row.given_fee || 0).toFixed(2)}` : '-' }}</p>
          <p v-if="!isRst">储值金额：{{ row.account_fee ? `￥${Number(row.account_fee || 0).toFixed()}` : '-' }}</p>
          <p v-if="isRst">已退金额：{{ row.refund_fee ? `￥${Number(row.refund_fee || 0).toFixed(2)}` : '-' }}</p>
        </div>
      </template>

      <template slot-scope="{ row }" slot="goods_names">
        <Tooltip v-if="row.goods_names.length" :content="row.goods_names.join(',')">
          <p class="ecs cursor">{{ row.goods_names.join(',') }}</p>
        </Tooltip>
        <p v-else>-</p>
      </template>
      <template slot-scope="{ row }" slot="rst_goods_names">
        <Tooltip v-if="row.gift_detail.length" :content="row.gift_detail.join(',')">
          <p class="ecs ecs-2 cursor">{{ row.gift_detail.join(',') }}</p>
        </Tooltip>
        <p v-else>-</p>
      </template>

      <template slot-scope="{ row }" slot="recharge_activity_name">
        <p>{{ row.recharge_activity_name || '-' }}</p>
      </template>

      <template slot-scope="{ row }" slot="finished_time">
        <p>{{ row.finished_time | data_format }}</p>
      </template>

      <template slot-scope="{ row }" slot="operate">
        <a v-if="!isRst" style="margin-left: 10px" @click="editRelatedPerson(row)">
          {{ row.relate_staff_list.length ? '编辑' : '添加' }}关联人
        </a>
        <a class="ml10" @click="detail(row)">详情</a>
      </template>
    </standard-table>

    <editRelateModal
      :order_id="relatedModalFormData.editOrderId"
      :checked-staff-list="relatedModalFormData.relateStaffIds"
      :staff-list="staff_list"
      :relate-visible.sync="relatedModalFormData.relateVisible"
    ></editRelateModal>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
/* eslint-disable */
import PayDialog from './components/pay-dialog';
import downloadExcel from '@/mixins/downloadExcel';
import search from '@/mixins/search';
import editRelateModal from './components/edit-relates';
import StandardTable from '@/components/StandardTable/index.vue';
import { isRstClinic } from '@/libs/runtime';
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  user_keyword: '', // 姓名/手机号
  out_trade_no: '',
  finish_st: '', // 完成时间
  finish_et: '', // 完成时间
  source: '', // 来源
  relate_staff_id: '', //关联人id
  activity_id: '', // 活动id
  goods_name: '', // 商品名称
  wallet_type: '', // 订单类型
  r: '',
};
const init_relate_form_data = {
  editOrderId: '',
  relateVisible: false,
  relateStaffIds: [],
  // classType: '',
};
export default {
  name: 'list',
  components: {
    StandardTable,
    PayDialog,
    editRelateModal,
  },
  mixins: [downloadExcel, search],
  data() {
    return {
      apiName: 'getROrderList',
      relatedModalFormData: {
        ...init_relate_form_data,
      },
      downloadApiName: 'exportROrder',
      queryFormData: { ...init_query_form_data },
      sourceDesc: [], // 订单来源枚举
      staff_list: [], // 关联人列表枚举
      orderTime: [], // 下单时间
      tableColsDefault: [
        { title: '订单信息', slot: 'out_trade_no', width: 240 },
        { title: '客户', slot: 'customer', width: 240 },
        { title: '储值金额', slot: 'price', minWidth: 180 },
        { title: '赠送商品', slot: 'goods_names', minWidth: 150, is_rst_hide: isRstClinic() },
        { title: '赠品', slot: 'rst_goods_names', minWidth: 150, is_rst_hide: !isRstClinic() },
        { title: '来源', key: 'source', minWidth: 80 },
        { title: '关联储值活动', slot: 'recharge_activity_name', minWidth: 150, is_rst_hide: isRstClinic() },
        { title: '订单类型', key: 'wallet_type_text', minWidth: 150, is_rst_hide: !isRstClinic() },
        { title: '订单状态', key: 'status', minWidth: 130 },
        { title: '完成时间', slot: 'finished_time', minWidth: 160 },
        { title: '操作', slot: 'operate', align: 'center', width: 140, fixed: 'right' },
      ],
      list: [],
      tableLoading: false,
      total: 0,
      activitylist: [],
      walletTypeDesc: [],
    };
  },
  computed: {
    isRst() {
      return isRstClinic();
    },
    tableCols() {
      return this.tableColsDefault.filter(item => !item.is_rst_hide);
    },
  },
  created() {
    this.getOrderOptions();
    this.getOrderActivitylist();
    // 获取关联人列表
    this.getStaffList();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  methods: {
    // 编辑关联人
    editRelatedPerson(row) {
      this.relatedModalFormData = { ...init_relate_form_data };
      this.relatedModalFormData.editOrderId = row.id;
      this.relatedModalFormData.relateStaffIds = [];
      this.relatedModalFormData.relateStaffIds = row.relate_staff_list;
      // this.relatedModalFormData.classType = row.class
      this.relatedModalFormData.relateVisible = true;
    },

    changeTime(times, type) {
      this.queryFormData[type + '_st'] = times[0];
      this.queryFormData[type + '_et'] = times[1];
    },

    // 创建订单
    createOrder() {
      this.$router.push({ path: '/trade/give/create' });
    },

    // 详情
    detail(row) {
      this.$router.push({
        path: '/trade/give/detail',
        query: {
          id: row.id,
        },
      });
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.orderTime = [];
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onStatusChange: function (status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },

    getOrderOptions() {
      this.$api.getROrderOptions().then(res => {
        // 来源数据
        this.sourceDesc = S.descToArrHandle(res.sourceDesc);
        this.walletTypeDesc = S.descToArrHandle(res.walletTypeDesc);
      });
    },

    getOrderActivitylist() {
      this.$api.getOrderActivitylist().then(res => {
        // 来源数据
        this.activitylist = res;
      });
    },

    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          console.log(data, 'data');
          this.list = data.orders;
          this.total = data.total;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },

    /* api-获取关联人 */
    getStaffList() {
      this.$api.getStaffList().then(res => {
        this.staff_list = res.staff_list;
      });
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange('finish_st', 'finish_et', 'orderTime');
    this.getsList();
    next();
  },
};
</script>

<style lang="less">
.order_attrs_table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
}

.order_attrs_table td {
  padding: 5px 5px;
  border-color: transparent;
}
.ml10 {
  margin-left: 10px;
}
.cursor {
  cursor: pointer;
}
</style>
<style lang="less" scoped>
::v-deep .ivu-form-item {
  margin-bottom: 12px;
}
p {
  margin: 0px;
}
</style>
