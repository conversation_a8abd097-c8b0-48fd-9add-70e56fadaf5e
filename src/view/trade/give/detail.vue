<template>
  <div class="detail-box">
    <div>
      <div class="block-header">订单信息</div>
      <div class="content">
        <div class="item">
          <div class="item-label">订单编号：</div>
          <div class="item-value">{{ echoData.out_trade_no || '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">交易号：</div>
          <div class="item-value">{{ echoData.transaction_no || '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">订单状态：</div>
          <div class="item-value">{{ echoData.status || '-' }}</div>
        </div>
        <div class="item" v-if="isRst">
          <div class="item-label">订单类型：</div>
          <div class="item-value">{{ echoData.wallet_type_desc || '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">订单备注：</div>
          <div class="item-value">{{ echoData.user_note_info || '-' }}</div>
        </div>
      </div>
    </div>

    <div>
      <div class="block-header">客户信息</div>
      <div class="content">
        <div class="item">
          <div class="item-label">注册名：</div>
          <div class="item-value">{{ echoData.nickname || '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">姓名：</div>
          <div class="item-value">{{ echoData.real_name || '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">手机号：</div>
          <div class="item-value">{{ echoData.user_mobile || '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">关联人：</div>
          <div class="item-value">{{ echoData.relate_staff_text || '-' }}</div>
        </div>
      </div>
    </div>

    <div>
      <div class="block-header">充值信息</div>
      <rst-rechage-info
        class="content"
        v-if="echoData.wallet_type === 'USER_RECHARGE_YZT'"
        :list="rechargeList"
        :gift_coupon_info="gift_coupon_info"
        :orderId="$route.query.id"
        @success="init"
      ></rst-rechage-info>
      <div class="content" v-else>
        <div class="item">
          <div class="item-label">实充金额：</div>
          <div class="item-value">{{ echoData.total_fee ? `￥${echoData.total_fee}` : '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">赠送金额：</div>
          <div class="item-value">{{ echoData.given_fee ? `￥${echoData.given_fee}` : '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">储值金额：</div>
          <div class="item-value">{{ echoData.account_fee ? `￥${echoData.account_fee}` : '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">关联储值活动：</div>
          <a @click="jumpRelate" v-if="echoData.recharge_activity_id && !isRst">{{ activity.name }}</a>
          <div v-else>{{ activity.name || '-' }}</div>
        </div>

        <div class="item-table">
          <div class="item-label">赠送商品：</div>
          <Table class="custom-table" :columns="tableCols" :data="echoData.goods">
            <template slot-scope="{ row }" slot="price">
              <p>¥{{ row.price || 0 }}</p>
            </template>

            <template slot-scope="{ row }" slot="operate">
              <a @click="cancelGive(row)" v-if="row.num > 0">取消赠送</a>
              <div v-else>-</div>
            </template>
          </Table>
        </div>
      </div>
    </div>

    <div>
      <div class="block-header mt20">支付信息</div>
      <div class="content">
        <div class="item">
          <div class="item-label">支付金额：</div>
          <div class="item-value">{{ echoData.payment_fee ? `￥${echoData.payment_fee}` : '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">支付方式：</div>
          <div class="item-value">{{ echoData.pay_type_desc || '-' }}</div>
        </div>

        <div class="item">
          <div class="item-label">支付时间：</div>
          <div class="item-value">{{ echoData.pay_time | data_format }}</div>
        </div>
      </div>
    </div>

    <div>
      <div class="block-header">操作记录</div>
      <div class="content">
        <div class="item-table">
          <Table class="custom-table" :columns="operatorCols" :data="operatorList">
            <template slot-scope="{ row }" slot="create_time">
              <p>{{ row.create_time | data_format }}</p>
            </template>

            <template slot-scope="{ row }" slot="operator_name">
              <p>{{ row.operator_name || '-' }}</p>
            </template>

            <template slot-scope="{ row }" slot="remark">
              <p>{{ row.remark || '-' }}</p>
            </template>
          </Table>
        </div>
      </div>
    </div>
    <div style="height: 35px">
      <div class="fixed-bottom-wrapper">
        <back-button class="mr10"></back-button>
        <Button @click="edit">编辑关联人</Button>
      </div>
    </div>

    <editRelateModal
      :order_id="relatedModalFormData.editOrderId"
      :isDetail="true"
      :checked-staff-list="relatedModalFormData.relateStaffIds"
      :staff-list="staff_list"
      :relate-visible.sync="relatedModalFormData.relateVisible"
    ></editRelateModal>

    <cancel-give-modal
      v-model="cancelVisible"
      :order_id="$route.query.id"
      :row="current_row"
      @success="init"
    ></cancel-give-modal>
  </div>
</template>

<script>
import editRelateModal from './components/edit-relates';
import cancelGiveModal from './components/cancelGiveModal.vue';
import rstRechageInfo from '@/view/trade/give/components/rstRechageInfo.vue';
import { isRstClinic } from '@/libs/runtime';

const init_relate_form_data = {
  editOrderId: '',
  relateVisible: false,
  relateStaffIds: [],
  // classType: '',
};
export default {
  name: 'detail',
  components: { editRelateModal, cancelGiveModal, rstRechageInfo },
  mixins: [],
  data() {
    return {
      relatedModalFormData: {
        ...init_relate_form_data,
      },
      staff_list: [], // 关联人列表
      echoData: {},
      activity: {},

      // 储值信息
      tableColumns: [
        { title: '储值活动名称', key: 'name', align: 'center' },
        { title: '储值规则', slot: 'give_desc', align: 'center', minWidth: 120 },
        { title: '用户实充', slot: 'total_fee', align: 'center' },
        { title: '赠送金额', slot: 'given_fee', align: 'center' },
        { title: '储值总金额', slot: 'account_fee', align: 'center' },
        { title: '用户实付金额', slot: 'payment_fee', align: 'center' },
      ],
      infoList: [],

      tableCols: [
        { title: 'ID', key: 'id' },
        { title: '商品名', key: 'name' },
        { title: '类型', key: 'goods_type_desc' },
        { title: '价格', slot: 'price' },
        { title: '赠送数量', key: 'num' },
        { title: '取消赠送数量', key: 'cancel_num' },
        { title: '操作', slot: 'operate', align: 'center' },
      ],
      list: [],

      operatorCols: [
        { title: '时间', slot: 'create_time' },
        { title: '操作人', slot: 'operator_name' },
        { title: '操作记录', slot: 'remark' },
      ],
      operatorList: [],
      cancelVisible: false,
      current_row: {},

      // rst的充值信息
      rechargeList: [],
      gift_coupon_info: [],
    };
  },
  computed: {
    isRst() {
      return isRstClinic();
    },
  },
  watch: {},
  created() {},
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getStaffList();
      let id = this.$route.query.id;
      if (id) {
        this.getROrderDetail(id);
        this.getRecord();
      }
    },
    jumpRelate() {
      this.$router.push({
        path: '/store/stored/created',
        query: { id: this.echoData.recharge_activity_id, type: 'detail' },
      });
    },
    cancelGive(row) {
      this.current_row = row;
      this.cancelVisible = true;
    },
    // 编辑关联人
    edit(row) {
      this.relatedModalFormData = { ...init_relate_form_data };
      this.relatedModalFormData.editOrderId = this.echoData.id;
      this.relatedModalFormData.relateStaffIds = [];
      this.relatedModalFormData.relateStaffIds = this.echoData.relate_staff_list;
      // this.relatedModalFormData.classType = row.class
      this.relatedModalFormData.relateVisible = true;
    },
    getStaffList() {
      this.$api.getStaffList().then(res => {
        this.staff_list = res.staff_list;
      });
    },
    // 返回
    cancel() {
      this.$router.push({
        path: '/trade/give/list',
      });
    },

    getRecord() {
      this.$api
        .getOrderOperationrecord({
          order_id: this.$route.query.id,
        })
        .then(res => {
          this.operatorList = res.list;
        });
    },

    // api
    /* api-详情 */
    getROrderDetail(id) {
      this.$api
        .getROrderDetail({
          order_id: id,
        })
        .then(res => {
          this.echoData = res.order;
          this.activity = res.activity;
          this.infoList = res.activity;

          let order = res.order || {};
          // 充值信息
          this.rechargeList = [
            {
              uid: order.uid,
              out_trade_no: order.out_trade_no,
              total_fee: Number(order?.total_fee || 0).toFixed(2),
              refund_fee: Number(order?.refund_fee || 0).toFixed(2),
              account_fee: Number(order?.account_fee || 0).toFixed(2),
              gift_desc: order.gift_desc,
            },
          ];
          this.gift_coupon_info = res.order.gift_coupon_info;
        });
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  padding-left: 10px;
  display: flex;
  flex-wrap: wrap;
  margin-right: 20%;
  .item-table {
    display: flex;
  }
  .item {
    display: flex;
    align-items: flex-start;
    width: 25%;
    margin-bottom: 30px;
  }
  .item-label {
    font-weight: 400;
    font-size: 12px;
    color: #999999;
    line-height: 17px;
    min-width: fit-content;
  }
  .item-value {
    font-weight: 400;
    font-size: 12px;
    color: #333333;
    line-height: 17px;
  }
}

::v-deep .custom-table {
  .ivu-table th {
    background: #f7f8fc;
    font-weight: 400;
    font-size: 13px;
    color: #000000;
    line-height: 17px;
  }
}

.mt20 {
  margin-top: 20px;
}
</style>
