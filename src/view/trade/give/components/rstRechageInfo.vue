<template>
  <div>
    <div class="item">
      <div class="item-label">充值信息：</div>
      <div class="item-value">
        <Table class="custom-table" :columns="columns" :data="list">
          <template slot-scope="{ row }" slot="total_fee">
            <p>¥{{ row.total_fee || '0.00' }}</p>
          </template>
          <template slot-scope="{ row }" slot="gift_desc">
            <p>{{ row.gift_desc || '-' }}</p>
          </template>

          <template slot-scope="{ row }" slot="refund_fee">
            <p>¥{{ row.refund_fee || '' }}</p>
          </template>

          <template slot-scope="{ row }" slot="operator">
            <a @click="refund(row)">退款</a>
          </template>
        </Table>
      </div>
    </div>
    <div class="item">
      <div class="item-label">赠送商品：</div>
      <div class="item-value">
        <Table class="custom-table" :columns="goodColumns" :data="gift_coupon_info"> </Table>
      </div>
    </div>

    <recharge-refund
      v-model="rechageInfoVisible"
      :out_trade_no="currentRow.out_trade_no"
      :uid="currentRow.uid"
      :orderId="orderId"
      @success="success"
    ></recharge-refund>
  </div>
</template>

<script>
import rechargeRefund from '@/components/rechargeRefund/index.vue';
export default {
  name: 'rstRechageInfo',
  components: {
    rechargeRefund,
  },
  mixins: [],
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    gift_coupon_info: {
      type: Array,
      default: () => [],
    },
    // 订单id
    orderId: {
      type: String,
      default: '',
    },
    goodList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      columns: [
        { title: '实充金额', slot: 'total_fee', align: 'center' },
        { title: '赠送金额', slot: 'gift_desc', align: 'center' },
        { title: '储值金额', slot: 'total_fee', align: 'center' },
        { title: '已退金额', slot: 'refund_fee', align: 'center' },
        { title: '操作', slot: 'operator', align: 'center' },
      ],
      goodColumns: [
        { title: '商品名称', key: 'name', align: 'center' },
        { title: '类型', key: 'type_desc', align: 'center' },
        { title: '价格', key: 'price', align: 'center' },
        { title: '赠送数量', key: 'total', align: 'center' },
        { title: '使用数量', key: 'used_total', align: 'center' },
      ],
      rechageInfoVisible: false,
      currentRow: {},
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    refund(row) {
      this.currentRow = row;
      this.rechageInfoVisible = true;
    },
    success() {
      this.$emit('success');
    },
  },
};
</script>

<style lang="less" scoped>
.item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30px;
}
.item-label {
  font-weight: 400;
  font-size: 12px;
  color: #999999;
  line-height: 17px;
  min-width: fit-content;
}
.item-value {
  font-weight: 400;
  font-size: 12px;
  color: #333333;
  line-height: 17px;
}
</style>
