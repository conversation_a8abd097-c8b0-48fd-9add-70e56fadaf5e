<template>
  <div ref="tableWrapRef" @mouseover="hidePop">
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      size="small"
      stripe
      class="pl-table"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" @click="importWarnStock">批量设置库存预警</Button>
        </div>
        <div class="form-warpper">
          <Form class="form-warpper_left" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <FormItem>
              <Input type="text" v-model="queryFormData.name" placeholder="货品名称" />
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.prod_type" placeholder="全部类型">
                <Option value="">全部类型</Option>
                <Option :value="item.id" v-for="item in prodTypes" :key="item.id">{{ item.name }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.source" placeholder="全部来源">
                <Option value="">全部来源</Option>
                <Option :value="index" v-for="(item, index) in prodSource" :key="index">{{ item.desc }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="initSearch"
                ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
              >
            </FormItem>
          </Form>
        </div>
      </template>

      <template slot-scope="{ row }" slot="id">
        {{ row.prod_id }}
      </template>

      <template slot-scope="{ row }" slot="generic_name">
        <product-show
          :generic_name="prods[row.prod_id].generic_name"
          :prod_spec="prods[row.prod_id].prod_spec"
          :grade_desc="prods[row.prod_id].grade_desc"
          :manufacturer="prods[row.prod_id].manufacturer"
          :row="prods[row.prod_id]"
          @showPop="showPop"
        ></product-show>
      </template>

      <template slot-scope="{ row }" slot="prod_unit">
        {{ prods[row.prod_id].prod_unit }}
      </template>

      <template slot-scope="{ row }" slot="prod_type">
        <Tooltip
          :content="formatMatch(prodTypes, row.prod_type)"
          :disabled="formatMatch(prodTypes, row.prod_type)?.length < 5"
        >
          <span class="ecs" :class="{ cursor: formatMatch(prodTypes, row.prod_type)?.length > 4 }">{{
            formatMatch(prodTypes, row.prod_type) || '-'
          }}</span>
        </Tooltip>
      </template>
      <template slot-scope="{ row }" slot="stock_warning_text">
        {{ row.stock_warning_text }}
      </template>
      <template slot-scope="{ row }" slot="stock_num">
        <span style="color: red">{{ row.stock_text }}</span>
      </template>
      <!--      <template slot-scope="{ row }" slot="price"> ¥{{ prods[row.prod_id].retail_price }} </template>-->
    </standard-table>

    <!-- 导入库存预警-excel -->
    <Modal
      v-model="importVisible"
      :mask-closable="false"
      title="导入库存预警"
      :closable="false"
      @on-cancel="importCancel"
    >
      <div slot="footer">
        <!-- btn -->
        <div class="flex flex-item-end">
          <Button type="primary" class="space6" @click="importConfirm" :disabled="isActiveBtn">
            {{ excelUploadLoading ? '导入中,请耐心等待···' : '导入' }}
          </Button>
          <Button type="default" class="space6" @click="importCancel" v-if="!excelUploadLoading">关闭</Button>
        </div>
      </div>
      <div class="import-content" v-if="importVisible">
        <div>
          <div class="flex flex-item-align">
            <p>导入表格：</p>
            <KExcelUpload
              btnType="text"
              :isWarn="true"
              :excelUploadLoading="excelUploadLoading"
              btnText="选择表格"
              @excelUpload="excelUpload"
            ></KExcelUpload>
            <p class="download cursor hover" @click="templateDownload">下载导入模板</p>
          </div>

          <Progress
            v-if="excelUploadLoading"
            :percent="uploadPercent"
            :stroke-width="20"
            status="active"
            text-inside
            class="mt30"
          />
          <!-- 错误报告 -->
          <div class="error-report flex flex-item-center" v-show="hasHandleExcelList.length && isImportSuccess">
            <p class="">本次成功导入{{ succ_num }}条记录，{{ fail_num }}条错误记录</p>
            <p class="download-error cursor hover" v-show="Number(fail_num) > 0" @click="seeReport">查看错误报告</p>
          </div>
        </div>
        <div class="spin-container" v-if="excelUploadLoading">
          <Spin fix></Spin>
        </div>
      </div>
    </Modal>
    <!--     查看错误报告 -->
    <Modal v-model="reportVisible" :mask-closable="false" width="800" title="错误报告">
      <div class="report-content" v-if="reportVisible">
        <Table :columns="reportColumn" :data="reportList" height="400"></Table>
      </div>
    </Modal>
    <Tooltip
      ref="custom-tooltip"
      popper-class="generic-name-link-tip"
      transfer-class-name="generic-name-link-tip"
      :reference="tooltipReference"
      :delay="1000"
      padding="0 0"
      style="height: 0; width: 0; display: none"
    >
      <template #content>
        <generic-name-tip :row="selectedToolTipRow" />
      </template>
    </Tooltip>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
import search from '@/mixins/search';

/* eslint-disable */
import KExcelUpload from '@/components/k-excel-upload/excel';
import downloadExceL from '@/mixins/downloadExcel';
import excelBatchUpload from '@/mixins/excelBatchUpload';
import {formatMatch} from "../../../utils/helper";
import StandardTable from "@/components/StandardTable/index.vue";
import tooltip from "@/view/stock/mixins/tooltip";
import GenericNameTip from "@/view/stock/components/generic-name-tip.vue";
let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  prod_type: '',
  source: '', //来源
};

export default {
  name: 'list',
  components: {
    GenericNameTip,
    StandardTable,
    KExcelUpload,
  },
  mixins: [downloadExceL, excelBatchUpload, search, tooltip],
  data() {
    return {
      formatMatch,
      queryFormData: { ...init_query_form_data },
      apiName: 'getProductWarningStockList',
      tableCols: [
        { title: '编码', slot: 'id', minWidth: 80, align: 'center' },
        { title: '货品', slot: 'generic_name', minWidth: 180, align: 'left' },
        { title: '类型', slot: 'prod_type', minWidth: 80, align: 'center' },
        { title: '来源', key: 'from_text', minWidth: 80, align: 'center' },
        { title: '状态', key: 'prod_status_text', minWidth: 80, align: 'center' },
        { title: '预警数量', slot: 'stock_warning_text', minWidth: 80, align: 'center' },
        { title: '库存数量', slot: 'stock_num', minWidth: 80, align: 'center' },
        // { title: '规格', slot: 'prod_spec' },
        { title: '单位', slot: 'prod_unit', minWidth: 80, align: 'center' },
        // { title: '零售价', slot: 'price' }
      ],
      tableLoading: false,

      list: [],
      total: 0,
      statusDesc: {},
      typeDesc: {},
      prods: {},
      discountTypeDesc: {},
      prodTypes: [],
      prodSource: {}, // 来源

      importVisible: false, // 导入库存预警modal
      reportVisible: false, // 错误报告的modal
      exportLoading: false, // 模板下载loading
      excelUploadLoading: false, // 数据上传的loading
      handleExcelList: [], // 处理后要上传的excel数据
      hasHandleExcelList: [], // 准备导入的数据
      isImportSuccess: false, // 是否导入成功
      fail_num: 0, // 错误数量
      succ_num: 0, // 成功数量
      reportList: [], // 错误报告list
      excelUploadApiName: 'excelBatcheditwarning',
      reportColumn: [
        {
          title: '编号',
          key: 'id',
          align: 'center',
        },
        {
          title: '库存预警数量',
          key: 'stock_warning',
          align: 'center',
        },
        {
          title: '错误原因',
          key: 'fail_msg',
          align: 'center',
        },
      ],
    };
  },
  watch: {
    importVisible(val) {
      if (!val) {
        this.hasHandleExcelList = [];
        this.isImportSuccess = false;
        this._initUploadData();
      }
    },
    hasHandleExcelList(val) {
      if (val) {
        // 如果重新选择了表格,将参数置为false,视为重新上传
        this.isImportSuccess = false;
        this._initUploadData();
      }
    },
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.getStatusList();
  },
  computed: {
    isActiveBtn() {
      if (this.hasHandleExcelList.length > 0 && !this.isImportSuccess) {
        return false;
      } else {
        return true;
      }
    },
  },

  methods: {
    getStatusList() {
      let res = io.get('/clinic/product.product.options');
      res
        .then(data => {
          // console.log(data,"data");
          this.prodTypes = S.descToArrHandle(data.prodTypes).sort((a, b) => a.sort - b.sort);
          this.prodSource = data.prodSource;
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
        });
      return res;
    },

    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.list = data.list;
          this.prods = data.prods;
          this.total = data.total;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },

    handleList: function (list) {
      return list;
    },

    initSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    importWarnStock() {
      this.importVisible = true;
    },
    // 模板下载
    templateDownload() {
      const { name, prod_type, status } = this.queryFormData;
      this.exportLoading = true;
      let params = {
        name,
        prod_type,
        status,
        type: ' PROD_STOCK_WARNING', // 商品模板标识
      };
      this.$api
        .getClinicTemplateurl(params)
        .then(res => {
          this.download(res.url);
        })
        .finally(() => (this.exportLoading = false));
    },
    // 获取获取excel处理后的数据
    excelUpload(hasHandleExcelList) {
      this.hasHandleExcelList = hasHandleExcelList;
    },
    // 查看错误报告
    seeReport() {
      this.importVisible = false;
      this.reportVisible = true;
    },
    // 导入
    importConfirm() {
      // this.saveExcel( this.hasHandleExcelList )
      // 批量轮询
      this._btachUploadPolling(this.hasHandleExcelList, true);
    },
    // 取消导入弹窗
    importCancel() {
      this.importVisible = false;
    },
    /**
     * @description: api-上传处理后的excel数据, 当前方法为一次性请求完
     * */
    // saveExcel( hasHandleExcelList ) {
    //   this.excelUploadLoading = true
    //   this.$api.excelBatcheditwarning( { batch_params: hasHandleExcelList,timeout: 60000 } ).then( res => {
    //     // this.$Message.success('导入成功')
    //     this.isImportSuccess = true
    //     this.succ_num = res.succ_num
    //     this.fail_num = res.fail_num
    //     this.reportList = res.fail_data
    //     // 重新拉取页面数据
    //     this.getsList()
    //   }, rej => this.$Message.error( rej.errmsg ) ).finally( () => {
    //     this.excelUploadLoading = false
    //   } )
    // },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style scoped lang="less">
.table-fun {
  margin: -10px 0 10px;
}
</style>

<style lang="less" scoped>
.import-content {
  height: 160px;

  p {
    margin-bottom: 0px;
  }

  .choose {
    color: rgba(17, 87, 229, 0.5);
  }

  .download {
    margin-left: 20px;
    color: rgb(155, 141, 141);
    border-bottom: 1px solid #ccc;
  }

  .error-report {
    margin-top: 80px;

    .download-error {
      margin-left: 15px;
      color: rgba(17, 87, 229, 0.5);
    }
  }
}
.readText {
  color: #ccc;
}
.ml2 {
  margin-left: 2px;
}

.cursor {
  cursor: pointer;
}

.hover {
  &:hover {
    color: #155bd4 !important;
  }
}
.spin-container {
  display: inline-block;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}
.mt30 {
  margin-top: 30px;
}
.generic-name-link-tip {
  .ivu-tooltip-inner {
    max-width: unset;
    background-color: #FFFDEC;
  }
}
</style>
