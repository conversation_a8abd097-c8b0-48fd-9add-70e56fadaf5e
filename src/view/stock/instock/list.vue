<template>
  <div>
    <div class="form-warpper">
      <Form
        class="form-warpper_left"
        style="width: 100%"
        inline
        :label-width="0"
        @submit.native.prevent
        @keyup.enter.native="onSearch"
      >
        <div class="flex flex-item-between">
          <div>
            <FormItem>
              <Input
                type="text"
                v-model="queryFormData.prod_search_name"
                placeholder="商品名/通用名/商品条形码/商品编码"
              />
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.prod_type" placeholder="全部类型">
                <Option value="">{{ '全部类型' }}</Option>
                <Option :value="index" v-for="(item, index) in prodTypes" :key="index">{{ item.name }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="initSearch"
                ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
              >
            </FormItem>
          </div>
          <Button class="space6" type="primary" :loading="downloadLoading" @click="downloadExcel()"
            >导出货品库存表</Button
          >
        </div>
      </Form>
    </div>
    <Table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      size="small"
      @on-sort-change="sortChanged"
      border
      stripe
      class="pl-table"
      :height="$store.state.app.clientHeight - 235"
    >
      <template slot-scope="{ row }" slot="id">
        {{ row.prod_id }}
      </template>
      <template slot-scope="{ row }" slot="generic_name">
        {{ prods[row.prod_id].generic_name || '-' }}
      </template>
      <template slot-scope="{ row }" slot="prod_name">
        {{ prods[row.prod_id].prod_name || '-' }}
      </template>
      <template slot-scope="{ row }" slot="prod_spec">
        {{ prods[row.prod_id].prod_spec || '-' }}
      </template>
      <template slot-scope="{ row }" slot="prod_type">
        {{ prodTypes[prods[row.prod_id].prod_type].name || '-' }}
      </template>
      <template slot-scope="{ row }" slot="manufacturer">
        {{ prods[row.prod_id].manufacturer || '-' }}
      </template>
      <template slot-scope="{ row }" slot="stock_num">
        {{ prods[row.prod_id].stock_text || '-' }}
      </template>
      <template slot-scope="{ row }" slot="retail_price">
        {{ prods[row.prod_id].retail_price }}/{{ prods[row.prod_id].prod_unit || '-' }}
      </template>
      <template slot-scope="{ row }" slot="split_price">
        <span v-if="prods && prods[row.prod_id].is_split === '1'"
          >{{ prods[row.prod_id].split_price }}/{{ prods[row.prod_id].split_prod_unit }}</span
        >
        <span v-else>{{ '-' }}</span>
      </template>
      <template slot-scope="{ row }" slot="total_price">
        {{ prods && prods[row.prod_id].total_money }}
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: right"
    />
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
import downloadExceL from '@/mixins/downloadExcel';
/* eslint-disable */

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  prod_search_name: '',
  prod_type: '',
  sort_field: '',
  sort: ''
};

export default {
  name: 'list',
  mixins: [downloadExceL],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getProductStockList',
      tableCols: [
        { title: '商品编码', slot: 'id', sortable: 'custom', resizable: true, width: 90 },
        { title: '通用名', slot: 'generic_name', resizable: true, width: 250 },
        { title: '商品名', slot: 'prod_name', resizable: true, width: 250 },
        { title: '规格	', slot: 'prod_spec', resizable: true, width: 100 },
        { title: '商品类型', slot: 'prod_type', resizable: true, width: 100 },
        { title: '生产厂家', slot: 'manufacturer', resizable: true, width: 200 },
        { title: '库存数量', slot: 'stock_num', sortable: 'custom', resizable: true, width: 100 },
        { title: '零售价(元)', slot: 'retail_price', sortable: 'custom', resizable: true, width: 100 },
        { title: '拆零价(元)', slot: 'split_price', sortable: 'custom', resizable: true, width: 100 },
        { title: '零售价总额(元)', slot: 'total_price', sortable: 'custom', resizable: true }
      ],
      tableLoading: false,

      list: [],
      total: 0,
      statusDesc: {},
      typeDesc: {},
      prodTypes: {},
      prods: {},
      discountTypeDesc: {},
      prodSplitDesc: {},
      downloadApiName: 'getprodstockurl' // 导出接口名称
    };
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },

  methods: {
    sortChanged({ column: { slot }, order }) {
      if (order === 'normal') {
        order = '';
      }
      if (slot) {
        this.queryFormData.sort_field = slot;
        this.queryFormData.sort = order;
        this.getsList();
      } else {
        this.$Message.error('无效排序字段');
      }
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.prodTypes = data.prodTypes;
          this.prods = data.prods;
          this.total = data.total;
          this.prodSplitDesc = data.prodSplitDesc;
          let tempArr = [];
          data.list.map(item => {
            tempArr.push({
              ...item,
              total_price: (Number(data.prods[item.prod_id].retail_price) * Number(item.stock_num)).toFixed(2)
            });
          });
          this.list = tempArr;
        })
        .catch(error => {
          this.$Message.error(error.errmsg || String(error));
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },
    handleList: function (list) {
      return list;
    },
    initSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    submitQueryForm: function (replace) {
      // 通过修改url参数，触发路由前置守卫(beforeRouteUpdate)，在前置守卫中获取列表数据
      this.queryFormData.r = S.random(6); // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      if (replace) {
        this.$router.replace({ query: this.queryFormData });
      } else {
        this.$router.push({ query: this.queryFormData });
      }
    }
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  }
};
</script>

<style lang="less"></style>
