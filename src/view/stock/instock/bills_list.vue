<template>
  <div ref="tableWrapRef" @mouseover="hidePop">
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      size="small"
      stripe
      class="pl-table"
      :row-class-name="disableClassName"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template slot="header">
        <div class="form-warpper">
          <Form class="form-warpper_left" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <Row>
              <FormItem>
                <Input type="text" v-model="queryFormData.prod_search_name" placeholder="货品名称/编码" />
              </FormItem>
              <FormItem>
                <Select v-model="queryFormData.prod_type" placeholder="全部类型">
                  <Option value="">全部类型</Option>
                  <Option :value="item.id" v-for="item in prodTypes" :key="item.id">{{ item.name }}</Option>
                </Select>
              </FormItem>
              <FormItem>
                <Select v-model="queryFormData.method" placeholder="全部操作类型">
                  <Option value="">全部操作类型</Option>
                  <Option :value="index" v-for="(item, index) in methodDesc" :key="index">{{ item.desc }}</Option>
                </Select>
              </FormItem>
              <FormItem>
                <Select v-model="queryFormData.type" placeholder="全部变更类型">
                  <Option value="">全部变更类型</Option>
                  <Option :value="index" v-for="(item, index) in typeDesc" :key="index">{{ item.desc }}</Option>
                </Select>
              </FormItem>
              <FormItem>
                <DatePicker
                  type="daterange"
                  :options="pickerOptions"
                  clearable
                  v-model="timeRange"
                  @on-change="times => handleTimeChange(times)"
                  placeholder="变更时间"
                >
                </DatePicker>
              </FormItem>
            </Row>
            <Row>
              <FormItem>
                <Select v-model="queryFormData.supplier_id" placeholder="全部供应商">
                  <Option value="">{{ '全部供应商' }}</Option>
                  <Option v-for="item in supplierTypes" :key="item.id" :value="item.id">{{ item.name }}</Option>
                </Select>
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.source" placeholder="全部来源">
                  <Option value="">全部来源</Option>
                  <Option :value="index" v-for="(item, index) in prodSource" :key="index">{{ item.desc }}</Option>
                </Select>
              </FormItem>

              <FormItem>
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <Button class="mr10" @click="exportExcel">导出</Button>
                <span class="list-reset-btn" @click="initSearch"
                  ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
                >
              </FormItem>
            </Row>
          </Form>
        </div>
      </template>

      <template slot-scope="{ row }" slot="id">
        {{ row.prod_id }}
      </template>
      <template slot-scope="{ row }" slot="time">
        {{ row.create_time | data_format('YYYY-MM-DD') }}
      </template>

      <template slot-scope="{ row }" slot="generic_name">
        <product-show
          :generic_name="prods[row.prod_id].generic_name"
          :prod_spec="prods[row.prod_id].prod_spec"
          :grade_desc="prods[row.prod_id].grade_desc"
          :manufacturer="prods[row.prod_id].manufacturer"
          :row="prods[row.prod_id]"
          @showPop="showPop"
        ></product-show>
      </template>

      <template slot-scope="{ row }" slot="prod_name">
        {{ prods[row.prod_id].prod_name || '-' }}
      </template>
      <template slot-scope="{ row }" slot="prod_spec">
        {{ prods[row.prod_id].prod_spec || '-' }}
      </template>
      <template slot-scope="{ row }" slot="prod_type">
        <Tooltip :content="row.prod_type_text" :disabled="row.prod_type_text?.length < 5">
          <span class="ecs" :class="{ cursor: row.prod_type_text?.length > 4 }">{{ row.prod_type_text || '-' }}</span>
        </Tooltip>
      </template>
      <template slot-scope="{ row }" slot="from_text">
        {{ prods[row.prod_id].from_text || '-' }}
      </template>
      <template slot-scope="{ row }" slot="manufacturer">
        {{ prods[row.prod_id].manufacturer || '-' }}
      </template>
      <template slot-scope="{ row }" slot="supplier_id">
        {{
          (row.prod_stock_detail_id &&
            stockDetails[row.prod_stock_detail_id].supplier_id &&
            stockDetails[row.prod_stock_detail_id] &&
            suppliers[stockDetails[row.prod_stock_detail_id].supplier_id] &&
            stockDetails[row.prod_stock_detail_id] &&
            suppliers[stockDetails[row.prod_stock_detail_id].supplier_id].name) ||
          '-'
        }}
      </template>
      <template slot-scope="{ row }" slot="batch_code">
        {{ stockDetails[row.prod_stock_detail_id].batch_code }}
      </template>
      <template slot-scope="{ row }" slot="produce_time">
        {{ stockDetails[row.prod_stock_detail_id].produce_time | data_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="expire_time">
        {{ stockDetails[row.prod_stock_detail_id].expire_time | data_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="type">
        <!--   todo 待修改     -->
        {{ typeDesc[row.type] && typeDesc[row.type].desc }}
      </template>
      <template slot-scope="{ row }" slot="method">
        {{ methodDesc[row.method].desc }}
      </template>
      <template slot-scope="{ row }" slot="stock_in_price">
        {{ row.cost_info?.stock_in_price }}
      </template>
      <template slot-scope="{ row }" slot="after_cost_price">
        {{ row.cost_info?.after_cost_price }}
      </template>
      <template slot-scope="{ row }" slot="cost_change_money">
        {{ row.cost_info?.cost_change_money }}
      </template>
      <template slot-scope="{ row }" slot="after_cost_balance_price">
        {{ row.cost_info?.after_cost_balance_price }}
      </template>
      <template slot-scope="{ row }" slot="quantity">
        {{ row.warehouse_stock_text }}
      </template>
      <template slot-scope="{ row }" slot="operate">
        <!-- 跳转到订单 -->
        <k-link
          v-if="row.type == 23 || row.type == 24 || row.type == 25 || row.type == 26"
          :to="{ path: '/trade/order/detail', query: { orderid: row.orderId, orderType: 'shop_order' } }"
          target="_blank"
          >查看</k-link
        >
        <!--  跳转到出入库管理详情(1: 采购入库；2: 手工入库)    -->
        <k-link
          v-else-if="row.type == 1 || row.type == 2"
          :to="{
            path: '/stock/ostock/edit',
            query: {
              id: row.type_id,
              prod_type: row.prod_type,
              prodTypeText: row.prod_type_text,
              type: 'detail',
              status: row.type,
              from: 'bills_list',
            },
          }"
          target="_blank"
          >查看</k-link
        >
        <!--  跳转到盘点入库详情(27: 盘点出库；28: 盘点入库)   -->
        <k-link
          v-else-if="row.type == 27 || row.type == 28"
          :to="{
            path: '/stock/inventory/detail',
            query: {
              id: row.type_id,
              prod_type: row.prod_type,
              prodTypeText: row.prod_type_text,
              type: 'detail',
            },
          }"
          target="_blank"
          >查看</k-link
        >
        <k-link
          v-else-if="row.type == 10"
          :to="{
            path: '/stock/ostock/edit',
            query: {
              id: row.type_id,
              prod_type: row.prod_type,
              prodTypeText: row.prod_type_text,
              type: 'edit',
              status: row.type,
              from: 'bills_list',
            },
          }"
          target="_blank"
          >查看</k-link
        >
        <k-link
          v-else-if="row.type == 11 || row.type == 12 || row.type == 17 || row.type == 99"
          :to="{
            path: '/stock/ostock/outedit',
            query: {
              id: row.type_id,
              prod_type: row.prod_type,
              prodTypeText: row.prod_type_text,
              type: 'edit',
              status: row.type,
            },
          }"
          target="_blank"
          >查看</k-link
        >
        <!-- row.pt_id > 0跳转到his -->
        <a v-else-if="+row.pt_id > 0" @click="toNewClinic(row)">查看</a>
        <div v-else><span class="text-muted">查看</span></div>
      </template>
    </standard-table>
    <Tooltip
      ref="custom-tooltip"
      popper-class="generic-name-link-tip"
      transfer-class-name="generic-name-link-tip"
      :reference="tooltipReference"
      :delay="1000"
      padding="0 0"
      style="height: 0; width: 0; display: none"
    >
      <template #content>
        <generic-name-tip :row="selectedToolTipRow" />
      </template>
    </Tooltip>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import search from '@/mixins/search';
import * as runtime from '@/libs/runtime'; // Runtime information
import download from '@/mixins/downloadExcel';
import config from '@/config';
import StandardTable from "@/components/StandardTable/index.vue";
import GenericNameTip from "@/view/stock/components/generic-name-tip.vue";
import tooltip from "@/view/stock/mixins/tooltip";

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  prod_search_name: '',
  prod_type: '',
  supplier_id: '',
  type: '',
  method: '',
  source: '',
};

export default {
  name: 'bills_list',
  components: {GenericNameTip, StandardTable},
  mixins: [download, search, tooltip],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getProductStockBillsList',
      downloadApiName: '',
      downloadLoading: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      tableCols: [
        { title: '编码', slot: 'id', width: 80, align: 'center' },
        { title: '货品', slot: 'generic_name', width: 200, align: 'left' },
        { title: '类型', slot: 'prod_type', width: 80, align: 'center' },
        { title: '来源', slot: 'from_text', width: 80, align: 'center' },
        { title: '变更时间', slot: 'time', width: 130, align: 'center' },
        { title: '变更类型', slot: 'type', width: 120, align: 'center' },
        { title: '操作类型', slot: 'method', width: 100, align: 'center' },
        { title: '进价', slot: 'stock_in_price', width: 100, align: 'center' },
        { title: '成本价', slot: 'after_cost_price', width: 100, align: 'center' },
        { title: '成本变动', slot: 'cost_change_money', width: 100, align: 'center' },
        { title: '成本结余', slot: 'after_cost_balance_price', width: 100, align: 'center' },
        { title: '变更数量', slot: 'quantity', width: 100, align: 'center' },
        { title: '库存结余', key: 'stock_text', width: 100, align: 'center' },
        { title: '供应商	', slot: 'supplier_id', minWidth: 200, align: 'center' },
        { title: '批号	', slot: 'batch_code', width: 130, align: 'center' },
        { title: '生产日期', slot: 'produce_time', width: 130, align: 'center' },
        { title: '有效期', slot: 'expire_time', width: 130, align: 'center' },
        { title: '操作	', slot: 'operate', width: 100, fixed: 'right' },
        // { title: '规格', slot: 'prod_spec', resizable: true, width: 200 },
      ],
      tableLoading: true,
      list: [],
      total: 0,
      prods: {},
      suppliers: {}, //供应商
      prodTypes: [], //商品类型
      supplierTypes: {},
      stockDetails: {},
      typeDesc: {},
      methodDesc: {},
      statusDesc: {},
      prodSource: {},
      timeRange: [],
    };
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.timeRange = [this.queryFormData.st, this.queryFormData.et];
    this.submitQueryForm(true);
    this.getFormList();
  },

  methods: {
    // 跳转his
    toNewClinic(row) {
      let patientId = row.pt_id;
      let clinic_mr_id = row.mr_id;
      this.$api.getHisToken().then(
        res => {
          const a = document.createElement('a');
          a.target = '_blank';
          a.href = `${config.HISDomain}/login?token=${res.token}&id=${patientId}&clinic_mr_id=${clinic_mr_id}&isToPatientDetail=true`;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        },
        rej => {
          console.log(rej);
          {
          }
        }
      );
    },

    /* api导出 */
    exportExcel() {
      this.downloadApiName = 'getProdStockChangeUrl';
      // let params = {
      //   prod_search_name: this.queryFormData.prod_search_name,
      //   prod_type: this.queryFormData.prod_type,
      //   type: this.queryFormData.type,
      //   method: this.queryFormData.method,
      //   st: this.queryFormData.st,
      //   et: this.queryFormData.et,
      // };
      this.downloadExcel(this.queryFormData);
    },
    changeSearchTime(times) {
      this.queryFormData.st = (times && times[0]) || '';
      this.queryFormData.et = (times && times[1]) || '';
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    initSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          this.list = data.list;
          this.prods = data.prods;
          this.stockDetails = data.stockDetails;
          this.suppliers = data.suppliers;
          this.typeDesc = data.typeDesc;
          this.methodDesc = data.methodDesc;
          this.prodTypes = S.descToArrHandle(data.prodTypes).sort((a, b) => a.sort - b.sort);
          this.prodSource = data.sourceDesc;
          this.total = data.total;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },

    getFormList() {
      io.get('clinic/supplier.pullDownList')
        .then(data => {
          this.supplierTypes = data.list;
        })
        .catch(error => {
          {
          }
        });
    },
    disableClassName(row) {
      if (row.cost_info?.cost_available === '0') {
        return 'disableTableRow';
      }
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
};
</script>

<style lang="less">
.generic-name-link-tip {
  .ivu-tooltip-inner {
    max-width: unset;
    background-color: #FFFDEC;
  }
}
</style>
