<template>
  <div ref="tableWrapRef" @mouseover="hidePop">
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      size="small"
      class="pl-table"
      stripe
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="form-warpper">
          <Form class="form-warpper_left" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <FormItem>
              <Input type="text" v-model="queryFormData.prod_search_name" placeholder="货品名称/编码" />
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.prod_type" placeholder="全部类型">
                <Option value="">{{ '全部类型' }}</Option>
                <Option :value="item.id" v-for="item in prodTypes" :key="item.id">{{ item.name }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.supplier_id" placeholder="全部供应商">
                <Option value="">{{ '全部供应商' }}</Option>
                <Option v-for="item in supplierTypes" :key="item.id" :value="item.id">{{ item.name }}</Option>
              </Select>
            </FormItem>

            <FormItem>
              <Select v-model="queryFormData.source" placeholder="全部来源">
                <Option value="">全部来源</Option>
                <Option :value="index" v-for="(item, index) in prodSource" :key="index">{{ item.desc }}</Option>
              </Select>
            </FormItem>

            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="initSearch"
                ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
              >
            </FormItem>
          </Form>
        </div>
      </template>
      <template slot-scope="{ row }" slot="id">
        {{ row.prod_id }}
      </template>
      <template slot-scope="{ row }" slot="time">
        {{ row.create_time | data_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="prod_name">
        <product-show
          :generic_name="prods[row.prod_id].generic_name"
          :prod_spec="prods[row.prod_id].prod_spec"
          :grade_desc="prods[row.prod_id].grade_desc"
          :manufacturer="prods[row.prod_id].manufacturer"
          :row="prods[row.prod_id]"
          @showPop="showPop"
        ></product-show>
      </template>

      <!-- 入库单号 -->
      <template slot-scope="{ row }" slot="bill_code">
        {{ row.bill_code || '-' }}
      </template>

      <template slot-scope="{ row }" slot="warehouse_unit">
        {{ row.warehouse_unit || '-' }}
      </template>
      <template slot-scope="{ row }" slot="prod_type">
        <Tooltip :content="row.prod_type_text" :disabled="row.prod_type_text?.length < 5">
          <span class="ecs" :class="{ cursor: row.prod_type_text?.length > 4 }">{{ row.prod_type_text || '-' }}</span>
        </Tooltip>
      </template>

      <template slot-scope="{ row }" slot="supplier_id">
        {{ suppliers[row.supplier_id].name || '-' }}
      </template>
      <template slot-scope="{ row }" slot="batch_code">
        {{ row.batch_code || '-' || '-' }}
      </template>
      <template slot-scope="{ row }" slot="expire_time">
        {{ row.expire_time | data_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="purchase_stock_num">
        {{ row.warehouse_stock_text || '-' }}
      </template>
      <template slot-scope="{ row }" slot="retail_price">
        {{ prods[row.prod_id].retail_price }}/{{ prods[row.prod_id].prod_unit || '-' }}
      </template>
      <template slot-scope="{ row }" slot="purchase_price">
        ¥{{ row.purchase_price }}/{{ row.warehouse_unit || '-' }}
      </template>
      <template slot-scope="{ row }" slot="stock_num">
        {{ row.stock_text || '-' }}
      </template>
      <template slot-scope="{ row }" slot="produce_time">
        {{ row.produce_time | data_format('YYYY-MM-DD') }}
      </template>
      <template slot-scope="{ row }" slot="split_price">
        <div class="slot-name flex-c" v-if="row.split_price">
          <span class="prod-name"> 拆零价：{{ '¥ ' + row.split_price }}</span>
          <span class="generic-name">拆零单位： {{ row.split_prod_unit || '-' }}</span>
          <span class="generic-name">拆零比： {{ row.split_num || '-' }}</span>
        </div>
        <span v-else>-</span>
      </template>

      <!-- 操作 -->
      <template slot-scope="{ row }" slot="operate">
        <span style="color: #999" v-if="!row.bill_id">来源</span>
        <k-link v-else :to="getJumpPathObject(row)" target="_blank">来源</k-link>
      </template>
    </standard-table>
    <!-- <div class="bottomText">零售合计：149716271.05 采购合计：320884174.51</div> -->

    <Tooltip
      ref="custom-tooltip"
      popper-class="generic-name-link-tip"
      transfer-class-name="generic-name-link-tip"
      :reference="tooltipReference"
      :delay="1000"
      padding="0 0"
      style="height: 0; width: 0; display: none"
    >
      <template #content>
        <generic-name-tip :row="selectedToolTipRow" />
      </template>
    </Tooltip>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import search from '@/mixins/search';
import * as runtime from '@/libs/runtime';
import StandardTable from "@/components/StandardTable/index.vue";
import tooltip from "@/view/stock/mixins/tooltip";
import GenericNameTip from "@/view/stock/components/generic-name-tip.vue"; // Runtime information
/* eslint-disable */

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  prod_search_name: '',
  prod_type: '',
  supplier_id: '',
  source: '', // 全部来源
};

export default {
  name: 'list',
  components: {GenericNameTip, StandardTable},
  mixins: [search, tooltip],
  data() {
    return {
      queryFormData: {...init_query_form_data},

      tableCols: [
        {title: '批次编码	', key: 'id', minWidth: 80, align: 'center'},
        {title: '生产批号	', slot: 'batch_code', minWidth: 120, align: 'center'},
        {title: '入库时间', slot: 'time', minWidth: 100, align: 'center'},
        // { title: '入库数量	', slot: 'purchase_stock_num', minWidth: 80, align: 'center' },
        {title: '当前库存	', slot: 'stock_num', minWidth: 100, align: 'center'},
        {title: '生产时间', slot: 'produce_time', minWidth: 100, align: 'center'},
        {title: '有效期', slot: 'expire_time', minWidth: 100},
        {title: '编码', slot: 'id', minWidth: 100, align: 'center'},
        {title: '货品', slot: 'prod_name', minWidth: 200, align: 'left'},
        {title: '类型', slot: 'prod_type', minWidth: 100, align: 'center'},
        {title: '来源	', key: 'from_text', minWidth: 100, align: 'center'},
        {title: '供应商	', slot: 'supplier_id', minWidth: 160, align: 'center'},
        {title: '入库单号', slot: 'bill_code', align: 'center', minWidth: 200},
        // { title: '单位', slot: 'warehouse_unit' },
        // {title: '零售价', slot: 'retail_price'},
        // {title: '拆零价', slot: 'split_price'},
        // { title: '采购价', slot: 'purchase_price', width: 120 }
        {title: '操作', slot: 'operate', width: 100, align: 'center', fixed: 'right'},
      ],
      tableLoading: true,
      suppliers: {}, //供应商
      prodTypes: [], //商品类型
      prodSource: {}, // 全部来源
      supplierTypes: {},
      list: [],
      total: 0,
      statusDesc: {},
      prods: {},

      discountTypeDesc: {},
    };
  },

  created() {
    this.getStatusList().then(() => {
      this.queryFormData = S.merge(this.queryFormData, this.$route.query);
      this.submitQueryForm(true);
    });
    this.getFormList();
  },

  methods: {
    getJumpPathObject(row) {
      let query = {
        type: 'edit',
        id: row.bill_id,
        prod_type: row.prod_type,
        prodTypeText: row.prod_type_text,
        status: row.status,
        ostock_type: '10',
        sourceType: row.bill_source,
      };
      if (row.is_covid === '1') {
        query.is_covid = '1';
      }
      return {
        path: '/stock/ostock/edit',
        query,
      };
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    initSearch() {
      this.queryFormData = {...init_query_form_data};
      this.submitQueryForm();
    },
    onResetSearch: function () {
      this.queryFormData = {...init_query_form_data};
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    onChangeStatus: function (id, action) {
      io.post('brms/salespromotion.status', {id: id, act: action})
        .then(() => {
          this.$Message.success('操作成功');
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
          ;
        });
    },

    getsList: function () {
      this.tableLoading = true;
      io.get('clinic/product.stock.batch', {data: this.queryFormData})
        .then(data => {
          this.list = data.list;
          this.prods = data.prods;
          this.suppliers = data.suppliers;
          this.prodTypes = S.descToArrHandle(data.prodTypes).sort((a, b) => a.sort - b.sort);
          this.total = data.total;
          // 全部来源
          this.prodSource = data.prodSources;
        })
        .catch(error => {
          {
          }
          ;
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    getFormList() {
      io.get('clinic/supplier.pullDownList')
        .then(data => {
          this.supplierTypes = data.list;
        })
        .catch(error => {
          {
          }
          ;
        });
    },

    getStatusList() {
      let res = io.get('/clinic/supplier.options');
      res
        .then(data => {
          this.statusDesc = data.statusDesc;
        })
        .catch(error => {
          {
          }
          ;
        });
      return res;
    },

    handleList: function (list) {
      return list;
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getsList();
    next();
  },
};
</script>

<style lang="less">
.bottomText {
  margin-top: 20px;
}
.generic-name-link-tip {
  .ivu-tooltip-inner {
    max-width: unset;
    background-color: #FFFDEC;
  }
}
</style>
