<template>
<!--   导入商品销售价-excel-->
      <Modal
        v-model="importVisible"
        :mask-closable="false"
        title="导入商品销售价"
        @on-ok="importConfirm"
        @on-cancel="importCancel">
        <div class="import-content" v-if="importVisible">
          <div class="flex flex-item-align">
            <p>导入表格：</p>
            <KExcelUpload btnType="text" :excelUploadLoading="excelUploadLoading" btnText="选择表格"
                          @excelUpload="excelUpload"></KExcelUpload>
            <p class="download cursor hover" @click="templateDownload">下载导入模板</p>
          </div>
          <!-- btn -->
          <div class="flex flex-item-center" style="margin-top: 40px;">
            <Button type="default" class="space6" @click="importCancel">取消</Button>
            <Button type="primary" class="spacce6" @click="importConfirm" :disabled="isActiveBtn"
                    :loading="excelUploadLoading">导入
            </Button>
          </div>
          <!-- 错误报告 -->
          <div class="error-report flex flex-item-center" v-show="hasHandleExcelList.length && isImportSuccess">
            <p class="">本次成功导入{{ succ_num }}条记录，{{ fail_num }}条错误记录</p>
            <p class="download-error cursor hover" v-show="Number(fail_num) > 0" @click="seeReport">查看错误报告</p>
          </div>
        </div>
      </Modal>
</template>
<script>
export default {
  data() {
    return {
      importVisible: false, // 导入商品销售价modal
      reportVisible: false,
      exportLoading: false, // 模板下载loading
      excelUploadLoading: false, // 数据上传的loading
      handleExcelList: [], // 处理后要上传的excel数据
      hasHandleExcelList: [], // 准备导入的数据
      isImportSuccess: false, // 是否导入成功
      fail_num: 0, // 错误数量
      succ_num: 0, // 成功数量
      reportList: [], // 错误报告list
    }
  },
  methods: {
    // 查看错误报告
    seeReport() {
      this.importVisible = false
      this.reportVisible = true
    },
    // 导入商品售价事件
    importEvent() {
      this.importVisible = true
    },
    // 导入
    importConfirm() {
      this.saveExcel( this.hasHandleExcelList )
    },
    // 取消导入弹窗
    importCancel() {
      this.importVisible = false
    },
    // 模板下载
    templateDownload() {
      this.exportLoading = true
      const { name, prod_type, staus } = this.queryFormData
      let params = {
        name,
        prod_type,
        staus,
        type: ' PROD', // 商品模板标识
      }
      this.$api.getClinicTemplateurl( params ).then( res => {
        this.download( res.url )
      }, rej => this.$Message.error( rej.errmsg ) ).finally( () => this.exportLoading = false )
    },
    // 获取获取excel处理后的数据
    excelUpload( hasHandleExcelList ) {
      this.hasHandleExcelList = hasHandleExcelList
    },
    // api-上传处理后的excel数据
    saveExcel( hasHandleExcelList ) {
      this.excelUploadLoading = true
      this.$api.saveBatcheditprice( { batch_params: hasHandleExcelList,timeout: 60000 } ).then( res => {
        // this.$Message.success('导入成功')
        this.isImportSuccess = true
        this.succ_num = res.succ_num
        this.fail_num = res.fail_num
        this.reportList = res.fail_data
        // 重新拉取页面数据
        this.getsList()
      }, rej => this.$Message.error( rej.errmsg ) ).finally( () => {
        this.excelUploadLoading = false
      } )
    },
  },
}
</script>
