<template>
  <div>
    <Tabs :value="queryFormData.ostock_type" @on-click="onTabChange">
      <TabPane label="入库单" name="10"></TabPane>
      <TabPane label="出库单" name="20"></TabPane>
    </Tabs>
    <div v-show="queryFormData.ostock_type == '10'">
      <standard-table
        :loading="tableLoading"
        :columns="tableCols"
        :data="list"
        :extra-height="49"
        size="small"
        stripe
        class="pl-table"
        :total="total"
        :page-size.sync="queryFormData.pageSize"
        :current.sync="queryFormData.page"
        @on-change="onPageChange"
      >
        <template #header>
          <div class="flex list-fn-mb-distance">
            <Dropdown placement="bottom-start" @on-click="addHandle" class="mr10">
              <Button type="primary" v-eleControl="'ElvnwYBP88'">
                <Icon type="md-add" size="16" />
                创建
                <Icon type="ios-arrow-down" />
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem v-for="item in inStockProdTypes" :key="item.id" :name="item.id">
                  <Icon type="ios-add" size="16" />
                  {{ item.name }}
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
            <!--        <Button type="default" @click="jumpCovid">新冠抗疫专方入库</Button>-->
          </div>
          <div class="form-warpper">
            <Form
              class="form-warpper_left"
              inline
              :label-width="0"
              @submit.native.prevent
              @keyup.enter.native="onSearch"
            >
              <FormItem>
                <Input type="text" v-model="queryFormData.prod_name" placeholder="货品名称" />
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.prod_type" placeholder="全部货品类型">
                  <Option value="">全部货品类型</Option>
                  <Option v-for="item in prodTypeDesc" :value="item.id" :key="item.id">{{ item.name }}</Option>
                </Select>
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.bill_source" placeholder="全部入库类型">
                  <Option value="">全部入库类型</Option>
                  <Option v-for="(item, key) in sourceTypeDesc" :value="key" :key="key">{{ item.desc }}</Option>
                </Select>
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.supplier_id" placeholder="全部供应商">
                  <Option value="">全部供应商</Option>
                  <Option v-for="(item, key) in suppliers" :value="key" :key="key">{{ item.name }}</Option>
                </Select>
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.status" placeholder="全部入库状态">
                  <Option value="">全部入库状态</Option>
                  <Option v-for="(item, key) in statusDesc" :value="key" :key="key">{{ item.desc }}</Option>
                </Select>
              </FormItem>

              <br />

              <FormItem>
                <DatePicker
                  type="daterange"
                  format="yyyy-MM-dd"
                  v-model="timeRange"
                  @on-change="times => handleTimeChange(times)"
                  placeholder="建单时间"
                ></DatePicker>
              </FormItem>
              <!--          <FormItem>-->
              <!--            <DatePicker type="datetime" format="yyyy-MM-dd" v-model="queryFormData.et"-->
              <!--                        @on-change="queryFormData.et = arguments[0]" placeholder="结束时间"></DatePicker>-->
              <!--          </FormItem>-->
              <FormItem>
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <span class="list-reset-btn" @click="onResetSearch('10')">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Form>
          </div>
        </template>

        <template slot-scope="{ row }" slot="id">
          {{ row.id }}
        </template>
        <template slot-scope="{ row }" slot="source">
          {{ row.source ? sourceTypeDesc[row.source] && sourceTypeDesc[row.source].desc : '-' }}
        </template>
        <template slot-scope="{ row }" slot="ostock_code">
          {{ row.ostock_code }}
        </template>
        <template slot-scope="{ row }" slot="arrivalTime">
          {{ row.create_time | data_format('YYYY-MM-DD') }}
        </template>
        <template slot-scope="{ row }" slot="auditTime">
          {{ row.audit_time | data_format('YYYY-MM-DD') }}
        </template>

        <template slot-scope="{ row }" slot="stockTime">
          {{ row.stock_time | data_format('YYYY-MM-DD') }}
        </template>
        <template slot-scope="{ row }" slot="supplierId">
          {{ row.supplier_name }}
        </template>

        <template slot-scope="{ row }" slot="prod_type_text">
          <Tooltip :content="row.prod_type_text" :disabled="row.prod_type_text?.length < 5">
            <span class="ecs" :class="{ cursor: row.prod_type_text?.length > 4 }">{{ row.prod_type_text || '-' }}</span>
          </Tooltip>
        </template>

        <template slot-scope="{ row }" slot="operatorName">
          {{ row.operator_name || '-' }}
        </template>
        <template slot-scope="{ row }" slot="order_code">
          {{ row.order_code || '-' }}
        </template>
        <template slot-scope="{ row }" slot="totalPrice"> ¥ {{ row.total_price }}</template>

        <template slot-scope="{ row }" slot="remark">
          <Tooltip v-if="row.remark" :content="row.remark">
            <span class="ecs">{{ row.remark }}</span>
          </Tooltip>
          <span v-else>-</span>
        </template>

        <template slot-scope="{ row }" slot="prod_names">
          <Tooltip transfer max-width="400">
            <div slot="content">
              {{
                row.item_names.length
                  ? row.item_names.slice(0, 60).join('、') +
                    (row.item_names.length > 60 ? `……等${row.item_names.length}件货品` : '')
                  : '-'
              }}
            </div>
            <div class="ecs" style="cursor: pointer">{{ row.item_names.length ? row.item_names.join('、') : '-' }}</div>
          </Tooltip>
        </template>
        <template slot-scope="{ row }" slot="auditOperatorName">
          {{ row.audit_operator_name || '-' }}
        </template>

        <template slot-scope="{ row }" slot="auditOpinion">
          <Tooltip v-if="row.audit_opinion" :content="row.audit_opinion">
            <span class="ecs">{{ row.audit_opinion }}</span>
          </Tooltip>
          <span v-else>-</span>
        </template>

        <template slot-scope="{ row }" slot="status">
          <mark-status :type="getStatusTextColor(row.status)">
            {{ row.status && statusDesc[row.status] && statusDesc[row.status].desc }}
          </mark-status>
        </template>
        <template slot-scope="{ row }" slot="operate">
          <div class="operateList">
            <a @click="toDetail(row, '', 'detail')" class="mr10">详情</a>
            <a v-eleControl="'ElvnwYBP88'" @click="toDetail(row)" class="mr10" v-if="row.status == '70'">修改</a>
            <!--            <a @click="examineHandle(row)" class="mr10" v-if="row.status == 10">审核</a>-->

            <a v-eleControl="'EGW1zWo2p2'" @click="toDetail(row, 'WAIT_AUDIT')" class="mr10" v-if="row.status == 10"
              >审核</a
            >
            <Poptip
              confirm
              transfer
              width="200"
              title="确定撤销该入库单吗? 撤销后将不能恢复"
              @on-ok="deleteHandle(row)"
            >
              <a v-eleControl="'ElvnwYBP88'" class="mr10" v-if="row.status == 10 && row.source === 'CLI'">撤销</a>
            </Poptip>
          </div>
        </template>
      </standard-table>
    </div>
    <div v-show="queryFormData.ostock_type == '20'">
      <standard-table
        :loading="tableLoading"
        :columns="tableCols2"
        :data="list"
        :extra-height="49"
        size="small"
        stripe
        class="pl-table"
        :total="total"
        :page-size.sync="queryFormData.pageSize"
        :current.sync="queryFormData.page"
        @on-change="onPageChange"
      >
        <template #header>
          <div class="flex list-fn-mb-distance">
            <Dropdown placement="bottom-start" @on-click="addHandle">
              <Button type="primary" v-eleControl="'E58oXxvk2l'">
                <Icon type="md-add" size="16" />
                创建
                <Icon type="ios-arrow-down" />
              </Button>
              <DropdownMenu slot="list">
                <DropdownItem v-for="item in outStockProdTypes" :key="item.id" :name="item.id">
                  <Icon type="ios-add" size="16" />
                  {{ item.name }}
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          </div>
          <div class="form-warpper">
            <Form
              class="form-warpper_left"
              inline
              :label-width="0"
              @submit.native.prevent
              @keyup.enter.native="onSearch"
            >
              <FormItem>
                <Input type="text" v-model="queryFormData.prod_name" placeholder="货品名称" />
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.prod_type" placeholder="全部货品类型">
                  <Option value="">全部货品类型</Option>
                  <Option v-for="item in prodTypeDesc" :value="item.id" :key="item.id">{{ item.name }}</Option>
                </Select>
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.out_type" placeholder="全部出库类型">
                  <Option value="">全部出库类型</Option>
                  <Option v-for="(item, key) in stockTypeOutDesc" :value="key" :key="key">{{ item.desc }}</Option>
                </Select>
              </FormItem>

              <FormItem>
                <Select v-model="queryFormData.status" placeholder="全部出库状态">
                  <Option value="">全部出库状态</Option>
                  <Option v-for="(item, key) in statusDesc" :value="key" :key="key">{{ item.desc }}</Option>
                </Select>
              </FormItem>
              <FormItem>
                <DatePicker
                  type="daterange"
                  format="yyyy-MM-dd"
                  v-model="timeRange"
                  @on-change="times => handleTimeChange(times)"
                  placeholder="建单时间"
                ></DatePicker>
              </FormItem>
              <FormItem>
                <Button type="primary" @click="onSearch">查询</Button>
                <Dvd />
                <Dvd />
                <span class="list-reset-btn" @click="onResetSearch('20')"
                  ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
                >
              </FormItem>
            </Form>
          </div>
        </template>
        <template slot-scope="{ row }" slot="id">
          {{ row.id }}
        </template>
        <template slot-scope="{ row }" slot="out_type">
          {{ stockTypeOutDesc[row.out_type] && stockTypeOutDesc[row.out_type].desc }}
        </template>

        <template slot-scope="{ row }" slot="prod_type_text">
          <Tooltip :content="row.prod_type_text" :disabled="row.prod_type_text?.length < 5">
            <span class="ecs" :class="{ cursor: row.prod_type_text?.length > 4 }">{{ row.prod_type_text || '-' }}</span>
          </Tooltip>
        </template>

        <template slot-scope="{ row }" slot="ostockCode">
          {{ row.ostock_code }}
        </template>
        <template slot-scope="{ row }" slot="createTime">
          {{ row.create_time | data_format('YYYY-MM-DD') }}
        </template>
        <template slot-scope="{ row }" slot="stockTime">
          {{ row.stock_time | data_format('YYYY-MM-DD') }}
        </template>

        <template slot-scope="{ row }" slot="auditTime">
          {{ row.audit_time | data_format('YYYY-MM-DD') }}
        </template>

        <!--        <template slot-scope="{row}" slot="supplierId">-->
        <!--          {{ row.supplier_id && suppliers[row.supplier_id].name }}-->
        <!--        </template>-->
        <template slot-scope="{ row }" slot="operatorName">
          {{ row.operator_name || '-' }}
        </template>
        <template slot-scope="{ row }" slot="prod_names">
          <Tooltip transfer max-width="400">
            <div slot="content">
              {{
                row.item_names.length
                  ? row.item_names.slice(0, 60).join('、') +
                    (row.item_names.length > 60 ? `……等${row.item_names.length}件货品` : '')
                  : '-'
              }}
            </div>
            <div class="ecs" style="cursor: pointer">{{ row.item_names.length ? row.item_names.join('、') : '-' }}</div>
          </Tooltip>
        </template>
        <template slot-scope="{ row }" slot="order_code">
          {{ row.order_code || '-' }}
        </template>
        <template slot-scope="{ row }" slot="totalPrice"> ¥ {{ row.total_price }}</template>

        <template slot-scope="{ row }" slot="remark">
          <Tooltip v-if="row.remark" :content="row.remark">
            <span class="ecs">{{ row.remark }}</span>
          </Tooltip>
          <span v-else>-</span>
        </template>

        <template slot-scope="{ row }" slot="auditOperatorName">
          {{ row.audit_operator_name || '-' }}
        </template>
        <template slot-scope="{ row }" slot="auditOpinion">
          <Tooltip v-if="row.audit_opinion" :content="row.audit_opinion">
            <span class="ecs">{{ row.audit_opinion }}</span>
          </Tooltip>
          <span v-else>-</span>
        </template>

        <template slot-scope="{ row }" slot="status">
          <mark-status :type="getStatusTextColor(row.status)">
            {{ row.status && statusDesc[row.status] && statusDesc[row.status].desc }}
          </mark-status>
        </template>

        <template slot-scope="{ row }" slot="operate">
          <div class="operateList">
            <!--            <router-link-->
            <!--              class="mr10"-->
            <!--              :to="{-->
            <!--                path: '/stock/ostock/outedit',-->
            <!--                query: {-->
            <!--                  type: 'edit',-->
            <!--                  id: row.id,-->
            <!--                  prod_type: row.prod_type,-->
            <!--                  prodTypeText: prodTypes[row.prod_type],-->
            <!--                  status: row.status,-->
            <!--                  ostock_type: queryFormData.ostock_type-->
            <!--                }-->
            <!--              }"-->
            <!--            >-->
            <!--              &lt;!&ndash;              {{ (row.status == '20'|| row.status == '80')? '详情' : '编辑' }}&ndash;&gt;-->
            <!--              详情-->
            <!--            </router-link>-->
            <a class="mr10" @click="toDetail(row, '', 'detail')">详情</a>
            <!--            <a @click="examineHandle(row)" class="mr10" v-if="row.status == 10">审核</a>-->
            <a v-eleControl="'EomnPm88db'" @click="toDetail(row, 'WAIT_AUDIT')" class="mr10" v-if="row.status == 10"
              >审核</a
            >
            <Poptip
              confirm
              transfer
              width="200"
              title="确定撤销该出库单吗? 撤销后将不能恢复"
              @on-ok="deleteHandle(row)"
            >
              <a v-eleControl="'E58oXxvk2l'" class="mr10" v-if="row.status == 10">撤销</a>
            </Poptip>
          </div>
        </template>
      </standard-table>
    </div>
    <!--    <ExamineModal ref="examineModal" :currentRow="currentRow" @getsList="getsList" />-->
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import search from '@/mixins/search';
/* eslint-disable */
import moment from 'moment';
import renderHeader from '@/mixins/renderHeader';
import StandardTable from "@/components/StandardTable/index.vue";
// import ExamineModal from './components/examineModal';

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  prod_name: '', // 货品名称
  supplier_id: '', // 全部供应商
  bill_source: '',
  out_type: '', // 全部出库类型
  status: '',
  ostock_type: '10',
  prod_type: '',
  st: '',
  et: '',
};

export default {
  name: 'list',
  components: {
    StandardTable
    // ExamineModal
  },
  mixins: [search, renderHeader],
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getOStockList',
      tableCols: [
        { title: '序号', slot: 'id', minWidth: 90, align: 'center' },
        { title: '建单时间', slot: 'arrivalTime', minWidth: 130, align: 'center' },
        { title: '入库类型	', slot: 'source', minWidth: 100, align: 'center' },
        { title: '供应商	', slot: 'supplierId', minWidth: 170, align: 'center' },
        { title: '货品类型	', slot: 'prod_type_text', minWidth: 100, align: 'center' },
        { title: '入库货品', slot: 'prod_names', minWidth: 130, align: 'center' },
        { title: '入库总额', slot: 'totalPrice', minWidth: 100, align: 'center' },
        { title: '建单人', slot: 'operatorName', minWidth: 130, align: 'center' },
        { title: '审核人', slot: 'auditOperatorName', minWidth: 70, align: 'center' },
        { title: '审核时间', slot: 'auditTime', minWidth: 130, align: 'center' },
        { title: '审核意见	', slot: 'auditOpinion', width: 100, align: 'center' },
        { title: '入库时间', slot: 'stockTime', minWidth: 130, align: 'center' },
        { title: '入库单号', slot: 'ostock_code', minWidth: 200, align: 'center' },
        {
          title: '关联单号',
          slot: 'order_code',
          minWidth: 130,
          align: 'center',
          renderHeader: (h, params) => this._renderHeader(h, params, '针对采购入库，关联单号是银联采购的单号'),
        },
        { title: '备注', slot: 'remark', minWidth: 180, align: 'center' },
        { title: '状态', slot: 'status', width: 100, fixed: 'right' },
        { title: '操作', slot: 'operate', width: 140, fixed: 'right' },
      ],
      tableCols2: [
        { title: '序号', slot: 'id', minWidth: 90, align: 'center' },
        { title: '创建时间', slot: 'createTime', minWidth: 130, align: 'center' },
        { title: '出库类型', slot: 'out_type', minWidth: 100, align: 'center' },
        { title: '货品类型', slot: 'prod_type_text', minWidth: 100, align: 'center' },
        { title: '出库货品', slot: 'prod_names', minWidth: 130, align: 'center' },
        { title: '出库总额', slot: 'totalPrice', minWidth: 100, align: 'center' },
        { title: '建单人', slot: 'operatorName', minWidth: 130, align: 'center' },
        { title: '审核人	', slot: 'auditOperatorName', minWidth: 70, align: 'center' },
        { title: '审核意见', slot: 'auditOpinion', minWidth: 100, align: 'center' },
        { title: '审核时间', slot: 'auditTime', minWidth: 130, align: 'center' },
        { title: '出库时间', slot: 'stockTime', minWidth: 130, align: 'center' },
        { title: '备注', slot: 'remark', minWidth: 100, align: 'center' },
        { title: '出库单号', slot: 'ostockCode', minWidth: 200, align: 'center' },
        { title: '状态', slot: 'status', width: 100, fixed: 'right' },
        { title: '操作', slot: 'operate', width: 140, fixed: 'right' },
      ],
      timeRange: [],
      tableLoading: false,
      cityList: [
        {
          value: 'New York',
          label: 'New York',
        },
        {
          value: 'London',
          label: 'London',
        },
      ],
      list: [],
      total: 0,
      // statusDescList: [],
      // prodTypesList: [],
      // statusDescObj: {},
      // currentRow: {},
      // prodTypeText: '',
      suppliers: {},

      prodTypes: {},
      prodTypeDesc: [],
      outStockProdTypes: [],
      inStockProdTypes: [],
      statusDesc: {},
      stockTypeOutDesc: {},
      sourceTypeDesc: {}, // 入库类型
    };
  },

  computed: {
    getStatusTextColor() {
      return status => {
        switch (status) {
          case '20':
            return 'success';
          case '10':
            return 'warn';
          case '80':
            return 'gray';
          case '70':
            return 'reject';
        }
      };
    },
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.ostock_type = this.$route.query.ostock_type || '10';

    this.getOptionsList();
  },

  methods: {
    toDetail(row, examineStatus = '', type = 'edit') {
      console.log('-> %c row  ===    %o', 'font-size: 15px;color: #fa8c16 ;', row);
      let path = '';
      if (this.queryFormData.ostock_type === '10') {
        path = '/stock/ostock/edit';
      } else {
        path = '/stock/ostock/outedit';
      }
      let query = {
        type,
        id: row.id,
        prod_type: row.prod_type,
        prodTypeText: row.prod_type_text,
        status: row.status,
        ostock_type: this.queryFormData.ostock_type,
        sourceType: row.source,
        examineStatus,
      };
      if (row.is_covid === '1') {
        query.is_covid = '1';
      }
      this.$router.push({
        path,
        query,
      });
    },
    jumpCovid() {
      this.$router.push({
        path: '/stock/ostock/edit',
        query: {
          prod_type: '2',
          prodTypeText: '中药饮片',
          is_covid: '1',
        },
      });
    },
    changeTimes(times) {
      this.queryFormData.st = (times && times[0]) || '';
      this.queryFormData.et = (times && times[1]) || '';
    },
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    onResetSearch: function (ostock_type) {
      this.queryFormData = { ...init_query_form_data, ostock_type };
      this.timeRange = [];
      this.submitQueryForm();
    },
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },
    getsList() {
      this.tableLoading = true;
      this.$api[this.apiName](this.queryFormData)
        .then(data => {
          if (!S.isEmptyObject(data.suppliers)) {
            for (const supplier in data.suppliers) {
              data.list.map(item => {
                if (item.supplier_id === supplier) {
                  item.supplier_name = data.suppliers[supplier].name;
                }
              });
            }
            this.suppliers = data.suppliers;
          }
          this.list = data.list;
          this.total = data.total;
        })
        .catch(error => {
          {
          }
        })
        .finally(() => {
          this.tableLoading = false;
          this.$store.commit('app/CHANGE_FRESH_STATUS', false);
        });
    },
    handleList: function (list) {
      if (list && list.length && list.length > 0) {
        list.map((item, i) => {
          item.startTime = moment(Number(item.start_time)).format('YYYY-MM-DD HH:mm');
          item.endTime = moment(Number(item.end_time)).format('YYYY-MM-DD HH:mm');
        });
      }
      return list;
    },

    addHandle(prod_type) {
      let prodTypeText = '',
        path = '';
      if (this.queryFormData.ostock_type === '10') {
        prodTypeText = this.inStockProdTypes.find(item => item.id === prod_type).name;
        path = '/stock/ostock/edit';
      } else {
        prodTypeText = this.outStockProdTypes.find(item => item.id === prod_type).name;
        path = '/stock/ostock/outedit';
      }
      this.$router.push({
        path,
        query: {
          type: 'add',
          prod_type,
          prodTypeText,
          ostock_type: this.ostock_type,
        },
      });
    },

    getOptionsList() {
      io.get('/clinic/ostock.options', {
        data: {
          source: 'prod_in_stock',
        },
      })
        .then(data => {
          this.prodTypes = this.sortTypeDesc(data.prodTypes);
          this.prodTypeDesc = this.sortTypeDesc(data.prodTypeDesc);
          this.inStockProdTypes = this.sortTypeDesc(data.inStockProdTypes);
          this.outStockProdTypes = this.sortTypeDesc(data.outStockProdTypes);
          this.statusDesc = data.statusDesc;
          this.stockTypeOutDesc = data.stockTypeOutDesc;
          this.sourceTypeDesc = data.sourceDesc;
          this.submitQueryForm(true);
        })
        .catch(error => {
          {
          }
        });
    },
    sortTypeDesc(types) {
      return S.descToArrHandle(types).sort((a, b) => a.sort - b.sort);
    },
    // examineHandle(row) {
    //   this.currentRow = row;
    //   this.$refs.examineModal.showModal = true;
    //   this.$refs.examineModal.formValidate.examine = 'PASS';
    //   this.$refs.examineModal.formValidate.remark = '';
    // },
    // 撤销
    deleteHandle(row) {
      let params = {
        id: row.id,
        act: 'STATUS_REVOKE',
      };
      io.post('/clinic/ostock.status', params)
        .then(data => {
          this.$Message.success({
            content: '撤销成功',
            onClose: () => {},
          });
          this.onSearch();
        })
        .catch(error => {
          {
          }
        });
    },
    onTabChange: function (type) {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.queryFormData.page = 1;
      this.queryFormData.ostock_type = type;
      this.submitQueryForm();
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
};
</script>

<style lang="less">
.operateList {
  display: flex;
  align-items: center;
}

.mr10 {
  margin-right: 10px;
}
</style>
