<template>
  <Modal
    :value="showModal"
    title="调整价格比例"
    @on-visible-change="changeVisibleHandle"
    width="820px"
    style="min-width: 720px;"
    :mask-closable="false"
  >
    <div>
      <Table :columns="tableCols" :data="dataSource" size="small" border stripe>
        <template slot-scope="{row, index}" slot="recentPurchasePrice">
          {{ row.purchasePrice }}元
        </template>
        <template slot-scope="{row, index}" slot="retailPrice">
          <InputNumber v-model="dataSource[index].newRetailPrice" @on-change="(e)=>changeRetailPrice(e,index)"
                       placeholder="当前零售价" :precision="4" :min="0" :active-change="false"></InputNumber>
        </template>
        <template slot-scope="{row, index}" slot="retailStatus">
          <div style="text-align: center;">
            <div v-if="typeof row.ratio === 'number'">
              <div v-if="(Number(row.ratio)<getMinRatio())"
                   style="display: flex;justify-content: center;align-items: center">
                偏低
                <Icon type="ios-arrow-round-down" style="font-size: 20px;color: red;font-weight: bold;"/>
              </div>
              <div v-else-if="(Number(row.ratio)>getMaxRatio())"
                   style="display: flex;justify-content: center;align-items: center">
                偏高
                <Icon type="ios-arrow-round-up"
                      style="font-size: 20px;line-height: normal;color: green;font-weight: bold;"/>
              </div>
              <div v-else>正常</div>
            </div>
            <span v-else>-</span>
          </div>

        </template>
      </Table>
    </div>
    <div slot="footer">
      <Button @click="restoreRetailPrice">还原零售价</Button>
      <Button @click="adjustAll" style="margin: 0 10px;">全部调整</Button>
      <Button @click="adjustLowPrice">只调整售价偏低的商品</Button>
      <Button type="primary" @click="okHandle" style="margin-left: 10px;" :loading="submitLoading">保存并退出</Button>
    </div>
  </Modal>
</template>

<script>
import cloneDeep from 'lodash.clonedeep'
import { $operator } from '@/libs/operation'

export default {
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    remindDrugs: {
      type: Array,
      default: () => []
    },
    adjustAndSave: {
      type: Function
    },
    ratioObj: {
      type: Object,
      default: () => ({
        ratio: 0,
        offset: 0
      })
    },
  },
  computed: {
    remindDrugNames() {
      return this.remindDrugs.map( item => item.goodsname ).join( '、' )
    },
    getMinRatio() {
      return ()=> $operator.subtract(this.ratioObj.ratio, this.ratioObj.offset)
    },
    getMaxRatio() {
      return ()=>$operator.add(this.ratioObj.ratio, this.ratioObj.offset)
    }
  },
  watch: {
    remindDrugs: {
      handler( val ) {
        if ( val.length ) {
          const newVal = cloneDeep( val )
          console.log( '-> %c newVal  === %o ', 'font-size: 15px', newVal )

          this.dataSource = newVal
        }
      }
    },
    // showModal: {
    //   handler( val ) {
    //     if ( val ) {
    //       this.getRatio()
    //     }
    //   }
    // }
  },
  data() {
    return {
      loading: false,

      tableCols: [
        { title: '序号', type: 'index', width: 50 },
        { title: '商品编码', key: 'number', minWidth: 50 },
        { title: '商品名称', key: 'goodsname', minWidth: 50 },
        { title: '单位', key: 'prod_unit', minWidth: 50 },
        { title: '最新进货价', slot: 'recentPurchasePrice', minWidth: 50 },
        { title: '当前零售价', slot: 'retailPrice', minWidth: 50 },
        {
          title: '零售进货价比', key: 'ratio', minWidth: 80, renderHeader: ( h, params ) => {
            return h( 'div', {
              style: {
                display: 'flex'
              }
            }, [
              h( 'span', '零售采购比' ),
              h( 'Tooltip', {
                    props: {
                      placement: 'bottom',
                      transfer: true,
                      transferClassName: 'table-head-tol',
                      maxWidth: 280,
                      width: 280
                    },
                  }, [
                    h( 'Icon', {
                      props: {
                        type: 'md-help-circle'
                      },
                      style: {
                        marginLeft: '0px',
                        fontSize: '16px',
                        cursor: 'pointer'
                      }
                    } ),
                    h( 'div', {
                      slot: 'content',
                      style: {
                        lineHeight: 1.5,
                        height: '200px',

                      }
                    }, [
                      h( 'div', {
                        style: {
                          width: '100%',
                          wordBreak: 'break-all',
                          overflowWrap: 'break-word',
                        }
                      }, '注：当前零售采购比仅针对【中药饮片】而言' ),
                      h( 'div', {
                        style: {
                          width: '100%',
                        }
                      }, `零售进货比${this.ratioObj.offset==0?`为${this.getMinRatio()}时`:`在${this.getMinRatio()}-${this.getMaxRatio()}之间`} ，系统判定零售价为正常状态；`, ),
                      h( 'div', {
                        style: {
                          width: '100%',
                        }
                      }, ` 小于${this.getMinRatio()}时，系统判定售价偏低，按此价格售卖会有亏损风险；`, ),
                      h( 'div', {
                        style: {}
                      }, `大于${this.getMaxRatio()}时，系统判定售价偏高，销售溢价偏高会影响用户购买率；` ),
                      h( 'div', {}, `如果按照系统规则调整，会将问题商品的零售进货比调整为${this.ratioObj.ratio}，门店可在该基础上自行微调。`, {
                        style: {
                          width: '100%',
                        }
                      } )
                    ], )
                  ],
              )
            ] )
          }
        },
        { title: '零售价状态', slot: 'retailStatus', minWidth: 40 },
      ],
      btnLoading: false,
      dataSource: [],
      submitLoading: false,
    }
  },
  methods: {
    //还原零售价 1.还原零售价 ---- 还原到出现该弹窗时的初始价格
    restoreRetailPrice() {
      this.dataSource.map( item => {
        item.newRetailPrice = Number( item.retailPrice )
        item.ratio = $operator.divide( Number( item.newRetailPrice ), Number( item.purchasePrice ) )
      } )
    },
    //全部调整     * 2.全部调整 ---- 将比例>3.2和<2.8的所有商品，都调整为3
    adjustAll() {
      this.dataSource.map( item => {
        item.newRetailPrice = $operator.multiply( item.purchasePrice, this.ratioObj.ratio, 4 )
        item.ratio = $operator.divide( Number( item.newRetailPrice ), Number( item.purchasePrice ) )
      } )
    },
    //只调整售价偏低的商品  3.只调整售价偏低的商品  ---- 将比例<2.8的所有商品，都调整为3
    adjustLowPrice() {
      this.dataSource.map( item => {
        if ( item.ratio < this.getMinRatio() ) {
          item.newRetailPrice = $operator.multiply( item.purchasePrice, this.ratioObj.ratio, 4 )
          item.ratio = $operator.divide( Number( item.newRetailPrice ), Number( item.purchasePrice ) )
        }
      } )
    },
    //调整零售价
    changeRetailPrice( val, index ) {
      !val && (val = 0)
      console.log( '-> %c val,index  === %o ', 'font-size: 15px', val, index )
      this.dataSource[index].ratio = $operator.divide( val, Number( this.dataSource[index].purchasePrice ) )
    },
    // 保存并退出  4.保存并退出 ---- 系统批量修改这部分商品的零售价，并关闭该弹窗，结束这次的入库流程
    okHandle() {
      const items = this.dataSource.filter( item => item.retailPrice !== item.newRetailPrice ).map( item => ({
        prod_id: item.number,
        retail_price: item.retailPrice,
        new_retail_price: item.newRetailPrice,
        unit: item.prod_unit
      }) )
      console.log( '-> %c items  === %o ', 'font-size: 15px', items )

      if ( items.length ) {
        this.submitLoading = true
        this.$api.updateRetailPrice( { items } ).then( res => {
          console.log( '-> %c res  === %o ', 'font-size: 15px', res )
          this.submitLoading = false
          this.adjustAndSave( this.dataSource )
        }, err => {
          this.$Message.error( err.errmsg )
        } )
      } else {
        this.adjustAndSave( this.dataSource )
      }

    },
    changeVisibleHandle( visible ) {
      console.log( '-> %c visible  === %o ', 'font-size: 15px', visible )
      !visible && this.$emit( 'update:showModal', false )
    },
    // getRatio() {
    //   this.$api.getRetailPurchaseRatio().then( res => {
    //     console.log( '-> %c res  === %o', 'font-size: 15px;color: green;', res )
    //     this.ratioObj.ratio = +res.ratio
    //     this.ratioObj.offset = +res.offset
    //   } )
    // }
  }
}
</script>
<style lang="less" scoped>
::v-deep .ivu-modal-body {
  height: 500px;
  overflow-y: auto;
}

//::v-deep .ivu-modal-footer {
//  display: block!important;
//}

</style>
