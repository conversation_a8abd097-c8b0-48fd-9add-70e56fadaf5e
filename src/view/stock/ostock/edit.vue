<template>
  <div class="ostockEdit" ref="tableWrapRef" @mouseover="hidePop">
    <Form ref="myForm" :model="formValidate" :rules="ruleValidate" :label-width="80">
      <Row>
        <!--              :remote-method="querySupplierList"-->
        <Col span="8">
          <FormItem label="供应商" prop="supplier">
            <Select
              v-model="formValidate.supplier"
              placeholder="请选择供应商"
              filterable
              @on-query-change="querySupplierList"
              ref="supplier_se"
              :clearable="true"
              :disabled="isSuplierDisabled"
            >
              <Option v-for="item in supplierList" :value="item.id" :key="item.id">{{ item.name }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="建单人" prop="user">
            <Input
              type="text"
              v-model.trim="formValidate.user"
              placeholder=""
              :disabled="true"
              @οn-change="val => (formValidate.user = val)"
            />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="入库单号" prop="ostock_code">
            <Input type="text" v-model="formValidate.ostock_code" placeholder="系统自动生成" :disabled="true" />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <FormItem label="建单时间" prop="create_time">
            <DatePicker
              type="datetime"
              format="yyyy-MM-dd"
              placeholder=""
              v-model="formValidate.create_time"
              :disabled="true"
            ></DatePicker>
          </FormItem>
        </Col>

        <Col span="8">
          <FormItem label="入库类型" prop="type">
            <Select v-model="formValidate.type" :disabled="true" placeholder="入库类型">
              <Option v-for="(item, key) in sourceTypeDesc" :value="key" :key="key">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col span="8">
          <!--          <FormItem label="验货时间">-->
          <!--            <DatePicker-->
          <!--              type="datetime"-->
          <!--              format="yyyy-MM-dd"-->
          <!--              placeholder=""-->
          <!--              v-model="formValidate.inspectionTime"-->
          <!--              :disabled="isDisatbed"-->
          <!--            ></DatePicker>-->
          <!--          </FormItem>-->
          <FormItem label="关联单号" prop="order_code">
            <Input type="text" v-model="formValidate.order_code" placeholder="针对采购入库有效" :disabled="true" />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="8">
          <FormItem label="附件上传">
            <MaterialPicture
              v-model="formValidate.images"
              :limit="9"
              :disabled="isDisatbed || !!is_cf_import || isExamine || isDetail"
            />
            <span class="images-tip">建议上传对应的随货单等，方便后续回溯；单张图片不超过3M，最多9张</span>
          </FormItem>
        </Col>

        <Col span="16">
          <FormItem label="备注" prop="remark">
            <Input
              v-model="formValidate.remark"
              placeholder="请输入备注内容"
              type="textarea"
              maxlength="200"
              show-word-limit
              :autosize="{ minRows: 2, maxRows: 2 }"
              :disabled="isDisatbed || isExamine || isDetail"
            />
          </FormItem>
        </Col>
      </Row>
      <Tabs v-model="currentComponent" @on-click="tabClick">
        <TabPane
          :label="
            tab_item.id === 'ostockDetail'
              ? `${tab_item.componentName}（${$route.query.prodTypeText}）`
              : tab_item.componentName
          "
          :name="tab_item.id"
          v-for="tab_item in tabComponents"
          :key="tab_item.id"
          v-if="(tab_item.id === 'operatorHistory' && $route.query.id) || tab_item.id !== 'operatorHistory'"
        ></TabPane>
      </Tabs>

      <div v-if="currentComponent === 'ostockDetail'">
        <div class="block-header flex flex-item-align flex-item-between" style="margin-top: 0">
          <div>
            入库明细 <span v-if="prodTypeText">（{{ prodTypeText }}）</span>
          </div>
          <div class="flex" v-if="!is_covid && !isDisatbed && !isExamine && !isDetail">
            <a
              class="space6"
              @click="changFanUploadCheck"
              v-show="prod_type === '2' || prod_type === '3' || prod_type === '15' || prod_type === '16'"
              >常繁线下订单导入</a
            >

            <a class="space6" @click="templateDownload" :loading="exportLoading">模板下载</a>
            <KExcelUpload
              v-show="!is_cf_import"
              ref="shuJiaUpload"
              :isStock="true"
              :excelUploadLoading="excelUploadLoading"
              btnText="批量导入"
              btnType="text"
              @stockExcelUpload="stockExcelUpload"
            ></KExcelUpload>
            <a v-show="is_cf_import" disabled>批量导入</a>
          </div>
        </div>

        <Table
          :columns="dynamicTableCols"
          :data="list"
          size="small"
          border
          stripe
          :loading="tableLoading"
          :height="$store.state.app.clientHeight - 500"
        >
          <template slot-scope="{ row, index }" slot="number">
            {{ index + 1 }}
          </template>
          <template slot-scope="{ row }" slot="goodscode">
            {{ row.number }}
          </template>
          <template slot-scope="{ row }" slot="goodsname">
            <product-show
              :generic_name="row.goodsname"
              :prod_spec="row.prod_spec"
              :grade_desc="row.grade_desc"
              :manufacturer="row.manufacturer"
              :row="formatSelectedRow(row)"
              @showPop="showPop"
            ></product-show>
          </template>
          <!--        <template slot-scope="{ row }" slot="manufactor">-->
          <!--          {{ row.manufactor }}-->
          <!--        </template>-->
          <template slot-scope="{ row }" slot="last_ostock_price">
            {{ row.last_ostock_price ? `¥${row.last_ostock_price}/${row.prod_unit}` : '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="company">
            <!-- {{row.company}} -->
            <Select
              @on-change="e => changeCompanyHandle(e, index, row)"
              :value="row.prod_unit"
              :disabled="isDisatbed || !!is_cf_import || isExamine || isDetail"
              placeholder="单位"
              style="width: 100%"
            >
              <Option v-for="(compItem, compIndex) in row.sales_units" :value="compItem.unit" :key="compItem.unit">
                {{ compItem.unit }}
              </Option>
            </Select>
          </template>
          <template slot-scope="{ row, index }" slot="quantity">
            <!-- <Input :value="row.quantity || ''" placeholder="" style="" type="number"
                   @on-change="(e) => changeQuantityHandle(e, index)" :disabled="isDisatbed"/> -->
            <InputNumber
              v-model="list[index].quantity"
              :min="0"
              :max="row.prod_unit === 'Kg' ? 100 : 90000000"
              placeholder="数量"
              style="width: 100%"
              :precision="getPrecision(row)"
              @on-focus="e => removeZero(e, index, 'quantity')"
              @on-change="e => changeQuantityHandle(e, index)"
              :disabled="isDisatbed || !!is_cf_import || isExamine || isDetail"
              :active-change="false"
            />
          </template>
          <template slot-scope="{ row, index }" slot="purchasePrice">
            <InputNumber
              style="width: 100%"
              v-model="list[index].purchasePrice"
              :precision="4"
              :placeholder="`每${row.prod_unit}采购价`"
              :active-change="false"
              @on-focus="e => purchaseInputFocus(e, index, 'purchasePrice', row)"
              @on-blur="() => purchaseInputBlur(index)"
              :min="0"
              @on-change="e => changePurchasePriceHandle(e, index)"
              :disabled="isDisatbed || !!is_covid || !!is_cf_import || isExamine || isDetail"
            />
          </template>
          <!--        <template slot-scope="{ row, index }" slot="costPrice">-->
          <!--          <Input-->
          <!--            :value="row.costPrice || ''"-->
          <!--            type="number"-->
          <!--            placeholder=""-->
          <!--            style=""-->
          <!--            @on-change="(e) => changeCostPriceHandle(e, index)"-->
          <!--            :disabled="isDisatbed"-->
          <!--          />-->
          <!--        </template>-->
          <template slot-scope="{ row, index }" slot="retailPrice">
            <!-- <Input :value="row.subtotal || ''" type="number" placeholder="" style="" @on-blur="(e) => changeSubtotalHandle(e, index)"  :disabled="valDisabledHandle(row, index) || isDisatbed" /> -->
            <!--          <InputNumber :value="row.retailPrice || 0" :precision="4" :active-change="false" :min="0"-->
            <!--                       disabled/>-->
            ¥ {{ row.retailPrice + '/' + row.prod_unit }}
          </template>
          <template slot-scope="{ row, index }" slot="subtotal">
            <!--          <Tooltip-->
            <!--            content="请先清空采购价，再修改小计金额"-->
            <!--            placement="top"-->
            <!--            :disabled="!valDisabledHandle(row, index)"-->
            <!--          >-->
            <!--            &lt;!&ndash; <Input :value="row.subtotal || ''" type="number" placeholder="" style="" @on-blur="(e) => changeSubtotalHandle(e, index)"  :disabled="valDisabledHandle(row, index) || isDisatbed" /> &ndash;&gt;-->
            <!--            <InputNumber-->
            <!--              :value="row.subtotal || 0"-->
            <!--              :precision="4"-->
            <!--              :active-change="false"-->
            <!--              :min="0"-->
            <!--              @on-change="(e) => changeSubtotalHandle(e, index)"-->
            <!--              :disabled="valDisabledHandle(row, index) || isDisatbed"-->
            <!--            />-->
            <!--          </Tooltip>-->
            {{ row.subtotal ? `¥ ${row.subtotal}` : '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="batchNumber">
            <Input
              :value="row.batchNumber || ''"
              placeholder="必填"
              style=""
              @on-change="e => changeBatchNumberHandle(e, index)"
              :disabled="isDisatbed || !!is_cf_import || isExamine || isDetail"
            />
          </template>
          <template slot-scope="{ row, index }" slot="productionDate">
            <DatePicker
              type="date"
              placeholder="必填"
              :value="row.productionDate != '0' ? row.productionDate : ''"
              @on-change="e => changeProductionDateHandle(e, index)"
              :disabled="isDisatbed || !!is_cf_import || isExamine || isDetail"
            ></DatePicker>
          </template>
          <template slot-scope="{ row, index }" slot="termOfValidity">
            <DatePicker
              type="date"
              :id="row.number + index"
              :value="row.termOfValidity"
              :options="row.termOfValidityOptions"
              :placeholder="specialRequiredType ? '必填' : '非必填'"
              @on-change="e => changeTermOfValidityDateHandle(e, index)"
              @on-open-change="e => dateOpenChange(e, index, row)"
              :disabled="isDisatbed || !!is_cf_import || isExamine || isDetail"
              :transfer-class-name="`onlyClass${row.number}${index} date-range-disabled`"
              placement="bottom-end"
            ></DatePicker>
          </template>
          <template slot-scope="{ row, index }" slot="invoiceNo">
            <Input
              :value="row.invoiceNo || ''"
              style=""
              placeholder="非必填"
              @on-change="e => changeInvoiceNoHandle(e, index)"
              :disabled="isDisatbed || isExamine || isDetail"
            />
          </template>
          <template slot-scope="{ row, index }" slot="operation">
            <!--          常繁订单导入后，暂时不禁用删除按钮-->
            <a @click="delListItemHandle(row, index)" :disabled="isDisatbed || isExamine || isDetail">删除</a>
          </template>
          <template slot-scope="{ row, index }" slot="spec_model">
            <Input
              v-model="list[index].spec_model"
              placeholder="请输入规格型号"
              :disabled="isDisatbed || isExamine || isDetail"
            />
          </template>
          <template slot-scope="{ row, index }" slot="reg_cert_no">
            <Input
              v-model="list[index].reg_cert_no"
              placeholder="请输入注册证号"
              :disabled="isDisatbed || isExamine || isDetail"
            />
          </template>
          <template slot-scope="{ row, index }" slot="reg_cert_name">
            <Input
              v-model="list[index].reg_cert_name"
              placeholder="请输入注册证名称"
              :disabled="isDisatbed || isExamine || isDetail"
            />
          </template>
          <template slot-scope="{ row, index }" slot="prod_source">
            <Select
              v-model="list[index].prod_source"
              placeholder="请选择"
              :disabled="isDisatbed || isExamine || isDetail"
            >
              <Option value="domestic_made">国产</Option>
              <Option value="imported">进口</Option>
            </Select>
          </template>
        </Table>
        <div class="flex flex-item-between" style="padding-bottom: 40px">
          <div class="table-search" style="position: relative">
            <goods-search
              class="tb10"
              style="width: 400px"
              ref="goods-search"
              :disabled="isDisatbed || !!is_cf_import || isExamine || isDetail"
              :isClearable="true"
              :params="{ prod_type: prod_type, is_get_last_ostock: 1, status: '1' }"
              placeholder="请搜索添加相关货品"
              @on-select="selectAddTableChangeHandle"
              v-model="selectAddTable"
            ></goods-search>
            <div class="flex">
              <div style="margin-right: 10px">入库（采购)总额:￥ {{ getMaxAmount() }}</div>
              <div>入库（采购)总量: {{ getTotalWeight() ? `${getTotalWeight()}kg` : '' }}</div>
              <div v-for="(item, index) in getOtherUnitList()" :key="index">
                <div v-show="item.quantity" style="margin-left: 5px">
                  <span v-if="index === 0">{{ index === 0 && getTotalWeight() ? '+' : '' }}</span>
                  <span v-else>+</span>
                  {{ item.quantity }} {{ item.prod_unit }}
                </div>
              </div>
            </div>
          </div>
          <Page
            :total="tableFormData.total"
            :page-size="tableFormData.pageSize"
            :current="tableFormData.page"
            :page-size-opts="[10, 20, 50, 80, 100]"
            @on-change="onPageChange"
            @on-page-size-change="onPageSizeChange"
            show-sizer
            show-elevator
            show-total
            transfer
            style="margin-top: 10px"
          >
          </Page>
        </div>
      </div>

      <!-- 操作历史 -->
      <div v-if="currentComponent === 'operatorHistory'">
        <Table :columns="historyColumns" :data="historyList" :height="$store.state.app.clientHeight - 370">
          <template slot-scope="{ row, index }" slot="time">
            {{ row.time | data_format }}
          </template>
          <template slot-scope="{ row, index }" slot="operator_name">
            {{ row.operator_name || '-' }}
          </template>
          <template slot-scope="{ row, index }" slot="remark">
            {{ row.remark || '-' }}
          </template>
        </Table>
      </div>
    </Form>
    <div class="fixed-bottom-wrapper" v-show="showBottomWrapper">
      <div v-if="!isDisatbed && $route.query.examineStatus !== 'WAIT_AUDIT' && !isDetail">
        <div v-if="currentComponent === 'ostockDetail'">
          <Poptip
            v-eleControl="'ElvnwYBP88'"
            confirm
            title="是否要清空当前页面全部数据?"
            @on-ok="clearAll"
            v-if="!$route.query.id"
          >
            <Button>清空</Button>
          </Poptip>
          <back-button style="margin: 0 12px">取消</back-button>
          <Button
            v-eleControl="'ElvnwYBP88'"
            type="primary"
            @click="submit(false, true)"
            class="submitBtn"
            :loading="subBtnLoading"
            >提交
          </Button>
        </div>
        <div v-else>
          <back-button></back-button>
        </div>
      </div>
      <div v-else>
        <back-button></back-button>
        <Button
          v-eleControl="'EGW1zWo2p2'"
          type="primary"
          v-if="status === '10' && $route.query.examineStatus === 'WAIT_AUDIT' && !isDetail"
          class="ml10"
          @click="examine"
          :loading="examineLoading"
          >审核
        </Button>
      </div>
    </div>

    <!-- 最近采购价曲线图 -->
    <Poptip
      :transfer="false"
      trigger="focus"
      placement="top-start"
      popper-class="custom-input-poptip"
      ref="inputPoptip"
      :reference="reference"
    >
      <!--              :disabled="!Boolean(current_single_info.new_purchase)"-->

      <div slot="content" class="input-poptip-content">
        <div class="content-price">
          <div class="price">最新采购价：￥{{ current_single_info.new_purchase }}/{{ current_single_info.unit }}</div>
          <div class="time">{{ current_single_info.new_time }}</div>
        </div>
        <Poptip
          trigger="hover"
          class="text"
          placement="bottom"
          :disabled="!Boolean(current_single_info.new_purchase)"
          popper-class="custom-echarts-poptip"
        >
          <a>查看历史采购价 →</a>
          <div slot="content" class="echarts-poptip-content">
            <div class="text-content">
              <div class="text-item">
                <div class="text-item-top">
                  <div class="item-top-title">历史最高</div>
                  <div class="item-top-time">{{ current_single_info.max_purchase_time }}</div>
                </div>
                <div class="item-price">¥{{ current_single_info.max_purchase }}/{{ current_single_info.unit }}</div>
                <div class="company">{{ current_single_info.max_pruchase_supplier_name }}</div>
              </div>

              <div class="text-item">
                <div class="text-item-top">
                  <div class="item-top-title">历史最低</div>
                  <div class="item-top-time">{{ current_single_info.min_purchase_time }}</div>
                </div>
                <div class="item-price">¥{{ current_single_info.min_purchase }}/{{ current_single_info.unit }}</div>
                <div class="company">{{ current_single_info.min_pruchase_supplier_name }}</div>
              </div>
            </div>
            <div>
              <chart-view height="260px" :chart-option="line_options_turover"></chart-view>
            </div>
          </div>
        </Poptip>
      </div>
    </Poptip>

    <!-- 查看错误报告 -->
    <Modal v-model="reportVisible" :mask-closable="false" width="800" :hide-footer="true" title="错误报告">
      <div class="report-content">
        <Table :columns="reportColumn" :data="reportList" height="400"></Table>
      </div>
    </Modal>
    <!-- 价格比例异常提醒弹窗-->
    <ReminderModal
      :show-modal.sync="remindVisible"
      :adjustSellingPrice="adjustSellingPrice"
      :remind-drugs="lowPriceItems"
      :submit="submitForm"
      :ratio-obj="ratioObj"
    ></ReminderModal>
    <!-- 价格比例极端提醒弹窗-->
    <ExtremeModal
      :show-modal.sync="extremeVisible"
      :ostockRules="ostockRules"
      :remind-drugs="extremePriceItems"
      :submit="submit"
    ></ExtremeModal>
    <!-- 价格比例异常调整弹窗-->
    <AdjustModal
      :ratioObj="ratioObj"
      :show-modal.sync="adjustVisible"
      :remind-drugs="abnormalItems"
      :adjustAndSave="adjustAndSave"
    ></AdjustModal>
    <!-- 仅调用组件方法，不展示弹框-->
    <k-batch-upload
      ref="KBatchUpload"
      :importVisible.sync="importVisible"
      :modalTitle="'批量导入'"
      :excelKeyMap="excelKeyMap"
      :reportColumns="reportColumns"
      :restParams="restParams"
      :downloadTemplateApiName="''"
      :importApiName="importApiName"
      :isRequiredCheck="true"
      :errorKey="errorKey"
      :precondition="handlePrecondition"
      :isUploadOnly="true"
      @emitFinish="emitFinish"
      @emitSuccessList="handleUploadSuccessData"
    >
      <div slot="remark" style="color: red; margin-top: 10px">*常繁线下采购发货后，可向常繁客服索取电子随货单导入</div>
    </k-batch-upload>
    <tips-modal
      v-model="confirmVisible"
      :contentText="'当前已经存在入库草稿数据，使用导入功能需要清空草稿，是否继续？'"
      :confirmText="'清空并继续'"
      @onOk="clearAndUpload"
    >
    </tips-modal>

    <ExamineModal ref="examineModal" :currentRow="currentRow" @getsList="examineSuccess" />
    <CFImportModal
      :visible.sync="CFImportVisible"
      :cf-import-list="CFImportList"
      @importCfReport="importCfReport"
    ></CFImportModal>
    <!--  提示弹框  -->
    <tips-modal v-model="noticeVisible" :contentText="noticeContent" :confirmText="'确定'" :showCancel="false">
    </tips-modal>

    <!--  提示类型不匹配弹框  -->
    <tips-modal v-model="warnVisible" :confirmText="'继续'" :isCustomContent="true" @onOk="submitForm">
      <template #content>
        <div>检查到该订单已经存在过入库记录，是否继续？</div>
        <div style="color: red">*重复入库会导致库存异常，可在「出入库管理」中进行确认</div>
      </template>
    </tips-modal>

    <Tooltip
      ref="custom-tooltip"
      popper-class="generic-name-link-tip"
      transfer-class-name="generic-name-link-tip"
      :reference="tooltipReference"
      :delay="1000"
      padding="0 0"
      style="height: 0; width: 0; display: none"
    >
      <template #content>
        <generic-name-tip :row="selectedToolTipRow" />
      </template>
    </Tooltip>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
import KExcelUpload from '@/components/k-excel-upload/excel';
/* eslint-disable */
import draft_mixin from './mixin/draft_mixin';
import moment from 'moment';
import debounce from 'lodash.debounce';
import cloneDeep from 'lodash.clonedeep';
import ReminderModal from './components/AbnormaProportionModal';
import AdjustModal from './components/AdjustModal';
import ExtremeModal from './components/ExtremeModal';
import { $operator } from '../../../libs/operation';
import KBatchUpload from '@/components/BatchUpload/KBatchUpload';
import confirmModal from '@/components/confirmModal/confirmModal';
import TipsModal from '@/components/confirmModal/TipsModal';
import GoodsSearch from './components/GoodsSearch.vue';
import ExamineModal from './components/examineModal';
import CFImportModal from './components/CFImportModal.vue';
import { getClinicName } from '../../../libs/runtime';
import GenericNameTip from "@/view/stock/components/generic-name-tip.vue";
import tooltip from "@/view/stock/mixins/tooltip";

let init_query_form_data = {
  keyword: '',
  status: '',
};

export default {
  name: 'edit',
  components: {
    GenericNameTip,
    KExcelUpload,
    ReminderModal,
    AdjustModal,
    ExtremeModal,
    KBatchUpload,
    confirmModal,
    TipsModal,
    GoodsSearch,
    ExamineModal,
    CFImportModal,
  },
  mixins: [draft_mixin, tooltip],
  data() {
    return {
      ratioObj: {
        ratio: 0,
        offset: 0,
      },
      tableLoading: false,
      tableFormData: {
        page: 1,
        pageSize: 20,
        total: 0,
      },
      queryFormData: { ...init_query_form_data },
      exportLoading: false, // 模板下载loading
      excelUploadLoading: false, // 数据上传的loading
      reference: null,
      list: [],
      localList: [], //本地维护的总数据
      supplierList: [],
      primitiveSupplier: {
        id: '',
        name: '',
      },
      formValidate: {
        supplier: '',
        user: '',
        ostock_code: '', // 入库单号
        create_time: '', // 建单时间
        type: 'CLI', // 入库类型
        order_code: '', // 关联单号
        images: [], // 附件
        remark: '', // 备注
        cf_remark: '',
        cf_code: '',
        // inspectionTime: '',
        // selectAddTable: '',
      },
      ruleValidate: {
        supplier: [{ required: true, message: '请选择供应商', trigger: 'change' }],
        user: [{ required: true, message: '请输入建单人', trigger: 'blur' }],
        type: [{ required: true, message: '请选择入库类型', trigger: 'change' }],
        create_time: [
          {
            required: true,
            type: 'date',
            message: '请选择建单时间',
            trigger: 'blur',
          },
        ],
        // inspectionTime: [
        //   {
        //     required: false,
        //     type: 'date',
        //     message: '请选择验货时间',
        //     trigger: 'blur'
        //   }
        // ],
        // selectAddTable: [{ required: false, message: '', trigger: 'blur' }],
        remark: [{ required: false, message: '', trigger: 'blur' }],
      },
      selectAddTable: '',
      fail_num: 0, // 错误条数
      failList: [], // 上传成功的条数
      reportVisible: false, // 错误报告弹窗标识
      reportList: [], // 错误报告数量
      reportColumn: [
        {
          title: '商品编号',
          key: 'id',
          align: 'center',
        },
        {
          title: '商品名称',
          key: 'generic_name',
          align: 'center',
        },
        {
          //todo 缺少该字段
          title: '商品类型',
          key: 'generic_name',
          align: 'center',
        },
        {
          title: '数量',
          key: 'purchase_stock_num',
          align: 'center',
        },
        {
          title: '单位',
          key: 'warehouse_unit',
          align: 'center',
        },
        {
          title: '采购价',
          key: 'purchase_price',
          align: 'center',
        },
        {
          title: '批号',
          key: 'batch_code',
          align: 'center',
        },
        {
          title: '生产日期',
          key: 'produce_time',
          align: 'center',
        },
        {
          title: '有效期',
          key: 'expire_time',
          align: 'center',
        },
        {
          title: '发票号',
          key: 'invoice_code',
          align: 'center',
        },
        {
          title: '错误原因',
          key: 'fail_msg',
          align: 'center',
        },
      ], //
      show: true,
      userInfo: {},
      goodsList: [],
      maxAmount: 0,
      id: null,
      ostock: {}, // 表单数据
      loading1: false,
      prod_type: '', // 类型，中药西药...
      prodTypeText: '',
      copyGoodsList: [],
      type: 'add',
      copyList: [],
      subBtnLoading: false,
      inputKey: 0,
      canSearch: true,
      isDisatbed: false,
      singleData: {},
      abnormalItems: [], //采购价格与零售价比例异常
      remindVisible: false, //提醒弹窗
      adjustVisible: false, //价格调整弹窗
      lowPriceItems: [], //价格偏低
      adjustList: [], //调整价格后的列表
      showBottomWrapper: false,
      sourceType: '', //入库类型
      extremePriceItems: [], //价格极端
      extremeVisible: false,
      is_covid: false, //是否是新冠入库
      excelKeyMap: {
        编码: { key: 'id', required: true },
        品级编码: { key: 'brand_code', required: false },
        产品规格: { key: 'prod_spec', required: false },
        产地: { key: 'prod_area', required: false },
        生产厂家: { key: 'manufacturer', required: false },
        单位: { key: 'warehouse_unit', required: true },
        产品名称: { key: 'generic_name', required: true },
        单价: { key: 'purchase_price', required: true },
        批号: { key: 'batch_code', required: true },
        数量: { key: 'purchase_stock_num', required: true },
        生产日期: { key: 'produce_time', required: true },
        失效日期: { key: 'expire_time', required: true },
      },
      importApiName: 'importPreCheckByChangFan',
      reportColumns: [
        { type: 'index', title: '序号', width: 60, align: 'center' },
        { title: '产品名称', key: 'generic_name', align: 'center' },
        { title: '失败原因', key: 'fail_msg', align: 'center' },
      ],
      errorKey: 'generic_name', // 错误拦截显示字段
      restParams: { prod_type: '' },
      cf_supplier: {},
      importVisible: false,
      is_cf_import: 0, // 是否使用常繁线下导入功能 0为否 1为是
      confirmVisible: false,
      filename: '',
      currentFile1: {},
      is_cf_filename_valid: false,

      sourceTypeDesc: {}, // 入库类型
      currentComponent: 'ostockDetail',
      tabComponents: [
        { componentName: '入库明细', id: 'ostockDetail' },
        { componentName: '操作历史', id: 'operatorHistory' },
      ],

      // 操作历史
      historyColumns: [
        { title: '操作时间', slot: 'time', align: 'center' },
        { title: '操作人', slot: 'operator_name', align: 'center' },
        { title: '操作详情', slot: 'remark', align: 'center' },
      ],
      historyList: [],

      ostockRules: {}, // 价格比例极端规则
      prodTypes: {},

      currentRow: {},
      examineLoading: false,
      status: '',
      clone_supplier_id: '',

      line_options_turover: {}, // 采购价单位及曲线图

      // 存储echarts 提示框相关信息
      cache_new_purchase: {},
      current_single_info: {
        new_purchase: '', // 最新采购价
        new_time: '', // 最新采购价时间
        min_purchase: '', // 最低采购价
        min_pruchase_supplier_name: '', // 最低采购价供应商
        min_purchase_time: '', // 最低采购价时间
        max_pruchase: '', // 最高采购价
        max_pruchase_supplier_name: '', // 最高采购价供应商
        max_purchase_time: '', // 最高采购价时间
        unit: '', // 当前对应的单位
      },
      current_warehouse_row: {}, // 聚焦缓存的单条入库数据
      current_production_date: '',
      CFImportVisible: false,
      CFImportList: [],
      clinicName: getClinicName(),
      products: {},
      noticeVisible: false,
      noticeContent: '',
      warnVisible: false,
    };
  },
  provide() {
    return {
      getFileName: this.getFileName,
    };
  },
  created() {
    this.getRules();
    this.initData();
    // let status = this.$route.query.status;
    this.getRatio();
  },

  methods: {
    getprodostockhistory(row, index) {
      let params = {
        prod_id: row.number,
      };
      this.$api.getprodostockhistory(params).then(res => {
        this.cache_new_purchase[row.number] = {
          ...res,
          echart_data: res.history_purchase || [],
        };
        this.calc_purchase_info(index);
        this.setAmountLine();
      });
    },
    setAmountLine() {
      let source_list = this.cache_new_purchase[this.current_warehouse_row.number]?.history_purchase || [];
      let list = cloneDeep(source_list);
      list = list.reverse();

      let xData = list.map(item => moment(Number(item.create_time * 1000 || 0)).format('MM-DD'));
      let yData = list.map(item => {
        return this.isBigUnit() ? item.purchase_price : item.split_purchase_price;
      });
      // 动态计算边距
      let left_percent = 10; // 默认10%
      let max_number = Math.max(...yData);
      let max_number_length = max_number?.toString()?.split('.')[0]?.length;
      if (max_number_length > 5) {
        left_percent = max_number_length * 2 > 20 ? 20 : max_number_length * 2;
      }

      // 动态计算隔点展示
      let interval_space = 0;
      if (8 < list.length && list.length < 15) {
        interval_space = 1;
      } else if (list.length >= 15) {
        interval_space = 2;
      }

      let that = this;
      this.line_options_turover = this.$eChartFn.areaLineOptions(
        {
          grid: {
            left: `${left_percent}%`,
            right: '10%',
          },
          tooltip: {
            backgroundColor: '#000',
            padding: [10, 10],
            formatter: function (param) {
              // 自定义tooltip
              let index = param[0].dataIndex;
              let create_time = moment(Number(list[index].create_time * 1000 || 0)).format('YYYY-MM-DD');
              let source_text = list[index].source_text;
              let price = that.isBigUnit() ? list[index].purchase_price : list[index].split_purchase_price;
              let unit = that.current_warehouse_row.prod_unit;
              var text = '';
              text += `
                <div style="width: 200px;">
                  <div style="display:flex;justify-content: space-between;font-size:14px;">
                    <div style="color: rgba(255,255,255,0.75) ">入库时间:</div>
                    <div style="color: #fff">${create_time}</div>
                  </div>
                  <div style="display:flex;justify-content: space-between;font-size:14px;">
                    <div style="color: #bdbcbc">入库方式:</div>
                    <div style="color: #fff">${source_text}</div>
                  </div>
                  <div style="display:flex;justify-content: space-between;font-size:14px;">
                    <div style="color: #bdbcbc">入库单价:</div>
                    <div style="color: #fff">¥${price}/${unit}</div>
                  </div>
                </div>
              `;
              return text;
            },
          },
          yAxis: {
            show: true,
            data: yData,
            scale: true,
            splitLine: {
              show: true,
              lineStyle: {
                type: 'dashed',
                color: '#ccc',
                opacity: 0.2,
              },
            },
          },
          xAxis: {
            data: xData,
            axisLabel: {
              interval: interval_space,
              // rotate: 45
            },
            splitLine: {
              show: false,
            },
          },
          series: [
            {
              areaStyle: {
                color: 'none', // 设置面积色
              },
              itemStyle: {
                normal: {
                  color: params => {
                    return list[params?.dataIndex]?.source === 'CLI' ? '#fff' : '#3088FF';
                  }, // 拐点颜色
                  borderColor: '#3088FF', // 拐点边框色
                  borderWidth: 2, // 拐点边框大小
                  lineStyle: {
                    color: '#3088FF', // 设置线条颜色
                    width: 2,
                  },
                },
              },
              showSymbol: true, // 鼠标选中悬浮才会显示拐点
              data: yData,
              symbol: 'circle',
              symbolSize: 8, // 设置拐点大小
              smooth: false,
            },
          ],
        },
        '今日营收',
        true
      );
    },
    /**
     * 判断大小单位
     * */
    isBigUnit() {
      // 默认取大单位, true大单位，false小单位
      let is_big = true;

      // 标识进行了拆零比，并且用了小单位
      if (this.current_warehouse_row.is_split === '1') {
        let sales_units = this.current_warehouse_row.sales_units;
        sales_units.forEach(item => {
          if (item.unit === this.current_warehouse_row.prod_unit && item.is_big == 0) {
            is_big = false;
          }
        });
      }

      return is_big;
    },
    /**
     * @description: 统一计算历史进价曲线图的数据
     * */
    calc_purchase_info(index) {
      let obj = {
        new_purchase: '', // 最新采购价
        new_time: '', // 最新采购价时间
        min_purchase: '', // 最低采购价
        min_pruchase_supplier_name: '', // 最低采购价供应商
        min_purchase_time: '', // 最低采购价时间
        max_pruchase: '', // 最高采购价
        max_pruchase_supplier_name: '', // 最高采购价供应商
        max_purchase_time: '', // 最高采购价时间
        unit: '', // 当前对应的单位
      };
      if (this.cache_new_purchase[this.current_warehouse_row.number]) {
        let is_big = this.isBigUnit();

        let row = this.cache_new_purchase[this.current_warehouse_row.number] || {};
        obj.min_pruchase_supplier_name = row.min_purchase?.supplier_name;
        obj.max_pruchase_supplier_name = row.max_purchase?.supplier_name;
        obj.new_purchase = is_big ? row.last_purchase?.purchase_price : row.last_purchase?.split_purchase_price;
        obj.new_time = row.last_purchase?.create_time
          ? moment(Number(row.last_purchase?.create_time * 1000 || 0)).format('YYYY-MM-DD')
          : '';
        obj.min_purchase_time = row.min_purchase?.create_time
          ? moment(Number(row.min_purchase?.create_time * 1000 || 0)).format('YYYY-MM-DD')
          : '';
        obj.max_purchase_time = row.max_purchase?.create_time
          ? moment(Number(row.max_purchase?.create_time * 1000 || 0)).format('YYYY-MM-DD')
          : '';
        obj.min_purchase = is_big ? row.min_purchase?.purchase_price : row.min_purchase?.split_purchase_price;
        obj.max_purchase = is_big ? row.max_purchase?.purchase_price : row.max_purchase?.split_purchase_price;
        obj.unit = this.current_warehouse_row.prod_unit;
      }

      if (Boolean(obj.new_purchase)) {
        const pop = this.$refs.inputPoptip;
        pop.visible = false;
        pop && pop.doDestroy();
        pop.visible = true;
      }
      this.current_single_info = obj;
    },
    examine() {
      this.currentRow = {
        id: this.$route.query.id,
      };
      this.$refs.examineModal.showModal = true;
      this.$refs.examineModal.formValidate.examine = 'PASS';
      this.$refs.examineModal.formValidate.remark = '';
    },
    examineSuccess() {
      this.$router.push({
        path: '/stock/ostock/list',
        query: { ostock_type: '10' },
      });
    },
    initData() {
      this.getOptionsList();

      this.is_covid = this.$route.query.is_covid;
      this.id = this.$route.query.id;
      this.prod_type = this.$route.query.prod_type;
      this.prodTypeText = this.$route.query.prodTypeText;
      this.sourceType = this.$route.query.sourceType;
      this.type = this.$route.query.type;
      this.userInfo = runtime.getUser();
      this.formValidate.user = runtime.getUser().name;
      if (this.sourceType === 'PMS') {
        this.isDisatbed = true;
      }
      let nowDate = new Date();
      this.formValidate.create_time = nowDate;
      // this.formValidate.inspectionTime = nowDate;
      if (this.id) {
        this.getDetail();
      } else {
        this.showBottomWrapper = true;
        this.querySupplierList();
      }
      /*常繁入库特殊逻辑*/
      // this.goodsSearchHandle('', true);
      if (!this.$route.query.id && !this.is_covid) {
        this.getDraft(10);
      }
      // 常繁订单导入传参
      this.restParams.prod_type = this.prod_type;
    },

    getOptionsList() {
      io.get('/clinic/ostock.options')
        .then(data => {
          this.sourceTypeDesc = data.sourceDesc;
        })
        .catch(error => {
          {
          }
        });
    },

    // 获取操作历史
    getOstockLogList() {
      let params = {
        id: this.$route.query.id,
      };
      this.$api.getOstockLogList(params).then(
        res => {
          this.historyList = res.list;
        },
        error => {}
      );
    },

    tabClick(val) {
      this.currentComponent = val;
      if (val === 'operatorHistory') {
        this.getOstockLogList();
      }
    },

    initCovidInStock() {
      this.getRatio();
      this.id = this.$route.query.id;
      this.prod_type = this.$route.query.prod_type;
      this.prodTypeText = this.$route.query.prodTypeText;
      this.sourceType = this.$route.query.sourceType;
      console.log('-> this.$route.query.prodTypeText', this.$route.query.prodTypeText);
      this.type = this.$route.query.type;
      this.userInfo = runtime.getUser();
      this.formValidate.user = runtime.getUser().name;
      if (this.sourceType === 'PMS') {
        this.isDisatbed = true;
      }
      let nowDate = new Date();
      this.formValidate.create_time = nowDate;
      // this.formValidate.inspectionTime = nowDate;
      this.querySupplierList();

      if (this.id) {
        this.getDetail();
      } else {
        this.showBottomWrapper = true;
      }
      // this.goodsSearchHandle();
      if (!this.$route.query.id) {
        this.getDraft(10);
      }
    },

    tableScrollToTop() {
      this.$nextTick(() => {
        document.getElementsByClassName('ivu-table-body')[0].scroll(0, 0);
      });
    },
    onPageChange(page, pageSize) {
      console.log('-> %c page,pageSize  === %o ', 'font-size: 15px', page, pageSize);
      this.tableFormData.page = page;
      const curSize = this.tableFormData.pageSize;
      const copyList = cloneDeep(this.localList);
      this.list = copyList.slice($operator.multiply(page - 1, curSize), $operator.multiply(page, curSize));
      this.tableScrollToTop();
    },
    onPageSizeChange(pageSize) {
      console.log('-> %c pageSize  === %o ', 'font-size: 15px', pageSize);
      this.tableFormData.pageSize = pageSize;
      this.tableFormData.page = 1;
      const copyList = cloneDeep(this.localList);
      this.list = copyList.slice(0, pageSize);
      // this.tableScrollToTop()
    },
    initBatchTableList() {
      this.tableFormData.page = 1;
      this.tableFormData.pageSize = 20;
      this.tableFormData.total = this.localList.length;
      this.list = this.localList.slice(0, 20);
      console.log('-> %c this.list  === %o ', 'font-size: 15px', this.list);
      this.tableScrollToTop();
    },
    removeZero(e, index, type) {
      if (!isNaN(Number(e.target.value)) && Number(e.target.value) === 0) {
        this.list[index][type] = null;
      }
    },
    purchaseInputFocus(e, index, type, row) {
      this.reference = e.target;
      this.removeZero(e, index, type);
      this.current_warehouse_row = row;
      this.current_single_info = {};
      if (!this.cache_new_purchase[row.number]) {
        this.getprodostockhistory(row, index);
      } else {
        this.calc_purchase_info(index);
        this.setAmountLine();
      }
    },
    purchaseInputBlur() {
      const pop = this.$refs.inputPoptip;
      pop && (pop.visible = false);
      this.reference = null;
    },
    adjustAndSave(items) {
      console.log('-> %c items  === %o ', 'font-size: 15px', items);
      // let temObj = {}
      //  items.map(item=> {
      //    temObj[item.number] = item
      //  })
      // this.list.map(item=>{
      //   if(item.number in temObj){
      //     console.log("-> %c item  === %o ", "font-size: 15px", item)
      //   }
      // })
      // return
      this.submitForm();
    },
    //调整售价
    adjustSellingPrice() {
      this.remindVisible = false;
      this.adjustVisible = true;
    },
    clearAll() {
      const supplier = this.formValidate.supplier;
      this.formValidate = {
        supplier: this.is_covid ? supplier : '',
        user: this.formValidate.user,
        type: this.formValidate.type,
        order_code: '',
        ostock_code: '',
        create_time: this.formValidate.create_time,
        // inspectionTime: '',
        // selectAddTable: '',
        cf_remark: '',
        cf_code: '',
        remark: '',
      };
      this.list = [];
      this.localList = [];
      !this.is_covid && this.$refs.supplier_se.clearSingleSelect();
      this.$refs.myForm.resetFields();
      this.is_cf_import = 0;
    },
    // 模板下载
    templateDownload() {
      this.exportLoading = true;
      let params = {
        name: '',
        prod_type: this.$route.query.prod_type,
        status: '',
        type: 'PROD_STOCK', // 商品库存模板
      };
      this.$api
        .getClinicTemplateurl(params)
        .then(res => {
          this.download(res.url);
        })
        .finally(() => (this.exportLoading = false));
    },
    // 获取获取excel处理后的数据
    stockExcelUpload(hasHandleExcelList) {
      if (!hasHandleExcelList.length) {
        this.$Message.error('请至少导入一条有效数据');
        return;
      }
      this.saveExcel(hasHandleExcelList);
    },
    // api-上传处理后的excel数据
    saveExcel(hasHandleExcelList) {
      this.excelUploadLoading = true;
      console.log('hasHandleExcelList', hasHandleExcelList);
      this.$api
        .saveBatchprodstock({
          batch_params: hasHandleExcelList,
          prod_type: this.$route.query.prod_type,
        })
        .then(res => {
          // this.$Message.success('导入成功')
          this.fail_num = res.fail_num;
          this.reportList = res.fail_data;
          if (this.reportList.length) {
            this.reportVisible = true;
          }
          // TODO
          if (res.succ_data.length) {
            this.handleUploadSuccessData(res.succ_data);
          }
        })
        .finally(() => {
          this.excelUploadLoading = false;
        });
    },
    // 将导入成功的数据并入
    handleUploadSuccessData(succ_data) {
      console.log('-> %c succ_data  === %o ', 'font-size: 15px', succ_data);
      const resList = succ_data.map(item => {
        return this.exportDataToTable(item);
      });
      const { pageSize, page } = this.tableFormData;
      console.log('-> %c pageSize,page  === %o ', 'font-size: 15px', pageSize, page);
      // this.list = [...resList,...this.list]
      this.localList = [...resList, ...this.localList];
      this.tableFormData.total = this.localList.length;
      const copyList = cloneDeep(this.localList);
      console.log('-> %c copyList  === %o ', 'font-size: 15px', copyList);
      this.list = copyList.slice((page - 1) * pageSize, $operator.multiply(page, pageSize));
      console.log('-> %c this.list  === %o ', 'font-size: 15px', this.list);
    },
    exportDataToTable(item) {
      let obj = {
        number: item.id, // 商品id
        goodscode: item.phonetic_code, // 商品编码
        goodsname: item.generic_name, // 商品名称
        prod_spec: item.prod_spec,
        grade_desc: item.grade_desc,
        manufacturer: item.manufacturer,
        // manufactor: item.manufacturer, // 生产厂家
        company: item.warehouse_unit || item.prod_unit, // 单位
        prod_unit: item.warehouse_unit,
        quantity: Number(item.purchase_stock_num), //  数量
        purchasePrice: Number(item.purchase_price), // 采购价
        subtotal: S.mathMul(item.purchase_stock_num, item.purchase_price), // 小计
        batchNumber: item.batch_code, // 批号
        productionDate: item.produce_time ? moment(parseInt(item.produce_time * 1000)).format('YYYY-MM-DD') : '', // 生产日期
        termOfValidity: item.expire_time ? moment(parseInt(item.expire_time * 1000)).format('YYYY-MM-DD') : '', // 有效期
        invoiceNo: item.invoice_code, // 发票号
        prod_stock_id: item.prod_stock_id, // 商品库存id
        subtotalDisabled: true, // 小计禁用
        sales_units: item.sales_units, // 单位
        operation: '操作',
        is_split: item.is_split, // 是否拆零
        split_num: item.split_num, // 拆零数量
        // prod_unit: item.last_warehouse_unit,
        // unit_price_option: item.unit_price_option,
        isExport: true,
        from_text: item.from_text || '-', //来源
        spec_model: item.spec_model || '',      // 规格型号
        reg_cert_no: item.reg_cert_no || '',    // 注册证号
        reg_cert_name: item.reg_cert_name || '',// 注册证名称
        prod_source: item.prod_source || '',    // 国产/进口
      };
      item.sales_units.map(sub => {
        console.log('-> %c sub  === %o', 'font-size: 15px;color: green;', sub);
        if (sub.unit === item.warehouse_unit) {
          obj.retailPrice = sub.price; // 零售价
          obj.unit_ratio = Number(sub.unit_ratio);
          obj.last_ostock_price = sub.last_ostock_price || '';
          obj.calcPrice = $operator.multiply(obj.unit_ratio, sub.price, 4);
          obj.is_big = sub.is_big;
        }
      });
      return obj;

      // if ( item.last_warehouse_unit ) {
      //   obj.purchasePrice =
      //     item.unit_price_option[item.last_warehouse_unit] - 0
      //     obj.prod_unit = item.last_warehouse_unit
      // }
    },
    // 通过a标签下载
    download(url) {
      const downloadLink = document.createElement('a');
      downloadLink.setAttribute('href', url);
      downloadLink.setAttribute('target', '_blank');
      downloadLink.setAttribute('style', 'display:none');
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    },
    // 编辑获取详情
    async getDetail() {
      let params = {
        id: this.id,
      };
      let that = this;
      console.log(Date.now());
      this.tableLoading = true;
      await io.get('/clinic/ostock.info', { data: params }).then(
        data => {
          console.log('-> %c data  === %o ', 'font-size: 15px', data);
          if (data.ostock.status === '20' || data.ostock.status === '80') {
            this.isDisatbed = true;
          }

          this.status = data.ostock.status;
          this.showBottomWrapper = true;
          this.primitiveSupplier = {
            name: data.ostock.supplier_name,
            id: data.ostock.supplier_id,
          };
          this.supplierList.push(this.primitiveSupplier);
          this.formValidate.supplier = data.ostock.supplier_id;
          // this.clone_supplier_id = data.ostock.supplier_id;
          this.formValidate.user = data.ostock.operator_name;
          this.formValidate.ostock_code = data.ostock.ostock_code;
          this.formValidate.create_time = data.ostock.create_time
            ? moment(data.ostock.create_time * 1000).format('YYYY-MM-DD')
            : '';
          this.formValidate.type = data.ostock.source;
          this.formValidate.order_code = data.ostock.order_code;
          this.formValidate.images =
            data.ostock.images && typeof data.ostock.images == 'string' ? JSON.parse(data.ostock.images) : [];

          // this.formValidate.inspectionTime = data.ostock.inspection_time;
          this.is_cf_import = Number(data.ostock.is_cf_import) || 0;
          this.formValidate.cf_remark = data.ostock.cf_remark;
          this.formValidate.cf_code = data.ostock.cf_code; // todo hcx 提前加该字段，未检查
          this.formValidate.remark =
            this.is_cf_import == '1' && (this.isDetail || this.isExamine) && data.ostock.cf_remark
              ? data.ostock.remark + '-' + data.ostock.cf_remark
              : data.ostock.remark;
          let itemList = data.ostock.items;
          this.prodTypes = data.prodTypes;
          let products = data.products;
          this.products = products || {};
          if (itemList.length) {
            const resList = itemList.map(item => {
              console.log('-> %c item  === %o ', 'font-size: 15px', item);
              let proItem = products[item.prod_id] || {};
              proItem?.sales_units?.map(subItem => {
                console.log('-> %c subItem  === %o ', 'font-size: 15px', subItem);
                if (
                  (subItem.is_big === '1' && item.is_split_unit === '0') ||
                  (subItem.is_big === '0' && item.is_split_unit === '1')
                ) {
                  proItem.warehouse_unit = subItem.unit;
                  proItem.prod_unit = subItem.unit;
                  proItem.unit_ratio = subItem.unit_ratio;
                  proItem.calcPrice = $operator.multiply(subItem.unit_ratio, item.purchase_price, 4);
                  proItem.last_ostock_price = subItem.last_ostock_price || '';
                  proItem.retailPrice = subItem.price;
                  proItem.is_big = subItem.is_big;
                }
              });
              console.log('-> %c proItem  === %o ', 'font-size: 15px', proItem);

              return {
                number: item.prod_id, // 商品id
                goodscode: proItem.phonetic_code, // 商品编码
                goodsname: proItem.prod_name || proItem.generic_name, // 商品名称
                prod_spec: proItem.prod_spec,
                grade_desc: proItem.grade_desc,
                manufacturer: proItem.manufacturer,
                retailPrice: proItem.retailPrice, // 零售价
                company: proItem.prod_unit, // 单位
                quantity: Number(item.purchase_stock_num), //  数量
                purchasePrice: Number(item.purchase_price), // 采购价
                purchase_price: Number(item.purchase_price), // 采购价
                subtotal:
                  this.sourceType === 'PMS'
                    ? item.pms_info.purchase_total_price
                    : S.mathMul(item.purchase_stock_num, item.purchase_price), // 小计
                batchNumber: item.batch_code, // 批号
                productionDate: item.produce_time !== '0' ? S.moment.unix(item.produce_time).format('YYYY-MM-DD') : '', // 生产日期
                termOfValidity: Boolean(Number(item.expire_time))
                  ? S.moment.unix(item.expire_time).format('YYYY-MM-DD')
                  : '', // 有效期
                invoiceNo: item.invoice_code, // 发票号
                prod_stock_id: item.prod_stock_id, // 商品库存id
                subtotalDisabled: true, // 小计禁用
                sales_units: proItem.sales_units, // 单位列表
                prod_unit: proItem.warehouse_unit, // 选中单位
                last_ostock_price: proItem.last_ostock_price, // 最后一次采购价
                operation: '操作',
                calcPrice: proItem.calcPrice,
                unit_ratio: proItem.unit_ratio,
                from_text: proItem.from_text || '-',
                is_big: proItem.is_big,
                is_split: proItem.is_split, // 是否拆零
                split_num: proItem.split_num, // 拆零数量
                termOfValidityOptions: {
                  shortcuts: [
                    {
                      text: '顺延12个月',
                      value() {
                        console.log('=>(edit.vue:1447) that.current_production_date', that.current_production_date);
                        return S.moment(that.current_production_date)
                          .add(12, 'months')
                          .subtract(1, 'days')
                          .format('YYYY-MM-DD');
                      },
                    },
                    {
                      text: '顺延18个月',
                      value() {
                        return S.moment(that.current_production_date)
                          .add(18, 'months')
                          .subtract(1, 'days')
                          .format('YYYY-MM-DD');
                      },
                    },
                    {
                      text: '顺延2年',
                      value() {
                        return S.moment(that.current_production_date)
                          .add(2, 'years')
                          .subtract(1, 'days')
                          .format('YYYY-MM-DD');
                      },
                    },
                    {
                      text: '顺延3年',
                      value() {
                        return S.moment(that.current_production_date)
                          .add(3, 'years')
                          .subtract(1, 'days')
                          .format('YYYY-MM-DD');
                      },
                    },
                  ],
                },
                spec_model: item.spec_model || '',      // 规格型号
                reg_cert_no: item.reg_cert_no || '',    // 注册证号
                reg_cert_name: item.reg_cert_name || '',// 注册证名称
                prod_source: item.prod_source || '',    // 国产/进口
              };
            });
            this.localList = resList;
            console.log('-> %c this.localList  === %o ', 'font-size: 15px', this.localList);
            this.initBatchTableList();
          }
          // this.queryGoodsList()
          this.tableLoading = false;
        },
        error => {
          {
          }
        }
      );
    },
    // 修改select
    selectAddTableChangeHandle(value, item) {
      console.log('-> value', value, item);
      if (!value) {
        return;
      }
      this.canSearch = false;
      let currentItem = item;
      // this.goodsList.map((subItem, i) => {
      //   if (subItem.id == value) {
      //     currentItem = subItem;
      //   }
      // });
      this.concatList(currentItem);
      // this.queryGoodsSingle(value);
      this.$nextTick(() => {
        // this.goodsSearchHandle('');
        this.selectAddTable = '';
        this.$refs['goods-search'].clear();
        this.inputKey = this.inputKey + 1;
        this.canSearch = true;
      });
    },
    concatList(currentItem) {
      let that = this;
      console.log('-> %c currentItem  === %o ', 'font-size: 15px', currentItem);
      let obj = {
        ...currentItem,
        number: currentItem.id, // 商品id
        goodscode: currentItem.phonetic_code, // 商品编码
        goodsname: currentItem.generic_name, // 商品名称
        // manufactor: currentItem.manufacturer, // 生产厂家
        company: currentItem.prod_unit, // 单位
        quantity: null, //  数量
        is_split: currentItem.is_split, // 是否拆零
        split_num: currentItem.split_num || '', // 是否拆零
        subtotal: '', // 小计
        batchNumber: '', // 批号
        productionDate: '', // 生产日期
        termOfValidity: '', // 有效期
        termOfValidityOptions: {
          shortcuts: [
            {
              text: '顺延12个月',
              value() {
                console.log('=>(edit.vue:1447) that.current_production_date', that.current_production_date);
                return S.moment(that.current_production_date)
                  .add(12, 'months')
                  .subtract(1, 'days')
                  .format('YYYY-MM-DD');
              },
            },
            {
              text: '顺延18个月',
              value() {
                return S.moment(that.current_production_date)
                  .add(18, 'months')
                  .subtract(1, 'days')
                  .format('YYYY-MM-DD');
              },
            },
            {
              text: '顺延2年',
              value() {
                return S.moment(that.current_production_date).add(2, 'years').subtract(1, 'days').format('YYYY-MM-DD');
              },
            },
            {
              text: '顺延3年',
              value() {
                return S.moment(that.current_production_date).add(3, 'years').subtract(1, 'days').format('YYYY-MM-DD');
              },
            },
          ],
        },
        invoiceNo: '', // 发票号
        from_text: currentItem.from_text || '-', // 商品类型
        prod_stock_id: currentItem.prod_stock_id, // 商品库存id
        subtotalDisabled: true, // 小计禁用
        sales_units: currentItem.sales_units, // 单位
        grade_desc: currentItem.grade_desc,
        prod_spec: currentItem.prod_spec,
        manufacturer: currentItem.manufacturer,
        operation: '操作',
        spec_model: '',
        reg_cert_no: '',
        reg_cert_name: '',
        prod_source: '',
      };
      currentItem.sales_units.map(item => {
        console.log('-> %c item  === %o ', 'font-size: 15px', item);
        if (item.checked === '1') {
          obj.prod_unit = item.unit;
          obj.retailPrice = item.price; // 零售价
          obj.last_ostock_price = item.last_ostock_price;
          obj.unit_ratio = Number(item.unit_ratio);
          obj.is_big = item.is_big;
          obj.purchasePrice = +item.purchase_price || null; // 采购价
          obj.purchase_price = +item.purchase_price || null; // 采购价
        }
      });
      console.log('obj', obj);
      this.localList.unshift(obj);
      this.initBatchTableList();
      console.log('-> %c this.localList  === %o ', 'font-size: 15px', this.localList);
    },
    handleCFSelectProduct(items) {
      if (!Array.isArray(items) || !items.length) {
        return [];
      }
      let res = [];
      items.forEach((currentItem, index) => {
        let obj = {
          number: currentItem.id, // 商品id
          goodscode: currentItem.phonetic_code, // 商品编码
          goodsname: currentItem.generic_name, // 商品名称
          // manufactor: currentItem.manufacturer, // 生产厂家
          company: currentItem.prod_unit, // 单位
          quantity: '', //  数量
          is_split: currentItem.is_split, // 是否拆零
          split_num: currentItem.split_num || '', // 是否拆零
          subtotal: '', // 小计
          batchNumber: '', // 批号
          productionDate: '', // 生产日期
          termOfValidity: '', // 有效期
          invoiceNo: '', // 发票号
          from_text: currentItem.from_text || '-', // 商品类型
          prod_stock_id: currentItem.prod_stock_id, // 商品库存id
          subtotalDisabled: true, // 小计禁用
          sales_units: currentItem.sales_units, // 单位
          operation: '操作',
          spec_model: '',
          reg_cert_no: '',
          reg_cert_name: '',
          prod_source: '',
        };
        currentItem.sales_units.map(item => {
          if (item.checked === '1') {
            obj.prod_unit = item.unit;
            obj.retailPrice = item.price; // 零售价
            obj.last_ostock_price = item.last_ostock_price;
            obj.unit_ratio = Number(item.unit_ratio);
            obj.is_big = item.is_big;
            obj.purchasePrice = +item.purchase_price || null; // 采购价
            obj.purchase_price = +item.purchase_price || null; // 采购价
          }
        });
        res.push(obj);
      });
      return res;
    },
    // 修改数量
    changeQuantityHandle(e, index) {
      console.log('-> %c this.list[index]  === %o', 'font-size: 15px;color: green;', this.list[index]);
      if (!e) {
        e = 0;
      }
      this.list[index].quantity = this.localList[this.getCurrentIndex(index)].quantity = e;
      if (this.list[index].purchasePrice) {
        this.list[index].subtotal = this.localList[this.getCurrentIndex(index)].subtotal = $operator.multiply(
          e,
          this.list[index].purchasePrice
        );
      }
    },
    // 修改采购价
    changePurchasePriceHandle(e, index) {
      console.log('-> %c this.list[index]  === %o', 'font-size: 15px;color: green;', this.list[index]);
      if (!e) {
        e = 0;
      }
      this.list[index].purchasePrice = this.localList[this.getCurrentIndex(index)].purchasePrice = e;
      // this.list[index].calcPrice = this.localList[this.getCurrentIndex( index )].calcPrice = $operator.multiply( this.list[index].unit_ratio, this.list[index].purchase_price, 4 )
      if (this.list[index].quantity) {
        this.list[index].subtotal = this.localList[this.getCurrentIndex(index)].subtotal = $operator.multiply(
          e,
          this.list[index].quantity
        );
      }
    },
    // // 修改零售价
    // changeCostPriceHandle( e, index ) {
    //   this.list[index].costPrice = e.target.value
    // },
    // 修改小计
    changeSubtotalHandle(e, index) {
      this.list[index].subtotal = this.localList[this.getCurrentIndex(index)].subtotal = e;
      let quantity = this.list[index].quantity;
      let purchasePrice = this.list[index].purchasePrice;
      if (quantity === '') {
        return;
      } else {
        this.list[index].purchasePrice = this.localList[this.getCurrentIndex(index)].purchasePrice = e / quantity;
      }
    },
    // 修改批号
    changeBatchNumberHandle(e, index) {
      this.list[index].batchNumber = this.localList[this.getCurrentIndex(index)].batchNumber = e.target.value;
    },
    // 修改生产日期
    changeProductionDateHandle(e, index) {
      this.list[index].productionDate = this.localList[this.getCurrentIndex(index)].productionDate = e;
    },
    // 修改有效期
    changeTermOfValidityDateHandle(e, index) {
      this.list[index].termOfValidity = this.localList[this.getCurrentIndex(index)].termOfValidity = e;
    },
    dateOpenChange(val, index, row) {
      if (val) {
        this.current_production_date = this.list[index].productionDate || '';
        console.log('=>(edit.vue:1625) this.current_production_date', this.current_production_date);
        this.$nextTick(() => {
          let pickerPop = document.getElementsByClassName(`onlyClass${row.number}${index}`);
          // console.log('=>(edit.vue:1630) pickerPop', pickerPop);
          // 本应该是pickerPop[0]，但由于缓存导致dom不销毁，故写成pickerPop.length - 2
          let el = pickerPop[0].querySelector('.ivu-picker-panel-sidebar');
          // console.log('=>(edit.vue:1627) el', el);
          if (el.childNodes.length) {
            if (!this.current_production_date) {
              // 当 current_production_date 为空时，添加 `date-disabled` 类
              el.childNodes.forEach(item => {
                if (!item.classList.contains('date-disabled')) {
                  item.classList.add('date-disabled');
                }
              });
            } else {
              // 当 current_production_date 有值时，移除 `date-disabled` 类
              el.childNodes.forEach(item => {
                if (item.classList.contains('date-disabled')) {
                  item.classList.remove('date-disabled');
                }
              });
            }
          }
        });
      }
    },
    // 修改发票号
    changeInvoiceNoHandle(e, index) {
      this.list[index].invoiceNo = this.localList[this.getCurrentIndex(index)].invoiceNo = e.target.value;
    },
    getCurrentIndex(index) {
      const { page, pageSize } = this.tableFormData;
      return $operator.multiply(page - 1, pageSize) + index;
    },
    // 修改单位
    changeCompanyHandle(e, index, row) {
      if (e === 'Kg' && this.list[index].quantity > 100) {
        this.list[index].quantity = 100;
      }

      console.log('-> %c e, index, row  === %o ', 'font-size: 15px', e, index, row);
      this.list[index].prod_unit =
        this.localList[this.getCurrentIndex(index)].prod_unit =
        this.list[index].warehouse_unit =
          e;
      // this.localList[this.getCurrentIndex( index )].prod_unit
      // this.localList[]
      this.list[index].sales_units.map(item => {
        console.log('-> %c item  === %o ', 'font-size: 15px', item);
        item.checked = '0';
        if (item.unit === e) {
          this.list[index].last_ostock_price = item.last_ostock_price;
          this.list[index].retailPrice = item.price;
          this.list[index].unit_ratio = item.unit_ratio;
          this.is_covid && (this.list[index].purchasePrice = this.list[index].purchase_price = +item.purchase_price);
          item.checked = '1';
          this.list[index].is_big = item.is_big;
          if (item.is_big !== '1' && this.list[index].quantity) {
            console.log(
              '-> %c this.list[index].quantity  === %o',
              'font-size: 15px;color: green;',
              this.list[index].quantity
            );
            this.list[index].quantity = $operator.toPrecision(this.list[index].quantity, 0);
            console.log('%c=>(edit.vue:1616) this.list[index]', 'color: #ECA233;font-size: 16px;', this.list[index]);
            // console.log("-> %c $operator.toPrecision  === %o", "font-size: 15px;color: green;", $operator.toPrecision(2,0))
          }
          this.list[index].purchasePrice &&
            (this.list[index].subtotal = $operator.multiply(
              this.list[index].quantity || 0,
              this.list[index].purchasePrice
            ));
        }
      });
      this.localList[this.getCurrentIndex(index)] = this.list[index];
      console.log(this.localList);
      // if ( row.unit_price_option ) {
      //   this.list[index].purchasePrice = row.unit_price_option[e] - 0
      //   this.list[index].subtotal = this.list[index].quantity * this.list[index].purchasePrice
      //   this.list.slice( 0, 0 )
      // }
    },
    // 删除
    delListItemHandle(row, index) {
      const { page, pageSize } = this.tableFormData;
      this.list.splice(index, 1);
      this.localList.splice(this.getCurrentIndex(index), 1);
      // 删除后本地列表为空且常繁导入为1时，重置列表
      if (this.localList.length === 0 && this.is_cf_import) {
        this.clearAll();
      }
      this.list = this.localList.slice($operator.multiply(page - 1, pageSize), $operator.multiply(page, pageSize));
      this.tableFormData.total = this.localList.length;
    },
    handleDraftParams() {
      let values = this.formValidate;
      const params = {
        ostock_type: 10,
        prod_type: this.$route.query.prod_type,
        order_code: values.order_code,
        ostock_code: values.ostock_code,
        supplier_id: values.supplier,
        operator_name: values.user,
        create_time: values.create_time,
        // inspection_time: values.inspectionTime ? moment(values.inspectionTime).format('YYYY-MM-DD') : '',
        remark: values.remark,
        cf_remark: values.cf_remark,
        items: this.localList,
        is_cf_import: this.is_cf_import,
        images: values.images,
        cf_code: values.cf_code,
      };
      return params;
    },
    handleSaveParams() {
      let values = this.formValidate;
      console.log('-> values.images', values.images);
      let params = {
        ostock_type: 10,
        prod_type: this.$route.query.prod_type,
        order_code: values.order_code,
        ostock_code: values.ostock_code,
        supplier_id: values.supplier,
        operator_name: values.user,
        // create_time: moment(values.create_time).format('YYYY-MM-DD'),
        // inspection_time: moment(values.inspectionTime).format('YYYY-MM-DD'),
        cf_remark: values.cf_remark,
        cf_code: values.cf_code,
        remark: values.remark,
        is_cf_import: this.is_cf_import,
        images: values.images,
      };
      let items = this.localList.map((item, i) => {
        return {
          prod_id: item.number,
          prod_stock_id: item.prod_stock_id,
          batch_code: item.batchNumber,
          purchase_stock_num: item.quantity,
          purchase_price: item.purchasePrice,
          produce_time: item.productionDate,
          expire_time: item.termOfValidity,
          invoice_code: item.invoiceNo,
          // manufacturer: item.manufactor,
          warehouse_unit: item.warehouse_unit || item.prod_unit,
          spec_model: item.spec_model,
          reg_cert_no: item.reg_cert_no,
          reg_cert_name: item.reg_cert_name,
          prod_source: item.prod_source,
        };
      });
      params.items = items;
      if (this.id) {
        params.id = this.id;
      }
      this.is_covid && (params.is_covid = 1);
      return params;
    },
    // 保存
    onSave() {},
    //检查零售价和采购价的比例是否都在2.8～3.2之间，存在不同则提示不在比例的商品

    validateTableData() {
      for (const item of this.localList) {
        // console.log( '-> %c item  === %o ', 'font-size: 15px', item )
        if (!item.prod_unit) {
          this.$Message.error(`货品【${item.goodsname}】的单位不能为空`);
          return false;
        }
        if (!item.quantity) {
          this.$Message.error(`货品【${item.goodsname}】的数量不能为空`);
          return false;
        }
        if (!item.purchasePrice) {
          this.$Message.error(`货品【${item.goodsname}】的采购价不能为空`);
          return false;
        }
        if (!item.batchNumber) {
          this.$Message.error(`货品【${item.goodsname}】的生产批号不能为空`);
          return false;
        }
        if (!item.productionDate) {
          this.$Message.error(`货品【${item.goodsname}】的生产日期不能为空`);
          return false;
        }
        if (this.specialRequiredType && !item.termOfValidity) {
          this.$Message.error(`货品【${item.goodsname}】的有效期不能为空`);
          return false;
        }
        // 当生产日期和有效期都有值得时候，有效期必须大于生产日期
        if (item.productionDate && item.termOfValidity && !moment(item.productionDate).isBefore(item.termOfValidity)) {
          this.$Message.error(`货品【${item.goodsname}】的有效期需要大于生产日期`);
          return false;
        }
      }
      return true;
    },
    checkExtremePrice() {},
    submit(isAdjust, shouldCheckExtremePrice) {
      // 编码相同的货品， 在提交的时候拦截
      let isExistGoodscode = this.list.some((item, index) => {
        return this.list.some((inner_item, inner_index) => {
          if (item.number === inner_item.number && index !== inner_index) {
            this.$Message.error(`【${item.goodsname}】存在重复添加`);
            return true;
          }
        });
      });

      if (isExistGoodscode) {
        return false;
      }

      this.$refs.myForm.validate(valid => {
        console.log('-> %c valid  === %o ', 'font-size: 15px', valid);
        if (valid) {
          if (this.validateTableData()) {
            if (this.is_covid) {
              this.submitForm(isAdjust);
            } else {
              if (this.checkPrice(true) && shouldCheckExtremePrice && this.prod_type == 2) {
                this.extremeVisible = true;
              } else {
                if (Number(this.is_cf_import) === 1) {
                  this.checkCfOutboundCode();
                } else {
                  this.submitForm(isAdjust);
                }
              }
            }
          }
        } else {
          this.$Message.error('请按提示完善表单信息');
        }
      });
    },

    checkPrice(isExtreme) {
      this.extremePriceItems = [];
      let temObj = {};
      this.localList.map((item, index) => {
        item.calcPrice = $operator.multiply(item.unit_ratio, item.purchasePrice, 4);
        item.ratio = $operator.divide(Number(item.retailPrice), Number(item.purchasePrice));
        item.newRetailPrice = Number(item.retailPrice);
        if (item.number in temObj) {
          if (temObj[item.number].calcPrice < item.calcPrice) {
            temObj[item.number] = item;
          }
        } else {
          temObj[item.number] = item;
        }
      });
      for (const temObjElement in temObj) {
        let temItem = temObj[temObjElement];
        if (temItem.prod_unit === 'g' && temItem.purchasePrice > this.ostockRules[temItem.prod_unit].price) {
          temItem.isExtreme = 'high';
          this.extremePriceItems.push(temItem);
        } else if (temItem.prod_unit === 'Kg' && temItem.purchasePrice < this.ostockRules[temItem.prod_unit].price) {
          temItem.isExtreme = 'low';
          this.extremePriceItems.push(temItem);
        }
      }
      return isExtreme ? Boolean(this.extremePriceItems.length) : Boolean(this.lowPriceItems.length);
    },
    submitForm(isAdjust) {
      isAdjust && (this.remindVisible = false);
      this.subBtnLoading = true;
      io.post('/clinic/ostock.edit', this.handleSaveParams())
        .then(
          data => {
            this.$Message.success({
              content: '提交成功',
              onClose: () => {},
            });
            this.$router.replace('/stock/ostock/list');
          },
          error => {}
        )
        .finally(() => {
          this.subBtnLoading = false;
        });
    },
    // 取消
    backHandle() {
      console.log(213213, this.$route.query);
      if (this.$route.query.from && this.$route.query.from === 'bills_list') {
        console.log('32323');
        this.$router.push('/stock/instock/bills_list');
      } else {
        this.$router.back();
      }
    },
    getDraft(ostock_type) {
      let that = this;
      const params = {
        prod_type: this.$route.query.prod_type,
        ostock_type,
      };
      this.$api.getStockDraft(params).then(res => {
        console.log('-> res', res);
        if (res.has_display === '1') {
          res.data.items.map(item => {
            item.purchasePrice = Number(item.purchasePrice);
            item.subtotal = Number(item.subtotal);
            item.quantity = Number(item.quantity);
            item.retailPrice = Number(item.retailPrice);
            item.sales_units.map(sub => {
              if (sub.checked === '1') {
                item.prod_unit = sub.unit;
                item.retailPrice = sub.price;
                item.last_ostock_price = sub.last_ostock_price;
                item.unit_ratio = Number(sub.unit_ratio);
                item.is_big = sub.is_big;
              }
            });
            item.calcPrice = $operator.multiply(item.unit_ratio, item.purchase_price, 4);
            item.termOfValidityOptions = {
              shortcuts: [
                {
                  text: '顺延12个月',
                  value() {
                    console.log('=>(edit.vue:1447) that.current_production_date', that.current_production_date);
                    return S.moment(that.current_production_date)
                      .add(12, 'months')
                      .subtract(1, 'days')
                      .format('YYYY-MM-DD');
                  },
                },
                {
                  text: '顺延18个月',
                  value() {
                    return S.moment(that.current_production_date)
                      .add(18, 'months')
                      .subtract(1, 'days')
                      .format('YYYY-MM-DD');
                  },
                },
                {
                  text: '顺延2年',
                  value() {
                    return S.moment(that.current_production_date)
                      .add(2, 'years')
                      .subtract(1, 'days')
                      .format('YYYY-MM-DD');
                  },
                },
                {
                  text: '顺延3年',
                  value() {
                    return S.moment(that.current_production_date)
                      .add(3, 'years')
                      .subtract(1, 'days')
                      .format('YYYY-MM-DD');
                  },
                },
              ],
            };
          });
          this.localList = res.data.items;
          console.log('-> %c this.localList  === %o ', 'font-size: 15px', this.localList);
          this.tableFormData.total = this.localList.length;
          const { page, pageSize } = this.tableFormData;
          this.list = cloneDeep(this.localList).slice(
            $operator.multiply(page - 1, pageSize),
            $operator.multiply(page, pageSize)
          );
          console.log('-> %c this.list  === %o ', 'font-size: 15px', this.list);
          // this.formValidate.inspectionTime = res.data.inspection_time;
          this.is_cf_import = Number(res.data.is_cf_import);

          this.formValidate.supplier = res.data.supplier_id;
          this.clone_supplier_id = res.data.supplier_id;
          this.formValidate.user = res.data.operator_name || runtime.getUser().name;
          this.formValidate.ostock_code = res.data.ostock_code;
          this.formValidate.type = res.data.type || 'CLI';
          this.formValidate.order_code = res.data.order_code;
          this.formValidate.images =
            typeof res.data.images == 'string' && res.data.images ? JSON.parse(res.data.images) : [];
          this.formValidate.remark = res.data.remark;
          this.formValidate.cf_remark = res.data.cf_remark;
          this.formValidate.cf_code = res.data.cf_code;
        }
      });
    },
    // 获取供应商列表
    querySupplierList: debounce(
      function (query) {
        let params = {
          page: 1,
          pageSize: 20,
          // must_contain_id: this.clone_supplier_id,
          name: query || '',
          status: this.$route.query.status === '20' ? '' : 1,
          prod_type: this.$route.query.status === '20' ? '' : this.prod_type,
        };
        io.get('/clinic/supplier.list', { data: params })
          .then(data => {
            if (this.primitiveSupplier.name.includes(query)) {
              let flag = data.suppliers.findIndex(supplier => supplier.id === this.primitiveSupplier.id);
              if (flag > -1) {
                this.supplierList = data.suppliers;
              } else {
                this.supplierList = [...data.suppliers, this.primitiveSupplier];
              }
            } else {
              if (this.formValidate.supplier === this.primitiveSupplier.id && this.primitiveSupplier.id) {
                this.supplierList = [...data.suppliers, this.primitiveSupplier];
              } else {
                this.supplierList = data.suppliers;
              }
            }
            console.log('=>(edit.vue:1614) this.supplierList', this.supplierList);
            // if (data.suppliers.length === 1) {
            //   this.formValidate.supplier = data.suppliers[0].id;
            // }
            this.cf_supplier = this.supplierList.find(item => item.is_by_cf === '1') || {};
          })
          .catch(error => {
            {
            }
          });
      },
      500,
      { leading: false, trailing: true }
    ),

    // 获取规则
    getRules() {
      this.$api.getOstockRules().then(
        res => {
          this.ostockRules = {};
          for (let key in res) {
            this.ostockRules[res[key].unit] = res[key];
          }
        },
        error => {}
      );
    },

    // 获取商品列表
    queryGoodsList() {
      let params = {
        page: 1,
        pageSize: 20,
        name: '',
        status: '',
        prod_type: this.prod_type,
      };
      io.get('/clinic/product.product.list', { data: params })
        .then(data => {
          this.goodsList = data.list;
          this.copyGoodsList = data.list;
        })
        .catch(error => {
          {
          }
        });
    },
    // 获取单条商品数据
    // queryGoodsSingle(id) {
    //   let params = {
    //     prod_id: id,
    //   };
    //   io.get("/clinic/product.product.ostock", { data: params })
    //     .then((data) => {
    //       console.log("-> data", data);
    //       if (S.isEmptyObject(data)) {
    //         return;
    //       }
    //       let len = this.list.length;
    //       this.list.forEach((v, i) => {
    //         if (v.number == id && !v.isExport && i == len - 1) {
    //           console.log(v);
    //           v.prod_unit = data.last_warehouse_unit;
    //           if (data.last_warehouse_unit) {
    //             v.purchasePrice =
    //               data.unit_price_option[data.last_warehouse_unit] - 0;
    //           }
    //           v.unit_price_option = data.unit_price_option;
    //         }
    //       });
    //     })
    //     .catch((error) => {
    //       {};
    //     });
    // },

    // goodsSearchHandle: debounce(function (query, shouldReset) {
    //   console.log('-> query', query);
    //   // if (!this.canSearch) {
    //   //   return
    //   // }
    //   let params = {
    //     page: 1,
    //     pageSize: 20,
    //     name: query,
    //     prod_type: this.prod_type,
    //     is_get_last_ostock: '1',
    //     status: '1'
    //   };
    //   this.loading1 = true;
    //   const searchApiName = this.is_covid ? 'getCFInStockList' : 'getInStockProductList';
    //   this.$api[searchApiName](params)
    //     .then(data => {
    //       console.log('-> data', data);
    //       this.loading1 = false;
    //       this.goodsList = data.list;
    //       if (this.is_covid && !this.id && shouldReset) {
    //         const resList = this.handleCFSelectProduct(data.list);
    //         this.localList = resList;
    //         this.formValidate.supplier = data.supplier_id;
    //         this.initBatchTableList();
    //       }
    //     })
    //     .catch(error => {
    //       {};
    //     });
    // }, 600),
    // 入库明
    getMaxAmount() {
      let maxAmount = 0;
      this.localList.length &&
        this.localList.length > 0 &&
        this.localList.map((item, i) => {
          maxAmount = maxAmount + item.subtotal * 1;
        });
      return S.number_format(maxAmount, 2);
    },
    // 总重量（仅计算单位为g与kg的商品）
    getTotalWeight() {
      return (
        this.localList &&
        this.localList.length > 0 &&
        this.localList.reduce((prev, curr) => {
          if (curr.prod_unit === 'Kg' || curr.prod_unit === 'g') {
            if (curr.prod_unit === 'g') {
              let kilogram = $operator.divide(curr.quantity || 0, 1000, 3);
              return $operator.add(prev, kilogram, 3);
            } else {
              return $operator.add(prev, curr.quantity || 0, 3);
            }
          } else {
            return prev;
          }
        }, 0)
      );
    },
    // 总数量（非克重单位的商品数量）
    getOtherUnitList() {
      // 当前列表非克重单位的剩余商品
      let lastList =
        this.localList.length &&
        this.localList.length > 0 &&
        this.localList.filter(item => item.prod_unit !== 'Kg' && item.prod_unit !== 'g');
      let unitList = [];
      lastList &&
        lastList.forEach(item => {
          let flag = unitList.findIndex(unit_item => unit_item.prod_unit === item.prod_unit);
          if (flag > -1) {
            unitList[flag].quantity = $operator.add(Number(unitList[flag].quantity || 0), Number(item.quantity || 0));
          } else {
            let currentObj = { prod_unit: item.prod_unit, quantity: item.quantity };
            unitList.push(currentObj);
          }
        });
      return unitList;
    },
    // 判断是否禁用
    valDisabledHandle(row, index) {
      if (!row.subtotalDisabled) {
        return false;
      }
      if (this.list[index].quantity !== '' && this.list[index].purchasePrice === null) {
        this.list[index].subtotalDisabled = false;
        return false;
      } else {
        return true;
      }
    },
    getRatio() {
      this.$api.getRetailPurchaseRatio().then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.ratioObj.ratio = +res.ratio;
        this.ratioObj.offset = +res.offset;
      });
    },
    // 常繁导入前，当本地有草稿数据，需清除
    changFanUploadCheck() {
      if (this.localList.length > 0) {
        this.confirmVisible = true;
      } else {
        this.changFanUpload();
      }
    },
    // 常繁上传
    changFanUpload() {
      this.getCFOutboundList();
      // 手动选择供应商导致远程搜索，选择列表中无对应供应商数据，后续无法直接切换为常繁供应商
      this.querySupplierList();
      // this.importVisible = true;
    },
    getCFOutboundList() {
      let params = {
        prod_type: this.$route.query.prod_type,
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getCFOutboundList(params)
        .then(res => {
          console.log('=>(detail.vue:242) res', res);
          if (!res.list.length) {
            this.noticeVisible = true;
            this.noticeContent = `暂未查询到「${this.clinicName}」的在途订单`;
          } else {
            this.CFImportVisible = true;
            this.CFImportList = res.list;
          }
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    // 常繁导入
    importCfReport(data, cf_code, cf_remark) {
      this.handleUploadSuccessData(data);
      this.is_cf_import = 1;
      this.formValidate.cf_remark = cf_remark;
      this.formValidate.cf_code = cf_code;
      this.formValidate.supplier = this.cf_supplier.id;
    },
    // flag由上传文件成功数量>0来判断，全部失败则为false
    emitFinish(flag) {
      if (flag && this.is_cf_filename_valid) {
        console.log('-> this.cf_supplier', this.cf_supplier);
        this.formValidate.supplier = this.cf_supplier.id;
        this.is_cf_import = 1;
        console.log('-> this.filename', this.filename);
      }
    },
    clearAndUpload() {
      this.clearAll();
      this.changFanUpload();
    },
    parseImportFileName(filename) {
      let params = {
        filename,
        type: '1', // 类型: 1=常繁随货单名称解析
      };
      console.log('=>(detail.vue:241) params', params);
      return this.$api
        .parseImportFileName(params)
        .then(res => {
          if (res.is_valid === '1') {
            this.formValidate.cf_remark = `${res.parse_data.relate_code}-${res.parse_data.code}`;
            this.is_cf_filename_valid = true;
          } else {
            this.$Message.error('文件名格式错误');
            this.is_cf_filename_valid = false;
          }
          console.log('=>(detail.vue:242) res', res);
          return new Promise(resolve => {
            resolve(this.is_cf_filename_valid);
          });
        })
        .catch(err => {});
    },
    // 从底层获取文件名
    getFileName(file) {
      this.filename = file;
    },
    handlePrecondition() {
      return this.parseImportFileName(this.filename);
    },
    checkCfOutboundCode() {
      let params = {
        cf_code: this.formValidate.cf_code,
        prod_type: this.$route.query.prod_type,
      };
      console.log('=>(detail.vue:241) params', params);
      this.$api.checkCfOutboundCode(params).then(res => {
        console.log('=>(detail.vue:242) res', res);
        if (res.is_exist === '1') {
          this.warnVisible = true;
        } else {
          this.submitForm();
          console.log('=>(CFImportModal.vue:145) go');
        }
      });
    },
  },
  computed: {
    /**
     * @description 针对以下药品类货品，进行合规性要求，有效期字段必填
     * 中药饮片         2
     * 中成药           3
     * 贵细药材         15
     * 中药饮片（非处方） 16
     * */
    specialRequiredType() {
      const type = this.prod_type;
      switch (type) {
        case '2':
        case '3':
        case '15':
        case '16':
          return true;
        default:
          return false;
      }
    },
    isSuplierDisabled() {
      return this.isDisatbed || !!this.is_covid || !!this.is_cf_import || this.isExamine || this.isDetail;
    },
    isDetail() {
      return this.$route.query.type === 'detail';
    },
    isExamine() {
      return this.$route.query.examineStatus === 'WAIT_AUDIT' && this.status === '10';
    },
    getPrecision() {
      return item => {
        if (item.is_split === '1' && item.is_big === '1') {
          const split_num = +item.split_num;
          return Math.floor(Math.log10(split_num));
        } else {
          return 0;
        }
      };
    },
    dynamicTableCols() {
      return [
        { title: '序号', slot: 'number', minWidth: 50 },
        { title: '商品编码', slot: 'goodscode', minWidth: 80 },
        { title: '商品名称', slot: 'goodsname', minWidth: 220 },
        { title: '来源', key: 'from_text', minWidth: 70 },
        { title: '数量', slot: 'quantity', minWidth: 80 },
        // 动态插入
        ...(this.prod_type === '17' ? [
          { title: '规格型号', slot: 'spec_model', minWidth: 120 },
          { title: '注册证号', slot: 'reg_cert_no', minWidth: 120 },
          { title: '注册证名称', slot: 'reg_cert_name', minWidth: 120 },
          { title: '国产/进口', slot: 'prod_source', minWidth: 100 },
        ] : []),
        { title: '单位', slot: 'company', minWidth: 80 },
        { title: '采购价', slot: 'purchasePrice', minWidth: 85 },
        { title: '采购合计', slot: 'subtotal', minWidth: 100 },
        { title: '零售价', slot: 'retailPrice', minWidth: 100 },
        { title: '生产批号', slot: 'batchNumber', minWidth: 90 },
        { title: '生产日期', slot: 'productionDate', minWidth: 130 },
        { title: '有效期', slot: 'termOfValidity', minWidth: 130 },
        { title: '操作', slot: 'operation', fixed: 'right', minWidth: 70 },
      ];
    },
    formatSelectedRow() {
      return row => {
        const prod = Object.values(this.products)?.find(item => item.id === row.number) || {}
        return { ...prod, ...row }
      }
    }
  },
};
</script>

<style lang="less" scoped>
.ostockEdit {
  //min-height: 100vh;

  .ivu-date-picker {
    width: 100%;
  }

  .ivu-table-body {
    // min-height: 120px;
  }

  .ivu-table-wrapper {
    overflow: inherit;
  }

  .submitBtn {
    margin-right: 20px;
  }
}

.images-tip {
  display: inline-block;
  line-height: 16px;
  color: #999;
  margin-top: -10px;
}

@picWidth: 50px;
::v-deep .picture-list .picture-list__pic {
  width: @picWidth;
  height: @picWidth;
  margin-bottom: 0px;
}

::v-deep .picture-upload {
  width: @picWidth;
  height: @picWidth;
  margin-bottom: 0px;
}

::v-deep .picture-upload .picture-upload-icon {
  width: @picWidth;
  height: @picWidth;
  background-size: 22px 17px;
  margin-bottom: 0px;
}

::v-deep .upload-limit {
  bottom: 2px;
}

.ml10 {
  margin-left: 10px;
}

///deep/ .ivu-modal-footer {
//  display: none;
//}
.input-poptip-content {
  min-width: 269px;
  background: #ffffff;
  border-radius: 4px;
  //border: 1px solid #3088FF;
  min-height: 78px;
  padding: 10px 16px;

  .content-price {
    display: flex;
    justify-content: space-between;
    font-weight: 400;
    font-size: 14px;
    color: #303133;
    line-height: 22px;

    .time {
      font-weight: 400;
      font-size: 12px;
      color: #909399;
      line-height: 22px;
      margin-left: 20px;
    }
  }

  .text {
    margin-top: 8px;
    font-weight: 400;
    font-size: 14px;
    color: #3088ff;
    line-height: 22px;
  }
}

.echarts-poptip-content {
  padding: 16px;
  background: #ffffff;
  width: 600px;
  max-width: 600px;
  min-height: 300px;
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  border-radius: 4px;

  .text-content {
    font-weight: 400;
    font-size: 14px;
    color: #303133;
    display: flex;

    .text-item {
      flex: 1;
      background: #f9fafb;
      border-radius: 4px;
      padding: 6px 12px;
      margin-right: 12px;

      &:last-child {
        margin-right: 0px;
      }

      .text-item-top {
        display: flex;
        justify-content: space-between;

        .item-top-title {
          font-weight: 400;
          font-size: 13px;
          color: #303133;
          line-height: 20px;
        }

        .item-top-time {
          font-weight: 400;
          font-size: 12px;
          color: #909399;
          line-height: 20px;
        }
      }

      .item-price {
        margin-top: 4px;
        font-weight: 500;
        font-size: 16px;
        color: #303133;
        line-height: 24px;
        text-align: center;
      }

      .company {
        margin-top: 4px;
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 20px;
        white-space: pre-wrap;
        text-align: justify;
      }
    }
  }
}
::v-deep .ivu-picker-panel-sidebar {
  width: 98px;
}
</style>
<style lang="less">
.search-sel {
  max-height: 300px !important;
}

[v-cloak] {
  display: none;
}

.custom-input-poptip {
  .ivu-poptip-inner {
    border-radius: 4px !important;
  }
}

.custom-echarts-poptip {
  .ivu-poptip-inner {
    border-radius: 4px !important;
  }
}
.ivu-modal-confirm-body {
  padding-left: 0;
}

.date-range-disabled {
  .ivu-picker-panel-sidebar {
    width: 98px;
  }
  .date-disabled {
    pointer-events: none;
    color: #ccc;
  }
}
.bottomText {
  margin-top: 20px;
}
.generic-name-link-tip {
  .ivu-tooltip-inner {
    max-width: unset;
    background-color: #FFFDEC;
  }
}
</style>
