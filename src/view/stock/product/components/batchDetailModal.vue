<template>
  <Modal
    ref="batchDetailModal"
    :value="visible"
    :mask-closable="false"
    width="800px"
    @on-cancel="cancel"
    @on-visible-change="changeVisible"
    footer-hide
  >
    <div slot="header" class="flex ivu-modal-header-inner">
      <div>批次明细</div>
      <div style="color: #999999; margin-left: 10px; font-size: 12px">批次：{{ batchInfo.batch_code }}</div>
    </div>
    <div>
      <Form ref="formData" inline @submit.native.prevent @keyup.enter.native="getProductStockBillsList">
        <FormItem>
          <Select v-model="formData.method" placeholder="全部操作类型" clearable @on-change="getProductStockBillsList">
            <Option value="">{{ '全部操作类型' }}</Option>
            <Option :value="index" v-for="(item, index) in methodDesc" :key="index">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Select v-model="formData.type" placeholder="全部变更类型" clearable @on-change="getProductStockBillsList">
            <Option value="">全部变更类型</Option>
            <Option :value="index" v-for="(item, index) in typeDesc" :key="index">{{ item.desc }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <DatePicker
            type="daterange"
            :options="pickerOptions"
            clearable
            v-model="timeRange"
            @on-change="times => handleTimeChange(times)"
            placeholder="变更时间"
          >
          </DatePicker>
        </FormItem>
        <!--        <FormItem>-->
        <!--          <Button type="primary" class="mr10" @click="onSearchBatch">筛选</Button>-->
        <!--          <Button class="mr10" @click="exportExcel">导出</Button>-->
        <!--          <span class="list-reset-btn" @click="initSearch"-->
        <!--            ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span-->
        <!--          >-->
        <!--        </FormItem>-->
      </Form>
      <Table :columns="tableCols" :data="list" height="400" class="mb20">
        <template v-slot:type="{ row }">
          {{ typeDesc[row.type] && typeDesc[row.type].desc }}
        </template>
        <template v-slot:method="{ row }">
          {{ methodDesc[row.method].desc }}
        </template>
        <template v-slot:quantity="{ row }">
          {{ row.warehouse_stock_text }}
        </template>
        <template v-slot:time="{ row }">
          {{ row.create_time | data_format('YYYY-MM-DD') }}
        </template>
      </Table>
      <KPage
        :total="total"
        :page-size.sync="formData.pageSize"
        :current.sync="formData.page"
        @on-change="onPageChange"
        style="text-align: right"
      />
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="submitForm" :loading="submitLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
import S from 'libs/util';
const initFormData = {
  page: 1,
  pageSize: 20,
  method: '', //销售单编号；如果不填写，创建时系统会自动生成
  type: '',
  st: '',
  et: '',
};

export default {
  name: 'batchDetailModal',
  mixins: [],
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    id: {
      type: String,
      default: '',
    },
    batchInfo: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      formData: { ...initFormData },
      submitLoading: false,
      supplierTypes: [],
      timeRange: [],
      tableCols: [
        { title: '变更类型', slot: 'type', align: 'center' },
        { title: '操作类型', slot: 'method', align: 'center' },
        { title: '变更数量', slot: 'quantity', align: 'center' },
        { title: '库存结余', key: 'stock_text', align: 'center' },
        { title: '变动时间', slot: 'time', align: 'center' },
      ],
      list: [],
      prods: {},
      stockDetails: {},
      suppliers: {},
      typeDesc: {},
      methodDesc: {},
      prodTypes: {},
      prodSource: {},
      total: 0,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    changeVisible(val) {
      if (val) {
        this.getProductStockBillsList();
      } else {
        this.formData = { ...initFormData }; // 重置数据
        // this.$refs['modal'].$el.querySelector('.ivu-modal-body').scrollTop = 0; // 重置滚动条高度
      }
    },
    cancel() {
      this.$emit('update:visible', false);
    },
    submitForm() {},
    handleTimeChange(times, startTime = 'st', endTime = 'et') {
      if (times) {
        this.formData[startTime] = times[0];
        this.formData[endTime] = times[1];
      } else {
        this.formData[startTime] = '';
        this.formData[endTime] = '';
      }
      this.getProductStockBillsList();
    },
    getProductStockBillsList() {
      let params = { ...this.formData, prod_id: this.batchInfo.prod_id, detail_id: this.batchInfo.id };
      console.log('=>(detail.vue:241) params', params);
      this.$api
        .getProductStockBillsList(params)
        .then(res => {
          this.list = res.list;
          this.prods = res.prods;
          this.stockDetails = res.stockDetails;
          this.suppliers = res.suppliers;
          this.typeDesc = res.typeDesc;
          this.methodDesc = res.methodDesc;
          // this.prodTypes = S.descToArrHandle(data.prodTypes).sort((a, b) => a.sort - b.sort);
          this.prodSource = res.sourceDesc;
          this.total = res.total;
        })
        .catch(err => this.$Message.error(err.errmsg));
    },
    onPageChange(page, pageSize) {
      this.formData.page = page;
      this.formData.pageSize = pageSize;
      this.formData.page_size = pageSize;
      this.getProductStockBillsList();
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less"></style>
