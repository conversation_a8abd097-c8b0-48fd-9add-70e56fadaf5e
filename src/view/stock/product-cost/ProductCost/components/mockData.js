// stock_num+"prod_unit"
let costList = [
  {
    id: 1,
    prod_id: '350695',
    prod_name: '枸杞子',
    prod_type: 0,
    prod_type_text: '中药饮片',
    source: 0,
    source_text: '系统内建',
    prod_status: '',
    prod_status_text: '正常',
    status: 0,
    status_text: '正常',
    prod_unit: 'g',
    split_prod_unit: '件',
    stock_num: '1120',
    supplier: '常繁医药xxxx公司',
    zx_update_time: '2025：05：08',
    zx_pur_price: '0.0886',
    zx_pur_price_text: '0.0886/g',
    cost_status: '2', // 1 = 正常 2 = 偏高 3 = 偏低
    cost_status_text: '偏高',
    cost_price: '0.3123',
    cost_total_price: '2321.38',
    sales_price: '0.3123',
    sales_total_price: '2321.38',
  },
  {
    id: 2,
    prod_id: '350694',
    prod_name: '白术',
    prod_type: 0,
    prod_type_text: '中药饮片',
    source: 0,
    source_text: '自定义',
    prod_status: '',
    prod_status_text: '正常',
    status: 0,
    status_text: '正常',
    prod_unit: 'g',
    split_prod_unit: '件',
    stock_num: '1120',
    supplier: '常繁医药xxxx公司',
    price_update_time: '1689061466',
    zx_pur_price: '0.0886',
    zx_pur_price_text: '0.0886/g',
    cost_status: '3', // 1 = 正常 2 = 偏高 3 = 偏低
    cost_status_text: '偏低',
    cost_price: '0.3123',
    cost_total_price: '2321.38',
    sales_price: '0.3123',
    sales_total_price: '2321.38',
  },
  {
    id: 3,
    prod_id: '350673',
    prod_name: '车前草',
    prod_type: 0,
    prod_type_text: '中药饮片',
    source: 0,
    source_text: '系统内建',
    prod_status: '',
    prod_status_text: '正常',
    status: 0,
    status_text: '正常',
    prod_unit: 'g',
    split_prod_unit: '件',
    stock_num: '1120',
    supplier: '常繁医药xxxx公司',
    price_update_time: '1689061466',
    zx_pur_price: '0.0886',
    zx_pur_price_text: '0.0886/g',
    cost_status: '1', // 1 = 正常 2 = 偏高 3 = 偏低
    cost_status_text: '正常',
    cost_price: '0.3123',
    cost_total_price: '2321.38',
    sales_price: '0.3123',
    sales_total_price: '2321.38',
  },
  {
    id: 3,
    prod_id: '350673',
    prod_name: '车前草',
    prod_type: 0,
    prod_type_text: '中药饮片',
    source: 0,
    source_text: '系统内建',
    prod_status: '',
    prod_status_text: '正常',
    status: 0,
    status_text: '正常',
    prod_unit: 'g',
    split_prod_unit: '件',
    stock_num: '1120',
    supplier: '常繁医药xxxx公司',
    price_update_time: '1689061466',
    zx_pur_price: '',
    zx_pur_price_text: '0.0886/g',
    cost_status: '', // 1 = 正常 2 = 偏高 3 = 偏低
    cost_status_text: '',
    cost_price: '0.3123',
    cost_total_price: '2321.38',
    sales_price: '0.3123',
    sales_total_price: '2321.38',
  },
];

let detailList = [
  {
    id: 1,
    bill_id: '1231313',
    bill_date: '2023/07/11',
    prod_name: '白术',
    bill_code: 'CK000002',
    bill_type: 0,
    bill_type_text: '出库',
    bill_abstract: 0,
    bill_abstract_text: '销售出库',
    prod_unit: 'g',
    split_prod_unit: '件',
    in: {
      num: '',
      price: '',
      total_price: '',
      cost_status: '2', // 1 = 正常 2 = 偏高 3 = 偏低
    },
    out: {
      num: '',
      price: '',
      total_price: '',
    },
    balance: {
      num: '',
      price: '',
      total_price: '',
    },
    is_auditing: '1', //是否在重置审核中  1 = 是 2 = 否
    is_show_set_price: '1', // 是否显示重置入库单价按钮 1 = 是 2 = 否
  },
  {
    id: 1,
    bill_id: '1231313',
    bill_date: '2023/07/11',
    prod_name: '白术',
    bill_code: 'CK000002',
    bill_type: 0,
    bill_type_text: '出库',
    bill_abstract: 0,
    bill_abstract_text: '销售出库',
    prod_unit: 'g',
    split_prod_unit: '件',
    in: {
      num: '0',
      price: '0',
      total_price: '0',
      cost_status: '3', // 1 = 正常 2 = 偏高 3 = 偏低
    },
    out: {
      num: '0',
      price: '0',
      total_price: '0',
    },
    balance: {
      num: '0',
      price: '0',
      total_price: '0',
    },
    is_auditing: '2', //是否在重置审核中  1 = 是 2 = 否
    is_show_set_price: '1', // 是否显示重置入库单价按钮 1 = 是 2 = 否
  },
  {
    id: 1,
    bill_date: '2023/07/11',
    prod_name: '白术',
    bill_code: 'CK000002',
    bill_type: 0,
    bill_type_text: '出库',
    bill_abstract: 0,
    bill_abstract_text: '销售出库',
    prod_unit: 'g',
    split_prod_unit: '件',
    in: {
      num: '0',
      price: '0',
      total_price: '0',
      cost_status: '1', // 1 = 正常 2 = 偏高 3 = 偏低
    },
    out: {
      num: '0',
      price: '0',
      total_price: '0',
    },
    balance: {
      num: '0',
      price: '0',
      total_price: '0',
    },
    is_auditing: '1', //是否在重置审核中  1 = 是 2 = 否
    is_show_set_price: '2', // 是否显示重置入库单价按钮 1 = 是 2 = 否
  },
];

let resetInstockList = [
  {
    id: 123,
    bill_id: '78789789',
    detail_id: '123',
    prod_id: '350695',
    prod_name: '枸杞子',
    prod_type: 0,
    prod_type_text: '中药饮片',
    source: 0,
    source_text: '系统内建',
    status: 0,
    status_text: '正常',
    prod_unit: 'g',
    split_prod_unit: '件',
    bill_code: 'RK230430185542',
    num: '1120',
    cost_price: '2200.00',
    total_price: '2200.00',
    now_cost_price: '2200.00',
    now_total_price: '2200.00',
    commitor: {
      id: 0,
      name: '张三',
      operate_time: '2023-06-15',
    },
    auditor: {
      id: 0,
      name: '李四',
      operate_time: '2023-06-15',
    },
    reason: '单据有误',
    audit_status: '3', // 1 = 待审核； 2 = 审核通过； 3 = 审核驳回； 4 = 已撤销
    audit_status_text: '审核驳回',
  },
  {
    id: 0,
    prod_id: '350695',
    prod_name: '枸杞子',
    prod_type: 0,
    prod_type_text: '中药饮片',
    source: 0,
    source_text: '系统内建',
    status: 0,
    status_text: '正常',
    prod_unit: 'g',
    split_prod_unit: '件',
    bill_code: 'RK230430185542',
    num: '1120',
    cost_price: '2200.00',
    total_price: '2200.00',
    now_cost_price: '2200.00',
    now_total_price: '2200.00',
    commitor: {
      id: 0,
      name: '张三',
      operate_time: '2023-06-15',
    },
    auditor: {
      id: 0,
      name: '李四',
      operate_time: '2023-06-15',
    },
    reason: '单据有误',
    audit_status: '2', // 1 = 待审核； 2 = 审核通过； 3 = 审核驳回； 4 = 已撤销
    audit_status_text: '审核通过',
  },
  {
    id: 0,
    prod_id: '350695',
    prod_name: '枸杞子',
    prod_type: 0,
    prod_type_text: '中药饮片',
    source: 0,
    source_text: '系统内建',
    status: 0,
    status_text: '正常',
    prod_unit: 'g',
    split_prod_unit: '件',
    bill_code: 'RK230430185542',
    num: '1120',
    cost_price: '2200.00',
    total_price: '2200.00',
    now_cost_price: '2200.00',
    now_total_price: '2200.00',
    commitor: {
      id: 0,
      name: '张三',
      operate_time: '2023-06-15',
    },
    auditor: {
      id: 0,
      name: '李四',
      operate_time: '2023-06-15',
    },
    reason: '单据有误',
    audit_status: '3', // 1 = 待审核； 2 = 审核通过； 3 = 审核驳回； 4 = 已撤销
    audit_status_text: '审核驳回',
  },
  {
    id: 0,
    prod_id: '350695',
    prod_name: '枸杞子',
    prod_type: 0,
    prod_type_text: '中药饮片',
    source: 0,
    source_text: '系统内建',
    status: 0,
    status_text: '正常',
    prod_unit: 'g',
    split_prod_unit: '件',
    bill_code: 'RK230430185542',
    num: '1120',
    cost_price: '2200.00',
    total_price: '2200.00',
    now_cost_price: '2200.00',
    now_total_price: '2200.00',
    commitor: {
      id: 0,
      name: '张三',
      operate_time: '2023-06-15',
    },
    auditor: {
      id: 0,
      name: '李四',
      operate_time: '2023-06-15',
    },
    reason: '单据有误',
    audit_status: '4', // 1 = 待审核； 2 = 审核通过； 3 = 审核驳回； 4 = 已撤销
    audit_status_text: '已撤销',
  },
];

let operateList = [
  {
    id: 0,
    operate_time: '2023-06-05 16:32:43',
    operator: '张三',
    remark:
      '编辑入库单价（三七）（原入库金额：￥2000   修改后入库金额：￥300）（原入库单价：￥200/g   修改后入库单价：￥3/g）入库单',
  },
  {
    id: 0,
    operate_time: '2023-06-05 16:32:43',
    operator: '张三',
    remark: '',
  },
  {
    id: 0,
    operate_time: '2023-06-05 16:32:43',
    operator: '张三',
    remark:
      '编辑入库单价（三七）（原入库金额：￥2000   修改后入库金额：￥300）（原入库单价：￥200/g   修改后入库单价：￥3/g）入库单',
  },
  {
    id: 0,
    operate_time: '2023-06-05 16:32:43',
    operator: '张三',
    remark:
      '编辑入库单价（三七）（原入库金额：￥2000   修改后入库金额：￥300）（原入库单价：￥200/g   修改后入库单价：￥3/g）入库单',
  },
];

let ostock = {
  id: 0,
  clinic_id: 0,
  ostock_code: '12364233215656',
  ostock_type: 'CLI',
  prod_type: 'string',
  order_code: '44864233215656',
  supplier_id: '5817',
  total_price: 'string',
  operator_name: '张三',
  audit_operator_name: 'string',
  audit_time: '2013:05:06',
  cost_audit_status: 'string',
  status: 0,
  source: 0,
  source_text: 'string',
  prod_ids: 'string',
  create_time: 'string',
  remark: '我是备注',
  items: [
    {
      prod_name: '枸杞子',
      purchase_total_price: '80',
      source_text: '系统内建',
      source: '',
      prod_id: '12',
      batch_code: '1414',
      expire_time: '2023:08:06',
      invoice_code: '42342424',
      produce_time: '023:08:06',
      is_split_unit: '',
      prod_stock_id: '4532',
      warehouse_num: '13',
      purchase_price: '16',
      warehouse_unit: 'g',
      split_prod_unit: 'Kg',
      purchase_stock_num: '10',
      now_cost_price: 25,
      now_total_price: '2',
    },
  ],
  is_covid: 'string',
  supplier_name: '平台创建的自用供应商',
  images: [
    'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0302/160401_68989.png-B.w300',
    'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0302/160401_68989.png-B.w300',
    'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0302/160401_68989.png-B.w300',
    'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0302/160401_68989.png-B.w300',
    'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0302/160401_68989.png-B.w300',
    'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0302/160401_68989.png-B.w300',
    'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0302/160401_68989.png-B.w300',
    'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0302/160401_68989.png-B.w300',
    'https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/0302/160401_68989.png-B.w300',
  ],
};

export { costList, detailList, resetInstockList, operateList, ostock };
