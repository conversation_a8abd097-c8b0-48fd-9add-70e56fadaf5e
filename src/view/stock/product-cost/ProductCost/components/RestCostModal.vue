<template>
  <Modal
    :value="value"
    :mask-closable="false"
    :closable="false"
    class-name="vertical-center-modal"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="flex flex-item-v-start reset-data-show">
        <Icon type="ios-alert" size="25" color="#f90" class="tip-icon" />
        <div>
          <div class="cost-data">
            <div class="tag-label"><span class="tag">原数据</span></div>
            <span
              >采购单价 ：<span class="red">￥{{ item.purchase_price }}/{{ item.split_prod_unit }}</span></span
            >
          </div>
          <div class="cost-data mt10">
            <div class="tag-label"><span class="tag">重置后待审核</span></div>
            <span>采购单价 ：￥{{ item.now_cost_price }}/{{ item.split_prod_unit }}</span>
          </div>
        </div>
      </div>

      <div class="red mt30">采购金额和单价只允许重置一次，请确认无误后提交</div>
    </div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">提交审核</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'RestCostModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    list: {
      type: Array,
      default: () => [],
    },
    params: {
      type: Object,
      default: () => {},
    },
  },

  data() {
    return {
      confirmLoading: false,
    };
  },

  computed: {
    item() {
      return this.list[0] || {};
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.$emit('input', false);
      this.confirmLoading = false;
    },

    confirm() {
      this.confirmLoading = true;
      this.$api
        .updatePCupdatePurchasePrice(this.params)
        .then(res => {
          this.$emit('success');
          this.cancel();
        })
        .finally(() => (this.confirmLoading = false));
    },

    changeVisible(visible) {
      if (!visible) {
        this.cancel();
      }
    },
  },
};
</script>

<style scoped lang="less">
.content {
  .reset-data-show {
    .tip-icon {
      margin-right: 20px;
    }
    .cost-data {
      line-height: 22px;
      display: flex;

      .tag-label {
        width: 100px;
        text-align: right;
        .tag {
          border: 1px solid #ccc;
          padding: 2px 6px;
          border-radius: 4px;
          margin-right: 8px;
          color: #ccc;
        }
      }
    }
  }
}

::v-deep .ivu-modal-header {
  display: none;
}

::v-deep .ivu-modal-body {
  padding: 50px 60px;
  max-height: 500px;
  min-height: 150px;
  overflow-y: auto;
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

.red {
  color: red;
}
.mt10 {
  margin-top: 10px;
}
.mt30 {
  margin-top: 30px;
  font-size: 14px;
}
</style>
