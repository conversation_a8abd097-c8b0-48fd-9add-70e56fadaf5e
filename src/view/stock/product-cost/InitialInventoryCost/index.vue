<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      size="small"
      stripe
      ref="pl-table"
      :total="total"
      :page-size="queryFormData.page_size"
      :current="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="list-tips-head">
          <span class="tips">
            <Icon
              type="md-alert"
              color="#FF1C1C"
              size="16"
              style="margin-right: 4px; vertical-align: baseline"
            />存在期初待确认的成本数据，请先完成所有期初成本的确认。期初成本数据截止{{ beginDate }}日
          </span>
        </div>
        <div class="form-wrap flex flex-item-between">
          <Form class="form-wrap_left" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
            <FormItem>
              <Input type="text" v-model="queryFormData.prod_name" placeholder="货品名称" />
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.prod_type" placeholder="全部类型">
                <Option value="">全部类型</Option>
                <Option :value="item.id" v-for="item in prodTypes" :key="item.id">{{ item.name }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.source" placeholder="全部来源">
                <Option value="">全部来源</Option>
                <Option :value="index" v-for="(item, index) in prodSource" :key="index">{{ item.desc }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Select v-model="queryFormData.begin_status" placeholder="全部期初状态">
                <Option value="">全部期初状态</Option>
                <Option :value="key" v-for="(item, key) in prodCostBeginStatus" :key="key">{{ item.desc }}</Option>
              </Select>
            </FormItem>
            <FormItem>
              <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
              <span class="list-reset-btn" @click="resetSearch"
                ><svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>清除条件</span
              >
            </FormItem>
          </Form>
        </div>
      </template>

      <template v-slot:begin_cost_price="{ row }">
        <div v-if="row.init_stock_take_type === '1'" style="color: #ff1c1c">无法预估</div>
        <div :style="{ color: getPriceColor(row.cost_status) }" v-else>
          <Tooltip placement="top" max-width="300">
            <div slot="content">
              <div>¥ {{ row.begin_cost_price }} / {{ row.split_prod_unit }}</div>
              <div class="mt-6" style="color: rgb(224, 226, 233); line-height: 17px">
                {{ getStatusTips(row.cost_status) }}
              </div>
            </div>

            <div class="flex flex-item-center flex-item-align">
              ¥ {{ row.begin_cost_price }}
              <Icon
                v-if="row.cost_status !== '1'"
                :type="row.cost_status === '2' ? 'ios-arrow-round-up' : 'ios-arrow-round-down'"
                :style="{
                  fontSize: '20px',
                  color: row.cost_status === '2' ? 'red' : 'green',
                  fontWeight: 'bold',
                }"
              />
            </div>
          </Tooltip>
        </div>
      </template>

      <template v-slot:stock_num="{ row }"> {{ row.stock_num || '0' }}{{ row.split_prod_unit }}</template>

      <template slot-scope="{ row }" slot="prod_name">
        <product-show
          :generic_name="row.prod_name"
          :prod_spec="row.prod_spec"
          :grade_desc="row.grade_desc"
          :manufacturer="row.manufacturer"
        ></product-show>
      </template>

      <template v-slot:split_prod_unit="{ row }">{{ row.split_prod_unit }}</template>

      <template v-slot:confirm_cost_price="{ row }">
        <Tooltip v-if="row.begin_status === '3'" placement="top">
          <div slot="content">
            <div>¥ {{ row.confirm_cost_price }} / {{ row.split_prod_unit }}</div>
          </div>

          <span>¥ {{ row.confirm_cost_price }}</span>
        </Tooltip>
        <span v-else>-</span>
      </template>

      <template v-slot:zx_pur_price="{ row }">
        <Tooltip placement="top" :disabled="!Number(row.zx_pur_price)">
          <div slot="content">
            <p class="mb-0">¥ {{ row.zx_pur_price }} / {{ row.split_prod_unit }}</p>
            <p style="margin: 8px 0">{{ row.supplier }}</p>
            <p style="color: #e0e2e9">最近更新：{{ row.zx_update_time }}</p>
          </div>
          <span v-if="!!Number(row.zx_pur_price)"> ¥ {{ row.zx_pur_price }} </span>
          <span v-else>-</span>
        </Tooltip>
      </template>

      <template v-slot:begin_status_text="{ row }">
        <status-text :background-color="getBackgroundColor(row.begin_status)"
          >{{ row.begin_status_text }}
          <Tooltip :content="row.reason" v-if="row.reason">
            <svg-icon iconClass="question" style="color: #155bd4; width: 14px; height: 14px; cursor: pointer" />
          </Tooltip>
        </status-text>
      </template>
      <template v-slot:commitor="{ row }">
        <div class="operator-box" v-if="row.commitor" style="line-height: 16px; padding: 6px 0">
          <div class="mb-4">{{ row.commitor && row.commitor.name }}</div>
          <div style="color: #999999">{{ row.commitor && row.commitor.operate_time }}</div>
        </div>
        <span v-else>-</span>
      </template>
      <template v-slot:auditor="{ row }">
        <div class="operator-box" v-if="row.auditor" style="line-height: 16px; padding: 6px 0">
          <div class="mb-4">{{ row.auditor && row.auditor.name }}</div>
          <div style="color: #999999">{{ row.auditor && row.auditor.operate_time }}</div>
        </div>
        <span v-else>-</span>
      </template>
      <template v-slot:operate="{ row }">
        <a @click="confirmStatus(row)" class="mr-10" v-if="row.begin_status === '1'">确认</a>
        <a @click="confirmStatus(row)" class="mr-10" v-if="row.begin_status === '4'">修改</a>
        <a @click="confirmStatus(row)" class="mr-10" v-if="row.begin_status === '2'">审核</a>
        <a @click="confirmStatus(row, true)">明细</a>
      </template>
    </standard-table>
    <edit-initial-cost-modal
      :id="editId"
      :prod-type="editProdType"
      :isReadOnly="isReadOnly"
      @reloadList="getsList"
      v-model="modalVisible"
    ></edit-initial-cost-modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import downloadExceL from '@/mixins/downloadExcel';
import search from '@/mixins/search';
import { data_format } from '@/libs/filters';
import StandardTable from "@/components/StandardTable/index.vue";

let init_query_form_data = {
  page: 1,
  page_size: 20,
  prod_name: '',
  prod_type: '',
  source: '',
  begin_status: '' //期初状态
};

export default {
  name: 'InitialInventoryCost',
  components: {
    StandardTable,
    EditInitialCostModal: () => import('./components/EditInitialCostModal.vue')
  },
  mixins: [downloadExceL, search],
  props: {
    beginDate: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getProdCostBeginList',
      tableCols: [
        { title: '编号', key: 'prod_id', align: 'center', minWidth: 80 },
        { title: '货品', slot: 'prod_name', align: 'left', minWidth: 220, tooltip: true },
        { title: '类型', key: 'prod_type_text', align: 'center', minWidth: 80 },
        { title: '来源', key: 'source_text', align: 'center', minWidth: 80 },
        { title: '状态', key: 'prod_status_text', align: 'center', minWidth: 60 },
        { title: '库存', slot: 'stock_num', align: 'center', minWidth: 80 },
        { title: '单位', slot: 'split_prod_unit', align: 'center', minWidth: 60 },
        { title: '采购参考价', slot: 'zx_pur_price', align: 'center', minWidth: 100 },
        { title: '系统预估期初成本价', slot: 'begin_cost_price', align: 'center', minWidth: 140 },
        { title: '修改后期初成本价', slot: 'confirm_cost_price', align: 'center', minWidth: 120 },
        { title: '期初状态', slot: 'begin_status_text', align: 'center', width: 90 },
        { title: '提交者', slot: 'commitor', align: 'center', width: 140 },
        { title: '审核者', slot: 'auditor', width: 140, align: 'center' },
        { title: '操作', slot: 'operate', width: 100, fixed: 'right' }
      ],
      prodSource: {},
      prodStatusDesc: {},
      prodTypes: [],
      prodCostBeginStatus: {},
      modalVisible: false,
      isReadOnly: false,
      editId: '',
      editProdType: ''
    };
  },
  created() {
    // axios.get('/cost/list').then(res=>{
    //   console.log(res)
    // })
    this.getOptions();
  },
  computed: {
    getBackgroundColor() {
      return status => {
        switch (status) {
          case '1':
          case '2':
            return '#FCBA6C';
          case '3':
            return '#6FD544';
          case '4':
            return '#FF8888';
          default:
            return '';
        }
      };
    },
    getPriceColor() {
      return status => {
        if (status === '2') {
          return '#FF1C1C';
        }
        if (status === '3') {
          return 'green';
        }
      };
    },
    getStatusTips() {
      return status => {
        if (status === '2') return '系统预估期初成本价 > 银联参考采购价的5倍，判定价格偏高';
        if (status === '3') return '系统预估期初成本价 < 银联参考采购价的1/5，判定价格偏低';
      };
    }
  },
  watch: {},
  methods: {
    data_format,
    resetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.submitQueryForm();
    },
    onSearch() {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },
    getOptions() {
      this.$api.getInStockProductOptions().then(data => {
        console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', data);
        this.prodTypes = S.descToArrHandle(data.prodTypes).sort((a,b)=>a.sort-b.sort);
        this.prodStatusDesc = data.prodStatusDesc;
        this.prodSource = data.prodSource;
        this.prodCostBeginStatus = data.prodCostBeginStatus;
        this.queryFormData = S.merge(this.queryFormData, this.$route.query);
        this.submitQueryForm(true);
      });
    },
    routeUpdateHandler(to, from, next) {
      this.queryFormData = S.merge(init_query_form_data, to.query);
      this.getsList();
      next();
    },
    openModal(modal, data) {
      this.modalVisible = true;
    },
    confirmStatus(row, readOnly) {
      this.editId = row.id;
      this.editProdType = row.prod_type;
      this.modalVisible = true;
      this.isReadOnly = Boolean(readOnly);
    }
  }
};
</script>

<style lang="less" scoped>
.list-tips-head {
  padding: 9px 16px;
  border-radius: 4px;
  border: 1px solid #ff1c1c;
  background: #ffe8e8;
  display: inline-flex;
  margin-bottom: 20px;

  .tips {
    display: inline-flex;
    align-items: center;
    color: #ff1c1c;
    line-height: 16px;
  }
}
</style>
