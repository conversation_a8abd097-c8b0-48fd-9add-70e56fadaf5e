<template>
  <Modal
    :value="modalVisible"
    class-name="vertical-center-modal"
    :mask-closable="false"
    title="审核期初成本单价"
    @on-visible-change="changeVisible"
  >
    <div class="mr-28">
      <KWidget label="" :label-width="20" style="text-align: right">
        <RadioGroup v-model="auditFormData.audit_status">
          <Radio label="3">
            <span>审核通过</span>
          </Radio>
          <Radio label="4">
            <span>审核驳回</span>
          </Radio>
        </RadioGroup>
      </KWidget>
      <KWidget label="" :label-width="20" style="margin-top: 12px">
        <Input
          type="textarea"
          style="max-width: 100%"
          :autosize="{ minRows: 6, maxRows: 6 }"
          clearable
          placeholder="审核驳回时，驳回理由必填"
          v-model="auditFormData.reason"
          ref="reasonIpt"
        />
      </KWidget>
    </div>
    <div slot="footer">
      <Button @click="changeVisible(false)" @keyup.enter.native.stop>取消</Button>
      <Button type="primary" @click="auditBeginPrice" :loading="auditLoading">确定</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'AuditBeginPrice',
  mixins: [],

  components: {},
  model: {
    prop: 'modalVisible',
    event: 'changeVisible'
  },
  props: {
    modalVisible: {
      type: Boolean,
      default: false
    },
    id: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      beginPrice: null,
      auditFormData: {
        audit_status: '3',
        reason: ''
      },
      auditLoading: false
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (!visible) {
        this.$emit('changeVisible', false);
        this.auditFormData = {
          audit_status: '3',
          reason: ''
        };
      } else {
        this.$nextTick(() => {
          this.$refs.reasonIpt.focus();
        });
      }
    },
    auditBeginPrice() {
      if (!this.auditFormData.audit_status) {
        this.$Message.error('请选择审核状态');
        return;
      }
      if (this.auditFormData.audit_status === '4' && !this.auditFormData.reason) {
        this.$Message.error('请填写驳回理由');
        return;
      }
      const params = {
        id: this.id,
        ...this.auditFormData
      };
      this.auditLoading = true;
      this.$api
        .updateProdCostBeginStatus(params)
        .then(
          res => {
            this.$Message.success('审核成功');
            if (res.is_finished_confirm === '1') {
              this.$emit('finishedConfirm');
            } else {
              // this.$emit('reloadDetail', true);
              this.changeVisible(false);
              this.$emit('closeModal');
            }
          },
          err => {
            {};
          }
        )
        .finally(() => {
          this.auditLoading = false;
        });
    }
  }
};
</script>

<style scoped lang="less"></style>
