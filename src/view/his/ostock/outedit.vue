<template>
  <div class="ostockEdit">
    <Form
      ref="myForm"
      :model="formValidate"
      :rules="ruleValidate"
      :label-width="120"
    >
      <Row>
        <Col span="8">
          <FormItem label="出库员" prop="user">
            <Input
              type="text"
              v-model="formValidate.user"
              placeholder=""
              :disabled="isDisatbed"
            />
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="出库类型" prop="supplier">
            <Select
              v-model="formValidate.supplier"
              placeholder="出库类型"
              :disabled="isDisatbed"
            >
              <Option
                v-for="(item, index) in supplierList"
                :value="index"
                :key="item.kw"
              >{{ item.desc }}
              </Option
              >
            </Select>
          </FormItem>
        </Col>
        <Col span="8" v-if="formValidate.supplier === '11'" prop="receiveName">
          <FormItem label="领用人" prop="receiveName">
            <Input
              type="text"
              v-model="formValidate.receiveName"
              placeholder=""
              :disabled="isDisatbed"
            />
          </FormItem>
        </Col>
      </Row>
      <Row>
        <Col span="24">
          <FormItem label="备注" prop="remark">
            <Input
              v-model="formValidate.remark"
              type="textarea"
              :autosize="true"
              placeholder=""
              :disabled="isDisatbed"
            />
          </FormItem>
        </Col>
      </Row>
      <div class="block-header">
        出库明细
        <span v-if="prodTypeText">（{{ prodTypeText }}）</span>
      </div>

      <Table :columns="tableCols" :data="list" size="small" border stripe>
        <template slot-scope="{ row, index }" slot="number">
          {{ index + 1 }}
        </template>
        <template slot-scope="{ row }" slot="goodscode">
          {{ row.number }}
        </template>
        <template slot-scope="{ row }" slot="goodsname">
          {{ row.goodsname }}
        </template>
        <template slot-scope="{ row }" slot="manufactor">
          {{ row.manufactor }}
        </template>
        <template slot-scope="{ row }" slot="retailPrice">
          {{ row.retailPrice }}
        </template>
        <template slot-scope="{ row }" slot="splitPrice">
          {{ row.splitPrice }}
        </template>
        <template slot-scope="{ row }" slot="purchasePrice">
          {{ row.purchasePriceText }}
        </template>
        <template slot-scope="{ row }" slot="expireTime">
          {{ row.expireTime | data_format("YYYY-MM-DD") }}
        </template>
        <template slot-scope="{ row }" slot="stockNum">
          {{ row.stockNum }}
        </template>
        <template slot-scope="{ row }" slot="supplierName">
          {{ row.supplierName }}
        </template>
        <template slot-scope="{ row, index }" slot="company">
          <Select
            @on-change="(e) => changeCompanyHandle(e, index)"
            :value="row.prod_unit"
            :disabled="isDisatbed"
          >
            <Option
              v-for="(compItem, compIndex) in row.sales_units"
              :value="compItem.unit"
              :key="compIndex"
            >{{ compItem.unit }}
            </Option
            >
          </Select>
        </template>
        <template slot-scope="{ row, index }" slot="quantity">
          <!-- <Input
            :value="row.quantity || ''"
            placeholder=""
            style=""
            type="number"
            @on-change="(e) => changeQuantityHandle(e, index)"
            :disabled="isDisatbed"
          /> -->
          <InputNumber :value="row.quantity || 0" :min="0"
                       @on-change="(e) => changeQuantityHandle(e, index)" :disabled="isDisatbed"/>
        </template>
        <template slot-scope="{ row, index }" slot="costPrice">
          <Input
            :value="row.costPrice || ''"
            type="number"
            placeholder=""
            style=""
            @on-change="(e) => changeCostPriceHandle(e, index)"
            :disabled="isDisatbed"
          />
        </template>
        <template slot-scope="{ row, index }" slot="batchList">
          <Select
            @on-change="(e) => changebatchHandle(e, index)"
            :value="row.curBatchId"
            :disabled="isDisatbed"
          >
            <Option
              v-for="(compItem, compIndex) in row.batchList"
              :value="compItem.id"
              :key="compItem.id"
            >{{ compItem.batch_code }}
            </Option
            >
          </Select>
        </template>
        <template slot-scope="{ row, index }" slot="operation">
          <a @click="delListItemHandle(row, index)" :disabled="isDisatbed"
          >删除</a
          >
        </template>
      </Table>
      <AutoComplete
        v-model="selectAddTable"
        style="width: 200px"
        class="tb10"
        @on-select="selectAddTableChangeHandle"
        filterable
        @on-search="goodsSearchHandle"
        :key="inputKey"
        transfer transfer-class-name="search-sel"
        :disabled="isDisatbed"
      >
        <Option
          v-for="(item, i) in goodsList"
          :value="item.id"
          :key="item.id"
        >{{ item.generic_name }}
        </Option
        >
      </AutoComplete>
      <div>
        出库总额（采购）: {{ getMaxAmount() }}元 出库总额（零售）:
        {{ getMaxRetail() }}元
      </div>
    </Form>
    <div class="fixed-bottom-wrapper" v-if="!isDisatbed">
      <Poptip
        confirm
        title="是否要清空当前页面全部数据?"
        @on-ok="clearAll"
        v-if="!$route.query.id"
      >
        <Button>清空</Button>
      </Poptip>
      <Button @click="backHandle" style="margin: 0 12px;">取消</Button>
      <Button
        type="primary"
        @click="onSave"
        class="submitBtn"
        :loading="subBtnLoading"
      >提交
      </Button
      >
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import S from "@/libs/util"; // Some commonly used tools
import io from "@/libs/io"; // Http request
import * as runtime from "@/libs/runtime"; // Runtime information
/* eslint-disable */
import moment from "moment";
import util from "@/libs/util";
import draft_mixin from "@/view/his/ostock/mixin/draft_mixin";
import debounce from 'lodash.debounce'
let init_query_form_data = {
  keyword: "",
  status: "",
};

export default {
  name: "edit",
  mixins: [draft_mixin],
  data() {
    return {
      queryFormData: {...init_query_form_data},
      tableCols: [
        {title: "序号", slot: "number", width: 40},
        {title: "商品编码", slot: "goodscode", minWidth: 50},
        {title: "商品名称", slot: "goodsname", minWidth: 50},
        {title: "生产厂家", slot: "manufactor", minWidth: 50},
        {title: "零售价", slot: "retailPrice", minWidth: 50},
        {title: "拆零价", slot: "splitPrice", minWidth: 50},
        {title: "采购价", slot: "purchasePrice", minWidth: 50},
        {title: "有效期	", slot: "expireTime", minWidth: 50},
        {title: "库存数量", slot: "stockNum", minWidth: 50},
        {title: "单位", slot: "company", minWidth: 60},
        {title: "出库数量	", slot: "quantity", minWidth: 50},
        {title: "批号", slot: "batchList", minWidth: 50},
        {title: "供应商", slot: "supplierName", minWidth: 50},
        {title: "操作", slot: "operation", minWidth: 30},
      ],
      list: [],
      supplierList: [],
      formValidate: {
        supplier: "",
        user: "",
        orderCode: "",
        selectAddTable: "",
        remark: "",
        receiveName: "", // 领用人
      },
      ruleValidate: {
        supplier: [
          {required: true, message: "请选择出库类型", trigger: "change"},
        ],
        user: [
          {required: false, message: "请输入验收/入库员", trigger: "blur"},
        ],
        orderCode: [
          {required: false, message: "请输入单据号", trigger: "change"},
        ],
        selectAddTable: [{required: false, message: "", trigger: "blur"}],
        receiveName: [{required: false, message: "", trigger: "blur"}],
        remark: [{required: false, message: "", trigger: "blur"}],
      },
      selectAddTable: "",
      show: true,
      userInfo: {},
      goodsList: [],
      maxAmount: 0,
      id: null,
      ostock: {}, // 表单数据
      loading1: false,
      prod_type: "", // 类型，中药西药...
      prodTypeText: "",
      copyGoodsList: [],
      type: "add",
      copyList: [],
      subBtnLoading: false,
      inputKey: 0,
      canSearch: true,
      isDisatbed: false,
      ostock_type: "",
      suppliers: [],
      stockDetails: {},
    };
  },

  created() {
    this.id = this.$route.query.id;
    this.prod_type = this.$route.query.prod_type;
    this.prodTypeText = this.$route.query.prodTypeText;
    this.type = this.$route.query.type;
    this.ostock_type = this.$router.ostock_type;
    this.userInfo = runtime.getUser();
    this.formValidate.user = runtime.getUser().name;
    let nowDate = new Date();
    if (this.id) {
      this.getDetail();
    }
    this.querySupplierList();
    this.queryGoodsList();
  },
  watch: {
    formValidate: {
      handler(val) {
        this.saveDraft()
      }, deep: true
    },
    list: {
      handler(val) {
        this.saveDraft()
      }, deep: true
    },
  },
  methods: {
    //清空全部
    clearAll() {
      console.info('clearall')
      this.formValidate = {
        supplier: "",
        user: "",
        orderCode: "",
        selectAddTable: "",
        remark: "",
        receiveName: "", // 领用人
      }
      this.list = []
    },
    getDraft(ostock_type) {
      const params = {
        prod_type: this.$route.query.prod_type,
        ostock_type
      }
      this.$api.getStockDraft(params).then(res => {
        console.log("-> res", res)
        if (res.has_display === '1') {
          res.data.items.map(item => {
            item.purchasePrice = Number(item.purchasePrice)
            item.subtotal = Number(item.subtotal)
            item.quantity = Number(item.quantity)
          })
          this.list = res.data.items
          this.formValidate.user = res.data.operator_name
          this.formValidate.remark = res.data.remark
          this.formValidate.supplier = res.data.out_type
          this.formValidate.receiveName = res.data.receive_name
        }
      })
    },
    handleDraftParams() {
      let values = this.formValidate;
      console.log("-> values", values)
      let params = {
        ostock_type: 20,
        prod_type: this.prod_type,
        operator_name: values.user,
        remark: values.remark,
        out_type: values.supplier,
        items: this.list,
        receive_name: values.receiveName
      }
      return params
    },
    // 编辑获取详情
    getDetail() {
      let params = {
        id: this.id,
      };
      io.get("/clinic/ostock.outinfo", {data: params})
        .then((data) => {
          console.log("🚀 ~ file: outedit.vue ~ line 293 ~ .then ~ data", data);
          this.ostock = data.ostock;
          if (data.ostock.status === "20" || data.ostock.status === "80") {
            this.isDisatbed = true;
          }
          this.stockDetails = data.stockDetails;
          this.formValidate.supplier = data.ostock.out_type;
          this.formValidate.user = data.ostock.operator_name;
          this.formValidate.remark = data.ostock.remark;
          this.formValidate.receiveName = data.ostock.receive_name || "";
          let itemList = data.ostock.items;
          let list = [];
          let products = data.products;
          let stockDetails = data.stockDetails;
          itemList.length &&
          itemList.length > 0 &&
          itemList.map((item, i) => {
            list.push({
              number: item.prod_id, // 商品id
              goodscode: products[item.prod_id].phonetic_code, // 商品编码
              goodsname: products[item.prod_id].generic_name, // 商品名称
              manufactor: products[item.prod_id].manufacturer, // 生产厂家
              retailPrice: products[item.prod_id].retail_price, // 零售价
              splitPrice: products[item.prod_id].split_price, // 拆零价
              purchasePrice: item.settlement_price.price, // 采购价
              purchasePriceText: item.settlement_price.purchase_price_text, // 采购价-用于展示
              expireTime:
                data.stockDetails &&
                data.stockDetails[item.prod_id] &&
                data.stockDetails[item.prod_id][item.stock_detail_id] &&
                data.stockDetails[item.prod_id][item.stock_detail_id]
                  .expire_time, // 有效期
              stockNum:
                data.stockDetails &&
                data.stockDetails[item.prod_id] &&
                data.stockDetails[item.prod_id][item.stock_detail_id]
                  ? data.stockDetails[item.prod_id][item.stock_detail_id]
                    .stock_text
                  : 0, // 库存数量
              company:
                data.stockDetails &&
                data.stockDetails[item.prod_id] &&
                data.stockDetails[item.prod_id][item.stock_detail_id]
                  ? data.stockDetails[item.prod_id][item.stock_detail_id]
                    .prod_unit
                  : "", // 单位
              quantity: item.purchase_stock_num, //  数量
              curBatchNum:
                data.stockDetails &&
                data.stockDetails[item.prod_id] &&
                data.stockDetails[item.prod_id][item.stock_detail_id]
                  ? data.stockDetails[item.prod_id][item.stock_detail_id]
                    .batch_code
                  : "", // 批号
              curBatchId:
                data.stockDetails &&
                data.stockDetails[item.prod_id] &&
                data.stockDetails[item.prod_id][item.stock_detail_id]
                  ? data.stockDetails[item.prod_id][item.stock_detail_id].id
                  : "", // 默认选中批号ID
              supplierName:
                data.stockDetails &&
                data.stockDetails[item.prod_id] &&
                data.stockDetails[item.prod_id][item.stock_detail_id]
                  ? data.suppliers[
                    data.stockDetails[item.prod_id][item.stock_detail_id]
                      .supplier_id
                    ].name
                  : "", // 供应商
              batchList:
                data.stockDetails &&
                data.stockDetails[item.prod_id] &&
                data.stockDetails[item.prod_id][item.stock_detail_id]
                  ? data.stockDetails[item.prod_id]
                  : "", // 批号列表
              prod_stock_id: item.prod_stock_id, // 商品库存id
              stockDetailId: item.stock_detail_id, // 明细id
              subtotalDisabled: true, // 小计禁用
              sales_units:
                data.stockDetails &&
                data.stockDetails[item.prod_id] &&
                data.stockDetails[item.prod_id][item.stock_detail_id]
                  ? data.stockDetails[item.prod_id][item.stock_detail_id]
                    .settlement_price
                  : {}, // 单位列表
              prod_unit: item.warehouse_unit, // 选中单位
              operation: "操作",
            });
          });
          this.list = list;
        })
        .catch((error) => {
          console.log(error);
          // {}
        });
    },
    // 修改select
    selectAddTableChangeHandle(value) {
      console.log("-> value", value)
      if (!value) {
        return;
      }
      this.canSearch = false;
      this.$nextTick(() => {
        this.goodsSearchHandle('')
        this.selectAddTable = "";
        this.inputKey = this.inputKey + 1;
        this.canSearch = true;
      });

      let currentItem = {};
      this.goodsList.map((item, i) => {
        if (item.id == value) {
          currentItem = item;
        }
      });

      let params = {
        prod_id: value,
      };
      io.get("/clinic/product.stock.num", {data: params})
        .then((data) => {
          this.selectAddTable = ''
          console.log("-> data", data)
          this.suppliers = data.suppliers;
          let currentSett = {};
          if (!data.list || !data.list.length) {
            this.$Message.error('当前商品没有入库记录，无法操作出库')
            return
          }
          data.list[0].settlement_price.map((citem, ci) => {
            if (citem.unit === data.list[0].warehouse_unit) {
              currentSett = citem;
            }
          });
          let obj = {
            number: currentItem.id, // 商品id
            goodscode: currentItem.phonetic_code, // 商品编码
            goodsname: currentItem.generic_name, // 商品名称
            manufactor: data.list[0].manufacturer, // 生产厂家
            retailPrice: currentItem.retail_price, // 零售价
            splitPrice: currentItem.split_price, // 拆零价
            purchasePrice: currentSett.price,
            purchasePriceText: currentSett.purchase_price_text,
            expireTime: data.list[0].expire_time, // 有效期
            stockNum: data.list[0].stock_text, // 库存数量
            supplierName: data.suppliers[data.list[0].supplier_id].name, // 供应商
            supplier_id: data.list[0].supplier_id, // 供应商
            stockDetailId: data.list[0].id, // 批次id
            productPrice: currentItem.retail_price, // 商品价格，用于计算零售总价
            prod_unit: data.list[0].warehouse_unit, // 单位
            quantity: 1, //  出库数量
            batchList: data.list, // 批号列表
            curBatchNum: data.list[0].batch_code, // 默认选中批号
            curBatchId: data.list[0].id, // 默认选中批号ID
            prod_stock_id: currentItem.prod_stock_id, // 商品库存id
            subtotalDisabled: true, // 小计禁用
            sales_units: data.list[0].settlement_price, // 单位列表
            operation: "操作",
          };
          this.list = [...this.list, obj];

        })
        .catch((error) => {
          console.log("-> error", error)
          {};
        });
    },
    // 修改数量
    changeQuantityHandle(e, index) {
      this.list[index].quantity = e
    },
    // 修改零售价
    changeCostPriceHandle(e, index) {
      this.list[index].costPrice = e.target.value;
    },
    // 修改单位
    changeCompanyHandle(e, index) {
      this.list[index].prod_unit = e;
      this.list[index].warehouse_unit = e;
      let curItem = {};
      for (let k in this.list[index].sales_units) {
        if (this.list[index].sales_units[k].unit === e) {
          curItem = this.list[index].sales_units[k];
        }
      }
      this.list[index].sales_units.map((item, i) => {
        item.isChecked = false;
        if (item.purchase_price_text === curItem.purchase_price_text) {
          item.isChecked = true;
        }
      });

      let curBatchitem = {};
      for (let k in this.list[index].batchList) {
        let item = this.list[index].batchList[k];
        if (item.id === this.list[index].curBatchId) {
          curBatchitem = item;
        }
      }
      curBatchitem.settlement_price.map((item, i) => {
        if (item.unit === e) {
          this.list[index].purchasePrice = item.price;
          this.list[index].purchasePriceText = item.purchase_price_text;
        }
      });
    },
    // 修改批号
    changebatchHandle(e, index) {
      let curItem = {};
      for (let k in this.list[index].batchList) {
        if (e == this.list[index].batchList[k].id) {
          curItem = this.list[index].batchList[k];
        }
      }
      let currentSettItem = {};
      this.$nextTick(() => {
        for (let k in this.list[index].batchList) {
          let item = this.list[index].batchList[k];
          if (e === item.id) {
            this.list[index].curBatchNum = item.batch_code;
            this.list[index].curBatchId = item.id;
            this.list[index].stockDetailId = e;
            item.settlement_price.map((settItem, settI) => {
              if (settItem.unit === this.list[index].prod_unit) {
                this.list[index].purchasePrice = settItem.price;
                this.list[index].purchasePriceText =
                  settItem.purchase_price_text;
              }
            });
          }
        }
      });

      this.list[index].purchasePrice = curItem.purchase_price;
      this.list[index].expireTime = curItem.expire_time;
      this.list[index].stockNum = curItem.stock_text;
      this.list[index].curBatchId = e;
      this.list[index].supplierName =
        (this.suppliers[curItem.supplier_id] &&
          this.suppliers[curItem.supplier_id].name) ||
        "";
    },
    // 删除
    delListItemHandle(row, index) {
      this.list.splice(index, 1);
    },
    // 保存
    onSave() {
      if (!this.list.length) {
        this.$Message.error('请至少选择一种商品')
        return
      }
      this.$refs.myForm.validate((valid) => {
        if (valid) {
          this.subBtnLoading = true;
          let values = this.formValidate;

          let params = {
            ostock_type: 20,
            prod_type: this.prod_type,
            operator_name: values.user,
            remark: values.remark,
            out_type: values.supplier,
          };
          if (this.formValidate.supplier === "11") {
            params.receive_name = values.receiveName || "";
          }


          let items = [];
          this.list.map((item, i) => {
            items.push({
              prod_id: item.number,
              prod_stock_id: item.prod_stock_id,
              purchase_stock_num: item.quantity,
              manufacturer: item.manufactor,
              warehouse_unit: item.warehouse_unit || item.prod_unit,
              purchase_price: item.purchasePrice,
              stock_detail_id: item.stockDetailId,
              batch_code: item.curBatchNum,
            });
          });
          params.items = items;
          if (this.id) {
            params.id = this.id;
          }
          io.post("/clinic/ostock.out", params)
            .then((data) => {
              this.$Message.success({
                content: "提交成功",
              });
              this.$router.replace("/his/ostock/list?ostock_type=20");
            }, error => {
              {};
            })
            .finally(() => {
              this.subBtnLoading = false;
            })
        } else {
        }
      });
    },
    // 取消
    backHandle() {
      this.$router.back();
    },
    // 获取出库类型
    querySupplierList(query) {
      io.get("/clinic/ostock.options", {data: {}})
        .then((data) => {
          this.supplierList = data.stockTypeOutDesc;
          if (!this.id) {
            this.getDraft(20)
          }
        })
        .catch((error) => {
          {};
        });
    },
    // 获取商品列表
    queryGoodsList() {
      let params = {
        page: 1,
        pageSize: 20,
        name: "",
        status: "",
        has_stock: true,
        prod_type: this.prod_type,
      };
      io.get("/clinic/product.product.list", {data: params})
        .then((data) => {
          this.goodsList = data.list;
          this.copyGoodsList = data.list;
        })
        .catch((error) => {
          {};
        });
    },
    goodsSearchHandle: debounce(function (query) {
      console.log("-> query", query)
      // if (!this.canSearch) {
      //   return;
      // }
      let params = {
        page: 1,
        pageSize: 20,
        name: query,
        prod_type: this.prod_type,
      };
      this.loading1 = true;
      io.get("/clinic/product.product.list", {data: params})
        .then((data) => {
          this.loading1 = false;
          this.goodsList = data.list;
        })
        .catch((error) => {
          {};
        });
    },600),
    // 计算采购总价
    getMaxAmount() {
      let maxAmount = 0;
      this.list.length &&
      this.list.length > 0 &&
      this.list.map((item, i) => {
        maxAmount = maxAmount + item.purchasePrice * item.quantity;
      });
      return S.number_format(maxAmount, 4);
    },
    // 计算零售总价
    getMaxRetail() {
      let maxAmount = 0;
      this.list.length &&
      this.list.length > 0 &&
      this.list.map((item, i) => {
        for (let k in item.batchList) {
          if (item.curBatchId === item.batchList[k].id) {
            item.batchList[k].settlement_price.map((settItem, settI) => {
              if (settItem.unit === item.prod_unit) {
                maxAmount = maxAmount + settItem.sales_price * item.quantity;
              }
            });
          }
        }
      });
      return S.number_format(maxAmount, 4);
    },
    queryGoodsItemInfo(item) {
      let params = {
        prod_id: 1,
      };
      io.get("/clinic/product.stock.num", {data: params})
        .then((data) => {
        })
        .catch((error) => {
          {};
        });
    },
    // 返回默认选中项
    getChecked(arr) {
      let obj = {};
      arr.map((item, i) => {
        if (item.isChecked) {
          obj = item;
        }
      });
      return obj;
    },
  },
};
</script>

<style lang="less">
.ostockEdit {
  min-height: 100vh;

  .ivu-date-picker {
    width: 100%;
  }

  .ivu-table-body {
    // min-height: 120px;
  }

  .ivu-table-wrapper {
    overflow: inherit;
  }

  .submitBtn {
    margin-right: 20px;
  }
}
</style>
<style lang="less">
.search-sel {
  max-height: 300px !important;
}
</style>
