<template>
  <Modal
    v-model="showModal"
    title="退药详情"
    width="1000"
    >
    <div slot="footer">
      <Button @click="cancleHandle">取消</Button>
      <Button type="primary" @click="okHandle" :loading="cancleBtnLoading">确认退药</Button>
    </div>
    <div>
      <Table :columns="columns" :data="refundList">
        <template slot-scope="{row}" slot="name">
          {{row.name}}
        </template>
        <template slot-scope="{row}" slot="manufacturer">
          {{row.manufacturer}}
        </template>
        <template slot-scope="{row}" slot="quantity">
          {{row.quantity}}
        </template>
        <template slot-scope="{row}" slot="unit_name">
          {{row.unit_name}}
        </template>
        <template slot-scope="{row}" slot="expire_time">
          {{row.expire_time}}
        </template>
        <template slot-scope="{row}" slot="batch_code">
          {{row.batch_code}}
        </template>
        <template slot-scope="{row}" slot="dispense_num">
          {{row.dispense_num}}
        </template>
      </Table>
    </div>
  </Modal>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util' // Some commonly used tools
import io from "@/libs/io" // Http request
import * as runtime from '@/libs/runtime' // Runtime information
/* eslint-disable */

  export default {
    props:["refundList","cancleBtnLoading"],
      data () {
        return {
          columns: [
            {title: '药品名称', slot: 'name', width: 100},
            {title: '生产厂家', slot: 'manufacturer', minWidth: 50},
            {title: '数量', slot: 'quantity', minWidth: 30},
            {title: '单位', slot: 'unit_name', minWidth: 30},
            // {title: '库存', slot: 'create_time', minWidth: 50},
            {title: '有效期', slot: 'expire_time', minWidth: 70},
            {title: '选择批号', slot: 'batch_code', minWidth: 50},
            {title: '发药数量', slot: 'dispense_num', minWidth: 50},
          ],
          showModal: false,          
          btnLoading: false,
        }
      },
      methods: {
        okHandle() {
          this.$emit('confirmRefund');
        },
        cancleHandle() {
          this.showModal = false;
        }
      }
  }
</script>
