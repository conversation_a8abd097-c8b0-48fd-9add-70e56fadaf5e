<template>
  <div>
    <Form ref="prodForm" :label-width="100" :model="formData" :rules="ruleValidate">
      <div class="block-header">商品基本信息</div>
      <div>
        <Row>
          <Col span="8">
            <FormItem label="通用名" prop="generic_name">
              <Input v-model="formData.generic_name" placeholder="通用名" @on-blur="onMakePhoneticCode"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="商品名" prop="prod_name">
              <Input v-model="formData.prod_name" placeholder="商品名"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="英文名" prop="english_name">
              <Input v-model="formData.english_name" placeholder="英文名"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="生产厂家" prop="manufacturer">
              <Input v-model="formData.manufacturer" placeholder="生产厂家"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="药品条形码" prop="bar_code">
              <Input v-model="formData.bar_code" placeholder="药品条形码"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="批准文号" prop="approval_number">
              <Input v-model="formData.approval_number" placeholder="批准文号"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="商品类型" prop="approval_number">
              <Select v-model="formData.prod_type" filterable placeholder="商品类型" @on-select="onChangeProdType">
                <Option v-for="(item, key) in prodTypes" :key="key" :value="item.id">{{ item.name }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="助记码" prop="phonetic_code">
              <Input v-model="formData.phonetic_code" placeholder="助记码"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="药品使用年龄" prop="usage_age">
              <RadioGroup v-model="formData.usage_age">
                <Radio v-for="(desc, key) in usageAgeDesc" :key="key" :label="key">{{ desc }}</Radio>
              </RadioGroup>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="效期预警时间" prop="valid_warning">
              <!-- <Input v-model="formData.valid_warning" type="number" placeholder="效期预警时间">
                <span slot="append">天数</span>
              </Input> -->
              <div class="flex" style="width:100%">
                <InputNumber style="flex:1" placeholder="效期预警时间" :min="0" v-model="formData.valid_warning">
                </InputNumber>
                <span class="number-box">天数</span>
              </div>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="预警数量" prop="stock_warning">
              <!-- <Input v-model="formData.stock_warning" placeholder="预警数量">
                <span slot="append">{{ formData.split_prod_unit || formData.prod_unit || '单位' }}</span>
              </Input> -->
              <div class="flex" style="width:100%">
                <InputNumber style="flex:1" placeholder="预警数量" :min="0" v-model="formData.stock_warning">
                </InputNumber>
                <span class="number-box">{{ formData.split_prod_unit || formData.prod_unit || '单位' }}</span>
              </div>
            </FormItem>
          </Col>
          <Col span="8"></Col>
          <Col v-if="col_class_show" span="8">
            <FormItem label="药品分类" prop="stock_warning">
              <Select v-model="formData.one_level_classid" filterable placeholder="药品一级分类"
                      @on-select="onSelectOneLevelClass">
                <Option v-for="(item, key) in prod_one_level_class" :key="key" :value="item.id">{{ item.name }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col v-if="col_class_show" span="8">
            <FormItem label="" prop="stock_warning">
              <Select v-model="formData.two_level_classid" filterable placeholder="药品二级分类">
                <Option v-for="(item, key) in prod_two_level_class" :key="key" :value="item.id">{{ item.name }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col v-if="col_class_show" span="8"></Col>
          <Col v-if="col_allergen_show" span="8">
            <FormItem label="过敏禁忌" prop="allergen_taboo">
              <RadioGroup v-model="is_allergen">
                <Radio :label="1">有</Radio>
                <Radio :label="0">无</Radio>
              </RadioGroup>
            </FormItem>
          </Col>
          <Col v-if="is_allergen && col_allergen_show" span="8">
            <FormItem label="" prop="allergen_taboo">
              <Select v-model="formData.allergen_taboo" filterable multiple placeholder="过敏源">
                <Option v-for="(item, key) in prodAllergen" :key="key" :value="item">{{ item }}</Option>
              </Select>
            </FormItem>
          </Col>
        </Row>
      </div>

      <div class="block-header">商品规格信息</div>
      <div>
        <Row>
          <Col span="8">
            <FormItem label="规格" prop="prod_spec">
              <Input v-model="formData.prod_spec" placeholder="商品规格信息"></Input>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="包装单位" prop="prod_unit">
              <Select v-model="formData.prod_unit" filterable placeholder="单位">
                <Option value="">单位</Option>
                <Option v-for="(desc, key) in prodUnits" :key="key" :value="desc">{{ desc }}</Option>
              </Select>
            </FormItem>
          </Col>
          <!-- <Col span="8"></Col> -->
          <Col span="8">
            <FormItem label="零售价" prop="retail_price">
              <InputNumber v-model="formData.retail_price" :active-change="false" :min="0" :precision="4"
                           placeholder="零售价" style="width: 100%"></InputNumber>
            </FormItem>
          </Col>
          <Col span="8">
            <FormItem label="是否拆零" prop="is_split">
              <RadioGroup v-model="formData.is_split" @on-change="changeSplit">
                <Radio :disabled="formEditData.isEdit" label="1">是</Radio>
                <Radio :disabled="formEditData.isEdit" label="2">否</Radio>
              </RadioGroup>
            </FormItem>
          </Col>
          <Col v-if="formData.is_split == '1'" span="8">
            <FormItem label="拆零比" prop="split_num">
              <Input v-model="formData.split_num" :disabled="formEditData.isEdit" placeholder="拆零比">
                <span slot="append">
                  <Select v-model="formData.split_prod_unit" filterable placeholder="单位" style="width: 80px">
                  <Option v-for="(desc, key) in prodUnits" :key="key" :value="desc">{{ desc }}</Option>
                </Select>
                </span>
              </Input>
            </FormItem>
          </Col>
          <Col v-if="formData.is_split == '1'" span="8">
            <FormItem label="拆零价" prop="split_price">
              <InputNumber v-model="formData.split_price" :active-change="false" :min="0" :precision="4"
                           placeholder="拆零价" style="width: 100%"></InputNumber>
            </FormItem>
          </Col>
        </Row>
      </div>

      <div class="block-header">商品用法</div>
      <Row>
        <Col span="8">
          <FormItem label="默认用法" prop="usage_type">
            <Select v-model="formData.usage_type" filterable placeholder="默认用法">
              <Option value="0">默认用法</Option>
              <Option v-for="(desc, key) in usage" :key="key" :value="key">{{ desc }}</Option>
            </Select>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="单次剂量" prop="single_dose">
            <Input v-model="formData.single_dose" placeholder="单次剂量">
              <span slot="append">
                <Select v-model="formData.single_unit" filterable placeholder="单位" style="width: 80px">
                <Option v-for="(desc, key) in prodUnits" :key="key" :value="desc">{{ desc }}</Option>
              </Select>
              </span>
            </Input>
          </FormItem>
        </Col>
        <Col span="8">
          <FormItem label="默认频次" prop="frequency">
            <Select v-model="formData.frequency" filterable placeholder="默认频次">
              <Option value="0">默认频次</Option>
              <Option v-for="(desc, key) in prodFrequency" :key="key" :value="key">{{ desc }}</Option>
            </Select>
          </FormItem>
        </Col>

        <Col span="16">
          <FormItem label="备注" prop="remark">
            <Input v-model="formData.remark" placeholder="备注" type="textarea"></Input>
          </FormItem>
        </Col>
        <Col span="16">
          <FormItem label="说明书" prop="instructions_txt">
            <Input v-model="formData.instructions_txt" placeholder="说明书" type="textarea"></Input>
          </FormItem>
        </Col>
        <Col span="16">
          <FormItem label="说明图片" prop="instructions_imgs">
            <MaterialPicture v-model="formData.instructions_imgs" :limit="9"/>
          </FormItem>
        </Col>
      </Row>

    </Form>

    <div class="fixed-bottom-wrapper">
      <Button :loading="statemanager.saveBtnLoading" type="primary" @click="onSave">保存</Button>
      <Dvd/>
      <Button @click="onReset">重置</Button>
      <Dvd/>
      <back-button></back-button>
    </div>

    <div class="block_45"></div>
  </div>
</template>

<script>
/* eslint-disable */
import io from '@/libs/io' // Http request
/* eslint-disable */



let init_edit_form_data = {
  id: 0,
  cas_token: 0,
}

export default {
  name: 'edit',
  components: {
  },

  data() {
    return {
      formData: {
        prod_name: '',
        generic_name: '',
        english_name: '',
        phonetic_code: '', // 助记码
        manufacturer: '', // 生产厂家
        approval_number: '', // 批准文号

        bar_code: '', // 药品条形码
        prod_type: '1', // 商品类型
        one_level_classid: '', //
        two_level_classid: '', //
        three_level_classid: '', //
        allergen_taboo: [], // 过敏禁忌
        usage_age: '0', // 使用年龄 0不限 1儿童 2成人
        valid_warning: '0', // 效期预警天数
        stock_warning: '0', // 库存预警数量

        prod_spec: '', // 规格
        prod_unit: '', // 包装单位
        retail_price: 0, // 零售价
        cost_price: '', // 成本价

        usage_type: '', // 使用方法
        single_dose: '', // 单次剂量
        single_unit: '袋', // 单次单位
        frequency: '', // 使用频次

        instructions_txt: '', // 说明书
        instructions_imgs: [], // 说明图片
        remark: '', // 备注

        is_split: '1',// 是否拆零
        split_num: '',//  拆零比
        split_prod_unit: '',// 拆零比unit
        split_price: 0,// 拆零比price
      },
      formEditData: {...init_edit_form_data},
      ruleValidate: {
        generic_name: [{required: true, message: '请填写通用名', trigger: 'blur'}],
        prod_type: [{required: true, message: '请选择药品类型', trigger: 'blur'}],
        phonetic_code: [{required: true, message: '请填写助记码', trigger: 'blur'}],
        prod_spec: [{required: true, message: '请填写商品规格信息', trigger: 'blur'}],
        prod_unit: [{required: true, message: '请填写包装单位', trigger: 'blur'}],
        retail_price: [{
          required: true,
          type: 'number',
          min: 0.0001,
          message: '请填写零售价',
          trigger: ['blur', 'change'],
          transform: value => {
            return Number(value)
          },
        }],
      },

      usageAgeDesc: {
        '0': '不限',
        '1': '儿童',
        '2': '成人',
      },
      is_allergen: 0,
      col_class_show: true, // 药品分类col
      col_allergen_show: true, // 过敏源col

      prod_one_level_class: [], // 商品一级分类
      prod_two_level_class: [], // 商品二级分类

      // options
      prodStatusDesc: {},
      prodTypes: {},
      prodUnits: {},
      prodUnits_bak: {},
      usage: {},
      prodUsage: {},
      prodTCMUsage: {},
      prodFrequency: {},
      prodAllergen: {},

      statemanager: {
        saveBtnLoading: false,
      },
    }
  },

  created() {
    if (this.$route.query.id) { // 编辑时才有的参数
      this.formEditData.id = this.$route.query.id
      this.formEditData.isEdit = true
    }

    this.getsOptions().then(() => {
      if (this.formEditData.id) {
        this.get()
      }
      this.handlerProdType(this.formData.prod_type)
      this.getsClass()
    })
  },

  methods: {
    get: function () {
      let id = this.formEditData.id
      io.get('clinic/product.product.info', {data: {id}}).then(data => {
        let product = data.product
        this.formData.prod_name = product.prod_name
        this.formData.generic_name = product.generic_name
        this.formData.english_name = product.english_name
        this.formData.phonetic_code = product.phonetic_code
        this.formData.manufacturer = product.manufacturer
        this.formData.approval_number = product.approval_number

        this.formData.bar_code = product.bar_code
        this.formData.prod_type = product.prod_type
        this.handlerProdType(this.formData.prod_type)
        this.formData.one_level_classid = product.one_level_classid
        this.formData.two_level_classid = product.two_level_classid
        if (this.formData.one_level_classid) this.getsClass(this.formData.one_level_classid)
        this.formData.three_level_classid = product.three_level_classid
        this.formData.allergen_taboo = product.allergen_taboo
        this.is_allergen = this.formData.allergen_taboo.length > 0 ? 1 : 0
        this.formData.usage_age = product.usage_age
        this.formData.valid_warning = product.valid_warning
        this.formData.stock_warning = product.stock_warning
        this.formData.prod_spec = product.prod_spec
        this.formData.prod_unit = product.prod_unit
        this.formData.retail_price = parseFloat(product.retail_price)
        this.formData.cost_price = product.cost_price

        this.formData.usage_type = product.usage_type
        this.formData.single_dose = product.single_dose
        this.formData.single_unit = product.single_unit
        this.formData.frequency = product.frequency

        this.formData.instructions_txt = product.instructions_txt
        this.formData.instructions_imgs = product.instructions_imgs
        this.formData.remark = product.remark

        this.formEditData.cas_token = product.cas_token

        this.formData.is_split = product.is_split
        this.formData.split_num = Number(product.split_num)
        this.formData.split_prod_unit = product.split_prod_unit
        this.formData.split_price = Number(product.split_price)


      }).catch(error => {
        {}
      })
    },
    changeSplit(val) {
      console.log(val)
      if (Number(val) !== 1) {
        this.formData.split_prod_unit = ''
      }
    },
    getsOptions: function () {
      const res = io.get('clinic/product.product.options')
      res.then(data => {
        this.prodStatusDesc = data.prodStatusDesc
        this.prodTypes = data.prodTypes
        this.prodUnits = data.prodUnits
        this.prodUnits_bak = data.prodUnits
        this.prodUsage = data.prodUsage
        this.prodTCMUsage = data.prodTCMUsage
        this.prodFrequency = data.prodFrequency
        this.prodAllergen = data.prodAllergen
      }).catch(error => {
        {}
      })
      return res
    },

    getsClass: function (parent_id = 0) {
      const res = io.get('clinic/product.prodclass.search', {data: {parent_id: parent_id}}).then(data => {
        if (parent_id == 0) {
          this.prod_one_level_class = data
        } else {
          this.prod_two_level_class = data
        }
      }).catch(error => {
        {}
      })
    },

    onSelectOneLevelClass: function (item) {
      this.getsClass(item.value)
    },

    onReset() {
      this.$refs.prodForm.resetFields()
    },

    onSave: function () {
      let isValid = true
      this.$refs.prodForm.validate(valid => isValid = valid)
      if (!isValid) {
        this.$Message.error('请完善商品信息')
        return
      }

      let formData = {...this.formData}

      if (formData.prod_unit == formData.split_prod_unit) {
        this.$Message.error('拆零单位和商品单位不能一致')
        return
      }

      if (this.formEditData.id) {
        formData = {...formData, ...this.formEditData}
      }

      this.statemanager.saveBtnLoading = true

      io.post('clinic/product.product.edit', formData).then((data) => {
        this.$Message.success('保存成功')
        this.$router.push('/his/product/list')
      }).catch(error => {
          {}
        },
      ).finally(() => {
        this.statemanager.saveBtnLoading = false
      })
    },

    onMakePhoneticCode: function () {
      io.get('clinic/index.zhuyin', {data: {q: this.formData.generic_name}}).then(data => {
        this.formData.phonetic_code = data.zy
      })
    },

    onChangeProdType: function (item) {
      this.handlerProdType(item.value)
    },

    handlerProdType: function (prod_type) {
      switch (prod_type) {
        case '1': // 西药
          this.col_class_show = true
          this.col_allergen_show = true
          this.prodUnits = this.prodUnits_bak
          this.usage = this.prodUsage
          break
        case '2': // 中药饮片
          this.col_class_show = false
          this.col_allergen_show = true
          // this.prodUnits = {7:'g'}
          // this.formData.prod_unit = 'g'
          this.usage = this.prodTCMUsage
          this.prodUnits = this.prodUnits_bak
          this.usage = this.prodUsage
          break
        case '3': // 中成药
          this.col_class_show = true
          this.col_allergen_show = false
          this.prodUnits = this.prodUnits_bak
          this.usage = this.prodUsage
          break
        default:
          this.col_class_show = false
          this.col_allergen_show = false
          this.prodUnits = this.prodUnits_bak
          this.usage = this.prodUsage
      }
    },
  },
  computed: {},
  watch: {
    'formData.split_num': {
      handler(newVal, oldVal) {
        console.log('-> newVal,oldVal', newVal, oldVal)
        const {retail_price} = this.formData
        if (!retail_price) this.formData.split_price = 0
        if(!newVal) {
          this.formData.split_num = this.formData.split_price = 0
          return
        }
        this.formData.split_price = Number(retail_price / newVal)
      }, immediate: true,
    },
    'formData.retail_price': {
      handler(newVal, oldVal) {
        console.log('-> newVal,oldVal', newVal, oldVal)
        const {split_num} = this.formData
        if (!split_num) {this.formData.split_price = 0}else{
          this.formData.split_price = Number(newVal / split_num)
        }
      }, immediate: true,
    },
  },
}
</script>

<style lang="less">
  .number-box{
    width: 39px;
    height: 32px;
    line-height: 32px;
    text-align: center;
    border: 1px solid #bbb;
    border-left: 0;
    background-color: #f8f8f9;
  }
</style>
