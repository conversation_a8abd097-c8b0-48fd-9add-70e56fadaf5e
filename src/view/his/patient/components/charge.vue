<template>
  <div class="chargePage">
    <Collapse v-model="initShow">
      <Panel :name="i + ''" v-for="(item,i) in tab4List" :key="i">

        <span v-if="item.patient.reg_time">{{ item.patient.reg_time }} /</span>
        <span v-if="item.patient.name">{{ item.patient.name }}</span>
        <span v-if="tab4Clinics.name">/ {{ tab4Clinics.name }}</span>
        <span v-if="item.cons_info.status">/ {{ item.cons_info.status }}</span>
        <Button @click.stop="print(item.order.id)" style="margin-left: 12px" type="primary">打印收费单</Button>
        <div class="panelList listBorder treatmentList" slot="content">
          <div class="titleInfo">
            <div class="titleText">
              收费明细
            </div>
            <div class="amountLabel">
              合计金额（元）：￥{{ item.order.payment_fee }}
            </div>
          </div>
          <div class="chargeItem mt14">
            <div class="chargeItemTitle">
              <span class="mdType">1.挂号费</span>
              <div class="subTitle">
                <div class="left ml8">
                  <div class="statusBtn" :style="{background: renderTagColor(item.order.status)}">
                    {{ item.order.status_text }}
                  </div>
                </div>
              </div>
            </div>
            <Table :columns="columns" :data="[item.reg_info]" class="mt14">
              <template slot-scope="{row}" slot="name">
                挂号费
              </template>
              <template slot-scope="{row}" slot="status">
                {{ row.money === '0' ? '-' : row.status }}
              </template>
              <template slot-scope="{row}" slot="usage">
                {{ row.money === '0' ? '-' : '自费' }}
              </template>
              <template slot-scope="{row}" slot="unitName">
                {{ row.money === '0' ? '-' : '次' }}
              </template>
              <template slot-scope="{row}" slot="discountFee">
                    <span v-if="row.money === '0'">
                      -
                    </span>
                <span v-else>
                      ￥{{ row.money }}
                    </span>
              </template>
              <template slot-scope="{row}" slot="quantity">
                {{ row.money === '0' ? '-' : 1 }}
              </template>
              <template slot-scope="{row}" slot="price">
                    <span v-if="row.money === '0'">
                      -
                    </span>
                <span v-else>
                      ￥{{ row.money }}
                    </span>
              </template>
              <template slot-scope="{row}" slot="paymentFee">
                    <span v-if="row.money === '0'">
                      -
                    </span>
                <span v-else>
                      ￥{{ row.money }}
                    </span>
              </template>

            </Table>
          </div>

          <div class="chargeItem mt14">
            <div class="chargeItemTitle">
              <span class="mdType">2.问诊费</span>
              <div class="subTitle">
                <div class="left ml8">
                  <div class="statusBtn" :style="{background: renderTagColor(item.order.status)}">
                    {{ item.order.status_text }}
                  </div>
                </div>
              </div>
            </div>
            <Table :columns="columns" :data="[item.cons_info]" class="mt14">
              <template slot-scope="{row}" slot="name">
                问诊费
              </template>
              <template slot-scope="{row}" slot="status">
                {{ row.money === '0' ? '-' : row.status }}
              </template>
              <template slot-scope="{row}" slot="usage">
                {{ row.money === '0' ? '-' : '自费' }}
              </template>
              <template slot-scope="{row}" slot="unitName">
                {{ row.money === '0' ? '-' : '次' }}
              </template>
              <template slot-scope="{row}" slot="discountFee">
                    <span v-if="row.money === '0'">
                      -
                    </span>
                <span v-else>
                      ￥{{ row.money }}
                    </span>
              </template>
              <template slot-scope="{row}" slot="quantity">
                {{ row.money === '0' ? '-' : 1 }}
              </template>
              <template slot-scope="{row}" slot="price">
                    <span v-if="row.money === '0'">
                      -
                    </span>
                <span v-else>
                      ￥{{ row.money }}
                    </span>
              </template>
              <template slot-scope="{row}" slot="paymentFee">
                    <span v-if="row.money === '0'">
                      -
                    </span>
                <span v-else>
                      ￥{{ row.money }}
                    </span>
              </template>

            </Table>
          </div>

          <div class="chargeItem mt14" v-for="(citem,ci) in item.pres_list" :key="citem.id">
            <div class="chargeItemTitle">
              <span class="mdType">{{ ci + 3 }}.{{ citem.type_text }}</span>
              <div class="subTitle">
                <div class="left ml8">
                  <div class="statusBtn" :style="{background: renderTagColor(item.order.status)}">
                    {{ item.order.status_text }}
                  </div>
                </div>
              </div>
            </div>
            <Table :columns="columns" :data="citem.attrs" class="mt14">
              <template slot-scope="{row}" slot="name">
                {{ row.name }}
              </template>
              <template slot-scope="{row}" slot="status">
                {{ row.status_text }}
              </template>
              <template slot-scope="{row}" slot="usage">
                自费
              </template>
              <template slot-scope="{row}" slot="unitName">
                {{ row.unit_name }}
              </template>
              <template slot-scope="{row}" slot="totalFee">
                ￥{{ row.total_fee }}
              </template>
              <template slot-scope="{row}" slot="quantity">
                {{ row.quantity }}
              </template>
              <template slot-scope="{row}" slot="price">
                {{ row.price }}
              </template>
              <template slot-scope="{row}" slot="paymentFee">
                {{ row.payment_fee }}
              </template>

            </Table>
          </div>
        </div>
      </Panel>
    </Collapse>
    <div style="display: none">
      <print-bill ref="bill"></print-bill>
    </div>
  </div>
</template>

<script>
import printBill from '@/components/print/print-bill'

export default {
  props: ['tab4List', 'tab4Clinics', 'presDesc'],
  components: {printBill},
  data() {
    return {
      initShow: '0',
      columns: [
        {title: '收费项目', slot: 'name', minWidth: 100},
        {title: '状态', slot: 'status', minWidth: 80},
        {title: '自费', slot: 'usage', minWidth: 50},
        {title: '单位', slot: 'unitName', minWidth: 50},
        {title: '单价', slot: 'price', minWidth: 100},
        {title: '数量', slot: 'quantity', minWidth: 100},
        {title: '原价（元）', slot: 'totalFee', minWidth: 50},
        {title: '折后价（元）', slot: 'paymentFee', width: 100},
      ],
    }
  },
  methods: {
    print(id) {
      this.$refs.bill.printBill(id)
    },
    renderTagColor(status) {
      if (status === 'WAIT_PAY') {
        return '#ED7676'
        // return 'error'
      } else if (status === 'HAS_PAY') {
        return '#2BAF52'
        // return 'success'
      } else {
        return '#999999'
        // return 'default'
      }
    },
  },

}
</script>

<style lang="less" scoped>
.chargePage {
  /deep/ .ivu-collapse-header {
    font-size: 14px;
    font-weight: bold;
  }

  .titleInfo {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .titleText {
      font-size: 16px;
      font-weight: 400;
      line-height: 20px;
      color: #979696;
    }

    .amountLabel {
      font-size: 14px;
      font-weight: 400;
      line-height: 20px;
      color: #333333;
    }
  }
}

.chargeItem {
  .name {
    display: flex;
    justify-content: flex-end;
  }

  .subTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

}

.amount {
  display: flex;
  justify-content: flex-end;

  .amountLabel {
    font-size: 15px;
  }

  .amountValue {
    font-size: 26px;
    font-weight: bold;
  }
}

.chargeItemTitle {
  display: flex;
  align-items: center;

  .mdType {
    font-size: 14px;
    font-weight: bold;
    line-height: 20px;
    color: #333333;
  }
}
</style>

