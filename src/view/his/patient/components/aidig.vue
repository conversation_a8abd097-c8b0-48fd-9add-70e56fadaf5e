<template >
  <div class="aidig">
    <Collapse v-model="initShow">
        <Panel :name="i + ''" v-for="(item,i) in tab7List" :key="i">
           <!-- {{item.create_time | data_format('YYYY-MM-DD HH:mm')}} / {{item.patient.name}} / {{item.clinic_name}} / {{item.patient.sex_text}} / {{item.patient.age}} 岁 -->

          <span v-if="item.create_time">{{item.create_time | data_format('YYYY-MM-DD HH:mm')}} /</span>
          <span v-if="item.patient.name">{{item.patient.name}}</span>
          <span v-if="item.clinic_name">/ {{item.clinic_name}} </span>
          <span v-if="item.patient.sex_text">/ {{item.patient.sex_text}}</span>
          <span v-if="item.patient.age">/ {{item.patient.age}} 岁</span>

           <div class="panelList" slot="content">
              <div class="itemTitle">基础信息</div> 
              <div class="panelListItem">
                <div class="label text-muted">
                  个人信息：
                </div>
                <div class="value">
                  {{item.patient.name}} <span v-if="item.patient.sex_text">（{{item.patient.sex_text}}）</span>
                </div>
              </div>
              <div class="panelListItem mt10">
                <div class="label text-muted">
                  出生日期：
                </div>
                <div class="value">
                  {{item.patient.birthday}}
                </div>
              </div>
              <div class="panelListItem mt10">
                <div class="label text-muted">
                  自选症状：
                </div>
                <div class="value">
                  {{item.symptoms.join()}}
                </div>
              </div>
              <Divider class="mt10" />
              <div class="itemTitle mt10">辨证结果</div>
              <div>
                <Tabs :value="item.dialectical[0].code" v-if="item.dialectical.length && item.dialectical.length>0">
                <TabPane :label="diaItem.name" :name="diaItem.code" v-for="(diaItem,diaI) in item.dialectical" :key="diaItem.code">
                  <div class="panelListItem mt10" v-for="(exItem,exI) in diaItem.explain" :key="exItem.sort">
                    <div class="label text-muted">
                      {{exItem.title}}：
                    </div>
                    <div class="value">
                      {{exItem.desc}}
                    </div>
                  </div>
                </TabPane>
              </Tabs>
              </div>

            </div>
        </Panel>
    </Collapse>
  </div>
</template>

<script>
export default {
  props: ["tab7List"],
  data() {
    return {
      initShow: '0'
    }
  },
  methods: {
    
  }
  
}
</script>

<style lang="less" scoped>
.aidig {
  /deep/ .ivu-collapse-header {
    font-size: 14px;
    font-weight: bold;
  }

  .panelList {
    .panelListItem {
      display: flex;
      .label {
        width: 70px;
        flex-shrink: 0;
        text-align: right;
      }
      .value {

      }
    }
  }
  .itemTitle {
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

</style>

