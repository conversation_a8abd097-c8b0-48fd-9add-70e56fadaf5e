<template>
  <div>
    <div class="form-warpper">
      <Form class="form-warpper_left" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
        <FormItem label=""   >
          <DatePicker format="yyyy-MM-dd" type="daterange" placement="bottom-end" placeholder="开始时间 - 结束时间" v-model="queryFormData.createDate" @on-change="(date)=>{queryFormData.st = date[0];queryFormData.et = date[1];queryFormData.createDate=[queryFormData.st, queryFormData.et]}"></DatePicker>
        </FormItem>
        <FormItem label="" prop="search_name"  >
          <Input type="text" v-model="queryFormData.search_name" placeholder="患者姓名/手机号" />
        </FormItem>
        <FormItem label="" prop="doctor_name"  >
          <Input type="text" v-model="queryFormData.doctor_name" placeholder="医生姓名" />
        </FormItem>
        <FormItem>
          <Button type="primary" @click="onSearch">筛选</Button>
          <Dvd/>
          <a @click="onResetSearch">重置筛选条件</a>
        </FormItem>
      </Form>
    </div>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" :height="$store.state.app.clientHeight-235">
      <template slot-scope="{row}" slot="name">
        {{row.name}}
      </template>
      <template slot-scope="{row}" slot="mobile">
        {{row.mobile}}
      </template>
      <template slot-scope="{row}" slot="sex">
        <span v-if="row.sex">
          {{row.sex==="1"? '男':'女'}}
        </span>
        <span v-else></span>
      </template>
      <template slot-scope="{row}" slot="age">
        {{row.age_year}}
      </template>
      <template slot-scope="{row}" slot="lastMedicalTime">
        {{row.last_medical_time | data_format('YYYY-MM-DD HH:mm')}}
      </template>
      <template slot-scope="{row}" slot="clinicId">
        {{row.clinic_id && clinics[row.clinic_id].name}}
      </template>
      <template slot-scope="{row}" slot="creator">
        {{row.creator_id && members[row.creator_id] && members[row.creator_id].name}}
      </template>
      <template slot-scope="{row}" slot="operate">
        <div class="operateList">
          <router-link :to="{path: '/his/patient/customerdetail', query: {id: row.id}}">查看详情</router-link>
        </div>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align:center"
    />
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util' // Some commonly used tools
import io from "@/libs/io" // Http request
import * as runtime from '@/libs/runtime' // Runtime information
/* eslint-disable */
import moment from "moment";

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  search_name: '',
  doctor_name: '',
  st_time: '',
  et_time: '',
  createDate: []
}

export default {
  name: "pharmacyList",
  data() {
    return {
      queryFormData: {...init_query_form_data},
      tableCols: [
        {title: '姓名', slot: 'name', width: 100},
        {title: '手机号', slot: 'mobile', minWidth: 80},
        {title: '性别', slot: 'sex', minWidth: 50},
        {title: '年龄', slot: 'age', minWidth: 50},
        {title: '最近就诊时间', slot: 'lastMedicalTime', minWidth: 120},
        {title: '最近就诊诊所', slot: 'clinicId', minWidth: 70},
        {title: '最近主治医生', slot: 'creator', minWidth: 70},
        {title: '操作', slot: 'operate', width: 100},
      ],
      tableLoading: false,
      list: [],
      total: 0,
      clinics: {},
      members: {}
    }
  },

  created() {
    this.queryFormData = S.merge(this.queryFormData, this.$route.query)
    this.submitQueryForm(true);
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1
      this.submitQueryForm()
    },
    onResetSearch: function () {
      this.queryFormData = {...init_query_form_data}
      this.submitQueryForm()
    },
    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page
      this.queryFormData.pageSize = pageSize
      this.submitQueryForm()
    },
    getsList: function () {
      this.tableLoading = true
      io.get('/clinic/patient.patient.list', {data: this.queryFormData}).then(data => {
        this.list = data.list;
        this.total = data.total;
        this.clinics = data.clinics;
        this.members = data.members;
      }).catch(error => {
        {}
      }).finally(() => {
        this.tableLoading = false
      })
    },
    submitQueryForm: function (replace) {
      // 通过修改url参数，触发路由前置守卫(beforeRouteUpdate)，在前置守卫中获取列表数据
      this.queryFormData.r = S.random(6) // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      if (replace) {
        this.$router.replace({query: this.queryFormData})
      } else {
        this.$router.push({query: this.queryFormData})
      }
    },
    addHandle() {
      this.$router.push('/his/ostock/edit?type=add' + '&prod_type=' + this.queryFormData.prod_type)
    },
  },
  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query)
    this.getsList()
    next()
  },
}
</script>

<style lang="less">
  .operateList {
    display: flex;
    align-items: center;

    .examineBtn {
      margin-left: 10px;
    }
  }
</style>
