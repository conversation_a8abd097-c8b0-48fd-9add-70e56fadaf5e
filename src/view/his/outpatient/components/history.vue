<template>
  <Modal
    class="historyBox"
    v-model="showHistory"
    :title="title"
    width="800"
    :scrollable="true"
    @on-ok="ok"
    @on-cancel="cancel"
  >
    <div>
      <div class="panel-nav mb20">
        <a
          class="nav"
          :class="activeStatus == index ? 'active' : ''"
          v-for="(item, index) in typeList"
          :key="index"
          @click="changeStatus(item, index)"
          >{{ item.title }}</a
        >
      </div>
      <div class="historyCase" v-if="activeStatus == '' || activeStatus == 0">
        <Collapse :accordion="isAccordion">
          <Panel v-for="(item, index) in tab1List" :key="index">
            <span v-if="item.create_time">{{ item.create_time | data_format('YYYY-MM-DD HH:mm') }} /</span>
            <span v-if="item.patient.name">{{ item.patient.name }}</span>
            <span v-if="tab1clinics.name">/ {{ tab1clinics.name }}</span>
            <span v-if="item.status"
              >/ {{ medicalStatusDesc[item.status] && medicalStatusDesc[item.status].desc_b }}</span
            >
            <div class="panelList" slot="content">
              <div class="panelListItem">
                <div class="label text-muted">主诉：</div>
                <div class="value">
                  {{ item.chief_complaint }}
                </div>
              </div>
              <Divider class="mt10" />
              <div class="panelListItem mt10">
                <div class="label text-muted">现病史：</div>
                <div class="value">
                  {{ item.medical_history && item.medical_history.past }}
                </div>
              </div>
              <Divider class="mt10" />
              <div class="panelListItem mt10">
                <div class="label text-muted">体格检查：</div>
                <div class="value">
                  {{ item.physical_check }}
                </div>
              </div>
              <Divider class="mt10" />
              <div class="panelListItem mt10">
                <div class="label text-muted">诊断结果：</div>
                <div class="value">
                  {{ item.diag_result && item.diag_result.length > 0 && diagResultHandle(item.diag_result) }}
                </div>
              </div>
              <Divider class="mt10" />
              <div class="panelListItem mt10">
                <div class="label text-muted">治疗意见：</div>
                <div class="value">
                  {{ item.diag_advice }}
                </div>
              </div>
            </div>
          </Panel>
        </Collapse>
      </div>
      <div class="constitutionCase" v-if="activeStatus == '1'">
        <Collapse :accordion="isAccordion">
          <Panel v-for="(item, index) in tab2List" :key="index">
            <span v-if="tab2patient.create_time">{{ tab2patient.create_time | data_format('YYYY-MM-DD') }} / </span>
            <span v-if="tab2patient.name">{{ tab2patient.name }} </span>
            <span v-if="tab1clinics.name">/ {{ tab1clinics.name }}</span>
            <span v-if="tab1clinics.sex != ''">
              <span v-if="tab1clinics.sex === '1'"> / 男</span>
              <span v-else> / 女</span>
            </span>
            <div class="panelList" slot="content">
              <div class="panelList" slot="content">
                <div class="panelListItem">
                  <div class="label text-muted">主体质：</div>
                  <div class="value">
                    {{ item.report.main_type_info && item.report.main_type_info.type_text }}
                  </div>
                </div>
                <div class="panelListItem">
                  <div class="label text-muted">主体质特征：</div>
                  <div class="value">
                    {{ item.report.main_type_info && item.report.main_type_info.desc }}
                  </div>
                </div>
                <div class="panelListItem">
                  <div class="label text-muted">倾向体质：</div>
                  <div class="value">
                    {{ item.report.main_type_info && item.report.main_type_info.tendency_types.join() }}
                  </div>
                </div>
                <Divider class="mt10" />
                <div class="centerItem mt10" v-for="(citem, ci) in advices[item.report.main_type_info.type]" :key="ci">
                  <div class="itemTitle">{{ citem.subject }}</div>
                  <div class="content mt10" v-for="(subItem, sui) in citem.items" :key="sui">
                    <div class="label text-muted">
                      <span v-if="ci === advices[item.report.main_type_info.type].length - 1">运动及自我调理：</span>
                      <span v-else>{{ subItem.title }}：</span>
                    </div>
                    <div class="value">
                      {{ subItem.content }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Panel>
        </Collapse>
      </div>
      <div class="intelligenceCase" v-if="activeStatus == '2'">
        <Collapse :accordion="isAccordion">
          <Panel v-for="(item, index) in tab3List" :key="index">
            <span v-if="item.create_time">{{ item.create_time }} /</span>
            <span v-if="item.patient.name">{{ item.patient.name }}</span>
            <span v-if="item.clinic_name">/ {{ item.clinic_name }}</span>
            <span v-if="item.sex_text">/ {{ item.sex_text }} </span>
            <span v-if="item.patient.age">/ {{ item.patient.age }}岁</span>
            <div class="panelList" slot="content">
              <div class="panelListItem">
                <div class="label">诊断结果：</div>
                <div class="value">
                  {{ item.body_mass && item.body_mass.join() }}
                </div>
              </div>
              <div class="panelListItem mt10">
                <div class="label">我的照片：</div>
                <div class="value">
                  <img :src="item.tongue_analysis && item.tongue_analysis.detect_img" alt="" style="width: 200px" />
                </div>
              </div>
              <div class="itemTitle">诊断分析</div>
              <Tabs :value="item.diag_list[0].title" v-if="item.diag_list.length && item.diag_list.length > 0">
                <TabPane
                  :label="diaItem.title"
                  :name="diaItem.title"
                  v-for="diaItem in item.diag_list"
                  :key="diaItem.title"
                >
                  <div class="panelListItem mt10" v-for="exItem in diaItem.items" :key="exItem.name">
                    <div class="label text-muted">{{ exItem.name }}：</div>
                    <div class="value">
                      {{ exItem.content }}
                    </div>
                  </div>
                </TabPane>
              </Tabs>
              <div class="itemTitle">舌像分析</div>
              <Tabs
                :value="item.tongue_analysis.tongue_list[0].title"
                v-if="
                  item.tongue_analysis.tongue_list &&
                  item.tongue_analysis.tongue_list.length &&
                  item.tongue_analysis.tongue_list.length > 0
                "
              >
                <TabPane
                  :label="diaItem.title"
                  :name="diaItem.title"
                  v-for="diaItem in item.tongue_analysis.tongue_list"
                  :key="diaItem.title"
                >
                  <div class="panelListItem mt10" v-for="exItem in diaItem.details" :key="exItem.name">
                    <div class="typeList mt10" v-for="(exChildItem, exChildIndex) in exItem" :key="exChildIndex">
                      <div class="label text-muted">{{ exChildItem.name }}：</div>
                      <div class="value" style="min-width: 50px">
                        {{ exChildItem.content }}
                      </div>
                    </div>
                  </div>
                </TabPane>
              </Tabs>
            </div>
          </Panel>
        </Collapse>
      </div>
      <div class="aiCase" v-if="activeStatus == '3'">
        <Collapse :accordion="isAccordion">
          <Panel v-for="(item, index) in tab4List" :key="index">
            <span v-if="item.create_time">{{ item.create_time | data_format('YYYY-MM-DD HH:mm') }} /</span>
            <span v-if="item.patient.name">{{ item.patient.name }}</span>
            <span v-if="item.clinic_name">/ {{ item.clinic_name }} </span>
            <span v-if="item.patient.sex_text">/ {{ item.patient.sex_text }}</span>
            <span v-if="item.patient.age">/ {{ item.patient.age }} 岁</span>

            <div class="panelList" slot="content">
              <div class="itemTitle">基础信息</div>
              <div class="panelListItem">
                <div class="label text-muted">个人信息：</div>
                <div class="value">
                  {{ item.patient.name }} <span v-if="item.patient.sex_text">（{{ item.patient.sex_text }}）</span>
                </div>
              </div>
              <div class="panelListItem mt10">
                <div class="label text-muted">出生日期：</div>
                <div class="value">
                  {{ item.patient.birthday }}
                </div>
              </div>
              <div class="panelListItem mt10">
                <div class="label text-muted">自选症状：</div>
                <div class="value">
                  {{ item.symptoms.join() }}
                </div>
              </div>
              <Divider class="mt10" />
              <div class="itemTitle mt10">辨证结果</div>
              <div>
                <Tabs :value="item.dialectical[0].code" v-if="item.dialectical.length && item.dialectical.length > 0">
                  <TabPane
                    :label="diaItem.name"
                    :name="diaItem.code"
                    v-for="diaItem in item.dialectical"
                    :key="diaItem.code"
                  >
                    <div class="panelListItem mt10" v-for="exItem in diaItem.explain" :key="exItem.sort">
                      <div class="label text-muted">{{ exItem.title }}：</div>
                      <div class="value">
                        {{ exItem.desc }}
                      </div>
                    </div>
                  </TabPane>
                </Tabs>
              </div>
            </div>
          </Panel>
        </Collapse>
      </div>
      <div class="mt20">
        <KPage
          :total="total"
          :page-size.sync="queryFormData.pageSize"
          :current.sync="queryFormData.page"
          @on-change="onPageChange"
          style="text-align: center"
        />
      </div>
    </div>
  </Modal>
</template>

<script>
import io from '@/libs/io'; // Http request
export default {
  props: ['medicalStatusDesc', 'pt_id'],
  data() {
    return {
      title: '患者历史',
      showHistory: false,
      isAccordion: true,
      value1: '1',
      tab1List: [],
      tab2List: [],
      tab1clinics: {},
      advices: {},
      tab2patient: {},
      tab3List: [],
      tab4List: [],
      tab4clinics: {},
      typeList: [
        {
          title: '历史病例',
        },
        {
          title: '中医体质',
        },
        {
          title: '智能舌诊',
        },
        {
          title: 'AI诊断',
        },
      ],
      activeStatus: '',
      queryFormData: {
        pageSize: 10,
        page: 1,
      },
      total: 0,
    };
  },
  mounted() {},
  methods: {
    ok() {
      // this.$Message.info('Clicked ok');
    },
    cancel() {
      // this.$Message.info('Clicked cancel');
    },
    diagResultHandle(arr) {
      if (arr.length > 0) {
        let newArr = [];
        arr.map((item, i) => {
          newArr.push(item.name);
        });
        return newArr.join('、');
      } else {
        return '';
      }
    },
    changeStatus(item, index) {
      this.activeStatus = index;
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = 10;
      if (this.activeStatus == '1') {
        this.queryTab2Info();
      } else if (this.activeStatus == '2') {
        this.queryTab3Info();
      } else if (this.activeStatus == '3') {
        this.queryTab4Info();
      } else {
        this.queryTab1Info();
      }
      // console.log(this.activeStatus,"activeStatus");
    },
    onPageChange(page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      if (this.activeStatus == '1') {
        this.queryTab2Info();
      } else if (this.activeStatus == '2') {
        this.queryTab3Info();
      } else if (this.activeStatus == '3') {
        this.queryTab4Info();
      } else {
        this.queryTab1Info();
      }
    },
    queryTab1Info() {
      console.log(312);
      let params = {
        page: this.queryFormData.page,
        pageSize: this.queryFormData.pageSize,
        pt_id: this.pt_id,
      };
      io.get('/clinic/patient.medical.list', { data: params })
        .then(data => {
          this.tab1List = data.list;
          this.tab1clinics = data.clinics;
          this.total = data.total;
        })
        .finally(() => {
          this.spinShow = false;
        })
        .catch(error => {
          {
          }
        });
    },
    //中医体质
    queryTab2Info() {
      let params = {
        page: this.queryFormData.page,
        pageSize: this.queryFormData.pageSize,
        pt_id: this.pt_id,
      };
      io.get('/clinic/patient.physique.list', { data: params })
        .then(data => {
          this.tab2List = data.list;
          this.tab1clinics = data.clinics;
          this.advices = data.advices;
          this.tab2patient = data.patient;
          this.total = data.total;
        })
        .finally(() => {
          this.spinShow = false;
        })
        .catch(error => {
          {
          }
        });
    },
    //智能舌诊
    queryTab3Info() {
      let params = {
        page: this.queryFormData.page,
        pageSize: this.queryFormData.pageSize,
        pt_id: this.pt_id,
      };
      io.get('/clinic/patient.tongue.list', { data: params })
        .then(data => {
          this.tab3List = data.list;
          this.total = data.total;
        })
        .finally(() => {
          this.spinShow = false;
        })
        .catch(error => {
          {
          }
        });
    },
    //AI诊断
    queryTab4Info() {
      let params = {
        page: this.queryFormData.page,
        pageSize: this.queryFormData.pageSize,
        pt_id: this.pt_id,
      };
      io.get('/clinic/patient.assistant.list', { data: params })
        .then(data => {
          this.tab4List = data.list;
          this.tab4clinics = data.clinics;
          this.total = data.total;
        })
        .finally(() => {
          this.spinShow = false;
        })
        .catch(error => {
          {
          }
        });
    },
  },
  watch: {
    pt_id: {
      handler(val) {
        if (val) {
          console.log('-> 🚀🚀🚀🚀🚀val', val);
          this.queryTab1Info();
        }
      },
      immediate: true,
    },
  },
};
</script>
<style lang="less">
.historyBox {
  .ivu-modal-body {
    max-height: 500px !important;
    overflow: auto !important;
  }
}

.ivu-collapse-content-box p {
  padding: 5px 0;
  font-size: 14px;
  border-bottom: 1px solid #ebebeb;
}

.ivu-collapse-content-box p:nth-last-child(1) {
  padding: 5px 0;
  border-bottom: none;
}

.ivu-collapse-header {
  font-size: 14px;
  font-weight: 600;
}

.historyCase {
  /deep/ .ivu-collapse-header {
    font-size: 14px;
    font-weight: bold;
  }

  .panelList {
    .panelListItem {
      display: flex;

      .label {
        width: 70px;
        text-align: center;
        flex-shrink: 0;
        text-align: right;
      }

      .value {
      }
    }
  }
}

.constitutionCase {
  .panelList {
    .panelListItem {
      display: flex;

      .label {
        width: 78px;
        flex-shrink: 0;
        text-align: right;
      }

      .value {
      }
    }

    .centerItem {
      border-bottom: 1px solid #e8eaec;
      padding-bottom: 10px;

      .content {
        display: flex;
        align-items: center;
      }
    }
  }

  .itemTitle {
    font-size: 14px;
    font-weight: bold;
    margin-top: 5px;
    text-align: left;
    width: 200px;
  }
}

.intelligenceCase {
  .panelList {
    .panelListItem,
    .typeList {
      display: flex;

      .label {
        width: 70px;
        flex-shrink: 0;
        text-align: right;
      }

      .value {
      }
    }
  }

  .itemTitle {
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.aiCase {
  .aidig {
    /deep/ .ivu-collapse-header {
      font-size: 14px;
      font-weight: bold;
    }

    .panelList {
      .panelListItem {
        display: flex;

        .label {
          width: 70px;
          flex-shrink: 0;
          text-align: right;
        }

        .value {
        }
      }
    }

    .itemTitle {
      font-size: 14px;
      font-weight: bold;
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }
}
</style>
