<template>
  <div class="topStep">
    <div class="commonBox">
      <img src="../../../../assets/image/outpatient/nav_first_pressed.png" alt="" class="commonImg" v-if="stepNum == 1">
      <img src="../../../../assets/image/outpatient/nav_first_default.png" alt="" class="commonImg" v-else>
      <span :class="stepNum == 1?'checkedText':'defaultText'">1.病患建档</span>
    </div>
    <div class="commonBox">
      <img src="../../../../assets/image/outpatient/nav_sec_pressed.png" alt="" class="commonImg" v-if="stepNum == 2">
      <img src="../../../../assets/image/outpatient/nav_sec_default.png" alt="" class="commonImg" v-else>
      <span :class="stepNum == 2?'checkedText':'defaultText'">2.医生接诊</span>
    </div>
    <div class="commonBox">
      <img src="../../../../assets/image/outpatient/nav_third_pressed.png" alt="" class="commonImg" v-if="stepNum == 3">
      <img src="../../../../assets/image/outpatient/nav_third_default.png" alt="" class="commonImg" v-else>
      <span :class="stepNum == 3?'checkedText':'defaultText'">3.开具处方</span>
    </div>
  </div>
</template>

<script>

export default {
  props:["stepNum"],
  data () {
    return {
      
    }
  },
  methods: {

  }
}
</script>
<style lang="less">
.topStep{
  display: flex;
  align-items: center;
  justify-content: space-between;
  .commonBox{
    position: relative;
    width: 33%;
    .commonImg{
      width: 100%;
      // height: 48px;
    }
    .defaultText{
      font-size: 14px;
      color: #333;
      position: absolute;
      top:50%;
      left: 50%;
      transform: translate(-50%,-50%);
    }
    .checkedText{
      font-size: 14px;
      color: #fff;
      position: absolute;
      top:50%;
      left: 50%;
      transform: translate(-50%,-50%);
    }
  }
}
</style>
