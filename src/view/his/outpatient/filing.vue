<template>
  <div class="mainContent">
    <div class="topContent">
      <Header :stepNum="stepNum"/>
    </div>
    <div>
      <Form ref="firstForm" :model="formData" :rules="ruleValidate" :label-width="100">
        <div class="block-header">基础信息</div>
        <div>
          <Row>
            <Col span="6">
              <FormItem label="患者姓名:" prop="name">
                <AutoComplete
                  :disabled="inputDisabled"
                  v-model="formData.name"
                  @on-search="handleSearch"
                  @on-select="handleSelect"
                  placeholder="请输入患者姓名"
                >
                  <div v-if="searchList.length">
                    <Option v-for="option in searchList" :value="option.json" :key="option.name">
                      <span class="demo-auto-complete-title space6">{{ option.name }}</span>
                      <span class="demo-auto-complete-count space6">{{ option.sex }}</span>
                      <span class="demo-auto-complete-count space6">{{ option.birthday }}</span>
                      <span class="demo-auto-complete-count space6">{{ option.mobile }}</span>
                    </Option>
                  </div>
                </AutoComplete>
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="手机号:" prop="mobile">
                <Input v-model="formData.mobile" placeholder="手机号" :disabled="inputDisabled" :maxlength="11"></Input>
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="出生日期:" prop="birthday">
                <DatePicker style="width:70%" type="datetime" format="yyyy-MM-dd" placeholder="出生日期"
                            v-model="formData.birthday" @on-change="changeBirthdayHandle"></DatePicker>
                <span class="ml10" v-if="age">{{ age }}</span>
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="性别:" prop="sex">
                <RadioGroup v-model="formData.sex">
                  <Radio label="1">男</Radio>
                  <Radio label="2">女</Radio>
                </RadioGroup>
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="患者证件:" prop="card_type">
                <Select v-model="formData.card_type" style="width:100%">
                  <Option :value="index" v-for="(item,index) in cardTypeDesc" :key="index">{{ item.desc }}</Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="证件号:" prop="card_no">
                <Input v-model="formData.card_no" placeholder="证件号"></Input>
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="来源:" prop="from">
                <Select style="width:100%" v-model="formData.from">
                  <Option value="">{{ '其它' }}</Option>
                  <Option :value="index" v-for="(item,index) in fromDesc" :key="index">{{ item.desc }}</Option>
                </Select>
              </FormItem>
            </Col>
          </Row>
          <Row>
            <Col span="18">
              <FormItem label="地址:">
                <div class="addWrap">
                  <div class="addressBox">
                    <v-region @values="regionChange" v-model="selected"></v-region>
                  </div>
                  <div class="addressInput ml10">
                    <Input v-model="formData.other" placeholder="详细地址"></Input>
                  </div>
                </div>
              </FormItem>
            </Col>
          </Row>
          <div class="block_20"></div>
          <Row>
            <Col span="18">
              <FormItem label="备注:" prop="remark">
                <Input v-model="formData.remark" type="textarea" :rows="4" placeholder="请填写备注"/>
              </FormItem>
            </Col>
          </Row>
        </div>
        <div class="block-header">就诊信息</div>
        <Row>
          <Col span="6">
            <FormItem label="就诊类型:" prop="type">
              <Select v-model="formData.type" style="width:100%">
                <Option :value="index" v-for="(item,index) in typeDesc" :key="index">{{ item.desc }}</Option>
              </Select>
            </FormItem>
          </Col>
          <Col span="6">
            <FormItem label="就诊医生:" prop="doctor">
              <Input v-model="formData.doctor" disabled></Input>
            </FormItem>
          </Col>
          <Col span="6">
            <FormItem label="挂号费:" prop="registration_fee">
              <!-- <Input v-model="formData.registration_fee" placeholder="请填写挂号费" type="number"></Input> -->
              <InputNumber style="width:100%" :min="0" placeholder="请填写挂号费" v-model="formData.registration_fee">
                </InputNumber>
            </FormItem>
          </Col>
        </Row>
        <div class="recently" v-if="date">
          <div class="leftTitle">
            <FormItem label="最近就诊:">
            </FormItem>
          </div>
          <div class="rightTitle">
            <div class="commonText" v-if="date">就诊日期: {{ date }}</div>
            <div class="commonText" v-if="clinic_name">诊所: {{ clinic_name }}</div>
            <div class="commonText" v-if="doctor">医生: {{ doctor }}</div>
            <div class="commonText" v-if="diag_result">诊断结果: {{ diag_result }}</div>
          </div>
        </div>
      </Form>
    </div>
    <div class="block_20"></div>
    <div class="footer">
      <Button type="primary" @click="handleSubmit('firstForm')">开始接诊</Button>
    </div>
  </div>
</template>

<script>
/* eslint-disable */
import Header from './components/header'
import S from '@/libs/util' // Some commonly used tools
import io from '@/libs/io' // Http request
import * as runtime from '@/libs/runtime' // Runtime information
import moment from 'moment'
/* eslint-disable */

let init_query_form_data = {
  name: '',
  sex: '1',
  birthday: '',
  card_type: '',
  card_no: '',
  from: '',
  mobile: '',
  other: '',
  remark: '',
  type: 'FIRST',
  doctor: '',
  registration_fee: 0,
}

export default {
  name: 'list',
  components: {
    Header,
  },
  data() {
    return {
      inputDisabled: false,
      stepNum: 1,
      showMr: false,
      patient_name: '',
      options3: [],
      statusDesc: [],
      searchList: [],
      selected: {},
      addressObj: {},
      // selected: {
      //   province: '350000',
      //   city: '350100',
      //   area: '350104',
      //   town: '350104008'
      // },
      // selected: {
      //   province: '北京市',
      //   city: '北京市',
      //   area: '顺义区',
      //   town: null
      // },
      ruleValidate: {
        name: [{required: true, message: '请填写姓名', trigger: 'change'}],
        other: [{required: false, message: '请填写详细地址', trigger: 'blur'}],
        birthday: [{required: true, type: 'date', message: '请选择出生日期', trigger: 'blur'}],
        card_type: [{required: false, message: '请选择证件类型', trigger: 'change'}],
        mobile: [
          {
            required: true, message: '请填写正确的手机号', trigger: 'blur', type: 'number', transform(value) {
              var myreg = /^1[3456789]\d{9}$/
              if (!myreg.test(value)) {
                return false
              } else {
                return Number(value)
              }
            },
          },
        ],
        card_no: [{required: false, message: '请填写证件号', trigger: 'blur'}],
        // registration_fee: [{required: false, message: '请填写挂号费', trigger: 'change'}],
        sex: [
          {required: true, message: '请选择性别', trigger: 'change'},
        ],
        type: [
          {required: true, message: '请选择就诊类型', trigger: 'change'},
        ],
      },
      formData: {...init_query_form_data},
      typeDesc: {},
      cardTypeDesc: {},
      fromDesc: {},
      latest_mr: {},
      age: '',
      discountTypeDesc: {},
      date: '',
      clinic_name: '',
      doctor: '',
      diag_result: '',
      pt_id: '',
      mr_id: '',
      isBack: false,
    }
  },

  created() {
    this.formData.doctor = runtime.getUser().name
    this.queryFormData = S.merge(this.queryFormData, this.$route.query)
    this.submitQueryForm(true)
    this.mr_id = this.$route.query.mr_id
    this.isBack = this.$route.query.isBack || false
    if (this.isBack === 'true') {
      this.queryInfo()
    }
  },
  methods: {
    // remoteMethod1(query){
    //   console.log(query,"query222222222");
    // },
    changeBirthdayHandle(date) {
      this.age = S.formatDuration(date)
    },
    handleSearch(value) {
      console.log(value)
      if (!value) return
      this.getKeyWordList(value)
    },
    handleSelect(value) {
      let d = JSON.parse(value)
      console.log("-> d", d);
      this.$nextTick(() => {
        this.formData.name = d.name
        this.inputDisabled = true
        this.date = d.latest_mr.date || ''
        this.diag_result = d.latest_mr.diag_result || ''
        this.doctor = d.latest_mr.doctor || ''
        this.clinic_name = d.clinic_name || ''
        this.formData.mobile = d.mobile
        this.formData.birthday = d.birthday
        this.formData.sex = d.sex
        this.formData.remark = d.remark
        this.formData.other = d.address.other
        this.formData.card_no = d.card_no
        this.formData.card_type = d.card_type
        this.formData.from = d.from
        this.age = S.formatDuration(d.birthday)
        this.pt_id = d.id
        this.selected = {
          province: S.isEmptyObject(d.address.prov) ? null : d.address.prov.code,
          city: S.isEmptyObject(d.address.city) ? null : d.address.city.code,
          area: S.isEmptyObject(d.address.county) ? null : d.address.county.code,
          town: null,
        }
      })
    },
    handleSubmit(name) {
    	console.log("-> name", name,this.$refs[name])
      this.$refs[name].validate((valid) => {
        console.log(valid, 'valid')
        if (!valid) {
          return
        }
        let values = this.formData
        console.log(values, 'values')
        console.log(this.addressObj, '0.0')
        // console.log(values,"values");
        values.birthday = moment(values.birthday).format('YYYY-MM-DD')
        let address = {}
        console.log('this.addressObj', this.addressObj)
        if (!S.isEmptyObject(this.addressObj) && !S.isNull(this.addressObj.province)) {
          address.prov = {
            code: this.addressObj.province.key,
            name: this.addressObj.province.value,
          }
        } else {
          address.prov = {}
        }
        if (!S.isEmptyObject(this.addressObj) && !S.isNull(this.addressObj.city)) {
          address.city = {
            code: this.addressObj.city.key,
            name: this.addressObj.city.value,
          }
        } else {
          address.city = {}
        }
        if (!S.isEmptyObject(this.addressObj) && !S.isNull(this.addressObj.area)) {
          address.county = {
            code: this.addressObj.area.key,
            name: this.addressObj.area.value,
          }
        } else {
          address.county = {}
        }
        address.other = this.formData.other
        values.address = address
        values.pt_id = this.pt_id
        if (this.isBack) {
          values.mr_id = this.mr_id
        }
        console.log(this.selected, 'this.selected',values)
        io.post('clinic/medical.record.pre', {...values}).then((res) => {
          if (res.mr_id) {
            this.$Message.success('建档成功!')
            this.$router.push({
              path: '/his/outpatient/diagnosis?isBack=' + this.isBack,
              query: {
                mr_id: res.mr_id,
              },
            })
          }
        },rej=>this.$Message.error(rej.errmsg))
        console.log(values, 'values')
      },(err)=>console.log(err))
    },
    regionChange(data) {
      console.log(data, '++')
      console.log(data)
      this.addressObj = data
    },
    getOptions() {
      var res = io.get('clinic/medical.record.options')
      res.then((res) => {
        this.typeDesc = res.typeDesc
        this.cardTypeDesc = res.cardTypeDesc
        this.fromDesc = res.fromDesc
      }).catch(error => {
        {}
      })
      return res
    },
    onChangeStatus: function (id, action) {
      io.post('clinic/supplier.status', {id: id, act: action}).then(() => {
        this.$Message.success('操作成功')
        this.submitQueryForm(true)
      }).catch(error => {
        {}
      })
    },
    getsList: function () {
      this.tableLoading = true
      io.get('/clinic/supplier.list', {data: this.queryFormData}).then(data => {
        this.list = data.suppliers
        this.total = data.total
      }).catch(error => {
        {}
      }).finally(() => {
        this.tableLoading = false
      })
    },
    //输入框远程搜索
    getKeyWordList(query) {
      io.get('/clinic/patient.patient.search', {data: {keyword: query}}).then(data => {
        let list = data.list
        let tempArr = []
        list.map((item) => {
          tempArr.push({
            name: item.name,
            mobile: item.mobile,
            sex: item.sex == '1' ? '男' : '女',
            birthday: S.formatDuration(item.birthday),
            json: JSON.stringify(item),
          })
        })
        this.searchList = tempArr.length ? tempArr : []
        // if(!S.isEmptyObject(data.latest_mr)){
        //   this.latest_mr = data.latest_mr;
        //   this.showMr = true;
        // }
      }).catch(error => {
        {}
      }).finally(() => {
        this.tableLoading = false
      })
    },
    handleList: function (list) {
      return list
    },

    submitQueryForm: function (replace) {
      // 通过修改url参数，触发路由前置守卫(beforeRouteUpdate)，在前置守卫中获取列表数据
      this.queryFormData.r = S.random(6) // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      if (replace) {
        this.$router.replace({query: this.queryFormData})
      } else {
        this.$router.push({query: this.queryFormData})
      }
    },
    queryInfo() {
      let params = {
        mr_id: this.mr_id,
      }
      io.get('/clinic/medical.record.info', {data: params}).then(data => {
        this.formData.name = data.patient.name
        this.formData.mobile = data.patient.mobile
        this.formData.birthday = data.patient.birthday
        this.formData.sex = data.patient.sex
        this.formData.card_type = data.patient.card_type
        this.formData.card_no = data.patient.card_no
        this.formData.from = data.patient.from
        this.formData.remark = data.patient.remark
        this.selected = {
          province: S.isEmptyObject(data.patient.address.prov) ? null : data.patient.address.prov.code,
          city: S.isEmptyObject(data.patient.address.city) ? null : data.patient.address.city.code,
          area: S.isEmptyObject(data.patient.address.county) ? null : data.patient.address.county.code,
          town: null,
        }
        this.formData.other = data.patient.address.other
        this.formData.type = data.type
        this.formData.registration_fee = data.attach.registration_fee
        this.pt_id = data.pt_id
      }).catch(error => {
        {}
      }).finally(() => {

      })
    },
  },

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query)
    this.getOptions()
    next()
  },
}
</script>

<style lang="less" scoped>
.mainContent {
  .ivu-input-wrapper, .ivu-select {
    max-width: 100%;
  }

  // padding: 0 24px;
  .flexBox {
    display: flex;

    .fx1 {
      flex: 1;
    }

    .fx2 {
      flex: 3;
    }
  }

  .addWrap {
    display: flex;

    .addressBox {
      // /deep/ .rg-select__el{
      //   /deep/ .rg-select__content {
      //     padding: 0 30px 0px 15px !important;
      //   }
      // }
      div.rg-select div.rg-select__el div.rg-select__content {
        padding: 0 30px 0px 15px !important;
      }
    }

    .addressInput {
      flex: 1;
    }
  }

  .textArea {
    display: flex;

    .ivu-input-type-textarea {
      flex: 1;
    }
  }

  .recently {
    display: flex;

    .rightTitle {
      margin-top: 7px;
      display: flex;
      flex-direction: column;
    }
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .topContent {
    margin-bottom: 20px;
  }

  .upWrap {
    .ulBox {
      overflow-x: hidden;
      overflow-y: auto;
      max-height: 100px;
      z-index: 300;
      position: absolute;
      left: 0;
      top: 32px;
      width: 400px;
      border: 1px solid #57a3f3;
      border-radius: 4px;
      background: #fff;

      li {
        list-style: none;
      }
    }
  }

  /deep/ .rg-select__content {
    padding: 0 0 0px 8px !important;
  }
}


</style>
