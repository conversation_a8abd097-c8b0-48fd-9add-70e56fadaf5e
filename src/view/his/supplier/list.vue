<template>
  <div>
    <div class="form-warpper">
      <Form class="form-warpper_left" inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
        <FormItem>
          <Input type="text" v-model="queryFormData.name" placeholder="供应商名称/联系人"/>
        </FormItem>
        <FormItem>
          <Select v-model="queryFormData.status" placeholder="全部状态">
            <Option value="">{{"全部状态"}}</Option>
            <Option :value="index" v-for="(item,index) in statusDesc" :key="index">{{item.desc}}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Button type="primary" class="space6" @click="onSearch">筛选</Button>
          <Button class="space6" @click="initSearch">重置筛选</Button>
        </FormItem>
      </Form>
      <div class="form-warpper_right">
        <Button type="primary" @click="addPage">新增供应商</Button>
        <Dvd/>
<!--        <Button>导出</Button>-->
      </div>
    </div>

    <Table :loading="tableLoading" :columns="tableCols" :data="list" size="small" border stripe class="pl-table" :height="$store.state.app.clientHeight-235">
      <template slot-scope="{row}" slot="id">
        {{row.id}}
      </template>
      <template slot-scope="{row}" slot="time">
        {{row.create_time|data_format('YYYY-MM-DD')}}<br>
      </template>
      <template slot-scope="{row}" slot="sup">
        {{row.name}}
      </template>
      <template slot-scope="{row}" slot="contact_name">
        {{row.contact_name||'-'}}
      </template>
      <template slot-scope="{row}" slot="contact_mobile">
        {{row.contact_phone||'-'}}
      </template>
      <template slot-scope="{row}" slot="status" v-if="statusDesc">
         {{statusDesc[row.status].desc}}
      </template>
      <template slot-scope="{row}" slot="operate">
        <router-link :to="{path: '/his/supplier/edit', query: {id: row.id}}" class="space6" v-if="row.clinic_id!=='0'">编辑</router-link>
        <Poptip
        v-if="row.status == '7'"
        confirm
        transfer
        title="确定启用吗?"
        @on-ok="onChangeStatus(row.id,'ENABLE')"
        >
        <a href="javascript:;">启用</a>
        </Poptip>
        <Poptip
        v-else
        confirm
        transfer
        title="确定停用吗?"
        @on-ok="onChangeStatus(row.id,'DISABLE')"
        >
        <a href="javascript:;">停用</a>
        </Poptip>
      </template>
    </Table>

    <div class="block_20"></div>

    <KPage :total="total"
           :page-size.sync="queryFormData.pageSize"
           :current.sync="queryFormData.page"
           @on-change="onPageChange"
           style="text-align:right"
    />
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util' // Some commonly used tools
import io from "@/libs/io" // Http request
import * as runtime from '@/libs/runtime' // Runtime information
/* eslint-disable */

let init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  status:"",
}

export default {
  name: "list",

  data() {
    return {
      queryFormData: {...init_query_form_data},

      tableCols: [
        {title: '编号', slot: 'id', resizable: true, width: 100},
        {title: '时间', slot: 'time', resizable: true, width: 200},
        {title: '供应商', slot: 'sup', resizable: true, width: 200},
        {title: '联系人', slot: 'contact_name', resizable: true, width: 200},
        {title: '联系电话', slot: 'contact_mobile', resizable: true, width: 200},
        {title: '状态', slot: 'status', resizable: true},
        {title: '操作', slot: 'operate', resizable: true, fixed: 'right', width: 100},
      ],
      tableLoading: true,
      list: [],
      total: 0,
      statusDesc: {},
      typeDesc: {},
      discountTypeDesc: {},
    }
  },

  created() {
    this.getStatusList().then(() => {
      this.queryFormData = S.merge(this.queryFormData, this.$route.query)
      this.submitQueryForm(true)
    });
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1
      this.submitQueryForm()
    },
    initSearch(){
      this.queryFormData = {...init_query_form_data}
      this.submitQueryForm()
    },
    addPage(){
      this.$router.push("/his/supplier/edit")
    },
    onResetSearch: function () {
      this.queryFormData = {...init_query_form_data}
      this.submitQueryForm()
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page
      this.queryFormData.pageSize = pageSize
      this.submitQueryForm()
    },

    onChangeStatus: function (id, action) {
      io.post('clinic/supplier.status', {id:id, act: action}).then(() => {
        this.$Message.success('操作成功')
        this.submitQueryForm(true)
      }).catch(error => {
        {}
      })
    },

    getsList: function () {
      this.tableLoading = true
      io.get('/clinic/supplier.list', {data: this.queryFormData}).then(data => {
        this.list = data.suppliers
        this.total = data.total
      }).catch(error => {
        {}
      }).finally(() => {
        this.tableLoading = false
      })
    },


    getStatusList(){
      let res = io.get('/clinic/supplier.options')
      res.then(data => {
        this.statusDesc = data.statusDesc
      }).catch(error => {
        {}
      })
      return res
    },

    handleList: function (list) {
      return list
    },

    submitQueryForm: function (replace) {
      // 通过修改url参数，触发路由前置守卫(beforeRouteUpdate)，在前置守卫中获取列表数据
      this.queryFormData.r = S.random(6) // 只有在参数发生变化时才会触发前置守卫；所以添加随机数，保证url参数一定有修改
      if (replace) {
        this.$router.replace({query: this.queryFormData})
      } else {
        this.$router.push({query: this.queryFormData})
      }
    }
  },

  beforeRouteUpdate: function (to, from, next) {
    // S.log(to.params['nav-stack-key-dir'], 'nav-stack-key-dir')
    this.queryFormData = S.merge(init_query_form_data, to.query)
    this.getsList();
    next()
  }
}
</script>

<style lang="less">

</style>
