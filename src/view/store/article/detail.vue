<template>
  <div class="editor-wrapper">
    <div id="mytoolbar"></div>
    <div class="editor-box">
      <div class="content-box hidden-scroll">
        <div class="title-box">
          <Input
            class="title-ipt"
            v-model="formData.title"
            style="max-width: 100%"
            placeholder="请在这里输入标题"
            :disabled="canotEdit"
            maxlength="30"
            show-word-limit
            type="textarea"
            autosize
            :border="false"
          />
        </div>
        <editor
          id="mceEditor"
          v-model="formData.content"
          placeholder="请输入正文内容"
          :init="init"
          style="min-height: 500px"
        ></editor>
        <div class="poster-box flex">
          <MaterialPicture v-model="formData.share_img" :disabled="canotEdit"></MaterialPicture>
          <p
            style="
              color: #aaaaaa;
              width: 425px;
              line-height: 22px;
              margin: 0;
              margin-top: 25px;
              padding: 0;
              font-size: 14px;
            "
          >
            请上传您的分享海报配图，只需要上传1张图，尺寸为460px*460px,支持JPG或者PNG格式，需要小于3M。
          </p>
        </div>
      </div>
    </div>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Dvd />
      <Dvd />
      <Button @click="preview" type="primary">预览</Button>
      <Dvd />
      <Dvd />
      <Button type="primary" @click="save" v-if="!canotEdit" :loading="submitLoading">保存 </Button>
    </div>
    <Preview :preview-visible.sync="previewVisible" :title="formData.title" :content="formData.content"></Preview>
    <k-goods-select-multiple
      v-model="goodsSelectVisible"
      @on-selected="selectGoods"
      :show-style="false"
    ></k-goods-select-multiple>
  </div>
</template>

<script>
import config from '@/config';
import tinymce from 'tinymce/tinymce';
import 'tinymce/models/dom';
import 'tinymce/icons/default'; //引入编辑器图标icon
import 'tinymce/themes/silver/theme.min';
import 'tinymce/skins/ui/oxide/skin.min.css';
import Editor from '@tinymce/tinymce-vue';

import Preview from './components/Preview';
import KGoodsSelectMultiple from '_c/k-goods-select-multiple';
import axios from 'axios';

const domain = process.env.VUE_APP_CMD === 'build' ? config.CdnDomain : '';
import request from '@/libs/io';
/*plugins*/
import 'tinymce/plugins/lists';
import 'tinymce/plugins/advlist';
import 'tinymce/plugins/quickbars';
import 'tinymce/plugins/fullscreen';
// import 'tinymce/plugins/link'
import 'tinymce/plugins/autolink';
import 'tinymce/plugins/code';
// import 'tinymce/plugins/image'
// import 'tinymce/plugins/editimage'

export default {
  name: 'article-edit',
  mixins: [],

  components: {
    Editor,
    Preview,
    KGoodsSelectMultiple,
  },

  props: {},

  data() {
    return {
      init: {},
      formData: {
        id: '', //商品id
        title: '', //商品标题
        share_img: '', //分享图
        content: '', //文章内容
      },
      submitLoading: false,
      isEdit: true,
      previewVisible: false,
      goodsSelectVisible: false,
      editor: null,
      promiseCb: {},
    };
  },

  computed: {
    canotEdit() {
      return this.$route.query.id && !this.isEdit;
    },
  },

  watch: {},
  activated() {
    console.log('-> %c this.editor  === %o', 'font-size: 15px;color: green;', this.editor);
    this.$store.commit('menus/SET_CUSTOM_PAGE_STYLE', {
      background: '#FAFAFA',
      padding: 0,
      margin: 0,
    });
    !this.editor && this.initEditor();
  },
  created() {
    console.log('-> %c created  === %o', 'font-size: 15px;color: green;', 'created');
    this.$route.query.id && this.getDetail();
    this.isEdit = this.$route.query.isEdit;
    this.$store.commit('menus/SET_CUSTOM_PAGE_STYLE', {
      background: '#FAFAFA',
      padding: 0,
      margin: 0,
    });
    this.initEditor();
  },

  mounted() {},

  methods: {
    initEditor() {
      this.init = {
        selector: 'textarea#mceEditor',
        height: 500,
        language_url: domain + '/tinymce/langs/zh-Hans.js',
        language: 'zh-Hans',
        icons: 'custom',
        icons_url: domain + '/tinymce/icons/custom/icon.js',
        theme_url: domain + '/tinymce/themes/silver/',
        skin_url: domain + '/tinymce/skins/ui/oxide/',
        inline: true,
        event_root: '#root',
        toolbar_persist: true,
        status_bar: false,
        menubar: false,
        toolbar:
          'blocks | fontfamily | fontsize | bold italic underline strikethrough | forecolor backcolor removeformat | bullist numlist advlist alignment | lineheight outdent indent  | charmap emoticons | fullscreen  ｜ upload-image addproduct ',
        plugins: ['code', 'advlist', 'lists', 'quickbars', 'fullscreen', 'autolink'],
        editimage_toolbar: 'rotateleft rotateright | flipv fliph | editimage imageoptions',
        fixed_toolbar_container: '#mytoolbar',
        toolbar_groups: {
          alignment: {
            icon: 'align-left',
            tooltip: '对齐方式',
            items: 'alignleft aligncenter alignright alignjustify',
          },
        },
        valid_style: {
          '*': 'font-size,font-family,color,text-decoration,text-align,width,height,display,max-width',
        },
        valid_children:
          '+a[div|a|p|img|section],+section[div|a|p|img|section],+div[div|a|p|img|section],+p[div|a|p|img|section],+img[div|a|p|img|section]',
        force_br_newlines: true,
        block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 标题4=h3; 标题5=h5; 标题6=h6;正文=p;',
        font_family_formats:
          '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
        font_size_formats: '12px 14px 16px 18px 20px 22px 24px 28px 32px 36px 48px', //字体大小
        placeholder: '请在这里输入正文内容',
        image_advtab: true,
        image_uploadtab: true,
        quickbars_selection_toolbar: 'bold italic | fontsizeselect forecolor backcolor',
        quickbars_insert_toolbar: false,
        emove_trailing_brs: true,
        smart_paste: true,
        end_container_on_empty_block: true,
        newline_behavior: 'block',
        forced_root_block: '',
        content_style: 'p {line-height: 1.8;font-size:14px;}',
        /*event */
        images_upload_handler: (blobInfo, progress) =>
          new Promise((resolve, reject) => {
            this.handleImgUpload(blobInfo, resolve, reject);
          }),
        paste_preprocess: (editor, args) => {
          console.log('-> %c editor, args  === %o', 'font-size: 15px;color: green;', editor, args);
          // console.log("-> %c args.isDefaultPrevented()  === %o", "font-size: 15px;color: green;", args.isDefaultPrevented())
          // args.content += ' preprocess';
        },
        setup: editor => {
          this.editor = editor;
          console.log(this.editor);
          editor.ui.registry.addContextToolbar('imagealignment', {
            predicate: node => {
              return node.getAttribute('contenteditable') === 'false';
            },
            items: 'remove ',
            position: 'selection',
            scope: 'node',
          });
          editor.ui.registry.addButton('upload-image', {
            icon: 'image',
            // text: '上传图片',
            tooltip: '上传图片',
            onAction: () => {
              this.inputFileUpload();
            },
          });
          editor.ui.registry.addButton('addproduct', {
            icon: 'add-product',
            text: '添加商品',
            tooltip: '添加商品',
            onAction: () => {
              // const content = `<section href="data-id=200" style="text-decoration: none;color:#000;display: block;">  <a>十二通</a> <img width="100%"  src="https://pic2.zhimg.com/v2-93ffc1821cb744d0f4839a6d58fee700_r.jpg?source=172ae18b" alt=""><a>2333</a></section>`
              // editor.insertContent( content )
              this.addProd(editor);
            },
          });
          editor.on('init', function (e) {
            editor.focus();
          });

          editor.on('keyUp', e => {
            this.handlerKeyUp(e, editor);
          });

          editor.on('paste', function (e) {
            const text = e.clipboardData.getData('text/plain');
            console.log('-> %c text  === %o', 'font-size: 15px;color: green;', text);
            const html = e.clipboardData.getData('text/html');
            console.log('-> %c html  === %o', 'font-size: 15px;color: green;', html);
            if (text && !html) {
              console.log('纯文本');
              e.preventDefault();
              editor.insertContent(text);
            }
            if (html) {
              console.log('纯html');
              e.preventDefault();
              editor.insertContent(html);
            }
          });
          if (this.canotEdit) {
            editor.mode.set('readonly');
            console.log('-> %c editor  === %o', 'font-size: 15px;color: green;', editor);
          }
        },
      };
    },
    inputFileUpload() {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.click();
      input.onchange = () => {
        const file = input.files[0];
        console.log('-> %c file  === %o', 'font-size: 15px;color: green;', file);
        const { resolve, reject } = this.promiseCb;
        this.fileUpload(file, resolve, reject);
      };
    },
    addProd(editor) {
      console.log('-> %c editor  === %o', 'font-size: 15px;color: green;', editor);
      this.editor = editor;
      this.goodsSelectVisible = true;
    },
    selectGoods(goods, style) {
      console.log(this.editor.selection.getNode());
      console.log('-> %c goods  === %o', 'font-size: 15px;color: green;', goods);
      let template = ``;
      for (let i = 0; i < goods.length; i++) {
        const good = goods[i];
        if (style === 1) {
          template += `<p class="mceNonEditable" data-goods-id="${good.id}" style="border: 1px solid #eee;width: 100%;box-sizing:border-box;display:flex;color:#000;background:#FAFAFA;border-radius:8px;padding: 6px 8px;font-size: 1rem;line-height: 20px;">
          <span  class="link-a" data-goods-id="${good.id}" style="display:block!important;align-items: center;"><img class="goods-img"  data-goods-id="${good.id}" style="vertical-align: bottom;margin:0!important;border-radius:6px;height: 112px;width: 112px;object-fit: cover;" src="${good.main_img}" alt="${good.id}"></span>
          <span class="link-a" style="width:100%;text-decoration:none;margin-left: 10px;flex:1;height: 100%;display: block!important;" data-goods-id="${good.id}">
           <span data-goods-id="${good.id}" style="font-weight: 600;margin-top: 12px;text-align:left!important;text-decoration: none;word-break: break-all;text-overflow: ellipsis;overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;">${good.name}</span>
           <span data-goods-id="${good.id}" style="letter-spacing:0;margin-top: 16px;text-align:left!important;text-decoration: none;color:#E5634B;display: block">￥${good.price}</span>
          </span>
          </p>`;
        } else {
          template += `<div class="mceNonEditable" data-goods-id="${good.id}" style="display:inline-block;text-decoration: none;height:100%;box-sizing:border-box;width: 47%;color:#000;background:#FAFAFA;border-radius:2px;padding: 12px 6px 6px;font-size: 1rem;margin-right: 2%; line-height: 20px;margin-bottom: 16px;border-radius: 2px;vertical-align: bottom;">
          <p class="link-a"  data-goods-id="${good.id}" ><img class="goods-img" style="display:block;height: 152px;width: 100%;object-fit: cover;border-radius:2px;" data-goods-id="${good.id}"  src="${good.main_img}" alt="${good.id}"></p>
          <p class="link-a" style="margin-top: 8px;padding: 0 13px 9px 9px;display: block; " data-goods-id="${good.id}">
           <span style="font-weight: 600;text-align:left!important;width: 100%;display:inline-block;text-decoration: none;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;" data-goods-id="${good.id}" >${good.name}</span><span>&ZeroWidthSpace;&ZeroWidthSpace;</span>
           <span style="text-decoration: none;margin-top: 10px;text-align:left!important;letter-spacing:0;" data-goods-id="${good.id}" >￥${good.price}</span>
          </p>
          </div>`;
        }
      }
      // const p = document.createElement( 'p' )
      // p.innerHTML = template
      console.log('-> %c template  === %o', 'font-size: 15px;color: green;', template);
      // console.log("-> %c div  === %o", "font-size: 15px;color: green;", div)
      // // template = `${template}`
      // if(style===2){
      //   template = `<div style="display:flex;flex-wrap:wrap;">${template}</div>`
      // }
      if (style === 1) {
        template = `${template}<p></p> `;
      } else {
        template = `<p>${template}</p>`;
      }
      this.editor.insertContent(template);
      // console.log("-> %c this.editor.selection  === %o", "font-size: 15px;color: green;", this.editor.selection.getNode())
      this.goodsSelectVisible = false;
      // this.editor.focus()
    },
    async fileUpload(file, success, failure) {
      console.log('-> %c success,failure  === %o', 'font-size: 15px;color: green;', success, failure);
      if (file.size / 1024 / 1024 >= this.maxSize) {
        failure('上传文件大小不能超过3M');
        return;
      }
      let ext =
        file.name.lastIndexOf('.') > 0 ? file.name.substring(file.name.lastIndexOf('.') + 1, file.name.length) : '';
      console.log('-> %c ext  === %o', 'font-size: 15px;color: green;', ext);

      await request.get('/pms_plat/qiniu.getuptoken', { data: { ext } }).then(
        data => {
          console.log('-> %c data  === %o', 'font-size: 15px;color: green;', data);
          // this.token = data.token
          // this.domain = data.domain
          const formData = new FormData();
          const failType = typeof success;
          console.log('-> %c failType  === %o', 'font-size: 15px;color: green;', failType);
          formData.append('file', file);
          formData.append('token', data.token);
          axios
            .post('https://upload.qiniup.com', formData)
            .then(res => {
              console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
              if (res.status === 200) {
                const imgSrc = data.domain + '/' + res.data.key;
                if (failType === 'function') {
                  success(imgSrc);
                } else {
                  this.editor.insertContent(`<img src="${imgSrc}" style="max-width:100%;"/><p></p>`);
                }
              } else {
                this.editor.notificationManager.open({
                  text: 'An error occurred.',
                  type: 'error',
                });
              }
            })
            .catch(err => {
              this.editor.notificationManager.open({
                text: 'An error occurred.',
                type: 'error',
              });
            });
        },
        reject => {
          console.log(2131232);
          this.$Message.error({
            content: reject.errmsg || String(reject),
            duration: 3,
          });
          throw new Error(reject.errmsg || reject);
        }
      );
    },
    handleImgUpload(blobInfo, success, failure) {
      console.log(
        '-> %c blobInfo, success, failure  === %o',
        'font-size: 15px;color: green;',
        blobInfo,
        success,
        failure
      );
      const file = blobInfo.blob();
      console.log('-> %c file  === %o', 'font-size: 15px;color: green;', file);
      // const imgBase64 = `data:${ blobInfo.blob().type };base64,${ blobInfo.base64() }`
      // success( imgBase64 )

      this.fileUpload(file, success, failure);

      // success(`${imgBase64}`)
      // uploadImgage(data).then(res => {
      //   // 传入success回调里的数据就是富文本编辑器里插入图片的src的值
      //   success(`${this.baseUrl}/${res.data[0]}`)
      // }).catch(() => { failure('error') })
    },
    preview() {
      this.previewVisible = true;
    },
    getDetail() {
      this.$api.getArticleInfo({ id: this.$route.query.id }).then(res => {
        this.formData.content = res.content;
        this.formData.title = res.title;
        this.formData.share_img = res.share_img;
      });
    },
    save() {
      const { title, share_img, content } = this.formData;
      if (!title) {
        this.$Message.error('请输入标题');
        return;
      }
      if (!content) {
        this.$Message.error('请输入内容');
        return;
      }
      if (!share_img) {
        this.$Message.error('请上传分享图');
        return;
      }
      this.submitLoading = true;
      this.formData.id = this.$route.query.id || '';
      this.$api
        .editArticle(this.formData)
        .then(
          res => {
            console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
            this.$Message.success('保存成功');
            this.$router.back();
          },
          err => {
            this.$Message.error(err.errmsg);
          }
        )
        .finally(() => {
          this.submitLoading = false;
        });
    },
    handlerKeyUp(e, editor) {
      if (editor.selection.getNode().className === 'mceNonEditable') {
        // editor.insertContent(`<p></p>`)
        const node = editor.selection.getNode();
        console.log('-> %c node  === %o', 'font-size: 15px;color: green;', node);
        node.removeAttribute('data-mce-selected');
        const parentNode = node.parentNode;
        const p = document.createElement('p');
        p.style.minHeight = '14px';
        p.style.lineHeight = '1.2';
        parentNode.insertBefore(p, node.nextSibling);
        editor.selection.setCursorLocation(node.nextSibling, 0);
        // editor.selection.setNode( node )
      }
    },
  },
  beforeDestroy() {
    console.log('destory');
    this.$store.commit('menus/RESET_CUSTOM_PAGE_STYLE');
    this.editor && this.editor.destroy();
  },
  deactivated() {
    console.log('deactivated');
    this.$store.commit('menus/RESET_CUSTOM_PAGE_STYLE');
    this.editor && this.editor.destroy();
  },

  destroyed() {},
};
</script>
<style scoped lang="less">
.editor-wrapper {
  height: 100%;
  background: #fafafa;
}

#mceEditor {
  outline: none;
  font-size: 14px;
}

.editor-wrapper {
  background: #fafafa;
  height: 100%;

  .toolbar-box {
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .content-box {
    width: 800px;
    max-width: 800px;
    box-sizing: border-box;
    padding: 26px 30px;
    margin: 0 auto 40px;
    border-top: none;
    background: #fff;
    overflow-y: scroll;
    height: 100%;
  }

  .title-ipt {
    padding: 10px 0;
    border-bottom: 1px solid #f0f2f5;
    margin-bottom: 17px;

    ::v-deep .ivu-input {
      font-size: 28px;
      font-weight: 500;
      line-height: 40px;
      border: none;
      color: #000000;
      width: 100%;
      outline: none;
      max-width: 100% !important;
      padding: 0;
    }

    input::-webkit-input-placeholder {
      /* WebKit browsers，webkit内核浏览器 */
      color: #ccc;
      font-size: 16px;
    }

    input:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #ccc;
      font-size: 16px;
    }

    input::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #ccc;
      font-size: 16px;
    }
  }
}

.editor-box {
  margin: 20px auto 0;
  height: 100%;
  overflow: hidden;
  padding-bottom: 100px;

  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  p,
  li,
  ul,
  ol,
  blockquote {
    margin: inherit;
    padding: inherit;
  }

  p {
    line-height: 1.8;
    font-size: 14px;
    display: block;
    //margin-block-start: 1em;
    //margin-block-end: 1em;
    margin-bottom: 14px;
  }
}

#mytoolbar {
  width: 100%;
  position: sticky;
  top: 56px;
  z-index: 10;
  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.14);

  ::v-deep .tox-tinymce {
    border-radius: 2px;
  }
}

::v-deep .tox-toolbar-overlord {
  //display: flex;
  //
  //justify-content: center;
}

::v-deep .mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before {
  color: #ccc;
  line-height: 1.8;
  content: attr(data-mce-placeholder);
  position: absolute;
}

.poster-box {
  padding-top: 29px;
  border-top: 1px solid #f0f2f5;
}

::v-deep .mce-content-body [contentEditable='false'][data-mce-selected] {
  cursor: not-allowed;
  outline: 3px solid #b4d7ff;
}

::v-deep .tox-toolbar__primary {
  display: flex;
  justify-content: center;
  background-color: #f7f7f7 !important;
}

::v-deep .tox-fullscreen {
  z-index: 1500000 !important;
}

::v-deep button[title='样式'] {
  width: 70px;
}

::v-deep button[title='字体'] {
  width: 90px;
}

::v-deep button[title='字体大小'] {
  width: 90px;
}

::v-deep .tox-toolbar__group {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    right: 0;
    width: 1px;
    background-color: #eaeaed;
    height: 23px;
    display: flex;
    align-items: center;
  }
}

::v-deep .tox-editor-header {
  border-radius: 0 !important;
  border: none !important;
  //box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.14);
}

//.tox-editor-container{
//  background: #00ff00 !important;
//}
//::v-deep .tox-toolbar-overlord{
//  background: #00ff00 !important;

//}
::v-deep .tox-tbtn--disabled {
  svg {
    fill: rgba(34, 47, 62, 0.5) !important;
  }
}

::v-deep .title-ipt {
  textarea {
    resize: none;
    padding-right: 60px !important;
  }

  .ivu-input-word-count {
    font-size: 16px;
    color: #aaa;
    top: 2px;
    align-items: center;
    background: #fff;
  }

  .ivu-input::placeholder {
    color: #ccc;
  }
}
</style>
<style lang="less">
.mce-visual-caret {
  display: none !important;
}

.mce-visual-caret-hidden {
  display: none !important;
}

[data-mce-bogus='all'] {
  display: none;
}

.mce-content-body .mce-offscreen-selection {
  position: absolute;
  left: -9999999999px;
  max-width: 1000000px;
}

#mceEditor {
  font-family: Microsoft YaHei;
  padding-bottom: 40px;

  li {
    list-style: inherit;
    //margin: 0;
    //padding: 0;
    margin-left: 2em;
  }

  img {
    max-width: 100%;
  }

  .mceNonEditable,
  p {
    //line-height: 1.8;
    //font-size: 14px;
    display: block;
    //margin-block-start: 1em;
    //margin-block-end: 1em;
    text-align: justify;
    margin-bottom: 14px;
  }
}
</style>
