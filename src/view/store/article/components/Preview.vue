<template>
  <Modal :value="previewVisible"  class="preview-modal"
         @on-visible-change="handleVisibleChange" :closable="false" footer-hide :mask-closable="false"
  >
    <div class="preview-container device-model">
      <div class="phone-receiver">
        <div class="ellipse"></div>
      </div>
      <div class="screen">
        <div  class="screen-header animate-color">
          <div  class="time">{{ currentTime }}</div>
          <svg-icon class="menu-bar" iconClass="wifi"></svg-icon>
        </div>
        <div class="page-nav-bar">
          <svg-icon iconClass="home" style="font-size: 25px;" class="nav-svg" ></svg-icon>
          <svg-icon iconClass="re" class="nav-svg"></svg-icon>
        </div>

        <div class="content-container hidden-scroll">
          <div class="content-title">
            <h1>{{ title }}</h1>
          </div>
          <div class="content" v-html="getPreviewContent"></div>
        </div>
      </div>
      <div class="btn-vol--up"></div>
      <div class="btn-vol--down"></div>
      <div class="btn-power"></div>
      <div class="phone-receiver"><div  class="ellipse"></div></div>
      <div class="close-button" @click="closeModal">
        关闭预览
      </div>
    </div>
  </Modal>

</template>


<script>
import moment from 'moment'
export default {
  name: 'Preview',
  mixins: [],

  components: {},

  props: {
    previewVisible: {
      type: Boolean,
      default: false,
    },
    content: {
      type: String,
      default: '',
    },
    title: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      currentTime: null,
    }
  },

  computed: {
    getPreviewContent(){
      return this.content
      // return this.content.replace(/<p>&nbsp;<\/p>/g,'')
    }
  },

  watch: {
    // content: {
    //   handler(val) {
    //     console.log("-> %c val  === %o", "font-size: 15px;color: green;", val)
    //     // this.$nextTick(() => {
    //     //
    //     //   this.$refs.contentInner = val
    //     // })
    //   },
    //   immediate: true,
    // }
  },

  created() {
    this.currentTime = moment().format('HH:mm')
  },

  mounted() {
  },

  methods: {
    handleVisibleChange(visible) {
      !visible&&this.closeModal()
    },
    closeModal(){
      this.$emit('update:previewVisible', false)
    }
  },

  destroyed() {
  },


}
</script>

<style scoped lang="less">
@baseColor: #000000;
.device-model{
  width: 366px;
  height: 788px;
  border-radius: 52px;
  display: flex;
  font-size: 14px;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  padding: 25px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  background-color: #fff;
  position: relative;
  .phone-receiver {
    border-radius: 0 0 19px 19px;
    border: 1px solid rgba(0, 0, 0, .08);
    border-top: 0;
    position: absolute;
    top: 24px;
    left: 50%;
    width: 152px;
    height: 38px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-transform: translateX(-50%);
    transform: translateX(-50%);
    background-color: #fff;

    .ellipse {
      background-color: #e8eaec;
      width: 40px;
      height: 8px;
      border-radius: 4px;
    }
  }
  .screen{
    box-shadow: 0 0 0 1px rgba(0, 0, 0, .08);
    height: 738px;
    width: 316px;
    border-radius: 32px;
    margin-top: 0;
    display: flex;
    overflow: hidden;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    .screen-header{
      flex-shrink: 0;
      display: flow-root;
      padding-top: 12px;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      .time{
        float: left;
        width: 56px;
        height: 18px;
        font-size: 13px;
        text-align: center;
        font-weight: 500;
        margin-left: 19px;
      }
      .menu-bar{
        float: right;
        //height: 16px;
        font-size: 16px;
        height: 16px;
        width: 60px;
        margin-right: 12px;
      }
    }
    .page-nav-bar{
      //padding: 0 15px 0 12px;
      padding: 0 8px;
      display: flex;
      margin-top: 21px;
      -webkit-box-sizing: border-box;
      box-sizing: border-box;
      margin-bottom: 15px;
      vertical-align: middle;
      align-items: center;
      justify-content: space-between;
      .nav-svg{
        float: left;
        font-weight: 300;
        font-size: 16px;
        fill: #000;
      }
      .nav-svg:last-child{
        float: right;
        height: 20px;
        width: 74px;
      }
    }
  }
  .content-container{
    overflow-x: hidden;
    overflow: scroll;
    height: 100%;
    -webkit-box-flex: 1;
    -ms-flex-positive: 1;
    flex-grow: 1;
    overflow-x: hidden;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 0 12px;
    text-align: justify;
    margin-bottom: 16px;
  }
}
.content{
  padding-bottom: 30px;
  margin-top: 16px;
  ::v-deep p{
    margin-bottom: 14px;
  }
}
.close-button{
  position: absolute;
  bottom: 30px;
  right: -130px;
  padding: 7px 17px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #155BD4;
  line-height: 20px;
  background: #FFFFFF;
  cursor: pointer;
}
.btn-vol--up{
  position: absolute;
  left: -10px;
  width: 10px;
  height: 63px;
  border-radius: 10px 0 0 10px;
  background-color: #fff;
  top: 147px;
}
.btn-vol--down{
  position: absolute;
  left: -10px;
  width: 10px;
  height: 63px;
  border-radius: 10px 0 0 10px;
  background-color: #fff;
  top: 218px;
}
.btn-power{
  position: absolute;
  right: -10px;
  width: 10px;
  height: 85px;
  border-radius: 0 10px 10px 0;
  background-color: #fff;
  top: 147px;
}
::v-deep img{
  max-width: 100%;
  height: auto;
}
::v-deep a{
  text-decoration: none;
  color: #000;
}
//::v-deep img:not(.goods-img){
//  margin-block-start: 1em;
//  margin-block-end: 1em;
//  margin-inline-start: 0px;
//  margin-inline-end: 0px
//}
::v-deep .mceNonEditable,p{
  display: block;
  //margin-block-start: 1em;
  //margin-block-end: 1em;
  //margin-inline-start: 0px;
  //margin-inline-end: 0px
  margin-bottom: 14px;
}
::v-deep li{
  list-style: inherit;
  margin-left: 2em;
}

</style>
<style lang="less">
.preview-modal{
  .ivu-modal{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .ivu-modal-content{
    border-radius: 52px;
  }
  .ivu-modal-body{
    padding: 0px;
    background: #f0f0f0;
    border-radius: 52px;
    font-size: 14px;
  }

}
</style>