<template>
  <div class="list-wrap">
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      @on-sort-change="sortChange"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" @click="createShoppingCoupon">创建购物赠券活动</Button>
        </div>
        <Form
          inline
          :label-width="0"
          @submit.native.prevent
          @keyup.enter.native="onSearch"
          class="flex flex-item-between"
        >
          <Row :gutter="24">
            <Col span="24">
              <FormItem label="">
                <Input type="text" v-model="queryFormData.name" placeholder="活动名称" />
              </FormItem>
              <FormItem label="">
                <Select v-model="queryFormData.status" placeholder="状态" clearable>
                  <Option v-for="item in statusList" :key="item.id" :value="item.id">
                    {{ item.desc }}
                  </Option>
                </Select>
              </FormItem>
              <FormItem label="">
                <DatePicker
                  type="daterange"
                  placeholder="活动时间"
                  :value="timeRange"
                  clearable
                  @on-change="times => handleTimeChange(times)"
                ></DatePicker>
              </FormItem>
              <FormItem>
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </template>

      <!--        <template slot-scope="{ row }" slot="associated_coupon">-->
      <!--          {{ row.buy_st | data_format }} ~ {{ row.buy_et | data_format }}-->
      <!--        </template>-->
      <template slot-scope="{ row }" slot="time">
        <div v-if="row.time_type === '1'">长期有效</div>
        <div style="text-align: left" v-else>
          <p class="flex">
            <span>开始时间：</span><span class="flex-1">{{ row.begin_time | data_format }}</span>
          </p>
          <p class="flex">
            <span>结束时间：</span><span class="flex-1">{{ row.end_time | data_format }}</span>
          </p>
        </div>
      </template>
      <template slot-scope="{ row }" slot="associated_coupon">
        <a @click="checkCoupon(row)">{{ row.coupon_ids.length }}</a>
      </template>
      <template slot-scope="{ row }" slot="description">
        <Tooltip style="width: 100%" max-width="300" :disabled="!row.description" :content="row.description">
          <div class="es-cell">{{ row.description || '-' }}</div>
        </Tooltip>
      </template>
      <!--操作记录-->
      <template slot-scope="{ row }" slot="action_log">
        <div style="text-align: left">
          <p>操作人：{{ row.op_info.operator }}</p>
          <p>操作时间：{{ row.op_info.op_time | date_format('YYYY.MM.DD HH:mm') }}</p>
        </div>
      </template>
      <template slot-scope="{ row }" slot="status_text">
        <Tooltip :content="row.status_msg" :disabled="row.status !== '3'">
          <mark-status :type="row.status === '2' ? 'success' : 'reject'">{{ row.status_desc }}</mark-status>
        </Tooltip>
      </template>
      <template slot-scope="{ row }" slot="action">
        <Poptip confirm :title="`你确定要${row.status === '2' ? '禁用' : '启用'}该优活动吗？`" @on-ok="setStatus(row)">
          <a class="ml8">{{ row.status === '2' ? '禁用' : '启用' }}</a>
        </Poptip>
        <a class="ml8" @click="toDetail(row, true)">编辑</a>
        <a class="ml8" @click="toDetail(row)">详情</a>
      </template>
    </standard-table>

    <associated-coupons v-model="modalVisible" :id="checkedId"></associated-coupons>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from '@/libs/util';
import AssociatedCoupons from './components/AssociatedCoupons.vue';
import { data_format } from '../../../libs/filters';
import StandardTable from '@/components/StandardTable/index.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  status: '',
  st: '',
  et: '',
  r: '',
};
export default {
  name: 'ShoppingCouponList',
  components: { StandardTable, AssociatedCoupons },
  mixins: [search],
  props: {},
  data() {
    return {
      apiName: 'getShoppingCouponList',
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      queryFormData: {
        ...init_query_form_data,
      },
      statusList: [], // 状态列表
      tableCols: [
        { title: 'ID', key: 'id', align: 'center' },
        { title: '活动时间', slot: 'time', align: 'center', width: 150 },
        { title: '活动名称', key: 'name', align: 'center' },
        { title: '活动备注', slot: 'description', align: 'center' },
        { title: '关联商品', key: 'goods_name', align: 'center' },
        { title: '商品类型', key: 'goods_type_desc', align: 'center' },
        { title: '关联优惠券数量', slot: 'associated_coupon', align: 'center' },
        { title: '发放方式', key: 'coupon_scheme_desc', align: 'center' },
        {
          title: '发放限制',
          key: 'coupon_limit_desc',
          align: 'center',
          render: (h, { row }) => h('span', {}, row.coupon_limit_desc || '-'),
        },
        { title: '活动状态', slot: 'status_text', align: 'center' },
        { title: '操作', slot: 'action', align: 'center', width: 140 },
      ],
      timeRange: [],
      confirmVisible: false,
      statusCount: {},
      checkedId: '',
      modalVisible: false,
    };
  },
  computed: {
    getTagColor() {
      return status => {
        switch (status) {
          case 'UNDERWAY':
            return 'success';
          case 'SOLD_OUT':
            return 'error';
          case 'WAIT':
            return 'warning';
          default:
            return 'default';
        }
      };
    },
  },
  watch: {},
  created() {
    this.getOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    data_format,
    setStatus({ id, status }) {
      this.$api.updateShoppingCouponStatus({ id, status: status === '2' ? '1' : '2' }).then(() => {
        this.$Message.success('操作成功');
        this.submitQueryForm();
      });
    },
    // 创建优惠券
    createShoppingCoupon() {
      this.$router.push({ path: '/store/shopping-coupons/detail' });
    },
    // 查看优惠券
    checkCoupon(row) {
      console.log('-> %c row  ===    %o', 'font-size: 15px;color: #fa8c16 ;', row);
      this.checkedId = row.id;
      this.modalVisible = true;
    },
    getOptions() {
      this.$api.getShoppingCouponOption().then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.statusList = S.descToArrHandle(res.status);
      });
    },
    // 状态切换
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },

    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    // 排序 asc/desc/normal
    sortChange(column, key, item) {
      console.log('column, key, item', column, key, item);
    },

    // 详情/编辑
    toDetail(row, isEdit) {
      let query = {
        id: row.id,
      };
      if (isEdit) {
        query.isEdit = 1;
      }
      this.$router.push({
        path: '/store/shopping-coupons/detail',
        query,
      });
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
  filters: {},
};
</script>

<style lang="less" scoped>
.ml8 {
  margin-left: 8px;
}

.es-cell {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}
</style>
