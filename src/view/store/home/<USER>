<template>
  <div class="edit-wrapper">
    <Form
      :model="formData"
      :label-width="90"
      ref="carouselForm"
      :rules="ruleValidate"
      :disabled="!$route.query.isEdit && !!$route.query.id"
    >
      <FormItem label="轮播图标题" prop="title">
        <Input v-model="formData.title" placeholder="轮播图标题" style="width: 300px" :maxlength="15" show-word-limit />
      </FormItem>
      <FormItem label="图片" prop="img_url">
        <MaterialPicture v-model="formData.img_url" :limit="1" :disabled="isQuery" />
        <div class="note">建议尺寸：750*350像素，图片大小不超过3.0M</div>
      </FormItem>
      <FormItem label="展示时间" prop="show_end_time">
        <DatePicker
          type="datetimerange"
          :options="disabledTime"
          format="yyyy-MM-dd HH:mm"
          placeholder="请选择展示时间"
          v-model="showTimes"
          @on-change="timeChange"
          style="width: 300px"
        ></DatePicker>
      </FormItem>
      <FormItem label="跳转" prop="type">
        <div class="jump-type">
          <Select v-model="formData.type" class="link-type" @on-change="selectType">
            <Option v-for="item in slideimg_type_desc" :lala="item" :value="item[1].type" :key="item[0]"
              >{{ item[1].desc }}
            </Option>
          </Select>
          <FormItem
            :prop="dataRequired ? 'data' : ''"
            v-show="
              formData.type !== 'article_detail' && formData.type !== 'goods_detail' && formData.type !== 'second_kill'
            "
          >
            <Input
              v-if="formData.type !== 'home_node'"
              v-model="formData.data"
              :placeholder="linkTypePlaceholder"
              class="link-params"
            />
            <Select v-else v-model="formData.data" class="link-params" :placeholder="linkTypePlaceholder">
              <Option v-for="item in prodHomeNodeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
            </Select>
          </FormItem>
        </div>

        <div
          class="select-container"
          v-show="
            formData.type === 'article_detail' || formData.type === 'goods_detail' || formData.type === 'second_kill'
          "
        >
          <div v-show="data_text" class="select-text ecs" style="margin-right: 10px">{{ data_text }}</div>
          <a
            v-show="formData.type === 'article_detail' && ($route.query.isEdit || !$route.query.id)"
            :style="{ textAlign: data_text ? 'right' : 'center' }"
            @click="articleVisible = true"
            >{{ data_text ? '更换' : '选择' }}文章</a
          >
          <a
            v-show="formData.type === 'goods_detail' && ($route.query.isEdit || !$route.query.id)"
            :style="{ textAlign: data_text ? 'right' : 'center' }"
            @click="goodVisible = true"
            >{{ data_text ? '更换' : '选择' }}商品</a
          >
          <a
            v-show="formData.type === 'second_kill' && ($route.query.isEdit || !$route.query.id)"
            :style="{ textAlign: data_text ? 'right' : 'center' }"
            @click="seckillVisible = true"
            >{{ data_text ? '更换' : '选择' }}秒杀活动</a
          >
        </div>
      </FormItem>
      <FormItem label="展示顺序" prop="order">
        <div class="carousel-order">
          <InputNumber v-model="formData.order" placeholder="展示顺序" style="width: 300px" :max="99" />
          <span style="margin-left: 20px">(数字越大，展示顺序越靠前，最大顺序为99)</span>
        </div>
      </FormItem>
    </Form>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Dvd />
      <Button
        v-show="$route.query.isEdit || !$route.query.id"
        type="primary"
        @click="handleSubmit('carouselForm')"
        :loading="submitLoading"
        >保存
      </Button>
    </div>

    <article-modal v-model="articleVisible" @selectArticle="selectArticle"></article-modal>
    <goods-modal v-model="goodVisible" @selectGood="selectGood"></goods-modal>
    <seckill-modal v-model="seckillVisible" @selectSecKill="selectSecKill"></seckill-modal>
  </div>
</template>

<script>
import moment from 'moment';
import articleModal from '../components/articleModal';
import goodsModal from '../components/goodsModal';
import seckillModal from '../components/seckillModal';
import S from '@/libs/util';

export default {
  name: 'edit_carousel',
  components: { articleModal, goodsModal, seckillModal },
  data() {
    const validatorOrder = (rule, value, callback) => {
      console.log(value);
      if (value === null) {
        callback(new Error('请输入展示顺序'));
      } else {
        if (value > 99) {
          callback(new Error('展示顺序不能超过99'));
        } else {
          console.log(221);
          callback();
        }
      }
    };
    return {
      linkTypePlaceholder: '跳转参数',
      dataRequired: false,
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() < Date.now() - 86400000;
        },
      },
      formData: {
        title: '',
        show_start_time: '',
        show_end_time: '',
        order: null,
        img_url: '',
        type: '',
        data: '',
      },
      showTimes: [],
      ruleValidate: {
        title: [{ required: true, message: '请输入轮播图标题', trigger: 'blur' }],
        img_url: [{ required: true, message: '请上传轮播图片', trigger: 'change' }],
        type: [{ required: true, message: '请选择跳转类型', trigger: 'change' }],
        data: [{ required: true, message: '请输入跳转类型参数', trigger: 'change' }],
        order: [{ required: true, validator: validatorOrder, trigger: 'change' }],
        show_end_time: [{ required: true, type: 'string', message: '请选择展示时间', trigger: 'change' }],
      },
      submitLoading: false,
      slideimg_type_desc: [],
      prodHomeNodeDesc: [],
      articleVisible: false,
      goodVisible: false,
      seckillVisible: false,
      data_text: '',
    };
  },
  created() {
    const id = this.$route.query.id;
    if (id) {
      this.getDetail(id);
    }
    this.getStatus();
  },
  computed: {
    isQuery() {
      const { id, isEdit } = this.$route.query;
      return id && !isEdit ? true : false;
    },
  },
  methods: {
    selectType(type) {
      let currentTypeItem = this.slideimg_type_desc.find(item => item[0] === type);
      this.formData.data = '';
      this.data_text = '';
      this.linkTypePlaceholder = currentTypeItem[1].tips;
      this.dataRequired = currentTypeItem[1].required === '1' ? true : false;
    },
    getDetail(id) {
      this.$api
        .getCarouselDetail({ id })
        .then(res => {
          console.log(res);
          res = res.slide_img;
          this.formData.title = res.title;
          this.formData.img_url = res.img_url;
          res.show_end_time = moment(res.show_end_time * 1000).format('YYYY-MM-DD HH:mm');
          res.show_start_time = moment(res.show_start_time * 1000).format('YYYY-MM-DD HH:mm');
          this.formData.show_end_time = res.show_end_time;
          this.formData.show_start_time = res.show_start_time;
          this.showTimes = [res.show_start_time, res.show_end_time];
          this.formData.order = res.order - 0;
          this.formData.data = res.data;
          this.formData.type = res.type;
          this.formData.title = res.title;
          this.data_text = res.data_text;
        })
        .catch(err => {
          console.log(err);
          {
          }
        });
    },
    getStatus() {
      this.$api.getCarouselStatusList().then(res => {
        console.log('-> res', res);
        this.slideimg_type_desc = Object.entries(res.slideimg_type_desc);
        this.prodHomeNodeDesc = S.descToArrHandle(res.prodHomeNodeDesc);
        console.log('-> slideimg_type_desc', this.slideimg_type_desc);
      });
    },
    handleSubmit(name) {
      this.$refs[name].validate(valid => {
        if (valid) {
          if (this.dataRequired && !this.formData.data) {
            this.$Message.error('该跳转类型不能为空');
            return;
          }
          if (this.$route.query.id) {
            this.formData = Object.assign(this.formData, { id: this.$route.query.id });
          }
          this.submitLoading = true;
          this.$api
            .editCarousel(this.formData)
            .then(
              res => {
                this.$Message.success(this.$route.query.isEdit ? '编辑轮播图成功' : '新增轮播图成功');
                this.$router.push('/store/home/<USER>');
              },
              err => {
                {
                }
              }
            )
            .finally(() => (this.submitLoading = false));
        } else {
          console.log(3212);
          this.$Message.error('请正确填写轮播图信息');
        }
      });
      console.log(this.formData);
    },
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    back() {
      this.$router.back();
    },
    timeChange(values) {
      console.log(values);
      if (values) {
        this.formData.show_start_time = values[0];
        this.formData.show_end_time = values[1];
      } else {
        this.formData.show_start_time = '';
        this.formData.show_end_time = '';
      }
    },

    selectArticle(row) {
      this.formData.data = row.id;
      this.data_text = row.title;
    },
    selectGood(row) {
      this.formData.data = row.id;
      this.data_text = row.name;
    },
    selectSecKill(row) {
      this.formData.data = row.id;
      this.data_text = row.name;
    },
  },
  beforeDestroy() {
    this.handleReset('carouselForm');
  },
};
</script>
<style scoped lang="less">
.jump-type {
  display: flex;

  .link-type {
    width: 300px;
    margin-right: 20px;
  }

  //::v-deep .ivu-select-selection {
  //  width: 300px;
  //}

  .link-params {
    width: 130px !important;
  }
}

.select-container {
  display: flex;
  flex-wrap: nowrap;
  //justify-content: center;
  align-items: center;
  width: 300px;
  height: 32px;
  margin-top: 10px;
  padding: 0 10px;
  background: #ececec;

  .select-text {
    width: 180px;
  }

  a {
    flex: 1;
  }
}
</style>
