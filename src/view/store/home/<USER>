<template>
  <div class="edit-wrapper">
    <Form
      :model="formData"
      :label-width="90"
      :rules="ruleValidate"
      ref="physicianForm"
      :disabled="!$route.query.isEdit && !!$route.query.id"
    >
      <FormItem label="医师名称" required prop="name">
        <Input v-model="formData.name" placeholder="医师名称" style="width: 80%" />
      </FormItem>
      <FormItem label="头像" required prop="avatar">
        <MaterialPicture v-model="formData.avatar" :limit="1" :disabled="isQuery" />
        <div class="note">建议尺寸：300*300像素，图片大小不超过3.0M</div>
      </FormItem>
      <FormItem label="简介" required prop="title">
        <Input v-model="formData.title" placeholder="医师简介" style="width: 80%" />
      </FormItem>
      <FormItem label="医师介绍" required prop="desc_img">
        <MaterialPicture v-model="formData.desc_img" :limit="1" :disabled="isQuery" />
        <div class="note">
          建议尺寸：宽750像素，高度不限，图片大小不超过3.0M
          <a @click="queryExample">查看示例</a>
        </div>
      </FormItem>
      <FormItem label="展示顺序" required prop="order">
        <div class="physician-order">
          <Input v-model="formData.order" placeholder="展示顺序" type="number" />
          <span style="margin-left: 20px">(数字越大，展示顺序越靠前)</span>
        </div>
      </FormItem>
    </Form>
    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Dvd />
      <Button
        v-if="$route.query.isEdit || !$route.query.id"
        type="primary"
        @click="handleSubmit('physicianForm')"
        :loading="submitLoading"
        >保存
      </Button>
    </div>
    <Modal v-model="checkDetail" title="医师介绍示例" footer-hide>
      <div class="img-info">
        <img src="@/assets/image/base/doctor.png" />
      </div>
    </Modal>
  </div>
</template>

<script>
export default {
  name: 'edit_physician',
  components: {},
  data() {
    const validatorOrder = (rule, value, callback) => {
      console.log(value);
      if (value === '') {
        callback(new Error('请输入展示顺序'));
      } else {
        if (value > 255) {
          callback(new Error('展示顺序不能超过255'));
        } else {
          console.log(221);
          callback();
        }
      }
    };
    return {
      showImg_url: '',
      checkDetail: false,
      formData: {
        name: '',
        avatar: '',
        title: '',
        desc_img: '',
        order: 0,
      },
      showTimes: [],
      ruleValidate: {
        name: [{ required: true, message: '请输入医师名称', trigger: 'blur' }],
        avatar: [{ required: true, message: '请上传医师头像', trigger: 'blur' }],
        title: [{ required: true, message: '请输入医师简介', trigger: 'change' }],
        order: [{ validator: validatorOrder, trigger: 'blur' }],
        desc_img: [{ required: true, message: '请上传医师介绍', trigger: 'blur' }],
      },
      submitLoading: false,
    };
  },
  created() {
    const id = this.$route.query.id;
    if (id) {
      this.getDetail(id);
    }
  },
  computed: {
    isQuery() {
      const { id, isEdit } = this.$route.query;
      return id && !isEdit ? true : false;
    },
  },
  methods: {
    getDetail(id) {
      this.$api
        .getPhysicianDetailInfo({ id })
        .then(res => {
          console.log(res);
          res = res.doctor;
          this.formData.name = res.name;
          this.formData.avatar = res.avatar;
          this.formData.title = res.title;
          this.formData.desc_img = res.desc_img;
          this.showTimes = [res.show_end_time, res.show_start_time];
          this.formData.order = res.order - 0;
        })
        .catch(err => {
          console.log(err);
          {
          }
        });
    },
    //表单提交
    handleSubmit(name) {
      console.log(this.formData);
      this.$refs[name].validate(valid => {
        if (valid) {
          if (this.$route.query.id) {
            this.formData = Object.assign(this.formData, { id: this.$route.query.id });
          }
          this.$api.editPhysician(this.formData).then(
            res => {
              console.log(res);
              let isEdit = this.$route.query.isEdit;
              this.$Message.success(isEdit ? '修改成功' : '新增成功');
              this.$router.push({
                path: '/store/home/<USER>',
                query: {
                  tab: 1,
                },
              });
            },
            err => {}
          );
        } else {
          this.$Message.error('请正确填写医师信息');
        }
      });
    },
    //重置表单数据
    handleReset(name) {
      this.$refs[name].resetFields();
    },
    back() {
      //  this.$router.push({
      //   path: '/store/home/<USER>',
      //    query: {
      //      tab: 1
      //    }
      // })
      this.$router.back();
    },
    //查看示例
    queryExample() {
      this.checkDetail = true;
    },
  },
};
</script>
<style scoped lang="less">
.jump-type {
  display: flex;

  .link-type {
    width: 300px;
    margin-right: 20px;
  }

  ::v-deep .ivu-select-selection {
    width: 300px;
  }

  .link-params {
    width: 130px !important;
  }
}

::v-deep .ivu-input-wrapper {
  width: 450px;
}

.img-info {
  display: flex;
  align-items: center;
  justify-content: center;

  > img {
    display: block;
    height: 600px;
    width: auto;
  }
}
</style>
