<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      @on-sort-change="sortChange"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" @click="createCoupon">创建优惠券</Button>
        </div>
        <Form
          inline
          :label-width="0"
          @submit.native.prevent
          @keyup.enter.native="onSearch"
          class="flex flex-item-between"
        >
          <Row :gutter="24">
            <Col span="24">
              <FormItem label="">
                <Input type="text" v-model="queryFormData.name" placeholder="卡券名称" />
              </FormItem>
              <FormItem label="">
                <Select v-model="queryFormData.type" placeholder="类型">
                  <Option v-for="type in typeList" :key="type.id" :value="type.id">
                    {{ type.desc }}
                  </Option>
                </Select>
              </FormItem>
              <FormItem label="">
                <Select v-model="queryFormData.status" placeholder="状态">
                  <Option v-for="status in statusList" :key="status.id" :value="status.id">
                    {{ status.desc }}
                  </Option>
                </Select>
              </FormItem>

              <FormItem>
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </template>

      <template slot-scope="{ row }" slot="status_text">
        <mark-status :type="row.status === '2' ? 'success' : 'reject'">{{ row.status_desc }}</mark-status>
      </template>

      <template slot-scope="{ row }" slot="action">
        <Poptip confirm :title="`你确定要${row.status === '2' ? '禁用' : '启用'}该优惠券吗？`" @on-ok="setStatus(row)">
          <a class="ml8">{{ row.status === '2' ? '禁用' : '启用' }}</a>
        </Poptip>

        <a class="ml8" @click="toDetail(row)">详情</a>
        <!--          <br />-->
        <!--          <a class="ml8" @click="toDetail(row)">手动发放</a>-->
        <a class="ml8" @click="editStock(row)">修改库存</a>
      </template>
    </standard-table>
    <distribute-coupon v-model="distributeVisible"></distribute-coupon>
    <edit-coupon-stock v-model="stockVisible" @on-refresh="getsList" :coupon-info="editCouponInfo"></edit-coupon-stock>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from '@/libs/util';
import DistributeCoupon from './components/DistributeCoupon.vue';
import EditCouponStock from './components/EditCouponStock.vue';
import StandardTable from '@/components/StandardTable/index.vue';

const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  status: '',
  type: '',
  r: '',
};
export default {
  name: 'CouponList',
  components: { StandardTable, DistributeCoupon, EditCouponStock },
  mixins: [search],
  props: {},
  data() {
    return {
      apiName: 'getCouponList',
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      queryFormData: {
        ...init_query_form_data,
      },
      statusList: [], // 状态列表
      typeList: [], // 类型列表
      tableCols: [
        { title: 'ID', key: 'id', align: 'center' },
        { title: '卡券名称', key: 'name', align: 'center' },
        { title: '卡券类型', key: 'type_desc', align: 'center' },
        { title: '优惠金额', key: 'discount_amount_desc', align: 'center' },
        { title: '适用范围', key: 'range_desc', align: 'center' },
        { title: '限制类型', key: 'limit_type_desc', align: 'center' },
        { title: '卡券有效期', key: 'period_desc', align: 'center' },
        { title: '领取上限', key: 'upper_limit', align: 'center' },
        { title: '卡券库存', key: 'stock_num', align: 'center' },
        { title: '卡券状态', slot: 'status_text', align: 'center' },
        { title: '操作', slot: 'action', align: 'center', width: 150 },
      ],
      timeRange: [],
      distributeVisible: false,
      editCouponInfo: {
        id: '',
        stock_num: '',
      },
      stockVisible: false,
    };
  },
  computed: {
    getTagColor() {
      return status => {
        switch (status) {
          case 'UNDERWAY':
            return 'success';
          case 'SOLD_OUT':
            return 'error';
          case 'WAIT':
            return 'warning';
          default:
            return 'default';
        }
      };
    },
  },
  watch: {},
  created() {
    this.getOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    setStatus({ id, status }) {
      this.$api.updateCouponStatus({ id, status: status === '2' ? '1' : '2' }).then(() => {
        this.$Message.success('操作成功');
        this.submitQueryForm();
      });
    },
    // 创建优惠券
    createCoupon() {
      this.$router.push({ path: '/store/coupon/detail' });
    },
    // 创建优惠券
    checkCoupon(id) {
      const query = { id };
      this.$router.push({ path: '/store/coupon/detail', query });
    },
    getOptions() {
      this.$api.getCouponOption().then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.statusList = S.descToArrHandle(res.status);
        this.typeList = S.descToArrHandle(res.type);
        console.log('-> %c this.statusList  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.statusList);
      });
    },
    // 状态切换
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },

    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    // 排序 asc/desc/normal
    sortChange(column, key, item) {
      console.log('column, key, item', column, key, item);
    },

    // 详情/编辑
    toDetail(row) {
      this.$router.push({
        path: '/store/coupon/detail',
        query: {
          id: row.id,
        },
      });
    },
    // 修改优惠券
    editStock(item) {
      console.log('-> %c id  ===    %o', 'font-size: 15px;color: #fa8c16 ;', item);
      this.editCouponInfo = item;
      this.stockVisible = true;
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
  filters: {},
};
</script>

<style lang="less" scoped>
.ml8 {
  margin-left: 8px;
}
</style>
