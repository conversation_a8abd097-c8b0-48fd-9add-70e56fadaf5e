<template>
  <Modal
    :value="value"
    class="distribute-modal"
    width="600"
    @on-visible-change="handleVisibleChange"
    :mask-closable="false"
    lock-scroll
  >
    <div class="header-box" slot="header">
      <div class="title">发放卡券</div>
      <div class="tips">选择一名用户方可发放优惠券</div>
    </div>
    <div class="content-box">
      <div class="form-box flex">
        <Input size="large" placeholder="请输入用户名" class="flex-1" style="max-width: none" />
        <Button type="primary" size="large">匹配</Button>
      </div>
      <div class="card-box">
        <div v-for="item in [1, 2, 3, 4]" class="cart-item-box">
          <div class="card-item flex">
            <div class="flex flex-item-align">
              <img width="44" height="44" style="object-fit: cover" src="" />
              <div class="card-info">
                <div class="name">张三</div>
                <div class="phone">13888888888</div>
              </div>
            </div>

            <div class="check-box" style="justify-self: flex-end">
              <div class="check-box-icon">
                <svg-icon iconClass="checked" class="check-icon"></svg-icon>
<!--                <div class="check-circle"></div>-->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="footer" slot="footer">
      <Button>取消</Button>
      <Button type="primary">立即发放</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'DistributeCoupon',
  mixins: [],

  components: {},
  model: {
    prop: 'value',
    event: 'update:visible'
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    content: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      currentTime: null
    };
  },

  computed: {
    getPreviewContent() {
      return this.content;
      // return this.content.replace(/<p>&nbsp;<\/p>/g,'')
    }
  },

  watch: {
    // content: {
    //   handler(val) {
    //     console.log("-> %c val  === %o", "font-size: 15px;color: green;", val)
    //     // this.$nextTick(() => {
    //     //
    //     //   this.$refs.contentInner = val
    //     // })
    //   },
    //   immediate: true,
    // }
  },

  created() {},

  mounted() {},

  methods: {
    handleVisibleChange(visible) {
      !visible && this.closeModal();
    },
    closeModal() {
      this.$emit('update:previewVisible', false);
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
.distribute-modal {
  .header-box {
    display: flex;
    align-items: center;
    line-height: 20px;

    .title {
      font-size: 14px;
      font-weight: 500;
      color: #000000;
      margin-right: 8px;
    }

    .tips {
      font-size: 12px;
      color: #e5634b;
    }
  }

  .content-box {
    padding: 20px;
    background: #f9fafc;
    border-radius: 4px;
    margin: 4px;
    min-height: 300px;

    .card-box {
      display: flex;
      flex-flow: row wrap;
      margin-top: 16px;

      .cart-item-box {
        flex: 0 0 50%;
        max-width: 50%;
        box-sizing: border-box;
        margin-bottom: 12px;

        &:nth-child(odd) {
          padding-right: 16px;
        }

        .card-item {
          box-shadow: 0px 0px 4px 0px #dcdcdc;
          border-radius: 8px;
          padding: 16px 12px;
          background: #fff;
          border: 1px solid transparent;
          align-items: center;
          justify-content: space-between;

          &:hover {
            border: 1px solid #1157e5;
            background: #e7ecfa;
          }

          .card-info {
            margin-left: 8px;

            .name {
              font-weight: 500;
              color: #000000;
              line-height: 16px;
            }

            .phone {
              color: #999999;
              line-height: 16px;
              margin-top: 2px;
            }
          }

          .check-box {
            .check-box-icon {
              .check-icon {
                font-size: 20px;
              }

              .check-circle {
                width: 18px;
                height: 18px;
                border-radius: 50%;
                border: 1px solid #1157e5;
                margin-right: 0;
              }
            }
          }
        }
      }
    }
  }
}
</style>
