<template>
  <div class="coupon-wrap">
    <Form
      ref="couponForm"
      :model="formData"
      label-colon
      :label-width="100"
      class="mt-10"
      style="width: 680px; padding-bottom: 40px"
      :rules="couponRules"
      :disabled="!!coupon_id"
    >
      <FormItem prop="name" label="卡券名称">
        <Input v-model="formData.name" placeholder="请输入卡券名称" maxlength="20" show-word-limit />
      </FormItem>
      <FormItem label="卡券类型">
        <div class="flex flex-c">
          <RadioGroup v-model="formData.type" @on-change="typeChange">
            <Radio label="1">满减券</Radio>
            <Radio label="2">立减券</Radio>
            <Radio label="3">折扣券</Radio>
            <Radio label="4">体验券</Radio>
          </RadioGroup>
          <p class="normal-tip mb-0">
            {{ getTypeTips }}
          </p>
          <div class="coupon-info">
            <div class="coupon-item flex" v-show="formData.type === '1'">
              订单满
              <InputNumber
                class="ml-2 mr-2"
                :min="0"
                :precision="2"
                :active-change="false"
                width="120px"
                placeholder="请输入金额"
                v-model="formData.type_rule.full_amount"
              />
              元， 立减

              <InputNumber
                class="ml-2 mr-2"
                :min="0"
                :precision="2"
                :active-change="false"
                width="120px"
                placeholder="请输入金额"
                v-model="formData.type_rule.instant_discount"
              />
              元
            </div>
            <div class="coupon-item" v-show="formData.type === '2'">
              扣减
              <InputNumber
                class="ml-2 mr-2"
                :active-change="false"
                :min="0"
                :precision="2"
                width="120px"
                placeholder="请输入金额"
                v-model="formData.type_rule.sub_amount"
              />
              元
            </div>
            <div class="coupon-item" v-show="formData.type === '3'">
              订单打折
              <InputNumber
                class="ml-2 mr-2"
                :precision="0"
                v-model="formData.type_rule.discount"
                :active-change="false"
                width="120px"
                :min="1"
                :max="99"
                placeholder="请输入折扣"
              />
              %
            </div>
            <div class="coupon-item" v-show="formData.type === '4'">
              指定产品/服务，按体验价
              <InputNumber
                class="ml-2 mr-2"
                :active-change="false"
                :min="0"
                :precision="2"
                width="120px"
                placeholder="请输入金额"
                v-model="formData.type_rule.experience_price"
              />
              元进行结算
            </div>
          </div>
        </div>
      </FormItem>
      <FormItem
        label="适用范围"
        prop="use_range_value"
        :rules="[{ required: true, message: getUseRangeTip, trigger: 'change' }]"
      >
        <div class="flex flex-c">
          <RadioGroup v-model="formData.use_range" @on-change="changeCouponType">
            <template v-if="formData.type !== '4'">
              <Radio label="1">付款渠道可用</Radio>
              <Radio label="2">商品类型可用</Radio>
            </template>
            <Radio label="3">商品可用</Radio>
            <Radio label="4" v-if="formData.type === '4'">服务可用</Radio>
          </RadioGroup>
          <p class="normal-tip mb-0">
            {{ getScopeTips }}
          </p>
          <div class="coupon-info">
            <div class="coupon-item" v-if="formData.use_range === '1'">
              <div class="flex">
                <span class="mr-12">付款渠道</span>
                <Select placeholder="请选择付款渠道" style="width: 200px" v-model="formData.use_range_value">
                  <Option
                    v-for="chanel in pay_channel"
                    :key="chanel.id"
                    :value="chanel.id"
                    :label="chanel.desc"
                  ></Option>
                </Select>
              </div>
            </div>
            <div class="coupon-item" v-else-if="formData.use_range === '2'">
              <div class="flex">
                <span class="mr-12">商品种类</span>
                <Select placeholder="请选择商品种类" style="width: 200px" v-model="formData.use_range_value">
                  <Option v-for="type in goodsTypes" :key="type.id" :value="type.id" :label="type.desc"></Option>
                </Select>
              </div>
            </div>
            <div class="coupon-item" v-else-if="formData.use_range === '3'">
              <div class="goods-list-wrapper" v-if="selectedGoodsInfo.id">
                <div class="goods-item flex">
                  <div class="flex goods-info">
                    <img
                      width="42"
                      height="42"
                      style="object-fit: cover"
                      :src="selectedGoodsInfo.main_img | imageStyle"
                      alt="goods_img"
                    />
                    <div class="ml-8">
                      <div class="goods-name">
                        {{ selectedGoodsInfo.name }}
                        <span class="goods-tag" v-if="selectedGoodsInfo.goods_type_text">{{
                          selectedGoodsInfo.goods_type_text
                        }}</span>
                      </div>
                      <div class="price-info">
                        <span class="mr-8">￥{{ selectedGoodsInfo.price }}</span>
                        <span v-if="selectedGoodsInfo.stored_price">储值价 ￥{{ selectedGoodsInfo.stored_price }}</span>
                      </div>
                    </div>
                  </div>
                  <a style="align-self: center; margin-left: 8px" @click="removeGoods" v-if="!coupon_id">移除</a>
                </div>
              </div>
              <a class="ml-8 mt-8" @click="goodsSelectVisible = true" v-if="!coupon_id">{{
                selectedGoodsInfo.id ? '重新选择商品' : '+ 添加商品'
              }}</a>
            </div>
            <div class="coupon-item" v-else-if="formData.use_range === '4'">
              <div class="services-list-wrapper" v-if="selectedServices.length > 0">
                <div class="service-item flex" v-for="service in selectedServices" :key="service.id">
                  <div class="service-info">
                    <div class="service-name">
                      <span>{{ service.name }}</span>
                      <span class="mr-8" v-if="service.price">￥{{ service.price }}</span>
                    </div>
                    <div class="price-info">
                      <span class="service-tag" v-if="service.serv_type_text">{{ service.serv_type_text }}</span>
                      <span class="service-tag" v-if="service.source_platform_text">{{
                        service.source_platform_text
                      }}</span>
                    </div>
                  </div>
                  <a style="align-self: center; margin-left: 8px" @click="removeService(service.id)" v-if="!coupon_id"
                    >移除</a
                  >
                </div>
              </div>
              <a class="ml-8 mt-8" @click="servicesSelectVisible = true" v-if="!coupon_id">{{
                selectedServices.length > 0 ? '重新选择服务' : '+ 添加服务'
              }}</a>
            </div>
          </div>
        </div>
      </FormItem>
      <FormItem label="限制类型">
        <RadioGroup v-model="formData.limit_type">
          <Radio label="1"> 互斥 </Radio>
          <div class="normal-tip">互斥：不可以与其他优惠券在同一个订单中叠加使用</div>
          <!--          <Radio class="mt-8" label="part"-->
          <!--            >截止到-->
          <!--            <DatePicker type="date"></DatePicker>-->
          <!--            可用-->
          <!--          </Radio>-->
        </RadioGroup>
      </FormItem>
      <FormItem label="卡券有效期" prop="period">
        <RadioGroup v-model="formData.period_type" @on-change="changeRangeType">
          <Radio label="1"
            >领取后
            <InputNumber
              class="ml-2 mr-2"
              :min="0"
              :precision="0"
              :active-change="false"
              width="120px"
              placeholder="请输入天数"
              v-model="formData.period"
            />
            天内可用
          </Radio>
          <br />
          <!--          <Radio class="mt-8" label="part"-->
          <!--            >截止到-->
          <!--            <DatePicker type="date"></DatePicker>-->
          <!--            可用-->
          <!--          </Radio>-->
        </RadioGroup>
      </FormItem>
      <FormItem label="使用限制">
        <Checkbox :value="true" @click.native.prevent.stop>
          卡券仅限原价购买商品时可用，不与优惠和储值等一系列活动合并使用
        </Checkbox>
      </FormItem>
      <FormItem label="领取上限" prop="upper_limit">
        <InputNumber
          :min="0"
          :precision="0"
          v-model="formData.upper_limit"
          :active-change="false"
          style="width: 160px"
          :max="100"
          placeholder="请输入领取上限"
        />
        <div class="normal-tip">单个用户的卡券领取上限，最高为100</div>
      </FormItem>
      <FormItem label="卡券库存" prop="stock_num">
        <InputNumber
          :min="0"
          :precision="0"
          v-model="formData.stock_num"
          :active-change="false"
          style="width: 160px"
          placeholder="请输入卡券库存"
        />
        <div class="normal-tip">库存为0时，将变更为“失效”状态，需增加库存方可重新启用</div>
      </FormItem>
    </Form>

    <div class="fixed-bottom-wrapper">
      <back-button class="mr-12"></back-button>
      <Button type="primary" v-if="!coupon_id" @click="submitForm('couponForm')" :loading="submitLoading"
        >保存并发布
      </Button>
    </div>
    <k-goods-select v-model="goodsSelectVisible" @on-selected="selectGoods"></k-goods-select>
    <k-goods-services
      v-model="servicesSelectVisible"
      :optionsList="servicesOptions"
      :checkedService="selectedServices"
      @on-selected="selectServices"
    ></k-goods-services>
  </div>
</template>

<script>
import KGoodsSelect from '@/components/k-goods-select';
import KGoodsServices from '@/components/k-goods-services';
import { formatDate } from 'element-ui/src/utils/date-util';

export default {
  name: 'CouponDetail',
  components: {
    KGoodsSelect,
    KGoodsServices,
  },
  data() {
    return {
      formData: {
        name: '',
        type: '1', // 卡券类型（1：满减券，2q：立减，3：折扣）
        type_rule: {
          full_amount: null, //满
          sub_amount: null, //满减
          discount: null, //折扣
          instant_discount: null, //立减
          experience_price: null, // 体验价格
        },
        use_range: '1', // 适用范围
        use_range_value: '', // 适用范围对应的值
        limit_type: '1', // 限制类型1：互斥，2：同享）
        period_type: '1', // 卡券有效期类型（1：领取后N天内可用，2：截止到N日期可用 ）
        period: null, // 卡券有效期
        upper_limit: null, // 单个用户卡券领取上限
        stock_num: null, // 卡券库存
      },
      submitLoading: false,
      couponRules: {
        name: [
          { required: true, message: '请输入卡券名称', trigger: 'blur' },
          { type: 'string', max: 20, message: '最多输入20个字符', trigger: 'change' },
        ],
        type: [{ required: true, message: '请输入卡券名称', trigger: 'change' }],
        // type_rule: {
        //   full_amount: [{ required: true, message: '请输入满减金额', trigger: 'change,blur' }],
        //   sub_amount: [{ required: true, message: '请输入立减金额', trigger: 'change' }],
        //   discount: [{ required: true, message: '请输入折扣金额', trigger: 'change' }],
        //   instant_discount: [{ required: true, message: '请输入立减金额', trigger: 'change' }]
        // },
        period: [{ required: true, type: 'number', message: '请输入卡券有效期', trigger: 'change' }],
        upper_limit: [{ required: true, type: 'number', message: '请输入领取上限', trigger: 'change' }],
        stock_num: [{ required: true, type: 'number', message: '请输入卡券库存', trigger: 'change' }],
      },
      goodsSelectVisible: false,
      servicesSelectVisible: false,
      goodsTypes: [],
      pay_channel: [],
      selectedGoodsInfo: {},
      selectedServices: [],
      servicesOptions: {
        servTypeDesc: [],
        sourcePlatformDesc: [],
      },
      coupon_id: '',
      getUseRangeTip: '请选择付款渠道',
    };
  },
  watch: {},
  computed: {
    getTypeTips() {
      switch (this.formData.type) {
        case '1':
          return `满减券：当订单价格≥N时，可以按固定金额进行扣减`;
        case '2':
          return `立减券：当订单价格≥优惠金额时，按优惠金额进行扣减`;
        case '3':
          return `折扣券：按商品价格的百分比进行扣减`;
        case '4':
          return `单品券：指定商品/服务按照一口价进行优惠`;
        default:
          return '';
      }
    },
    getScopeTips() {
      switch (this.formData.use_range) {
        case '1':
          return `付款渠道可用：通过某种付款渠道支付可以使用该卡券`;
        case '2':
          return `商品类型可用：当前某些品类可以使用该卡券`;
        case '3':
          return `商品可用：当前某个商品可以使用该卡券`;
        case '4':
          return `服务可用：当前选中的服务可用该卡券（服务类支持多选）`;
        default:
          return '';
      }
    },
    // getUseRangeTip() {
    //   switch (this.formData.use_range) {
    //     case '1':
    //       return `请选择付款渠道`;
    //     case '2':
    //       return `请选择商品种类`;
    //     case '3':
    //       return `请选择商品`;
    //     default:
    //       return '';
    //   }
    // }
  },
  created() {
    this.getFormOptions();
    this.$router.onReady(() => {
      this.coupon_id = this.$route.query.id;
      this.coupon_id && this.getCouponDetail();
    });
  },
  methods: {
    typeChange(val) {
      if (val === '4') {
        this.formData.use_range = '3';
        this.formData.use_range_value = '';
      } else {
        this.formData.use_range = '1';
      }
      this.getUseRangeTip = '';
    },
    removeGoods() {
      this.selectedGoodsInfo = {};
    },
    removeService(serviceId) {
      this.selectedServices = this.selectedServices.filter(service => service.id !== serviceId);
      this.updateServiceRangeValue();
    },
    updateServiceRangeValue() {
      if (this.selectedServices.length > 0) {
        this.formData.use_range_value = this.selectedServices.map(service => service.id).join(',');
      } else {
        this.formData.use_range_value = '';
      }
      this.$nextTick(() => {
        this.$refs.couponForm.validateField('use_range_value');
      });
    },
    getCouponDetail(id) {
      this.$api.getCouponInfo({ id: this.coupon_id }).then(res => {
        console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);
        this.formData = res;
        console.log('=>(detail.vue:428) this.formData', this.formData);
        this.formData.type_rule.discount = +res.type_rule.discount;
        this.formData.type_rule.full_amount = +res.type_rule.full_amount;
        this.formData.type_rule.instant_discount = this.formData.type === '1' ? +res.type_rule.sub_amount : null;
        this.formData.type_rule.experience_price =
          this.formData.type === '4' ? Number(res.type_rule.experience_price || 0) : null;
        this.formData.type_rule.sub_amount = +res.type_rule.sub_amount;
        this.formData.upper_limit = +res.upper_limit;
        this.formData.stock_num = +res.stock_num;
        this.formData.period = +res.period;
        console.log('-> %c this.formData.  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.formData);
        this.selectedGoodsInfo = res.goods || {};
        this.selectedServices = res.services || [];
      });
    },
    changeCouponType(type) {
      console.log('-> %c type  ===    %o', 'font-size: 15px;color: #fa8c16 ;', type);
      this.formData.use_range_value = '';
      this.selectedGoodsInfo = {};
      this.selectedServices = [];
      if (type === '1') {
        this.getUseRangeTip = '请选择付款渠道';
      } else if (type === '2') {
        this.getUseRangeTip = '请选择商品种类';
      } else if (type === '3') {
        this.getUseRangeTip = '请选择商品';
      } else if (type === '4') {
        this.getUseRangeTip = '请选择服务';
      }
      this.$nextTick(() => {
        this.$refs.couponForm.validateField('use_range_value');
      });
    },
    //提交表单
    submitForm(formEl) {
      this.$refs[formEl].validate(valid => {
        console.log('-> %c valid  ===    %o', 'font-size: 15px;color: #fa8c16 ;', valid);
        console.log(this.formData);
        if (valid) {
          const params = {
            ...this.formData,
          };
          if (this.formData.type === '1') {
            if (!this.formData.type_rule.full_amount || !this.formData.type_rule.instant_discount) {
              this.$Message.error('请输入满减条件');
              return;
            }
            params.type_rule = {
              full_amount: this.formData.type_rule.full_amount,
              sub_amount: this.formData.type_rule.instant_discount,
              discount: '',
            };
          }
          if (this.formData.type === '2' && !this.formData.type_rule.sub_amount) {
            this.$Message.error('请输入立减金额');
            return;
          }
          if (this.formData.type === '3' && !this.formData.type_rule.discount) {
            this.$Message.error('请输入折扣');
            return;
          }
          if (this.formData.type === '4' && !this.formData.type_rule.experience_price) {
            this.$Message.error('请输入体验价');
            return;
          }
          if (this.formData.type === '4' && this.formData.use_range === '4' && this.selectedServices.length === 0) {
            this.$Message.error('请选择服务');
            return;
          }

          this.submitLoading = true;
          this.$api
            .createCoupon(params)
            .then(
              res => {
                this.$Message.success('创建成功');
                this.$router.push({ path: '/store/coupon/list' });
              },
              err => {
                {
                }
              }
            )
            .finally(() => {
              this.submitLoading = false;
            });
        } else {
          this.$Message.error('请完善表单内容');
        }
      });
    },
    changeRangeType() {},
    // 选择商品
    selectGoods(item) {
      console.log('-> %c item  ===    %o', 'font-size: 15px;color: #fa8c16 ;', item);
      this.selectedGoodsInfo = item;
      this.formData.use_range_value = item.id;
      console.log(
        '-> %c this.formData.use_range_value  ===    %o',
        'font-size: 15px;color: #fa8c16 ;',
        this.formData.use_range_value
      );
      this.goodsSelectVisible = false;
      this.$nextTick(() => {
        this.$refs.couponForm.validateField('use_range_value');
      });
    },
    // 选择服务
    selectServices(items) {
      console.log('-> %c selected services  ===    %o', 'font-size: 15px;color: #fa8c16 ;', items);
      this.selectedServices = items;
      this.updateServiceRangeValue();
      this.servicesSelectVisible = false;
    },
    getFormOptions() {
      console.log(213123);
      this.$api.getCouponOption().then(res => {
        console.log('-> %c res  ===    %o', 'font-size: 15px;color: #fa8c16 ;', res);
        this.pay_channel = this.descToArrHandle(res.pay_channel);
        this.goodsTypes = this.descToArrHandle(res.goods_type);
      });
      // 获取服务选项
      this.getServicesOptions();
    },
    getServicesOptions() {
      this.$api.getIndexOptions().then(res => {
        this.servicesOptions.servTypeDesc = this.descToArrHandle(res.servTypeDesc);
        this.servicesOptions.sourcePlatformDesc = this.descToArrHandle(res.sourcePlatformDesc);
      });
    },
    descToArrHandle(obj) {
      let arr = [];
      let kArr = Object.keys(obj);
      kArr.map((item, i) => {
        arr.push({
          id: item,
          desc: obj[item].desc || '',
          kw: obj[item].kw || '',
          ...obj[item],
        });
      });
      return arr;
    },
  },
};
</script>

<style lang="less" scoped>
.normal-tip {
  color: #ccc;
  //line-height: 17px;
}

.goods-list-wrapper,
.services-list-wrapper {
  &::-webkit-scrollbar {
    display: none;
  }

  .goods-item,
  .service-item {
    margin-bottom: 8px;

    .goods-info,
    .service-info {
      padding: 10px;
      border-radius: 2px;
      background: #fafafa;
      min-width: 360px;

      .price-info {
        color: #999999;
        font-size: 12px;
        margin-top: 4px;
        margin-left: -4px;
        height: 16px;
        margin-bottom: 10px;
      }

      .goods-name,
      .service-name {
        font-weight: 500;
        color: #333333;
        line-height: 16px;
        margin-right: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .goods-tag,
      .service-tag {
        transform: scale(0.8);
        transform-origin: left bottom;
        display: inline-block;
        color: #999999;
        border: 1px solid #e0e0e0;
        padding: 3px 6px;
        line-height: 1;
        border-radius: 10px;
      }
    }
  }
}

.services-list-wrapper {
  max-height: 300px;
  overflow-y: auto;

  .service-item {
    margin-bottom: 8px;

    .service-card {
      position: relative;
      padding: 10px;
      border: 1px solid #e8e8e8;
      border-radius: 2px;
      background: #fff;
      min-width: 360px;

      .service-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;

        .service-name {
          font-size: 16px;
          font-weight: 500;
          color: #333333;
          line-height: 22px;
          flex: 1;
        }

        .service-price {
          font-size: 16px;
          font-weight: 500;
          color: #333333;
          line-height: 22px;
        }
      }

      .service-tags {
        display: flex;
        gap: 8px;

        .service-tag {
          padding: 2px 8px;
          background: #f5f5f5;
          border-radius: 2px;
          font-size: 12px;
          color: #999999;
          line-height: 16px;
        }
      }

      .remove-btn {
        position: absolute;
        top: 16px;
        right: 16px;
        color: #999999;
        font-size: 12px;
        text-decoration: none;

        &:hover {
          color: #f65554;
        }
      }
    }
  }
}
</style>
