<template>
  <Modal :value="value"
         title="选择商品"
         :mask-closable="false"
         :width="900"
         footer-hide
         @on-visible-change="visibleChange"
  >
    <div class="flex flex-item-l-end">
      <div>

        <Select v-model="queryFormData.goods_type" clearable placeholder="请选择商品类型" @on-change="onSearch" style="width:200px">
          <Option v-for="(item,id) in goodsTypeDesc" :key="id" :value="id">{{ item.desc }}</Option>
        </Select>
        <dvd></dvd><dvd></dvd>

        <Select v-model="queryFormData.source_platform" clearable placeholder="请选择商品来源" @on-change="onSearch" style="width:200px">
          <Option v-for="(item,id) in sourcePlatformDesc" :key="id" :value="id">{{ item.desc }}</Option>
        </Select>
        <dvd></dvd><dvd></dvd>
        <Input v-model="queryFormData.name" placeholder="输入商品名称搜索"
               clearable
               @keyup.enter.native="onSearch" @on-clear="onSearch"
               style="width: 180px">
          <Icon type="ios-search" slot="suffix" />
        </Input>
      </div>
      <dvd></dvd>
      <Button type="primary" @click="onSearch">筛选</Button>
    </div>
    <div class="block_10"></div>
    <div>
      <Table ref="selection" :height="500" :columns="tableCols" :data="list" :loading="tableLoading">
<!--        售价-->
        <template slot-scope="{row}" slot="price_section">
          <p v-if="!row.price_section.max">￥{{row.price_section.min}}</p>
          <p v-else>￥{{row.price_section.min}} - ￥{{ row.price_section.max }}</p>
        </template>

        <template slot-scope="{row}" slot="status">
          {{statusDesc[row.status].desc}}
        </template>

        <template slot-scope="{row}" slot="create_time">
          {{row.create_time|data_format}}
        </template>

        <template slot-scope="{row}" slot="action">
          <a @click="selectItem(row)">选中</a>
          <dvd></dvd>
          <dvd></dvd>
          <dvd></dvd>
          <KLink :to="{path: '/goods/item/edit', query: {id: row.id}}" target="_blank">详情</KLink>
        </template>
      </Table>

      <div class="block_20"></div>
      <KPage v-if="total > 0" :total="+total"
             :page-size="queryFormData.pageSize"
             :page-size-opts="[20]"
             :current="queryFormData.page"
             @on-change="handleCurrentChange"
             @on-page-size-change="handleSizeChange"
             style="text-align:center"
      />
    </div>
  </Modal>
</template>

<script>

let init_query_from_data = {
  page: 1,
  pageSize: 20,
  status: 200,
  goods_type: '',
  source_platform: '', // 商品来源
  name: '',
}

export default {
  name: "goods-modal",
  props: {
    value: {
      type: Boolean,
      default: false
    },
  },

  data() {
    return {
      queryFormData: {...init_query_from_data},
      modalValue: false,

      tableCols: [
        {title: '商品编号',align: 'center', key: 'id'},
        {title: '商品名',align: 'center', key: 'name'},
        {title: '商品类型',align: 'center', key: 'goods_type_text'},
        {title: '商品来源',align: 'center', key: 'source_platform_text'},
        {title: '售价',align: 'center', slot: 'price_section',},
        {title: '状态',align: 'center', slot: 'status',},
        {title: '创建时间',align: 'center',  slot: 'create_time', width: 160},
        {title: '操作',align: 'center', slot: 'action'},
      ],
      tableLoading: false,
      list: [],
      total: 0,
      selected_items: {},
      statusDesc: {},
      goodsTypeDesc: {},
      sourcePlatformDesc: {}, // 商品来源枚举
    }
  },

  created() {
    // this.getGoodsList()
  },

  methods: {
    visibleChange(val){
      if (val) {
        this.$api.getIndexOptions().then(res=>{
          this.goodsTypeDesc = res.goodsTypeDesc
          this.sourcePlatformDesc = res.sourcePlatformDesc
        })
        this.clearQuery()
        // 选中渲染
        this.getGoodsList()
      }else{
        this.cancel()
      }
    },

    cancel(){
      this.$emit('input', false)
    },

    onSearch() {
      this.queryFormData.page = 1
      this.getGoodsList()
    },

    handleSizeChange( val ) {
      this.queryFormData.page = 1
      this.queryFormData.pageSize = val
      this.getGoodsList()
    },
    handleCurrentChange( val ) {
      this.queryFormData.page = val
      this.getGoodsList()
    },

    onConfirm() {
      let items = []
      for (let key in this.selected_items) {
        items.push({
          id: this.selected_items[key].id,
          title: this.selected_items[key].title
        })
      }
      this.$emit('on-selected', items)
    },

    // api - 获取商品列表
    getGoodsList() {
      this.tableLoading = true
      let params = {...this.queryFormData}
      this.$api.getGoodsList(params).then(data => {
        this.list = data.goods_items
        this.statusDesc = data.statusDesc
        this.total = data.total
      }).catch(error => {
        {}
      }).finally(() => this.tableLoading = false)
    },

    // 重置数据
    clearQuery () {
      this.queryFormData = {...init_query_from_data}
      this.queryFormData.page = 1
      this.list = []
      this.total = 0
      this.selected_items = {}
    },
    selectItem(row){
      console.log("-> row", row);
      this.$emit('selectGood', row)
      this.cancel()
    }
  },

  watch: {}
}
</script>

<style lang="less" scoped>

.disabled{
  cursor: not-allowed;
  color: #999999;
}
</style>
