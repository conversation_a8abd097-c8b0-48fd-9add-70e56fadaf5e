<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <div class="flex list-fn-mb-distance">
          <Button type="primary" @click="create">创建储值活动</Button>
        </div>
        <!-- 筛选条件 -->
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <Row :gutter="24">
            <Col span="24">
              <FormItem label="">
                <Input type="text" placeholder="请输入活动名称" v-model="queryFormData.keyword" />
              </FormItem>

              <FormItem label="">
                <Select v-model="queryFormData.status" placeholder="状态" clearable>
                  <Option v-for="desc_item in status_desc" :key="desc_item.id" :value="desc_item.id">{{
                    desc_item.desc
                  }}</Option>
                </Select>
              </FormItem>

              <FormItem label="">
                <DatePicker
                  type="daterange"
                  placeholder="活动时间"
                  :value="timeRange"
                  clearable
                  @on-change="times => handleTimeChange(times)"
                ></DatePicker>
              </FormItem>

              <FormItem>
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>
      </template>
      <!-- 活动时间 -->
      <template slot-scope="{ row }" slot="time_type_text">
        <p v-if="row.time_type == 'FOREVER'">{{ row.time_type_text }}</p>
        <div v-else>
          <p class="flex">
            <span class="slot-label-create">开始时间：</span>{{ row.begin_time | data_format('YYYY-MM-DD HH:mm') }}
          </p>
          <p class="flex">
            <span class="slot-label-create">结束时间：</span>{{ row.end_time | data_format('YYYY-MM-DD HH:mm') }}
          </p>
        </div>
      </template>

      <!-- 储值人数 -->
      <template slot-scope="{ row }" slot="cv">
        <p>{{ row.recharge.cv }}</p>
      </template>

      <!-- 实充金额 -->
      <template slot-scope="{ row }" slot="real_fee">
        <p>{{ row.recharge && row.recharge.real_fee ? `￥${row.recharge.real_fee}` : '-' }}</p>
      </template>

      <!-- 赠送金额 -->
      <template slot-scope="{ row }" slot="given_fee">
        <p>{{ row.recharge && row.recharge.given_fee ? `￥${row.recharge.given_fee}` : '-' }}</p>
      </template>

      <!-- 总储值金额 -->
      <template slot-scope="{ row }" slot="total_fee">
        <p>{{ row.recharge && row.recharge.total_fee ? `￥${row.recharge.total_fee}` : '-' }}</p>
      </template>

      <!-- 累计赠送商品金额 -->
      <template slot-scope="{ row }" slot="total_goods_amount">
        <p>{{ row.total_goods_amount ? `￥${row.total_goods_amount}` : '-' }}</p>
      </template>

      <!-- 创建信息 -->
      <template slot-scope="{ row }" slot="operate_info">
        <p class="flex">
          <span class="slot-label-create">创建人：</span>
          {{ (row.operate_info && row.operate_info.operator) || '-' }}
        </p>
        <p class="flex">
          <span class="slot-label-create">创建时间：</span>
          {{ row.operate_info && row.operate_info.time | data_format('YYYY-MM-DD HH:mm') }}
        </p>
      </template>

      <template slot-scope="{ row }" slot="operate">
        <a @click="edit(row)" v-if="row.status !== 'HAS_MANUAL_END' && row.status !== 'HAS_AUTO_END'">编辑</a>
        <a class="ml10" @click="stop(row)" v-if="row.status == 'UNDERWAY'">停止</a>
        <a class="ml10" @click="toDetail(row)">详情</a>
      </template>
    </standard-table>

    <ConfirmModal
      content="是否确认停止储值有礼业务?"
      contentText="停止后用户无法继续在该活动下充值，已储值用户可正常消费"
      :confirmVisible.sync="confirmVisible"
      @ok="confirmFn"
    ></ConfirmModal>
  </div>
</template>

<script>
import S from '@/libs/util'; // Some commonly used tools
import ConfirmModal from '@/components/confirmModal/confirmModal';
import search from '../../../mixins/search';
import StandardTable from '@/components/StandardTable/index.vue';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  keyword: '', // 活动名称
  status: '', // 状态
  st: '',
  et: '',
  r: '',
};
export default {
  name: 'list',
  components: {
    StandardTable,
    ConfirmModal,
  },
  mixins: [search],
  props: {},
  data() {
    return {
      queryFormData: { ...init_query_form_data },
      apiName: 'getActivityList',
      tableCols: [
        { title: '活动名称', key: 'name', minWidth: 120 },
        { title: '活动时间', slot: 'time_type_text', minWidth: 210 },
        { title: '优先级', key: 'sort', minWidth: 100 },
        { title: '储值人数', slot: 'cv', minWidth: 100 },
        { title: '累计实充金额', slot: 'real_fee', minWidth: 100 },
        { title: '累计赠送金额', slot: 'given_fee', minWidth: 100 },
        { title: '总储值金额', slot: 'total_fee', minWidth: 100 },
        { title: '累计赠送商品的数量', key: 'total_goods_num', minWidth: 140 },
        { title: '累计赠送商品的金额', slot: 'total_goods_amount', minWidth: 140 },
        { title: '创建人/创建时间', slot: 'operate_info', minWidth: 210 },
        { title: '状态', key: 'status_text', minWidth: 100 },
        { title: '操作', slot: 'operate', align: 'center', fixed: 'right', width: 120 },
      ],
      tableLoading: false,
      total: 0,
      list: [],

      confirmVisible: false, // 停止提示框

      status_desc: [], // 状态枚举

      id: null,
    };
  },
  computed: {},
  watch: {
    confirmVisible(val) {
      if (!val) {
        this.id = null;
      }
    },
  },
  created() {
    this.getActivityOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.submitQueryForm();
    },

    onResetSearch: function () {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.submitQueryForm();
    },

    // 创建储值活动
    create() {
      this.$router.push({ path: '/store/stored/created' });
    },

    stop(row) {
      this.confirmVisible = true;
      this.id = row.id;
    },

    // 编辑
    edit(row) {
      this.$router.push({
        path: '/store/stored/created',
        query: { id: row.id },
      });
    },

    // 详情
    toDetail(row) {
      this.$router.push({
        path: '/store/stored/created',
        query: { id: row.id, type: 'detail' },
      });
    },

    /* api-获取状态 */
    getActivityOptions() {
      this.$api.getActivityOptions().then(res => {
        this.status_desc = S.descToArrHandle(res.statusDesc);
      });
    },

    /* api-停止 */
    confirmFn() {
      const { id } = this;
      this.$api
        .getActivityHasmanualend({ id })
        .then(
          () => {
            this.getsList();
          },
          () => {}
        )
        .finally(() => {
          this.confirmVisible = false;
        });
    },
  },
  filters: {},

  beforeRouteUpdate: function (to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
};
</script>

<style lang="less" scoped>
p {
  margin: 0;
}
.mr10 {
  margin-right: 10px;
}
.ml10 {
  margin-left: 10px;
}
.slot-label-create {
  width: 70px;
  min-width: 70px;
  text-align: right;
  color: #aaa;
}
</style>
