<template>
  <div>
    <standard-table
      :loading="tableLoading"
      :columns="tableCols"
      :data="list"
      @on-sort-change="sortChange"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
    >
      <template #header>
        <Form inline :label-width="0" @submit.native.prevent @keyup.enter.native="onSearch">
          <Row>
            <Col>
              <FormItem>
                <Input v-model="queryFormData.name" placeholder="请输入活动名称" clearable />
              </FormItem>
            </Col>
            <Col>
              <FormItem>
                <Select v-model="queryFormData.status" placeholder="全部状态" clearable>
                  <Option v-for="item in statusList" :value="item.id" :key="item.id">{{ item.desc }}</Option>
                </Select>
              </FormItem>
            </Col>
            <FormItem>
              <DatePicker
                type="daterange"
                clearable
                :options="disabledTime"
                format="yyyy-MM-dd"
                placeholder="活动时间"
                v-model="timeRange"
                @on-change="times => handleTimeChange(times)"
                class="time-range"
              ></DatePicker>
            </FormItem>
            <Col>
              <FormItem style="text-align: left">
                <Button type="primary" class="mr10" @click="onSearch">筛选</Button>
                <span class="list-reset-btn" @click="onResetSearch">
                  <svg-icon class="reset-icon" iconClass="btn-clear"></svg-icon>
                  <span>清除条件</span>
                </span>
              </FormItem>
            </Col>
          </Row>
        </Form>
        <div>
          <div class="flex flex-item-between">
            <div class="panel-nav">
              <a class="nav" :class="{ active: !$route.query.status }" @click.prevent.capture="onStatusChange('')">
                全部
              </a>
              <a
                :class="{ active: $route.query.status == item.id }"
                class="nav"
                v-for="item in statusList"
                :key="item.id"
                @click.prevent.capture="onStatusChange(item.id)"
              >
                {{ item.desc }}
                <Tag :color="getTagColor(item.id)" v-show="statusCount[item.id]">
                  {{ statusCount[item.id] }}
                </Tag>
              </a>
            </div>
          </div>
        </div>
      </template>
      <!-- demo1 -->
      <template slot-scope="{ row }" slot="activity_time">
        {{ row.buy_st | data_format }} ~ {{ row.buy_et | data_format }}
      </template>
      <!-- demo1 -->
      <template slot-scope="{ row }" slot="status_text">
        <status-text :status="row.status">{{ row.status_text }}</status-text>
      </template>
      <!--操作记录-->
      <template slot-scope="{ row }" slot="action_log">
        <div style="text-align: left">
          <p>操作人：{{ row.op_info.operator }}</p>
          <p>操作时间：{{ row.op_info.op_time | date_format('YYYY.MM.DD HH:mm') }}</p>
        </div>
      </template>

      <!-- demo1 -->
      <template slot-scope="{ row }" slot="action">
        <a class="ml8" @click="checkOrder(row)">查看订单</a>
        <a class="ml8" @click="toDetail(row)">详情</a>
      </template>
    </standard-table>
  </div>
</template>

<script>
import search from '@/mixins/search';
import S from '@/libs/util';
import StandardTable from '@/components/StandardTable/index.vue';
const init_query_form_data = {
  page: 1,
  pageSize: 20,
  name: '',
  status: '',
  r: '',
};
export default {
  name: 'Promotion-list',
  components: { StandardTable },
  mixins: [search],
  props: {},
  data() {
    return {
      apiName: 'getPromotionList',
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        },
      },
      queryFormData: {
        ...init_query_form_data,
      },
      statusList: [], // 状态列表
      tableCols: [
        { title: '活动编号', key: 'id', align: 'center' },
        { title: '活动名称', key: 'name', align: 'center' },
        { title: '活动简介', key: 'desc', align: 'center', width: 120 },
        { title: '类型', key: 'type_text', align: 'center' },
        { title: '活动时间', slot: 'activity_time', align: 'center', width: 260 },
        { title: '状态', slot: 'status_text', align: 'center' },
        { title: '操作', slot: 'action', align: 'center', width: 140 },
      ],
      timeRange: [],
      confirmVisible: false,
      statusCount: {},
      editId: '',
    };
  },
  computed: {
    getTagColor() {
      return status => {
        switch (status) {
          case 'UNDERWAY':
            return 'success';
          case 'SOLD_OUT':
            return 'error';
          case 'WAIT':
            return 'warning';
          default:
            return 'default';
        }
      };
    },
  },
  watch: {},
  created() {
    this.getOptions();
    this.queryFormData = S.merge(this.queryFormData, this.$route.query);
    this.submitQueryForm(true);
  },
  mounted() {},
  methods: {
    endActivity(id) {
      this.editId = id;
      this.confirmVisible = true;
    },
    // 跳转关联订单页
    checkOrder(row) {
      this.$router.push({
        path: '/purchase/order/list',
        query: {
          promotion_type: 'CLI_PLAT_SECKILL',
          promotion_id: row.id,
        },
      });
    },
    confirmEndActivity() {
      const params = {
        id: this.editId,
        act: 'END',
      };
      this.$api.setPromotionStatus(params).then(
        () => {
          this.$Message.success('结束活动成功');
          this.loadList();
        },
        () => {}
      );
    },
    // 创建活动
    createActivity() {
      this.$router.push({ path: '/miniprogram/promotion/detail' });
    },
    getOptions() {
      this.$api.getPromotionOptions().then(res => {
        console.log('-> %c res  === %o', 'font-size: 15px;color: green;', res);
        this.statusList = S.descToArrHandle(res.statusDesc);
      });
    },
    // 状态切换
    onStatusChange(status) {
      this.queryFormData.page = 1;
      this.queryFormData.status = status;
      this.submitQueryForm();
    },

    // 重置
    onResetSearch() {
      this.queryFormData = { ...init_query_form_data };
      this.timeRange = [];
      this.submitQueryForm();
    },

    // 排序 asc/desc/normal
    sortChange(column, key, item) {
      console.log('column, key, item', column, key, item);
    },

    // 详情/编辑
    toDetail(row) {
      this.$router.push({
        path: '/store/activity/detail',
        query: {
          id: row.id,
        },
      });
    },
  },
  beforeRouteUpdate(to, from, next) {
    this.queryFormData = S.merge(init_query_form_data, to.query);
    this.getTimeRange();
    this.getsList();
    next();
  },
  filters: {},
};
</script>

<style lang="less" scoped>
.ml8 {
  margin-left: 8px;
}
</style>
