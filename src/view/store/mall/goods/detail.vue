<template>
  <div class="wrapper">
    <Form
      :model="formData"
      ref="formData"
      :label-width="90"
      :rules="ruleValidate"
      :disabled="!$route.query.isEdit && !!$route.query.id"
    >
      <FormItem label="节点名称" prop="title">
        <Input v-model="formData.title" placeholder="节点名称" style="width: 80%" :maxlength="15" show-word-limit />
      </FormItem>

      <FormItem label="节点排序值" prop="sort">
        <div class="carousel-order">
          <InputNumber v-model="formData.sort" placeholder="节点排序值" style="width: 300px" :max="99" />
          <span style="margin-left: 20px">(数字越大，展示顺序越靠前，最大值为99)</span>
        </div>
      </FormItem>
    </Form>

    <!--商品信息-->
    <div class="goods">
      <div class="block-header">商品信息</div>
      <div class="flex flex-item-between">
        <p class="goods-info-tip mb20">
          当前共{{ goodsList.length }}个商品，可以拖动改变其在小程序中的显示顺序，最多可添加 20 个商品
        </p>
        <p
          class="mb20 goods-info-delete flex flex-item-align"
          @click="removeAll"
          v-if="!(!$route.query.isEdit && !!$route.query.id)"
        >
          <svg-icon style="font-size: 20px" iconClass="delete"></svg-icon>
          移除全部
        </p>
      </div>
      <div class="goods-block">
        <draggable v-model="goodsList" filter=".add-goods" class="goods-block" :move="onMove">
          <div class="goods-block-item" v-for="(goods_item, index) in goodsList" :key="'goods' + index">
            <viewer :photo="[goods_item.main_img]" v-if="goods_item.main_img">
              <img class="goods-img" :src="goods_item.main_img | imageStyle" alt="" />
            </viewer>
            <p v-else class="goods-img">暂无商品图片</p>
            <p class="ecs ecs-2 goods-introduce">{{ goods_item.name || '-' }}</p>
            <div class="item-delete" @click="deleteItem(index)" v-if="!(!$route.query.isEdit && !!$route.query.id)">
              x
            </div>
          </div>
          <div
            class="goods-block-item add-goods"
            @click="addProduct"
            v-if="goodsList.length < 20 && !(!$route.query.isEdit && !!$route.query.id)"
          >
            <svg-icon style="font-size: 80px" iconClass="add-fill"></svg-icon>
            <p class="block-item-text">添加</p>
            <p class="block-item-num">{{ goodsList.length }} / 20</p>
          </div>
        </draggable>
      </div>
    </div>

    <div class="fixed-bottom-wrapper">
      <back-button></back-button>
      <Dvd />
      <Dvd />
      <Button
        v-show="$route.query.isEdit || !$route.query.id"
        type="primary"
        @click="handleSubmit()"
        :loading="submitLoading"
        >保存
      </Button>
    </div>

    <!--选择商品-->
    <goods-choose v-model="goodsVisible" @on-selected="selected" :selectedList="goodsList"></goods-choose>
  </div>
</template>

<script>
// 选择商品
import GoodsChoose from './components/GoodsChoose';
import moment from 'moment/moment';
import { number } from 'mathjs';

export default {
  name: 'detail',
  components: {
    GoodsChoose,
  },
  mixins: [],
  props: {},
  data() {
    const validatorOrder = (rule, value, callback) => {
      if (value === null) {
        callback(new Error('请输入节点排序值'));
      } else {
        if (value > 99) {
          callback(new Error('节点排序值不能超过99'));
        } else {
          callback();
        }
      }
    };
    return {
      formData: {
        title: '',
        sort: null,
        test: '',
      },
      submitLoading: false, // 保存的loading

      ruleValidate: {
        title: [{ required: true, message: '请输入节点名称', trigger: 'change' }],
        sort: [{ required: true, validator: validatorOrder, trigger: 'change' }],
      },

      goodsVisible: false, // 添加商品弹窗
      goodsList: [], // 商品节点中的商品
      cas_token: '',
    };
  },
  computed: {},
  watch: {},
  created() {
    const id = this.$route.query.id;
    if (id) {
      this.getDetail(id);
    }
  },
  mounted() {},
  methods: {
    //禁止拖动
    onMove(e) {
      if (!e.relatedContext.element) return false;
      return true;
    },

    // 添加商品
    addProduct() {
      this.goodsVisible = true;
    },

    // 选中的商品
    selected(list) {
      this.goodsList = this.goodsList.concat(list);
    },

    // 删除指定的商品
    deleteItem(index) {
      this.goodsList.splice(index, 1);
    },

    // 删除全部商品
    removeAll() {
      this.goodsList = [];
    },

    // 获取已选商品的id合计
    getGoodsId() {
      let ids = [];
      this.goodsList.forEach(item => {
        ids.push(item.id);
      });
      return ids;
    },

    // 获取节点详情
    getDetail(id) {
      this.$api
        .getGoodsNodeInfo({ id })
        .then(res => {
          let info = res.info;
          this.goodsList = info.goods_list;
          this.cas_token = info.cas_token;
          this.formData.title = info.title;
          this.formData.sort = Number(info.sort);
        })
        .catch(err => {
          {
          }
        });
    },

    // 保存
    handleSubmit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          let goods_ids = this.getGoodsId();
          console.log('-> ids', goods_ids);
          if (goods_ids.length == 0) {
            this.$Message.error('选择商品不可为空');
            return;
          }

          let params = {
            ...this.formData,
            id: this.$route.query.id,
            goods_ids: goods_ids,
            cas_token: this.cas_token,
          };
          this.$api.getGoodsNodeEdit(params).then(
            res => {
              this.$Message.success(this.$route.query.isEdit ? '编辑节点成功' : '新增节点图成功');
              // this.$router.push({
              //   path: '/store/mall/configuration?tab=1',
              // })
              this.$router.back();
            },
            err => {
              {
              }
            }
          );
        } else {
          this.$Message.error('请正确填写节点信息');
        }
      });
    },

    back() {
      // this.$router.push({
      //   path: '/store/mall/configuration?tab=1',
      // })
      this.$router.back();
    },
  },
  filters: {},
};
</script>

<style lang="less" scoped>
.wrapper {
  padding-bottom: 50px;
}

.goods {
  .goods-info-tip {
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    line-height: 16px;
  }

  .goods-info-delete {
    color: #145cd3;
    cursor: pointer;
  }

  .goods-block {
    display: flex;
    flex-wrap: wrap;

    .add-goods {
      //display: flex;
      //align-items: center;
      //justify-content: center;
      padding-top: 40px !important;
      text-align: center;
      font-size: 18px;
      font-weight: bold;

      .block-item-text {
        margin-top: 6px;
        font-size: 12px;
        font-weight: 400;
        color: #145cd3;
        line-height: 12px;
      }

      .block-item-num {
        margin-top: 21px;
        font-size: 12px;
        font-weight: 400;
        color: #cccccc;
        line-height: 20px;
      }
    }

    .goods-block-item {
      border-radius: 2px;
      width: 142px;
      height: 188px;
      margin: 0 20px 20px 0px;
      padding: 10px;
      border: 1px solid #ccc;
      position: relative;
      cursor: pointer;

      &:hover {
        .item-delete {
          display: flex;
        }
      }

      .item-delete {
        position: absolute;
        border: 1px solid #ccc;
        border-radius: 50%;
        color: #fff;
        top: -11px;
        right: -10px;
        width: 20px;
        height: 20px;
        font-weight: bold;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 15px;
        cursor: pointer;
        background: #ed4014;
        display: none;

        &:hover {
          color: #fff;
          border-color: #ed4014;
        }
      }

      .goods-img {
        width: 120px;
        height: 120px;
        border-right: 5%;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .goods-introduce {
        width: 120px;
        margin-top: 10px;
      }
    }
  }
}

.mb20 {
  margin-bottom: 20px;
}
</style>
