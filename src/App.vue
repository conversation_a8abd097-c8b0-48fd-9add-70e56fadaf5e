<template>
  <div @click="handleClickOncePage">
    <router-view />
    <div style="position: fixed; top: -200px; left: -200px">
      <Input type="text" name="username" />
    </div>
    <!-- AI助手弹窗 -->
    <!-- :is-half-screen="$store.state.app.assistantModalHalfScreen" -->

    <assistant-modal
      :visible="$store.state.app.assistantModalVisible"
      @update:visible="handleAssistantVisibleChange"
      @update:is-half-screen="handleAssistantHalfScreenChange"
    />
  </div>
</template>

<script>
import AssistantModal from '@/layout/main/components/ai-assistant/assistant-modal.vue';
import { isLogin } from './libs/runtime';

export default {
  name: 'App',
  components: {
    AssistantModal,
  },
  directives: {},
  created() {
    isLogin() && this.getUserConfig();
  },
  methods: {
    getUserConfig() {
      this.$api.getUserPermission().then(res => {
        console.log('%c=>(App.vue:38) res', 'color: #ECA233;font-size: 16px;', res);
        this.$store.commit('app/SET_USER_CONFIG', res);
      });
    },
    handleClickOncePage() {
      this.$store.dispatch('app/setClickOncePage', true);
    },
    handleAssistantVisibleChange(visible) {
      this.$store.commit('app/SET_ASSISTANT_MODAL_VISIBLE', visible);
    },
    handleAssistantHalfScreenChange(isHalfScreen) {
      this.$store.commit('app/SET_ASSISTANT_MODAL_HALF_SCREEN', isHalfScreen);
    },
  },
};
</script>
