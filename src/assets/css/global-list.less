@search-default-height: 52px; // 32 + 20
@list-theme-color:#155BD4; // #3088FF;
@list-base-size: 13px;
@input-default-border-color: #DCDFE6;
// 按钮统一变量
@btn-height: 32px;
@btn-primary-bg-color:  #155BD4; // #3088FF;
@btn-primary-border-color: #3088FF; // 边框色
@btn-primary-color: #FFF;
@btn-default-bg-color: #FFF;
@btn-default-color: #606266;
@btn-default-border-color: #DCDFE6; // 边框色
@btn-radius: 4px;
@btn-size: 13px;

.global-list-box {
  .ivu-btn {
    background-image: none;
  }
  .ivu-btn-primary {
    border-color: @btn-primary-bg-color;
    background-color: @btn-primary-bg-color;
  }
  .ivu-btn-primary:hover {
    background-color: #447cdd;
    border-color: #447cdd;
  }

  .ivu-btn-default {
    border-color: @btn-default-border-color;
  }
  .ivu-btn-default:hover {
    border-color: @btn-primary-bg-color
  }

  // 搜索区域
  .global-list-search {
    display: flex;
    //justify-content: space-between;
    height: @search-default-height;
    overflow: hidden;
    border-bottom: 1px solid #EFEFEF;
    a {
      color: @list-theme-color
    }

    //搜索条件区域
    .global-list-search-condition {
      display: flex;
      flex-wrap: wrap;
      input, .ivu-select-selection {
        border-radius: @btn-radius;
        border-color: @input-default-border-color;
        //border-color: #E0E0E0;
        font-size: @list-base-size;
      }

      // 聚焦悬浮设置高亮
      .ivu-input:focus, .ivu-input:hover, .ivu-select-visible .ivu-select-selection, .ivu-select-selection:hover {
        border-color: @list-theme-color;
        box-shadow: none;
      }
    }

    //搜索条件功能区域
    .global-list-search-operate {
      margin-top: 1px;
      width: 180px;
      min-width: fit-content;
      display: flex;
      align-items: flex-start;
      //justify-content: flex-end;

      .search-btn {
        margin-right: 10px;
      }

      .reset-btn {

      }

      .expand-or-collapse {
        cursor: pointer;
        margin-left: 24px;
        font-weight: 400;
        font-size: @list-base-size;
        color: #3088FF;
        line-height: 32px;
        display: flex;
        align-items: center;

        img {
          margin-left: 6px;
          width: 10px;
          height: 10px;
        }
      }
    }
  }

  // 功能区域
  .global-list-function {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  // 列表区域
  .global-list-table {
    .global-panel-box {
      /* 列表上方状态tab模块 */
      .global-panel-nav {
        height: 40px;
        position: relative;
        margin-left: 1px;
        padding: 0;
        a:first-child {
          border-top-left-radius: 4px;
        }
        a:last-child {
          border-top-right-radius: 4px;
        }
      }

      .global-panel-nav a {
        background: #fff;
        border: 1px solid #DCDFE6;
        box-sizing: border-box;
        color: #333;
        float: left;
        font-size: 14px;
        height: 40px;
        line-height: 40px;
        padding: 0 12px;
        margin-left: -1px;
        list-style: none;
        position: relative;
        z-index: 1;
      }

      .global-panel-nav a.global-nav-active {
        background: #F5F6F8;
        border-bottom: 1px solid #F5F6F8;
      }
    }

    table {
      a {
        color: @list-theme-color
      }
    }
    .ivu-table-header, .ivu-table-fixed-header {
      th {
        background: #F5F6F8;
      }
    }
    .ivu-table th, .ivu-table td {
      border-color: #EBEDF0;
    }

    .ivu-divider {
      background: #EBEDF0;
    }

    // 禁用的表格行置灰样式
    .ivu-table .del-cell td{
      color: #C0C4CC;
      a, p, div, span {
        color: #C0C4CC;
      }
    }

    // 置灰的前提下，需要标签高亮
    .a-plus {
      color: #155BD4 !important;
      a {
        color: #155BD4 !important;
      }
    }
  }

  // 分页区域
  .global-list-page {
    font-size: @list-base-size;
    .ivu-page-item {
      a {
        color: #606266;
      }
    }
    .ivu-select-selected-value {
      color: #606266;
    }
    .ivu-select-selection {
      border-color: #EBEDF0;
    }
    .ivu-page-options-elevator {
      input {
        color: #606266;
        border-color: #EBEDF0;
      }
    }
    .ivu-page {
      text-align: right;
      li, .ivu-select-selection, input {
        border-radius: @btn-radius;
        border-color: #EBEDF0;
        box-shadow: none;
        &:hover, &:focus {
          border-color: @btn-primary-bg-color;
        }
      }
    }

    .ivu-page-item-active {
      a {
        color: #fff !important;
      }
    }

    .ivu-page-item-active {
      background: @list-theme-color;
      border-color: @list-theme-color;
    }
  }

  // 统一列表的表格样式
  button {
    height: @btn-height;
    font-size: @btn-size;
    border-radius: @btn-radius;
    display: flex;
    align-items: center;

    span {
      display: flex;
      align-items: center;
      .export-icon {
        width: 14px;
        height: 13px;
        margin-right: 6px;
      }
    }
  }
}
.standard-table-footer {
  // 分页区域
  .global-list-page {
    font-size: @list-base-size;
    .ivu-page-item {
      a {
        color: #606266;
      }
    }
    .ivu-select-selected-value {
      color: #606266;
    }
    .ivu-select-selection {
      border-color: #EBEDF0;
    }
    .ivu-page-options-elevator {
      input {
        color: #606266;
        border-color: #EBEDF0;
      }
    }
    .ivu-page {
      text-align: right;
      li, .ivu-select-selection, input {
        border-radius: @btn-radius;
        border-color: #EBEDF0;
        box-shadow: none;
        &:hover, &:focus {
          border-color: @btn-primary-bg-color;
        }
      }
    }

    .ivu-page-item-active {
      a {
        color: #fff !important;
      }
    }

    .ivu-page-item-active {
      background: @list-theme-color;
      border-color: @list-theme-color;
    }
  }
}
