import util from '@/libs/util';
export default {
  data() {
    return {
      // 取消订单
      cancalVisible: false,
      clinicMrId: '', // 临时存储诊所的clinic_mr_id
      noStockVisible: false,
      overStockList: [], //
      rightLoading: false, // 右侧区域加载数据的loading
      deleteVisible: false, // 作废处方单弹窗
      deleteEventId: '', // 即将作废的处方单id
      xdVisible: false, // 存为协定方的弹窗
      xdfId: '', // 协定方的id
      form: {
        name: '', // 协定方名称
        desc: '', // 协定方描述
      }, // 协定方数据
      navActiveIndex: 0, // 默认选中就诊人第一阶段
      navList: [], // 左侧菜单
      tabId: 1, // 默认选中同阶段得第一个信息
      aiActiveRadio: '', // AI诊断得辨证结果tab切换，默认选中第一个
      aiRadioList: [], // AI的辨证结果切换tab
      igzdActiveRadio: '', // 智能舌诊-诊断分析-选中
      igzdRadioList: [], // 智能舌诊-诊断分析-list数据
      igsxActiveRadio: '', // 智能舌诊-诊断分析-选中
      igsxRadioList: [], // 智能舌诊-诊断分析-list数据
      jzObj: {
        allData: [], // 就诊信息-基础信息数据,此字段弃用
        info: {}, // 就诊记录对应的就诊信息,现在info数据依赖于左侧点击获取
        prescriptionData: [], // 处方单数据
        chargeData: {
          pres_list: [],
        }, // 收费单数据
        is_new_follow_up: false, // 是否是新随访
      }, // 就诊信息数据
      aiObj: {
        allData: [],
        dialecticalData: [], // AI的辨证数据
      }, // AI诊断数据
      cmObj: {
        allData: [],
      }, // 中医体质数据
      igObj: {
        allData: [],
        igzdData: [], // 智能舌诊-诊断分析数据
        igsxData: [], // 智能舌诊-舌像分析数据
      }, // 智能舌诊数据
      tableData: [],
      urlList: [],
      overStockVisible: false,
      user_mr_id: '', // 选中的就诊人id
      reportVisible: false, // 报告单弹窗
      isPatient: false, // 是否是就诊人

      // 获取左侧菜单的通用参数
      params: {
        page: 1,
        pageSize: 1000,
        pt_id: '',
      },
      medicalListStatusDesc: [],
      checkedOptions: [], // 选中的筛选状态
			medicalRecordVisible: false, // 修改病历弹窗
      his_version: '',
		}
	},
	watch: {
		pt_id: {
			handler(val, oldVal) {
				if (val) {
					this.$nextTick(() => {
						this.params.pt_id = this.pt_id;
						this.init();
					});
				}
			},
			immediate: true,
		},
	},
	computed: {
    /**
     * @description 获取收费状态
     * */
    getFeeStatusTagStyle() {
      return status => {
        switch (status) {
          case 'WAIT_PAY':
            return {
              color: 'rgba(54, 162, 31, 1)',
              background: 'rgba(54, 162, 31, 0.1)',
            };
          case 'HAS_PAY':
            return {
              color: 'rgba(2, 69, 204, 1)',
              background: 'rgba(2, 69, 204, 0.1)',
            };
          case 'HAS_REFUND':
          case 'WAIT_PAY_CLOSED':
            return {
              color: 'rgba(102, 102, 102, 1)',
              background: 'rgba(102, 102, 102, 0.1)',
            };
          case 'REFUND_IN':
            return {
              color: '#f42f26',
              background: 'rgba(244, 47, 38, 0.08)',
            };
          case 'PART_PAY':
            return {
              color: '#EC861E',
              background: '#F4E4D6',
            }
          default:
            return {
              color: 'rgba(102, 102, 102, 1)',
              background: 'rgba(102, 102, 102, 0.1)',
            };
        }
      };
    },
    // 根据处方单状态映射颜色
    getMedicineStatusColor() {
      return status => {
        switch (status) {
          case 'LIST_STATUS_WAIT_PAY': // 待付款
            return '#155BD4';
          case 'LIST_STATUS_HAS_PAY': // 已收款
            return '#F59816';
          case 'LIST_STATUS_PARTIAL_CLOSED': // 部分作废/取消
            return '#B97ED1';
          case 'LIST_STATUS_CLOSED': // 已取消
            return '#BBBBBB';
          case 'UNKNOWN': // 问诊中
            return '#E5634B';
          default:
            return '#BBBBBB';
        }
      };
    },
    // 格式化日期
    formatTime() {
      return timestamp => {
        return timestamp && this.$moment(timestamp * 1000).format('ddd HH:mm');
      };
    },
    /**
     * 对页面所有功能进行管控，弹窗中除存为协定方外，其他功能均不可使用
     * 针对于诊所，弹窗中所有的功能都关闭
     * */
    isCanUseEventBtn() {
      // 患者档案才显示打印
      // if (this.$router.history.current.path === '/his/patient/patientinfo') {
      // 	return true
      // }
      return false;
    },

    /**
     * @description: 是否显示导入历史问诊的按钮，只在问诊第二步，导入历史问诊中显示
     * */
    isShowImportBtn() {
      const isQueryBtn = !!this.$route.query.btn;
      if (isQueryBtn && this.jzObj.info.sl_ai_version != 1) {
        return true;
      }
      return false;
    },
    /**
     * 患者档案的tab头展示逻辑与弹窗展示逻辑不同
     * */
    isShowHeaderTab() {
      // 弹窗打开，如果是患者本人，则不展示头部,非患者本人，依旧展示头部, 如果外部没有传titlelist进来，默认也不展示头部
      if (this.isModal && (this.isPatient || this.getTitList.length == 0)) {
        return false;
      }
      return true;
    },

    // 2023-04-25  就诊信息的辨证参考数据
    jzDialecticalData() {
      if (this.jzObj.info) {
        return this.jzObj.info.dialectical;
      } else {
        return {};
      }
    },

    // 2023-04-25 不同病历数据的mr_id
    mr_id() {
      return this.jzObj.info.id;
    },

    getInvalidateText(type) {
      return type => {
        // item.type === 'HERBS' ? '作废中药处方单' : item.type === 'MEDICINE' ? '作废中成药处方单' : item.type === 'PLASTER' ? '作废膏方' : '作废治疗单'
        switch (type) {
          case 'HERBS':
            return '作废中药饮片处方单';
          case 'MEDICINE':
            return '作废中成药处方单';
          case 'PLASTER':
            return '作废膏方';
          case 'PHYSICAL':
            return '作废调理产品';
          default:
            return '作废治疗单';
        }
      };
    },
    // 随访记录-全身舒适度标签背景色
    tagColor() {
      return status => {
        switch (status) {
          case 'GOOD':
            return '#6DB62B';
          case 'VERY_GOOD':
            return '#A6C338';
          case 'COMMONLY':
            return '#D7B12E';
          case 'UNWELL':
            return '#ADADAD';
        }
      };
    },
    // h5上传的照片
    record_image_details() {
      return this.jzObj.info.record_image_details || [];
    },
    // 既往史
    medical_history() {
      return this.jzObj.info.medical_history || [];
    },
    medical_details() {
      return this.jzObj.info.medical_details || [];
    },
    // 身体指标
    body_index() {
      return this.jzObj.info.body_index || {};
    },
    // 是否显示身体指标
    isShowBodyIndex() {
      return Object.keys(this.body_index || {}).length || false;
    },
    // 随访记录
    visit_list() {
      return this.jzObj.info.visit_list || [];
    },
    // 中医体质-总体特征
    cmXT() {
      if (this.cmObj.allData[this.navActiveIndex]) {
        return this.cmObj.allData[this.navActiveIndex].main_type_info.user_advices[0].items;
      } else {
        return [];
      }
    },
    // 中医体质-药膳调理
    cmYS() {
      if (this.cmObj.allData[this.navActiveIndex]) {
        return this.cmObj.allData[this.navActiveIndex].main_type_info.user_advices[1].items;
      } else {
        return [];
      }
    },
    unHealth() {
      if (this.tabId === 4) {
        const { navActiveIndex } = this;
        if (this.igObj.allData.length > 0) {
          let e = this.igObj.allData[navActiveIndex].tongue_analysis;
          if (+e.unhealthy_items_count > 0) {
            let text = '';
            e.unhealthy_items.forEach(item => {
              text = text + `${item}分析`;
            });
            return text;
          } else {
            return '';
          }
        }
      } else {
        return '';
      }
    },
    // 智舌舌诊-舌像分析-异常文案
    unHealthText() {
      if (this.tabId === 4) {
        const { navActiveIndex } = this;
        if (this.igObj.allData.length > 0) {
          let e = this.igObj.allData[navActiveIndex].tongue_analysis;
          if (+e.unhealthy_items_count > 0) {
            return `${e.unhealthy_items_count}项异常：${e.unhealthy_items_text}`;
          } else {
            return '';
          }
        }
      } else {
        return '';
      }
    },
    basicDetail() {
      return this.jzObj.info.basic_details || [];
    },
    currentData() {
      return this.jzObj.info || {};
    },
    getTitList() {
      return this.isPatient ? this.titList.filter(item => item.id === 1) : this.titList;
    },
  },
  methods: {
    // 初始函数
    init() {
      let clinic_mr_id = this.$route.query.clinic_mr_id;
      this.clinicMrId = clinic_mr_id;
      this.tabId = 1;
      this.getPatientMedicalOptions().then(res => {
        this.changeTab(this.tabId);
      });
    },

    /**
     * @description: 功能性
     * */
    // resetHeight
    resetHeightArea(ref) {
      if (ref) {
        this.$refs[ref].scrollTo({ top: 0, behavior: 'instant' });
      }
    },

    // 如果从clinic跳转过来，跳到指定的档案记录
    isFromCinic() {
      const { clinicMrId } = this;
      if (clinicMrId) {
        this.clinicMrId = '';
        this.navList.some((item, index) => {
          if (item.id == clinicMrId) {
            this.navActiveIndex = index;
            return true;
          }
        });
      } else {
        // this.navActiveIndex = 0
      }
    },

    // 导入历史病历
    importBtn(id, hasImage) {
      if (hasImage) {
        this.$confirm('是否将该病例中上传的患者舌诊等照片一起导入？', {
          type: 'warning',
          showClose: false,
          cancelButtonText: '否',
          confirmButtonText: '是',
        })
          .then(() => {
            this.$bus.$emit('importId', { from_mr_id: id, is_import_tongue: 1 });
          })
          .catch(() => {
            this.$bus.$emit('importId', { from_mr_id: id, is_import_tongue: 0 });
          });
      } else {
        this.$bus.$emit('importId', { from_mr_id: id });
      }
    },

    // 修改病历
    editMedicalRecord() {
      this.medicalRecordVisible = true;
    },

    // 修改病历成功
    updateRecordSuccess() {
      let current_id = this.navList[this.navActiveIndex] && this.navList[this.navActiveIndex].id;
      if (current_id) {
        this.getJZRecordInfo(current_id);
      }
    },

    // 筛选类型
    filterChange(val) {
      this.checkedOptions = val;
      this.navActiveIndex = 0;
      this.changeTab(1);
    },

    /**
     * @description: 对数据进行处理（接口数据，切换数据）
     * */

    // 设置左侧菜单数据
    setNavList(arr = []) {
      this.navList = arr.list;
      // arr.list.forEach(e => {
      // 	this.navList.push({
      // 		time: e.create_time,
      // 		contain_herbs: e.contain_herbs,
      // 		name: e.patient.name,
      // 		sex_text: e.patient.sex_text,
      // 		age: `${e.patient.age}岁`,
      // 		type_text: e.type_text,
      // 		status: e.visit_list && e.visit_list.length ? 'FOLLOW_UP' : '',
      // 		inquiry_type: e.inquiry_type,
      // 		id: e.id,
      // 	});
      // });
    },

    // 切换就诊人阶段，左侧菜单
    change(item, index) {
      this.resetHeightArea('content');
      this.navActiveIndex = index;
      if (this.tabId === 1) {
        // 切换时，切换数据源
        this.getJZRecordInfo(item.id);
      }
      // AI诊断动态切换辨证结果数据
      if (this.tabId === 2) {
        this.aiRadioList =
          this.aiObj.allData[this.navActiveIndex] && this.aiObj.allData[this.navActiveIndex].dialectical;
        if (this.aiRadioList.length > 0) {
          this.radioChange(this.aiRadioList[0].code);
        }
      }

      // 智能舌诊动态切换数据
      if (this.tabId === 4) {
        this.setIgRadioList();
      }
    },

    // 切换就诊人同阶段不同信息，头部切换
    changeTab(id) {
      this.tabId = id;
      // 重置不同问诊为第一阶段
      /**
       * @description:如果默认存在展示第几条数据，将navActiveIndex设置为指定索引
       * */

      // 就诊信息
      if (+id == 1) {
        // this.sdInterFace();
        this.ReGetRecordInfo();
      } else {
        this.navActiveIndex = 0;
      }

      // AI问诊
      if (+id === 2) {
        this.aiInterFace();
      }
      // 中医体质
      if (+id === 3) {
        this.chineseMedicineInterFace();
      }
      // 智能舌诊
      if (+id === 4) {
        this.intelligenceInterFace();
      }
      // todo 慢病信息
      if (+id === 5) {
      }
    },

    // 处理就诊信息的处方和收费数据-根据左侧navActiveIndex变换而变换
    setPrescriptionData() {
      let info = this.jzObj.info;
      const { navActiveIndex } = this;
      if (!Object.keys(info).length) {
        return;
      }
      this.jzObj.prescriptionData = info.pres_list;
      this.jzObj.chargeData = JSON.parse(JSON.stringify(info.pay_order));
      const header = this.jzObj.chargeData;
      // 在收费上默认追加一个挂号费，一个问诊单数据
      // 如果收费单有数据，则追加挂号费和问诊费
      if (header && header.pres_list && header.pres_list.length > 0) {
        let arr = [
          {
            type_text: '挂号费',
            his_pay_status: (header.reg_info && header.reg_info.his_pay_status) || '-',
            attrs: [
              {
                name: '挂号费',
                his_status_text: (header.reg_info && header.reg_info.his_pay_status_text) || '-',
                price: header.reg_info && header.reg_info.money,
                total_fee: header.reg_info && header.reg_info.money,
                unit_name: '次',
                quantity: 1,
              },
            ],
            his_pay_status_text: header.reg_info && header.reg_info.his_pay_status_text,
          },
          {
            type_text: '问诊费',
            his_pay_status: (header.cons_info && header.cons_info.his_pay_status) || '-',
            attrs: [
              {
                name: '问诊费',
                his_status_text: (header.cons_info && header.cons_info.his_pay_status_text) || '-',
                price: header.cons_info && header.cons_info.money,
                total_fee: header.cons_info && header.cons_info.money,
                unit_name: '次',
                quantity: 1,
              },
            ],
            his_pay_status_text: header.cons_info && header.cons_info.his_pay_status_text,
          },
        ];

				let lastItem = {};
				// this.jzObj.chargeData.pres_list.map(item => {
				// 	if (item.type === 'HERBS') {
				// 		let len = item.attrs.length - 1;
				// 		lastItem = item.attrs[len];
				// 		if (lastItem.name !== '加工费' && info.pay_order.decoct_info.money) {
				// 			item.attrs.push({
				// 				name: '加工费',
				// 				total_fee: info.pay_order.decoct_info.money,
				// 				his_status_text: info.pay_order.decoct_info.his_pay_status_text,
				// 			});
				// 		}
				// 	}
				// });

        this.jzObj.chargeData.pres_list = [...arr, ...this.jzObj.chargeData.pres_list];
      }
    },

    // 智能舌诊-根据左侧navActiveIndex变换而动态刷新数据
    setIgRadioList() {
      const { navActiveIndex } = this;
      this.urlList = [];
      // 我的照片数据赋值
      if (this.igObj.allData[navActiveIndex]) {
        this.urlList.push(this.igObj.allData[navActiveIndex].tongue_analysis.detect_img);
      }
      // *处理诊断分析tab切换数据处理
      // diag_list
      if (this.igObj.allData.length > 0 && this.igObj.allData[navActiveIndex].diag_list.length > 0) {
        this.igzdRadioList = this.igObj.allData[navActiveIndex].diag_list;
      }
      // 初始默认展示辨证结果数组的第一个数组
      this.igObj.igzdData = this.igzdRadioList.length > 0 ? this.igzdRadioList[0] : {};
      // 默认选中辨证结果第一个
      this.igzdActiveRadio = 0;

      // *处理舌像分析tab切换数据处理
      // tongue_analysis
      if (this.igObj.allData.length > 0 && this.igObj.allData[navActiveIndex].tongue_analysis.tongue_list.length > 0) {
        this.igsxRadioList = this.igObj.allData[navActiveIndex].tongue_analysis.tongue_list;
      }
      // 初始置空数据，防止叠加
      this.igObj.igsxData = [];
      // 解析出所有的待遍历数据
      if (this.igsxRadioList[0]) {
        this.igsxRadioList[0].details.forEach(e => {
          e.forEach(item => {
            this.igObj.igsxData = this.igObj.igsxData.concat(item);
          });
        });
      }
      this.igsxRadioList.length > 0 ? this.igsxRadioList[0] : {};
      // 默认选中辨证结果第一个
      this.igsxActiveRadio = 0;
    },

    // 智能舌诊 - 诊断分析切换
    igzdRadioChange(index) {
      this.igzdActiveRadio = index;
      this.igObj.igzdData = {};
      this.igObj.igzdData = this.igzdRadioList[index];
    },

    // 智能舌诊 - 舌像分析切换
    igsxRadioChange(index) {
      this.igsxActiveRadio = index;
      this.igObj.igsxData = [];
      this.igsxRadioList[index].details.forEach(e => {
        e.forEach(item => {
          this.igObj.igsxData = this.igObj.igsxData.concat(item);
        });
      });
    },

    // AI诊断，辨证结果切换
    radioChange(id) {
      this.aiActiveRadio = id;
      this.aiObj.dialecticalData = {};
      if (Array.isArray(this.aiRadioList)) {
        const resultArr = this.aiRadioList.filter(item => {
          return item.code === id;
        });
        this.aiObj.dialecticalData = resultArr[0];
      } else {
        this.aiObj.dialecticalData = [];
        this.aiRadioList = [];
      }
    },

    // api-获取菜单左侧菜单列表
    getNavRecordList() {
      this.rightLoading = true;
      let params = {
        ...this.params,
        status: 'FINISHED',
        source: this.$route.query.btn ? 'import' : '', // 来源：导入
        treatment: this.$route.path.includes('treatment') ? '1' : '',
        order_status: this.getCheckedIds(),
      };
      this.navList = [];
      this.$api.getPatientRecordList(params).then(
        res => {
          this.isPatient = res.is_patient === '1';
          this.setNavList(res);
          this.isFromCinic();
          // 如果外部跳转进来需要定位到选中的数据/非外部跳转，直接取第一个，用navActiveIndex取id均满足
          let current_id = this.navList[this.navActiveIndex] && this.navList[this.navActiveIndex].id;
          this.rightLoading = false;
          if (current_id) {
            this.getJZRecordInfo(current_id);
          }
        },
        err => {}
      );
    },

    // api-获取右侧患者信息
    getJZRecordInfo(mr_id = '') {
      this.rightLoading = true;
      let params = {
        pt_id: this.params.pt_id,
        mr_id,
      };
      this.$api
        .getPatientRecordInfo(params)
        .then(
          res => {
            this.jzObj.info = res || {};
            this.his_version = res.his_version;
            this.setPrescriptionData();
          },
          err => {}
        )
        .finally(() => {
          this.rightLoading = false;
        });
    },

    // 获取options
    getPatientMedicalOptions() {
      return new Promise(resolve => {
        this.$api.getPatientMedicalOptions().then(
          res => {
            this.medicalListStatusDesc = util.descToArrHandle(res.medicalListStatusDesc);
            this.checkedOptions = this.medicalListStatusDesc;
            resolve();
          },
          err => {}
        );
      });
    },

    /**
     * 获取筛选项的id合集
     * */
    getCheckedIds() {
      let ids = this.checkedOptions.map(item => item.id);
      return ids;
    },

    /**
     * @description: 重构就诊信息整体请求
     * @note:  2024.3.18 进行就诊信息的接口重构，左侧菜单和具体数据拆分开
     */
    ReGetRecordInfo() {
      this.getNavRecordList();
    },

    // api-就诊信息
    // sdInterFace() {
    // 	this.rightLoading = true;
    // 	const { params, navActiveIndex } = this;
    // 	let newParams = {
    // 		...params,
    // 		status: 'FINISHED',
    // 	};
    // 	this.navList = [];
    // 	this.$api.getSDPetientInfo(newParams).then(
    // 		res => {
    // 			this.isPatient = res.is_patient === '1';
    // 			this.rightLoading = false;
    // 			this.jzObj.allData = res.list;
    // 			this.isFromCinic();
    // 			this.setNavList(res);
    // 			this.setPrescriptionData(res.list);
    // 		},
    // 		err => {}
    // 	);
    // },

    // api-AI诊断接口
    async aiInterFace() {
      this.rightLoading = true;
      const { params, navActiveIndex } = this;
      this.navList = [];
      const res = await this.$api.getAIPetientInfo(params);
      if (res) {
        this.rightLoading = false;
      }
      this.setNavList(res);
      this.aiObj.allData = res.list;
      // 设置辨证结果所有的类型
      if (res.list.length > 0) {
        this.aiRadioList = res.list[navActiveIndex].dialectical;
      }
      // 初始默认展示辨证结果数组的第一个数组
      this.aiObj.dialecticalData = this.aiRadioList.length > 0 ? this.aiRadioList[0] : {};
      // 默认选中辨证结果第一个
      this.aiActiveRadio = this.aiObj.dialecticalData.code;
    },

    // api-中医体质接口
    async chineseMedicineInterFace() {
      this.rightLoading = true;
      const { params } = this;
      this.navList = [];
      const res = await this.$api.getCMPetientInfo(params);
      if (res) {
        this.rightLoading = false;
      }
      this.setNavList(res);
      this.cmObj.allData = res.list;
    },

    // api-智能舌诊
    async intelligenceInterFace() {
      this.rightLoading = true;
      const { params } = this;
      this.navList = [];
      this.urlList = [];
      const res = await this.$api.getIGPetientInfo(params);
      if (res) {
        this.rightLoading = false;
      }
      this.setNavList(res);
      this.igObj.allData = res.list;
      this.setIgRadioList();
    },

    /**
     * @description: 以下功能为页面操作功能
     * */
    // *作废处方单-todo-以前的作废需要拦截到整个，现在支持单个作废
    deleteEvent(item, status, curRecord) {
      if (!(status == 'HAS_PAY' || status == 'HAS_MDC')) {
        this.open();
        return;
      }

      // 如果是治疗但且没有用过卡券，直接作废，用过卡券，弹窗操作确认框
      if (
        item &&
        (item.type === 'TREAT' || item.type === 'TREAT_TJL' || item.type === 'TREAT_AJ' || item.type === 'TREAT_QS') &&
        +item.is_use_service_card
      ) {
        // 弹窗提示作废
        this.deleteVisible = true;
        this.deleteEventId = item.id;
      } else {
        // 二次确认作废
        if (curRecord.chargeData?.order.pay_type === 'OFFLINE_YB') {
          this.cancelAllYBOrder();
          return;
        }
        this.confirmAgain(item.id);
      }
    },

    // 医保支付作废所有订单
    cancelAllYBOrder() {
      this.$confirm('该订单为医保支付订单，继续此操作将作废全部处方/理疗单，并自动退药。是否继续？', {
        type: 'warning',
        distinguishCancelAndClose: true,
        confirmButtonText: '确认作废',
        cancelButtonText: '我再想想',
      })
        .then(() => {
          const mr_id = this.jzObj.info.id;
          this.$api
            .cancelAllOrder({ mr_id })
            .then(res => {
              this.$Message.success('作废成功');
              this.changeTab(1);
            })
            .catch(err => {
              {
              }
            });
        })
        .catch(e => {
          console.log(e);
        });
    },

    // 作废处方单二次确认
    confirmAgain(id) {
      this.$confirm('是否确认作废当前处方单？', {
        type: 'warning',
        distinguishCancelAndClose: true,
        cancelButtonText: '我再想想',
        confirmButtonText: '确认作废',
      })
        .then(() => {
          // 作废
          this.deletePrescription(id);
        })
        .catch(() => {});
    },

    //消完整诊疗订单二次确认弹窗
    open() {
      this.$confirm('订单待收款，无法作废单个处方单；是否取消完整诊疗订单？', {
        type: 'warning',
        distinguishCancelAndClose: true,
        confirmButtonText: '取消订单',
        cancelButtonText: '我再想想',
      })
        .then(() => {
          this.cancelOrder();
        })
        .catch(() => {});
    },

    // *作废处方单接口
    deletePrescription(id = '') {
      let params = {
        pres_id: id,
      };
      this.$api.deletePrescription(params).then(
        res => {
          this.$Message.success('作废成功');
          const index = JSON.parse(JSON.stringify(this.navActiveIndex));
          this.changeTab(1);
          setTimeout(() => {
            this.navActiveIndex = index;
          }, 200);
        },
        rej => {}
      );
    },

    // *打印处方单
    dispensePrint() {
      const mr_id = this.jzObj.info.id;
      this.$refs.patientPrinter.printPres(mr_id);
    },

    // *打印收费单
    orderPrint() {
      const { navActiveIndex } = this;
      let header = this.jzObj.info;
      let pay_order_id = header && header.pay_order && header.pay_order.order.id;
      this.$refs.patientBillPrinter.printBill(pay_order_id);
    },

    // *打印病历
    medicalRecordPrint() {
      const { navActiveIndex } = this;
      const mr_id = this.jzObj.info.id;
      this.$refs.medicalRecordPrinter.printRecord(mr_id);
    },

    // 弹窗二次确认作废
    continueDelete() {
      const { deleteEventId } = this;
      this.deletePrescription(deleteEventId);
      this.deleteVisible = false;
    },

    // 处理时间
    dateFormat(timestamp, format = 'YYYY-MM-DD HH:mm:ss', dot = '-') {
      if (!timestamp || timestamp == 0) return dot;
      return util.moment.unix(timestamp).format(format);
    },

    // *点击协定方按钮，获取数据
    saveXDF(item) {
      this.xdfId = item.id;
      const { navActiveIndex } = this;
      this.xdVisible = true;
      let header = this.jzObj.info;
      this.form.name = header.patient && header.patient.name + ' ' + this.dateFormat(header.create_time, 'YYYY-MM-DD');
      this.form.desc = `类型：${header.type_text}\n医生：${header.doctor_name}\n症状：${header.diag_result}`;
    },

    // 点击弹框确定，存为协定方
    saveAgreement(item) {
      const { name, desc } = this.form;
      let params = {
        pres_id: this.xdfId + '',
        name: name,
        desc: desc,
      };
      this.$api.saveAgreement(params).then(
        res => {
          this.xdVisible = false;
          this.$Message.success('存为协定方成功');
        },
        rej => {}
      );
    },

    // 新冠愈后自测 - 查看报告
    seeReport(user_mr_id) {
      this.user_mr_id = this.jzObj.info.user_mr_id;
      this.reportVisible = true;
    },

    // *快捷问诊
    inquiry() {
      this.$router.push({
        path: '/his/outpatient-v3/filing',
        query: {
          type: 'patient',
          pt_id: this.params.pt_id,
        },
      });
    },

    // 取消订单
    cancelOrder() {
      this.cancalVisible = true;
    },

    // 成功取消订单
    cancelSuccess() {
      const index = JSON.parse(JSON.stringify(this.navActiveIndex));
      this.changeTab(1);
      this.navActiveIndex = index;
    },

    // 库存不足，查看详情
    checkOverStockDetail(item) {
      this.noStockVisible = true;
      this.overStockList = item.stock_out_list;
    },
  },
};
