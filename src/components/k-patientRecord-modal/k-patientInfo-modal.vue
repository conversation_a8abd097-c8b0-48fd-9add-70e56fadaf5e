<template>
  <div class="patient-info-com-wrapper">
    <!-- 就诊信息 -->
    <div class="visit-information">
      <div class="header-tab">
        <div class="tab-nav flex flex-item-l-center" v-if="titList.length > 1">
          <div
            v-for="(item, index) in titList"
            :key="index + item.name"
            :class="{ cur: tabId == item.id }"
            class="title"
            @click="changeTab(item.id)"
          >
            <div>
              {{ item.name }}
            </div>
            <div v-if="tabId == item.id" class="line"></div>
          </div>
        </div>
      </div>
      <div
        class="content flex"
        v-loading="rightLoading"
        element-loading-spinner="el-icon-loading"
        element-loading-text="加载中.."
        :style="{ marginTop: titList.length > 1 ? '0' : '10px' }"
      >
        <div class="left-nav" v-if="navList.length && tabId != 5">
          <div
            v-for="(item, index) in navList"
            :key="'nav' + index"
            :class="{ 'stage-active': navActiveIndex === index }"
            class="stage-w"
            @click="change(item, index)"
          >
            <div :class="{ 'stage-content-active': navActiveIndex === index }" class="stage-content">
              <p class="stage-time">
                {{ item && item.time | data_format('YYYY-MM-DD HH:mm') }}
              </p>
              <!-- <p v-show="+tabId === 1" class="stage-info">{{ item && item.type_text || '-' }} / {{ item && item.name || '-' }}</p> -->
              <p v-show="+tabId !== 3 || +tabId === 4" class="stage-info">
                {{ item && item.name }} / {{ item && item.sex_text }} {{ item && item.age && `/ ${item.age}` }}
              </p>
              <p v-show="+tabId === 3" class="stage-info">{{ item && item.name }} / {{ item && item.sex_text }}</p>

              <p
                class="small-visit-tag mt6"
                :class="{ 'small-visit-tag-active': navActiveIndex === index }"
                v-if="item.status == 'FOLLOW_UP'"
              >
                已随访
              </p>
            </div>
          </div>
          <div v-if="+navList.length === 0" class="noData">暂无数据</div>
        </div>
        <!-- right -->
        <div class="right-content" ref="content">
          <!-- // *就诊信息 -->
          <div v-show="tabId === 1" class="basic-info">
            <div v-show="+navList.length !== 0">
              <!-- //*患者病历 -->
              <div class="basic-info-header flex-b">
                <div class="flex flex-item-v-center">
                  <p class="vertical-line"></p>
                  <p class="basic-distance basic-title">患者病历</p>
                </div>
              </div>

              <div class="basic-personal-info flex flex-item-v-center">
                <!-- 就诊二级标题 -->
                <!-- icon -->
                <div class="icon">
                  <p class="one-icon"></p>
                  <p class="two-icon"></p>
                </div>
                <p class="second-level-tip">患者信息</p>
              </div>

              <el-row v-for="(item, index) in basicDetail" :key="index">
                <p class="ml46 small-title mb8">{{ item.title }}</p>
                <el-col :span="6" class="flex" v-for="(info, infoIndex) in item.contents" :key="infoIndex">
                  <p class="basic-label" :style="{ minWidth: infoIndex === 0 ? '100px' : '80px' }">
                    {{ info.type_text }}
                  </p>
                  <p class="basic-value">{{ info.type_val }}</p>
                </el-col>
              </el-row>

              <!-- 病历信息 -->
              <div class="basic-personal-info flex flex-item-v-center">
                <!-- 就诊二级标题 -->
                <!-- icon -->
                <div class="icon">
                  <p class="one-icon"></p>
                  <p class="two-icon"></p>
                </div>
                <p class="second-level-tip">病历信息</p>
              </div>
              <!-- 影像资料 -->
              <div v-if="record_image_details.length">
                <div class="flex mb12">
                  <p class="ml46 small-title">影像资料</p>
                </div>
                <div
                  class="mb12 flex"
                  v-for="(images_item, images_index) in record_image_details"
                  :key="'images' + images_index"
                >
                  <div class="basic-label key ml46 mr10" v-if="images_item.type_val.length">
                    {{ images_item.type_text }}
                  </div>
                  <div class="img-wrapper" v-if="images_item.type_val.length">
                    <el-image
                      v-for="(img_item, img_index) in images_item.type_val"
                      :key="'img_index' + img_index"
                      :src="img_item"
                      class="cursor custom-img"
                      :preview-src-list="images_item.type_val"
                    >
                    </el-image>
                    <!-- <img class="cursor" :src="img_item" alt="" v-for="( img_item, img_index ) in images_item.type_val" :key="'img_index'+img_index"> -->
                  </div>
                </div>
              </div>

              <!-- 症状与主诉 -->
              <el-row
                v-if="
                  jzObj.allData[this.navActiveIndex] &&
                  jzObj.allData[this.navActiveIndex].symptom_details &&
                  jzObj.allData[this.navActiveIndex].symptom_details.length
                "
              >
                <div class="flex mb12">
                  <p class="ml46 small-title">症状与主诉</p>
                </div>
                <el-col
                  v-for="(item, index) in (jzObj.allData[this.navActiveIndex] &&
                    jzObj.allData[this.navActiveIndex].symptom_details) ||
                  []"
                  :key="'md' + index"
                  :span="24"
                  class="flex"
                >
                  <p class="basic-label key ml46">{{ item && item.type_text }}</p>
                  <p class="basic-value">
                    {{ item.type_val || '-' }}
                  </p>
                </el-col>
              </el-row>

              <!-- 既往史与身体指标，体格检查与身体指标并入循环 -->
              <div class="block ml46">
                <!--                <div >-->
                <!--                  <p class="small-title mb12" v-if="medical_history.length">体格检查与身体指标</p>-->

                <!--                  &lt;!&ndash; 体格检查 &ndash;&gt;-->
                <!--                  <div class="flex" v-if="medical_history.length">-->
                <!--                    <div-->
                <!--                      v-for="( symptom_item, symptom_index ) in medical_history" :key="'symptom'+symptom_index"-->
                <!--                      :span="24"-->
                <!--                      class="flex"-->
                <!--                    >-->
                <!--                      <p class="basic-label key">{{ symptom_item.type_text }}</p>-->
                <!--                      <p class="basic-value">{{ symptom_item.type_val || '-'}}</p>-->
                <!--                    </div>-->
                <!--                  </div>-->

                <!--                  &lt;!&ndash; 身体指标 &ndash;&gt;-->
                <!--                  <div class="block-content" v-if="isShowBodyIndex">-->
                <!--                    <div class="flex">-->
                <!--                      <p class="small-block">-->
                <!--                        <span class="basic-label key">身高</span>-->
                <!--                        <span class="basic-value">{{ `${body_index.height}cm` || '-' }}</span>-->
                <!--                      </p>-->

                <!--                      <p class="small-block">-->
                <!--                        <span class="basic-label key">体重</span>-->
                <!--                        <span class="basic-value">{{ `${body_index.weight}kg` || '-' }}</span>-->
                <!--                      </p>-->

                <!--                      <p class="small-block">-->
                <!--                        <span class="basic-label key">体温</span>-->
                <!--                        <span class="basic-value">{{ `${body_index.temperature}℃` || '-' }}</span>-->
                <!--                      </p>-->

                <!--                      <p class="small-block">-->
                <!--                        <span class="basic-label key">心率</span>-->
                <!--                        <span class="basic-value">{{ `${body_index.heart_rate}bmp` || '-' }}</span>-->
                <!--                      </p>-->
                <!--                    </div>-->

                <!--                    <div class="flex">-->
                <!--                      <p class="small-block">-->
                <!--                        <span class="basic-label key">收缩压</span>-->
                <!--                        <span class="basic-value">{{ `${body_index.systolic_pressure}mmHg` || '-' }}</span>-->
                <!--                      </p>-->

                <!--                      <p class="small-block">-->
                <!--                        <span class="basic-label key">舒张压</span>-->
                <!--                        <span class="basic-value">{{ `${body_index.diastolic_pressure}mmHg` || '-' }}</span>-->
                <!--                      </p>-->
                <!--                      <p class="small-block"></p>-->
                <!--                      <p class="small-block"></p>-->
                <!--                    </div>-->
                <!--                  </div>-->
                <!--                </div>-->

                <div class="mt12" v-if="medical_details.length">
                  <div
                    class="flex"
                    v-for="(medical_item, medical_index) in medical_details"
                    :key="'medical' + medical_index"
                  >
                    <span class="key">{{ medical_item.type_text }}</span>
                    <span class="basic-value">{{ medical_item.type_val || '-' }}</span>
                  </div>
                </div>
              </div>

              <!-- 处方 -->
              <div>
                <div class="basic-info-header flex-b flex-item-v-center">
                  <div class="flex flex-item-v-center">
                    <p class="vertical-line"></p>
                    <p class="basic-distance basic-title">处方</p>
                  </div>
                  <el-button
                    v-if="isShow && jzObj.prescriptionData && jzObj.prescriptionData.length"
                    class="print-btn"
                    size="medium"
                    @click="dispensePrint"
                    >打印处方
                  </el-button>
                </div>
                <div
                  v-for="(item, index) in jzObj.prescriptionData"
                  :key="'pre' + index"
                  class="pharmacy-table-wrapper"
                >
                  <div class="pharmacy-th flex-b">
                    <div class="flex">
                      <div class="flex-c">
                        <!-- icon -->
                        <div class="icon">
                          <p class="one-icon"></p>
                          <p class="two-icon"></p>
                        </div>
                      </div>
                      <div class="pharmacy-th-name flex-c">
                        {{ item && item.type_text }}
                      </div>
                      <div class="pharmacy-th-order flex-c">单号:{{ item && item.pres_code }}</div>
                      <div v-show="item" class="pharmacy-th-tag-w flex">
                        <p v-if="item.his_pres_status === 'WAIT_PAY'" class="pharmacy-th-tag flex-c tag1">
                          {{ item.his_pres_status_text }}
                        </p>
                        <p v-if="item.his_pres_status === 'HAS_PAY'" class="pharmacy-th-tag flex-c tag2">
                          {{ item.his_pres_status_text }}
                        </p>
                        <p v-if="item.his_pres_status === 'HAS_MDC'" class="pharmacy-th-tag flex-c tag3">
                          {{ item.his_pres_status_text }}
                        </p>
                        <p v-if="item.his_pres_status === 'ABORT'" class="pharmacy-th-tag flex-c tag4">
                          {{ item.his_pres_status_text }}
                        </p>
                      </div>
                    </div>
                    <div>
                      <!-- <el-button v-show="item.type === 'HERBS' && isShow" class="recipel-btn" @click="saveXDF(item)">
												存为协定方
											</el-button>
											<el-button v-show="isShow" :disabled="!(item.his_pres_status === 'HAS_PAY' || item.his_pres_status === 'HAS_MDC')"
											           class="recipel-btn"
											           @click="deleteEvent(item)">
												{{ item.type === 'HERBS' ? '作废中药处方单' : item.type === 'MEDICINE' ? '作废西药处方单' : '作废治疗单' }}
											</el-button> -->
                      <!-- <el-button class="recipel-btn" :disabled="jzObj.chargeData.pres_list[index+2] && jzObj.chargeData.pres_list[index+2].his_pay_status !== 'HAS_PAY'" @click="deleteEvent(item)">作废处方单</el-button> -->
                    </div>
                  </div>
                  <el-table
                    v-if="item && item.attrs"
                    :cell-style="{ textAlign: 'center' }"
                    :data="item && item.attrs"
                    :header-cell-style="{
                      background: '#F7F7F8',
                      color: '#333333',
                      textAlign: 'center',
                    }"
                    style="width: 100%"
                  >
                    <!-- 中药饮片 -->
                    <el-table-column align="center" label="项目图片" prop="image" v-if="item.type === 'TREAT_BJG'">
                      <template v-slot="{ row }">
                        <el-image
                          fit="cover"
                          style="width: 60px"
                          :src="row.image"
                          :preview-src-list="[row.image]"
                        ></el-image>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.type === 'HERBS' || item.type === 'MEDICINE'"
                      label="药品"
                      prop="name"
                      key="1"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="
                        item.type === 'TREAT' ||
                        item.type === 'TREAT_TJL' ||
                        item.type === 'TREAT_AJ' ||
                        item.type === 'TREAT_QS' ||
                        item.type === 'TREAT_BJG' ||
                        item.type === 'PLASTER' ||
                        item.type === 'PHYSICAL'
                      "
                      label="项目"
                      prop="name"
                      key="2"
                    ></el-table-column>

                    <!-- 西药增加字段 -->
                    <template v-if="item.type == 'MEDICINE'">
                      <el-table-column align="center" label="类型" width="62" prop="prod_type_text"></el-table-column>
                      <el-table-column align="center" label="处方药" width="76" prop="is_otc"></el-table-column>
                      <el-table-column align="center" label="规格" prop="prod_spec"></el-table-column>
                      <el-table-column align="center" label="厂商" prop="manufacturer">
                        <template slot-scope="{ row, $index }">
                          {{ row.manufacturer || '-' }}
                        </template>
                      </el-table-column>
                    </template>

                    <el-table-column v-if="item.type === 'PHYSICAL'" label="规格" prop="spec" key="spec">
                    </el-table-column>
                    <el-table-column
                      v-if="item.type === 'HERBS'"
                      label="剂量"
                      prop="medicine_info.unit_text"
                      key="3"
                    ></el-table-column>
                    <el-table-column
                      v-if="item.type === 'MEDICINE'"
                      label="单次剂量"
                      prop="medicine_info.unit_text"
                      key="21321dsadas323214"
                    ></el-table-column>
                    <el-table-column
                      v-if="item.type === 'MEDICINE'"
                      label="用法"
                      prop="medicine_info.usage"
                      key="5"
                    ></el-table-column>
                    <el-table-column
                      v-if="item.type === 'MEDICINE'"
                      label="频次"
                      prop="medicine_info.freq"
                      key="6"
                    ></el-table-column>

                    <!-- 处方单价展示（单价/单位）：中药，西药/中成药展示接口返回的单位，膏方写死瓶，非膏方，中药，西药/中成药写死次 -->
                    <el-table-column label="单价" prop="price" key="8">
                      <template slot-scope="scope">
                        <span v-if="item.type === 'HERBS'"
                          >￥{{ scope.row.price }} /
                          {{ scope.row.medicine_info && scope.row.medicine_info.unit_name }}</span
                        >
                        <span v-if="item.type === 'MEDICINE'"
                          >￥{{ scope.row.price }} /
                          {{ scope.row.medicine_info && scope.row.medicine_info.sales_unit }}</span
                        >
                        <span v-if="item.type !== 'HERBS' && item.type !== 'MEDICINE' && scope.row.price"
                          >￥{{ scope.row.price }} / {{ item.type === 'PLASTER' ? '瓶' : '次' }}</span
                        >
                      </template>
                    </el-table-column>

                    <el-table-column
                      v-if="item.type === 'MEDICINE'"
                      label="数量"
                      prop="quantity_text"
                      key="7"
                    ></el-table-column>
                    <el-table-column
                      v-if="
                        item.type === 'TREAT' ||
                        item.type === 'TREAT_TJL' ||
                        item.type === 'TREAT_AJ' ||
                        item.type === 'TREAT_QS' ||
                        item.type === 'TREAT_QS' ||
                        item.type === 'TREAT_BJG' ||
                        item.type === 'PLASTER' ||
                        item.type === 'PHYSICAL'
                      "
                      label="数量"
                      prop="quantity"
                      key="9"
                    ></el-table-column>

                    <!-- 单位：4种情况  1:中药：用接口unit_name字段，2：西药/中成药: 用接口sales_unit字段，3：膏方：写死瓶，4：非膏方，中药，西药/中成药: 写死次-->
                    <el-table-column v-if="item.type === 'HERBS'" label="单位" key="unit" column-key="HERBS_UNIT">
                      <template slot-scope="{ row }">
                        <span>{{ row.medicine_info && row.medicine_info.unit_name }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column v-if="item.type === 'MEDICINE'" label="单位" key="unit" column-key="MEDICINE_UNIT">
                      <template slot-scope="{ row }">
                        <span>{{ row.medicine_info && row.medicine_info.sales_unit }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        (item.type !== 'PLASTER' && item.type === 'TREAT') ||
                        item.type === 'TREAT_TJL' ||
                        item.type === 'TREAT_AJ' ||
                        item.type === 'TREAT_QS' ||
                        item.type == 'PHYSICAL'
                      "
                      label="单位"
                      key="10"
                      >次</el-table-column
                    >
                    <el-table-column v-if="item.type === 'PLASTER'" label="单位" key="unit">瓶</el-table-column>

                    <!-- <el-table-column label="折扣" prop="discount_rate" key="11"></el-table-column> -->
                    <el-table-column label="总价" prop="payment_fee" key="12">
                      <template slot-scope="scope">
                        <span>￥{{ scope.row.payment_fee }}</span>
                      </template>
                    </el-table-column>
                    <!-- <el-table-column v-if="item.type === 'TREAT' || item.type === 'TREAT_TJL' || item.type === 'TREAT_AJ' || item.type === 'TREAT_QS' || item.type === 'PLASTER' " label="总价" prop="total_fee" key="13"></el-table-column> -->
                    <!-- <el-table-column label="备注" prop="remark" show-overflow-tooltip key="4">
											<template slot-scope="scope">
												<span>{{ scope.row.remark || '-' }}</span>
											</template>
										</el-table-column> -->
                  </el-table>
                  <div class="remark" v-if="item.remark || item.medical_advice">
                    <p v-if="item.medical_advice">医嘱和注意事项：{{ item.medical_advice }}</p>
                    <p v-else>方案备注：{{ item.remark }}</p>
                  </div>
                  <div class="flex-b">
                    <div v-show="item.usage_text" class="pharmacy-tip">
                      {{ item.usage_text }}
                    </div>
                    <div v-show="!item.usage_text" class="pharmacy-tip"></div>
                    <div class="total-count">
                      共
                      {{ item.count_entry }}种 总计：￥{{ item.payment_fee }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- // *收费 -->
              <div>
                <div class="basic-info-header flex-b flex-item-v-center">
                  <div class="flex flex-item-v-center">
                    <p class="vertical-line"></p>
                    <p class="basic-distance basic-title">收费</p>
                  </div>
                  <el-button
                    v-if="isShow && jzObj.chargeData && jzObj.chargeData.pres_list"
                    class="print-btn"
                    size="medium"
                    @click="orderPrint"
                    >打印收费单
                  </el-button>
                </div>
                <template v-if="jzObj.chargeData">
                  <div
                    v-for="(item, index) in jzObj.chargeData.pres_list"
                    :key="'order' + index"
                    class="pharmacy-table-wrapper"
                  >
                    <div class="pharmacy-th flex-b">
                      <div class="flex">
                        <div class="flex-c">
                          <!-- icon -->
                          <div class="icon">
                            <p class="one-icon"></p>
                            <p class="two-icon"></p>
                          </div>
                        </div>
                        <div class="pharmacy-th-name flex-c">
                          {{ item && item.type_text }}
                        </div>
                        <div v-show="item && item.pres_code" class="pharmacy-th-order flex-c">
                          单号:{{ item && item.pres_code }}
                        </div>
                        <div v-show="item && item.his_pay_status_text" class="pharmacy-th-tag-w flex">
                          <p v-if="item.his_pay_status === 'WAIT_PAY'" class="pharmacy-th-tag flex-c tag1">
                            {{ item.his_pay_status_text }}
                          </p>
                          <p v-if="item.his_pay_status === 'HAS_PAY'" class="pharmacy-th-tag flex-c tag2">
                            {{ item.his_pay_status_text }}
                          </p>
                          <p v-if="item.his_pay_status === 'HAS_REFUND'" class="pharmacy-th-tag flex-c tag3">
                            {{ item.his_pay_status_text }}
                          </p>
                          <!-- <p class="pharmacy-th-tag flex-c tag1" v-if="item.his_pay_status_text === '待退费'">{{ item.his_pay_status_text === "待退费" ? "已支付" : "" }}</p> -->
                        </div>
                      </div>
                    </div>
                    <el-table
                      v-if="item && item.attrs"
                      :cell-style="{ textAlign: 'center' }"
                      :data="item && item.attrs"
                      :header-cell-style="{
                        background: '#F7F7F8',
                        color: '#333333',
                        textAlign: 'center',
                      }"
                      style="width: 100%"
                    >
                      <el-table-column label="收费项目" prop="name" min-width="100"> </el-table-column>
                      <el-table-column label="状态" prop="his_status_text" width="70"> </el-table-column>
                      <el-table-column label="自费">是</el-table-column>

                      <!-- 西药增加字段 -->
                      <template v-if="item.type == 'MEDICINE'">
                        <el-table-column align="center" label="类型" width="68" prop="prod_type_text"></el-table-column>
                        <el-table-column align="center" label="处方药" width="76" prop="is_otc"></el-table-column>
                        <el-table-column align="center" label="规格" prop="prod_spec"></el-table-column>
                        <el-table-column align="center" label="厂商" prop="manufacturer">
                          <template slot-scope="{ row, $index }">
                            {{ row.manufacturer || '-' }}
                          </template>
                        </el-table-column>
                      </template>

                      <!-- 单价展示（单价/单位），中药，膏方，西药/中成药 -->
                      <el-table-column label="单价" prop="price">
                        <template slot-scope="scope">
                          <span v-if="scope.row.price">￥{{ scope.row.price }}/{{ scope.row.unit_name || '次' }}</span>
                        </template>
                      </el-table-column>

                      <!-- 单位：中药，膏方，西药/中成药用接口返回的单位，其余写死次 -->
                      <el-table-column
                        v-if="item.type === 'HERBS' || item.type === 'PLASTER' || item.type === 'MEDICINE'"
                        label="单位"
                        prop="unit_name"
                      ></el-table-column>
                      <el-table-column v-else label="单位">次</el-table-column>

                      <el-table-column label="数量" prop="quantity"> </el-table-column>
                      <el-table-column label="总价" prop="total_fee">
                        <template slot-scope="scope">
                          <span>￥{{ scope.row.total_fee }}</span>
                        </template>
                      </el-table-column>
                      <!-- <el-table-column label="总价" v-if="item.type !== 'TREAT'" prop="total_fee"></el-table-column>
											<el-table-column label="总价" v-if="item.type === 'TREAT'" prop="payment_fee"></el-table-column> -->
                      <!-- <el-table-column label="备注" prop="remark" show-overflow-tooltip>
												<template slot-scope="scope">
													<span>{{ scope.row.remark || '-' }}</span>
												</template>
											</el-table-column> -->
                    </el-table>
                    <div class="remark" v-if="item.remark || item.medical_advice">
                      <p v-if="item.medical_advice">医嘱和注意事项：{{ item.medical_advice }}</p>
                      <p v-else>方案备注：{{ item.remark }}</p>
                    </div>
                  </div>
                </template>
                <div
                  v-if="jzObj.chargeData && jzObj.chargeData.pres_list && jzObj.chargeData.pres_list.length > 0"
                  class="flex-b"
                >
                  <div class="pharmacy-tip"></div>
                  <div class="total-count">
                    共{{ jzObj.chargeData.order && jzObj.chargeData.order.count_entry }}种 总计：￥{{
                      jzObj.chargeData.order && jzObj.chargeData.order.payment_fee
                    }}
                  </div>
                </div>
              </div>

              <!-- 随访记录 -->
              <div v-for="(visit_item, visit_index) in visit_list" :key="visit_index + 'visit'">
                <div class="basic-info-header flex flex-item-v-center">
                  <p class="vertical-line"></p>
                  <p class="basic-distance basic-title">随访记录{{ visit_index + 1 }}</p>
                </div>

                <template>
                  <div class="flex mt24">
                    <div class="flex flex-1">
                      <p class="basic-label mr10">随访人</p>
                      <p class="basic-value">{{ visit_item.up_info.operator }}</p>
                    </div>
                    <div class="flex flex-1">
                      <p class="basic-label mr10">随访时间</p>
                      <p class="basic-value">{{ visit_item.up_info.time | data_format('MM/DD HH:mm') }}</p>
                    </div>
                  </div>

                  <div class="flex">
                    <div class="flex flex-1">
                      <p class="basic-label mr10">随访类型</p>
                      <p class="basic-value">{{ visit_item.type_text }}</p>
                    </div>
                    <div class="flex flex-1">
                      <p class="basic-label mr10">随访方式</p>
                      <p class="basic-value">{{ visit_item.mode_text }}</p>
                    </div>
                  </div>

                  <div class="flex">
                    <div class="flex flex-1">
                      <p class="basic-label mr10">随访情况</p>
                      <p class="basic-value" v-if="visit_item.mr_info.visit_situation == 'NOT_REACHED'">
                        {{ visit_item.mr_info.visit_situation_text }},{{ visit_item.mr_info.next_plan_time }}继续跟进
                      </p>
                      <p class="basic-value" v-else>{{ visit_item.mr_info.visit_situation_text || '-' }}</p>
                    </div>
                    <div class="flex flex-1">
                      <p class="basic-label mr10">复诊时间</p>
                      <p class="basic-value">{{ visit_item.mr_info.return_visit_date || '-' }}</p>
                    </div>
                  </div>

                  <div class="flex">
                    <p class="basic-label mr10">用户反馈</p>
                    <p class="basic-value">{{ visit_item.mr_info.feedback_desc || '-' }}</p>
                  </div>

                  <div class="flex">
                    <p class="basic-label mr10">随访记录</p>
                    <p class="basic-value">{{ visit_item.mr_info.visit_record || '-' }}</p>
                  </div>

                  <div class="flex flex-item-align">
                    <p class="visit-label mr10">全身舒适度</p>
                    <p
                      class="visit-text visit-tag flex flex-item-center"
                      :style="{ background: tagColor(visit_item.mr_info.comfort) }"
                    >
                      {{ visit_item.mr_info.comfort_text }}
                    </p>
                  </div>
                </template>

                <!-- 随访表格 -->
                <template>
                  <el-table
                    :data="visit_item.mr_info.diag_result"
                    :cell-style="{ textAlign: 'center' }"
                    :header-cell-style="{
                      background: '#F7F7F8',
                      color: '#333333',
                      textAlign: 'center',
                    }"
                    class="mt10"
                    style="width: 100%"
                  >
                    <el-table-column prop="name" label="初始症状"> </el-table-column>
                    <el-table-column prop="feedback_type_text" label="疗效反馈">
                      <template slot-scope="scope">
                        <span>{{ scope.row.feedback_type_text || '-' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="take_medicine_day" label="疗效时间(服药后时间)">
                      <template slot-scope="scope">
                        <span
                          >{{ scope.row.take_medicine_day || '-'
                          }}<span v-if="scope.row.take_medicine_day">天</span></span
                        >
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
            <div v-show="!rightLoading && +navList.length === 0" class="empty-show flex-c">
              <el-empty></el-empty>
            </div>
          </div>
          <!-- // *AI诊断 -->
          <div v-show="tabId === 2" class="basic-info">
            <div v-show="+navList.length !== 0">
              <!-- // *基础信息 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">基础信息</p>
              </div>
              <div>
                <el-row class="basic-personal-info">
                  <el-col :span="8" class="flex">
                    <p class="basic-label">姓名</p>
                    <p class="basic-value">
                      {{
                        (aiObj.allData[this.navActiveIndex] && aiObj.allData[this.navActiveIndex].patient.name) || '-'
                      }}
                    </p>
                  </el-col>
                  <el-col :span="8" class="flex">
                    <p class="basic-label">性别</p>
                    <p class="basic-value">
                      {{
                        (aiObj.allData[this.navActiveIndex] && aiObj.allData[this.navActiveIndex].patient.sex_text) ||
                        '-'
                      }}
                    </p>
                  </el-col>
                  <el-col :span="8" class="flex">
                    <p class="basic-label">出生日期</p>
                    <p class="basic-value">
                      {{
                        (aiObj.allData[this.navActiveIndex] && aiObj.allData[this.navActiveIndex].patient.birthday) ||
                        '-'
                      }}
                    </p>
                  </el-col>
                  <el-col :span="24" class="flex">
                    <p class="basic-label">自选症状</p>
                    <p class="basic-value">
                      {{ (aiObj.allData[this.navActiveIndex] && aiObj.allData[this.navActiveIndex].symptoms) || '-' }}
                    </p>
                  </el-col>
                </el-row>
              </div>

              <!-- // *辨证结果 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">辨证结果</p>
              </div>

              <el-row v-if="aiRadioList.length" class="basic-personal-info dialectical">
                <el-col :span="24">
                  <el-radio-group
                    v-model="aiActiveRadio"
                    fill="#0245CC"
                    size="mini"
                    style="margin-bottom: 30px"
                    @change="radioChange"
                  >
                    <el-radio-button v-for="(item, index) in aiRadioList" :key="'radio' + index" :label="item.code"
                      >{{ item.name }}
                    </el-radio-button>
                  </el-radio-group>
                </el-col>

                <!-- <el-col :span="24" class="flex">
									<p class="basic-label">置信值</p>
									<p class="basic-value current-theme-bcolor">
										{{ aiObj.dialecticalData.score || '-' }}
									</p>
								</el-col> -->
                <el-col :span="24" class="flex">
                  <p class="basic-label">辨证占比</p>
                  <p class="basic-value current-theme-bcolor">
                    {{ aiObj.dialecticalData.percent || '-' }}
                  </p>
                </el-col>
                <el-col
                  v-for="(item, index) in this.aiObj.dialecticalData.explain"
                  :key="'ai' + index"
                  :span="24"
                  class="flex"
                >
                  <p class="basic-label">{{ item.title }}</p>
                  <p class="basic-value">
                    {{ item.desc }}
                  </p>
                </el-col>
              </el-row>
            </div>
            <div v-show="!rightLoading && +navList.length === 0" class="empty-show flex-c">
              <el-empty></el-empty>
            </div>
          </div>

          <!-- // *中医体质 -->
          <div v-show="tabId === 3" class="basic-info">
            <div v-show="+navList.length !== 0">
              <!-- 基础信息 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">基础信息</p>
              </div>

              <el-row class="basic-personal-info">
                <el-col :span="24" class="flex">
                  <p class="basic-label">主体质</p>
                  <p class="basic-value">
                    {{
                      (cmObj.allData[this.navActiveIndex] && cmObj.allData[this.navActiveIndex].main_type_info.type) ||
                      '-'
                    }}
                  </p>
                </el-col>
                <el-col :span="24" class="flex">
                  <p class="basic-label">主体质特征</p>
                  <p class="basic-value">
                    {{
                      (cmObj.allData[this.navActiveIndex] &&
                        cmObj.allData[this.navActiveIndex].main_type_info.type_text) ||
                      '-'
                    }}
                  </p>
                </el-col>
                <el-col :span="24" class="flex">
                  <p class="basic-label">倾向体质</p>
                  <p class="basic-value">
                    {{
                      (cmObj.allData[this.navActiveIndex] && cmObj.allData[this.navActiveIndex].main_type_info.desc) ||
                      '-'
                    }}
                  </p>
                </el-col>
              </el-row>

              <!-- 总体特征 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">总体特征</p>
              </div>

              <el-row v-show="cmXT.length" class="basic-personal-info">
                <el-col v-for="(item, index) in cmXT" :key="'cmXT' + index" :span="24" class="flex">
                  <p class="basic-label">
                    {{ item.title }}
                  </p>
                  <p class="basic-value">
                    {{ item.content }}
                  </p>
                </el-col>
              </el-row>

              <!-- //*药膳调理 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">药膳调理</p>
              </div>

              <el-row class="basic-personal-info">
                <el-col v-for="(item, index) in cmYS" :key="'cmYS' + index" :span="24" class="flex">
                  <p class="basic-label">{{ item.title }}</p>
                  <p class="basic-value">
                    {{ item.content }}
                  </p>
                </el-col>
              </el-row>
            </div>
            <div v-show="!rightLoading && +navList.length === 0" class="empty-show flex-c">
              <el-empty></el-empty>
            </div>
          </div>

          <!-- // *智能舌诊 -->
          <div v-show="tabId === 4" class="basic-info">
            <div v-show="+navList.length !== 0">
              <!-- // *基础信息 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">基础信息</p>
              </div>

              <el-row class="basic-personal-info">
                <el-col :span="24" class="flex">
                  <p class="basic-label">诊断结果</p>
                  <p class="basic-value">
                    {{
                      (igObj.allData[this.navActiveIndex] && igObj.allData[this.navActiveIndex].body_mass_text) || '-'
                    }}
                  </p>
                </el-col>
                <el-col :span="24" class="flex">
                  <p class="basic-label">我的照片</p>
                  <div class="basic-value image-wrapper">
                    <!-- v-for="(item, index) in urlList"
											:key="'url' + index" -->
                    <el-image
                      v-if="
                        igObj.allData[this.navActiveIndex] &&
                        igObj.allData[this.navActiveIndex].tongue_analysis.detect_img
                      "
                      :preview-src-list="urlList"
                      :src="
                        igObj.allData[this.navActiveIndex] &&
                        igObj.allData[this.navActiveIndex].tongue_analysis.detect_img
                      "
                      class="basic-distance"
                      style="width: 66px; height: 66px"
                    >
                    </el-image>
                    <span v-else>-</span>
                  </div>
                </el-col>
              </el-row>

              <!-- // *诊断分析 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">诊断分析</p>
              </div>

              <el-row v-if="igzdRadioList.length" class="basic-personal-info dialectical">
                <el-col :span="24">
                  <el-radio-group
                    v-model="igzdActiveRadio"
                    fill="#0245CC"
                    size="mini"
                    style="margin-bottom: 30px"
                    @change="igzdRadioChange"
                  >
                    <el-radio-button v-for="(item, index) in igzdRadioList" :key="'radio' + index" :label="index"
                      >{{ item.title }}
                    </el-radio-button>
                  </el-radio-group>
                </el-col>

                <el-col
                  v-for="(item, index) in this.igObj.igzdData.items"
                  :key="'aiys' + index"
                  :span="24"
                  class="flex"
                >
                  <p class="basic-label">{{ item.name }}</p>
                  <p class="basic-value">
                    {{ item.content }}
                  </p>
                </el-col>
              </el-row>

              <!-- // *舌像分析 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">
                  舌像分析
                  <span v-show="unHealthText" class="current-theme-ocolor">({{ unHealthText }})</span>
                </p>
              </div>

              <el-row class="basic-personal-info">
                <el-col :span="24">
                  <el-radio-group
                    v-model="igsxActiveRadio"
                    fill="#0245CC"
                    size="mini"
                    style="margin-bottom: 30px"
                    @change="igsxRadioChange"
                  >
                    <el-radio-button v-for="(item, index) in igsxRadioList" :key="'radio' + index" :label="index"
                      >{{ item.title }}
                      <p :class="{ dot: unHealth.includes(item.title) }"></p
                    ></el-radio-button>
                  </el-radio-group>
                </el-col>

                <el-col v-for="(item, index) in this.igObj.igsxData" :key="'aisx' + index" :span="24" class="flex">
                  <p class="basic-label">{{ item.name }}</p>
                  <p
                    :class="{ abnormal: index === 0 && unHealth.includes(igsxRadioList[igsxActiveRadio].title) }"
                    class="basic-value"
                  >
                    {{ item.content }}
                    <span v-show="index === 0 && unHealth.includes(igsxRadioList[igsxActiveRadio].title)"
                      >( 异常 )</span
                    >
                  </p>
                </el-col>
              </el-row>
            </div>
            <div v-show="!rightLoading && +navList.length === 0" class="empty-show flex-c">
              <el-empty></el-empty>
            </div>
          </div>

          <!-- 食养信息 -->
          <div v-if="tabId == 5" class="basic-info">
            <chronic-disease :pt_id="pt_id"></chronic-disease>
          </div>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="deleteVisible" append-to-body lock-scroll custom-class="del-dia" width="480px">
      <div class="dia-header">
        <div class="flex" style="padding: 0 0 14px 14px">
          <i class="el-icon-warning" icon-color="red" style="font-size: 24px"></i>
          <h4>作废治疗单</h4>
        </div>
      </div>
      <p>该治疗单里，有部分治疗服务已被使用，是否继续作废？</p>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" type="default" @click="deleteVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="continueDelete">继续作废</el-button>
      </div>
    </el-dialog>
    <!-- 协定方弹窗 -->
    <el-dialog
      :visible.sync="xdVisible"
      append-to-body
      lock-scroll
      custom-class="xd-dia"
      title="存为协定方"
      width="480px"
    >
      <div class="xd-content">
        <el-form ref="form" :model="form" label-width="100px">
          <el-form-item label="协定方名称">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="协定方描述">
            <!-- <el-input :disabled="true" type="textarea" v-model="form.desc"></el-input> -->
            <div class="read white-space">
              <p>{{ form.desc }}</p>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="default" @click="xdVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="saveAgreement">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script type="text/javascript">
import util from '@/libs/util'; // 工具
// 食养信息
import ChronicDisease from '../chronic/chronic-disease';
export default {
  name: 'patientInfoCom',
  components: {
    ChronicDisease,
  },
  mixins: [],
  props: {
    // navList: {
    //   type: Array,
    //   default: () => [],
    // },
    titList: {
      type: Array,
      default: () => [],
    },
    pt_id: {
      type: [String, Number],
      default: () => '',
    },
  },
  data() {
    return {
      rightLoading: false, // 右侧区域加载数据的loading
      isShow: false,
      deleteVisible: false, // 作废处方单弹窗
      deleteEventId: '', // 即将作废的处方单id
      xdVisible: false, // 存为协定方的弹窗
      xdfId: '', // 协定方的id
      form: {
        name: '', // 协定方名称
        desc: '', // 协定方描述
      }, // 协定方数据
      dataLoadStop: false, //列表数据加载停止状态
      dataLoadNomore: false, //列表数据加载是否完成
      navActiveIndex: 0, // 默认选中就诊人第一阶段
      navList: [], // 左侧菜单
      tabId: 1, // 默认选中同阶段得第一个信息
      aiActiveRadio: '', // AI诊断得辨证结果tab切换，默认选中第一个
      aiRadioList: [], // AI的辨证结果切换tab
      igzdActiveRadio: '', // 智能舌诊-诊断分析-选中
      igzdRadioList: [], // 智能舌诊-诊断分析-list数据
      igsxActiveRadio: '', // 智能舌诊-诊断分析-选中
      igsxRadioList: [], // 智能舌诊-诊断分析-list数据
      jzObj: {
        allData: [], // 就诊信息-基础信息数据
        jzblData: [], // 就诊信息-就诊病历数据
        prescriptionData: [], // 处方单数据
        chargeData: {
          pres_list: [],
        }, // 收费单数据
      }, // 就诊信息数据
      aiObj: {
        allData: [],
        dialecticalData: [], // AI的辨证数据
      }, // AI诊断数据
      cmObj: {
        allData: [],
      }, // 中医体质数据
      igObj: {
        allData: [],
        igzdData: [], // 智能舌诊-诊断分析数据
        igsxData: [], // 智能舌诊-舌像分析数据
      }, // 智能舌诊数据
      tableData: [],
      params: {
        page: 1,
        pageSize: 200,
        pt_id: '',
      },
      urlList: [],
    };
  },
  computed: {
    // 随访记录-全身舒适度标签背景色
    tagColor() {
      return status => {
        switch (status) {
          case 'GOOD':
            return '#6DB62B';
          case 'VERY_GOOD':
            return '#A6C338';
          case 'COMMONLY':
            return '#D7B12E';
          case 'UNWELL':
            return '#ADADAD';
        }
      };
    },
    // h5上传的照片
    record_image_details() {
      return (
        (this.jzObj.allData[this.navActiveIndex] && this.jzObj.allData[this.navActiveIndex].record_image_details) || []
      );
    },
    // 既往史
    medical_history() {
      return (this.jzObj.allData[this.navActiveIndex] && this.jzObj.allData[this.navActiveIndex].medical_history) || [];
    },
    medical_details() {
      return (this.jzObj.allData[this.navActiveIndex] && this.jzObj.allData[this.navActiveIndex].medical_details) || [];
    },
    // 身体指标
    body_index() {
      return (this.jzObj.allData[this.navActiveIndex] && this.jzObj.allData[this.navActiveIndex].body_index) || {};
    },
    // 是否显示身体指标
    isShowBodyIndex() {
      return Object.keys(this.body_index || {}).length || false;
    },
    // 随访记录
    visit_list() {
      return (this.jzObj.allData[this.navActiveIndex] && this.jzObj.allData[this.navActiveIndex].visit_list) || [];
    },
    isShowBtn() {
      return this.$route.query.btn === 'true' ? true : false;
    },
    // 中医体质-总体特征
    cmXT() {
      if (this.cmObj.allData[this.navActiveIndex]) {
        return this.cmObj.allData[this.navActiveIndex].main_type_info.user_advices[0].items;
      } else {
        return [];
      }
    },
    // 中医体质-药膳调理
    cmYS() {
      if (this.cmObj.allData[this.navActiveIndex]) {
        return this.cmObj.allData[this.navActiveIndex].main_type_info.user_advices[1].items;
      } else {
        return [];
      }
    },
    unHealth() {
      if (this.tabId === 4) {
        const { navActiveIndex } = this;
        if (this.igObj.allData.length > 0) {
          let e = this.igObj.allData[navActiveIndex].tongue_analysis;
          if (+e.unhealthy_items_count > 0) {
            let text = '';
            e.unhealthy_items.forEach(item => {
              text = text + `${item}分析`;
            });
            return text;
          } else {
            return '';
          }
        }
      } else {
        return '';
      }
    },
    // 智舌舌诊-舌像分析-异常文案
    unHealthText() {
      if (this.tabId === 4) {
        const { navActiveIndex } = this;
        if (this.igObj.allData.length > 0) {
          let e = this.igObj.allData[navActiveIndex].tongue_analysis;
          if (+e.unhealthy_items_count > 0) {
            return `${e.unhealthy_items_count}项异常：${e.unhealthy_items_text}`;
          } else {
            return '';
          }
        }
      } else {
        return '';
      }
    },
    // 患者信息
    basicDetail() {
      if (this.jzObj.allData[this.navActiveIndex]) {
        return this.jzObj.allData[this.navActiveIndex].basic_details;
      } else {
        return [];
      }
    },
  },
  watch: {},
  created() {},
  mounted() {
    // 患者档案不显示打印
    this.isShow = false;
    // this.params.pt_id = this.pt_id
    // 初始状态下，默认调取就诊信息数据
    this.$nextTick(() => {
      this.params.pt_id = this.pt_id;
      this.init();
    });
  },
  methods: {
    // 初始函数
    init() {
      this.changeTab(this.tabId);
    },
    loadDataList() {
      // this.params.page++
      // this.aiInterFace()
    },
    setNavList(arr = []) {
      arr.list.forEach(e => {
        this.navList.push({
          time: e.create_time,
          name: e.patient.name,
          sex_text: e.patient.sex_text,
          age: `${e.patient.age}岁`,
          type_text: e.type_text,
          status: e.visit_list && e.visit_list.length ? 'FOLLOW_UP' : '',
        });
      });
    },
    // *就诊信息
    sdInterFace() {
      this.rightLoading = true;
      const { params, navActiveIndex } = this;
      let newParams = {
        ...params,
        status: 'FINISHED',
      };
      this.navList = [];
      this.$api.getSDPetientInfo(newParams).then(
        res => {
          this.rightLoading = false;
          this.jzObj.allData = res.list;
          this.setNavList(res);
          this.setPrescriptionData(res.list);
        },
        err => {}
      );
    },
    // !处理就诊信息的处方和收费数据-根据左侧navActiveIndex变换而变换
    setPrescriptionData() {
      const { navActiveIndex } = this;
      this.jzObj.allData[navActiveIndex] &&
        (this.jzObj.prescriptionData = this.jzObj.allData[navActiveIndex].pres_list);
      this.jzObj.allData[navActiveIndex] &&
        (this.jzObj.chargeData = JSON.parse(JSON.stringify(this.jzObj.allData[navActiveIndex].pay_order)));
      const header = this.jzObj.chargeData;
      // 在收费上默认追加一个挂号费，一个问诊单数据
      // 如果收费单有数据，则追加挂号费和问诊费
      if (header && header.pres_list && header.pres_list.length > 0) {
        let arr = [
          {
            type_text: '挂号费',
            his_pay_status: (header.reg_info && header.reg_info.his_pay_status) || '-',
            attrs: [
              {
                name: '挂号费',
                his_status_text: (header.reg_info && header.reg_info.his_pay_status_text) || '-',
                price: header.reg_info && header.reg_info.money,
                total_fee: header.reg_info && header.reg_info.money,
                unit_name: '次',
                quantity: 1,
              },
            ],
            his_pay_status_text: header.reg_info && header.reg_info.his_pay_status_text,
          },
          {
            type_text: '问诊单',
            his_pay_status: (header.cons_info && header.cons_info.his_pay_status) || '-',
            attrs: [
              {
                name: '问诊费',
                his_status_text: (header.cons_info && header.cons_info.his_pay_status_text) || '-',
                price: header.cons_info && header.cons_info.money,
                total_fee: header.cons_info && header.cons_info.money,
                unit_name: '次',
                quantity: 1,
              },
            ],
            his_pay_status_text: header.cons_info && header.cons_info.his_pay_status_text,
          },
        ];
        // *数据置空

        // this.jzObj.chargeData.pres_list = []
        // let copyItem = JSON.parse(JSON.stringify(this.jzObj.chargeData.pres_list))
        // // let lastItem = copyItem.pop()
        let lastItem = {};
        this.jzObj.chargeData.pres_list.map(item => {
          if (item.type === 'HERBS') {
            let len = item.attrs.length - 1;
            lastItem = item.attrs[len];
            if (lastItem.name !== '加工费' && this.jzObj.allData[navActiveIndex].pay_order.decoct_info.money) {
              item.attrs.push({
                name: '加工费',
                total_fee: this.jzObj.allData[navActiveIndex].pay_order.decoct_info.money,
                his_status_text: this.jzObj.allData[navActiveIndex].pay_order.decoct_info.his_pay_status_text,
              });
            }
          }
        });
        this.jzObj.chargeData.pres_list = [...arr, ...this.jzObj.chargeData.pres_list];
      }
    },
    // *AI诊断接口
    async aiInterFace() {
      this.rightLoading = true;
      const { params, navActiveIndex } = this;
      this.navList = [];
      const res = await this.$api.getAIPetientInfo(params);
      if (res) {
        this.rightLoading = false;
      }
      this.setNavList(res);
      this.aiObj.allData = res.list;
      // 设置辨证结果所有的类型
      if (res.list.length > 0) {
        this.aiRadioList = res.list[navActiveIndex].dialectical;
      }
      // 初始默认展示辨证结果数组的第一个数组
      this.aiObj.dialecticalData = this.aiRadioList.length > 0 ? this.aiRadioList[0] : {};
      // 默认选中辨证结果第一个
      this.aiActiveRadio = this.aiObj.dialecticalData.code;
    },
    // *中医体质接口
    async chineseMedicineInterFace() {
      this.rightLoading = true;
      const { params } = this;
      this.navList = [];
      const res = await this.$api.getCMPetientInfo(params);
      if (res) {
        this.rightLoading = false;
      }
      this.setNavList(res);
      this.cmObj.allData = res.list;
    },
    // *智能舌诊
    async intelligenceInterFace() {
      this.rightLoading = true;
      const { params } = this;
      this.navList = [];
      this.urlList = [];
      const res = await this.$api.getIGPetientInfo(params);
      if (res) {
        this.rightLoading = false;
      }
      this.setNavList(res);
      this.igObj.allData = res.list;
      this.setIgRadioList();
    },
    // !智能舌诊-根据左侧navActiveIndex变换而动态刷新数据
    setIgRadioList() {
      const { navActiveIndex } = this;
      this.urlList = [];
      // 我的照片数据赋值
      if (this.igObj.allData[navActiveIndex]) {
        this.urlList.push(this.igObj.allData[navActiveIndex].tongue_analysis.detect_img);
      }
      // *处理诊断分析tab切换数据处理
      // diag_list
      if (this.igObj.allData.length > 0 && this.igObj.allData[navActiveIndex].diag_list.length > 0) {
        this.igzdRadioList = this.igObj.allData[navActiveIndex].diag_list;
      }
      // 初始默认展示辨证结果数组的第一个数组
      this.igObj.igzdData = this.igzdRadioList.length > 0 ? this.igzdRadioList[0] : {};
      // 默认选中辨证结果第一个
      this.igzdActiveRadio = 0;

      // *处理舌像分析tab切换数据处理
      // tongue_analysis
      if (this.igObj.allData.length > 0 && this.igObj.allData[navActiveIndex].tongue_analysis.tongue_list.length > 0) {
        this.igsxRadioList = this.igObj.allData[navActiveIndex].tongue_analysis.tongue_list;
      }
      // 初始置空数据，防止叠加
      this.igObj.igsxData = [];
      // 解析出所有的待遍历数据
      if (this.igsxRadioList[0]) {
        this.igsxRadioList[0].details.forEach(e => {
          e.forEach(item => {
            this.igObj.igsxData = this.igObj.igsxData.concat(item);
          });
        });
      }
      this.igsxRadioList.length > 0 ? this.igsxRadioList[0] : {};
      // 默认选中辨证结果第一个
      this.igsxActiveRadio = 0;
    },
    // *切换就诊人阶段
    change(item, index) {
      this.resetHeightArea('content');
      this.navActiveIndex = index;
      if (this.tabId === 1) {
        // 切换时，切换数据源
        this.setPrescriptionData();
      }
      // AI诊断动态切换辨证结果数据
      if (this.tabId === 2) {
        this.aiRadioList =
          this.aiObj.allData[this.navActiveIndex] && this.aiObj.allData[this.navActiveIndex].dialectical;
        if (this.aiRadioList.length > 0) {
          this.radioChange(this.aiRadioList[0].code);
        }
      }

      // 智能舌诊动态切换数据
      if (this.tabId === 4) {
        this.setIgRadioList();
      }
    },

    // resetHeight
    resetHeightArea(ref) {
      if (ref) {
        this.$refs[ref].scrollTo({ top: 0, behavior: 'instant' });
      }
    },

    // *切换就诊人同阶段不同信息
    changeTab(id) {
      this.tabId = id;
      // 重置不同问诊为第一阶段
      this.navActiveIndex = 0;
      // 就诊信息
      if (+id === 1) {
        this.sdInterFace();
      }
      // AI问诊
      if (+id === 2) {
        this.aiInterFace();
      }
      // 中医体质
      if (+id === 3) {
        this.chineseMedicineInterFace();
      }
      // 智能舌诊
      if (+id === 4) {
        this.intelligenceInterFace();
      }
    },
    // *智能舌诊 - 诊断分析切换
    igzdRadioChange(index) {
      this.igzdActiveRadio = index;
      this.igObj.igzdData = {};
      this.igObj.igzdData = this.igzdRadioList[index];
    },
    // *智能舌诊 - 舌像分析切换
    igsxRadioChange(index) {
      this.igsxActiveRadio = index;
      this.igObj.igsxData = [];
      this.igsxRadioList[index].details.forEach(e => {
        e.forEach(item => {
          this.igObj.igsxData = this.igObj.igsxData.concat(item);
        });
      });
    },
    // *AI诊断，辨证结果切换
    radioChange(id) {
      this.aiActiveRadio = id;
      this.aiObj.dialecticalData = {};
      if (Array.isArray(this.aiRadioList)) {
        const resultArr = this.aiRadioList.filter(item => {
          return item.code === id;
        });
        this.aiObj.dialecticalData = resultArr[0];
      } else {
        this.aiObj.dialecticalData = [];
        this.aiRadioList = [];
      }
    },
    // *作废处方单
    deleteEvent(item) {
      // 如果是治疗但且没有用过卡券，直接作废，用过卡券，弹窗操作确认框
      if (
        item &&
        (item.type === 'TREAT' || item.type === 'TREAT_TJL' || item.type === 'TREAT_AJ' || item.type === 'TREAT_QS') &&
        +item.is_use_service_card
      ) {
        // 弹窗提示作废
        this.deleteVisible = true;
        this.deleteEventId = item.id;
      } else {
        // 直接作废
        this.deletePrescription(item.id);
      }
    },
    // *作废处方单接口
    deletePrescription(id = '') {
      let params = {
        pres_id: id,
      };
      this.$api.deletePrescription(params).then(
        res => {
          this.$Message.success('作废成功');
          this.changeTab(1);
        },
        rej => {
        }
      );
    },
    // *打印处方单
    dispensePrint() {
      const { navActiveIndex } = this;
      const mr_id = this.jzObj.allData[navActiveIndex].id;
      this.$refs.patientPrinter.printPres(mr_id);
    },
    // *打印收费单
    orderPrint() {
      const { navActiveIndex } = this;
      let header = this.jzObj.allData[navActiveIndex];
      let pay_order_id = header && header.pay_order && header.pay_order.order.id;
      this.$refs.patientBillPrinter.printBill(pay_order_id);
    },
    // 弹窗二次确认作废
    continueDelete() {
      const { deleteEventId } = this;
      this.deletePrescription(deleteEventId);
      this.deleteVisible = false;
    },
    dateFormat(timestamp, format = 'YYYY-MM-DD HH:mm:ss', dot = '-') {
      if (!timestamp || timestamp == 0) return dot;
      return util.moment.unix(timestamp).format(format);
    },
    // *点击协定方按钮，获取数据
    saveXDF(item) {
      this.xdfId = item.id;
      const { navActiveIndex } = this;
      this.xdVisible = true;
      let header = this.jzObj.allData[navActiveIndex];
      this.form.name = header.patient && header.patient.name + ' ' + this.dateFormat(header.create_time, 'YYYY-MM-DD');
      this.form.desc = `类型：${header.type_text}\n医生：${header.doctor_name}\n症状：${header.diag_result}`;
    },
    // 点击弹框确定，存为协定方
    saveAgreement(item) {
      const { name, desc } = this.form;
      let params = {
        pres_id: this.xdfId + '',
        name: name,
        desc: desc,
      };
      this.$api.saveAgreement(params).then(
        res => {
          this.xdVisible = false;
          this.$Message.success('存为协定方成功');
        },
        rej => {
        }
      );
    },
  },
  filters: {
    lineFeed(val) {},
  },
};
</script>
<style lang="less" scoped>
.patient-info-com-wrapper {
  // *就诊流程信息样式
  .visit-information {
    background: #fff;
    border-radius: 4px;
    // margin-top: 30px;
    // padding: 20px 20px;
    padding: 20px 16px 20px 20px;
    // height: calc(100vh - 482px);
    // height: calc(100vh - 258px);
    // min-height: 400px;
    height: 610px;
    overflow: hidden;
    .content {
      height: 100%;
      min-height: 400px;
      // height: calc(100% - 100px);
      // overflow-y: scroll ;
    }
    // tab切换
    .tab-nav {
      box-sizing: content-box;
      height: 36px;
      line-height: 36px;
      margin-bottom: 31px;
      padding-top: 15px;
      padding-bottom: 6px;
      border-bottom: 1px solid #f5f5f6;
      .title {
        font-size: 18px;
        margin-right: 50px;
        cursor: pointer;
      }
      .title:hover {
        color: #155bd5;
      }
      .line {
        height: 2px;
        background-image: linear-gradient(to right, #0043cb, #357aff);
        border-radius: 1px;
        margin: 5px auto;
        animation: lineMove 0.2s linear;
      }
      @keyframes lineMove {
        0% {
          width: 0;
        }
        20% {
          width: 20%;
        }
        50% {
          width: 50%;
        }
        100% {
          width: 100%;
        }
      }
      .cur {
        color: #155bd5;
      }
    }
    .left-nav {
      background: #f7f8f9;
      // min-width: 220px;
      min-width: 172px;
      min-height: 400px;
      height: 100%;
      padding-bottom: 80px;
      overflow-y: scroll;
      &::-webkit-scrollbar {
        display: none;
      }
      .stage-active {
        background: #eff4ff;
      }
      .stage-content-active {
        border-left-color: #0245cc !important;
        color: #155bd5;
      }
      .stage-w:hover {
        background: #eff4ff;
        cursor: pointer;
      }
      .stage-w {
        min-height: 92px;
        padding: 30px 0;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        .stage-content {
          border-left: 2px solid transparent;
          padding: 0 16px;
          .stage-info {
            margin-top: 10px;
          }
        }
      }
    }
    .right-content {
      padding: 0px 16px;
      padding-bottom: 80px;
      width: 100%;
      // min-height: 400px;
      height: 100%;
      overflow-y: scroll;
      &::-webkit-scrollbar {
        display: none;
      }
      .basic-info {
        // margin-bottom: 40px;
        // basic公共样式
        color: #333;
        min-height: 400px;
        .basic-title {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
        }
        .basic-distance {
          margin-left: 6px;
        }
        .basic-info-header {
          line-height: 50px;
          border-bottom: 1px solid #dadde7;
        }
        .basic-label {
          color: #aaa;
          min-width: 80px;
          text-align: right;
          line-height: 22px;
          margin-bottom: 16px;
          font-size: 13px;
        }
        .basic-value {
          margin-left: 10px;
          margin-bottom: 16px;
          color: #000;
          line-height: 22px;
          white-space: normal;
          font-size: 13px;
          //text-indent: -4px;
        }
        .abnormal {
          color: #e5634b;
        }
        // 带icon得二级标题
        .second-level-tip {
          margin-left: 10px;
          font-size: 16px;
          line-height: 27px;
        }
        // 个人信息数据展示
        .basic-personal-info {
          margin: 31px 0 20px 0;
        }
      }
    }
  }
  // *处方样式
  .pharmacy-table-wrapper {
    margin-top: 13px;
    .pharmacy-th {
      padding: 18px 0;
      > div {
        vertical-align: center;
      }
      .pharmacy-th-name {
        margin-left: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #333333;
      }
      .pharmacy-th-order {
        margin-left: 26px;
        color: #999999;
        font-size: 12px;
      }
      .pharmacy-th-tag-w {
        margin-left: 20px;
        .pharmacy-th-tag {
          font-size: 12px;
          margin-right: 10px;
          min-width: 46px;
          background: #36a21f;
          padding: 4px 4px;
          border-radius: 16px;
        }
        .tag1 {
          background: rgba(54, 162, 31, 0.1);
          color: rgba(54, 162, 31, 1);
        }
        .tag2 {
          background: rgba(229, 99, 75, 0.1);
          color: rgba(229, 99, 75, 1);
        }
        .tag3 {
          background: rgba(2, 69, 204, 0.1);
          color: rgba(2, 69, 204, 1);
        }
        .tag4 {
          background: rgba(102, 102, 102, 0.1);
          color: rgba(102, 102, 102, 1);
        }
      }
    }
  }
  .pharmacy-tip {
    font-size: 12px;
    padding: 20px;
    color: #999999;
  }
  .total-count {
    margin-right: 20px;
    font-weight: 600;
  }
}
// AI诊断
.dialectical {
  p {
    margin-bottom: 20px !important;
  }
}
// 作废处方单样式
.del-dia {
  p {
    margin-left: 54px;
    width: 376px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 22px;
  }
  .dia-header {
    display: flex;
    h4 {
      margin-left: 17px;
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
    }
  }
  .el-icon-warning {
    color: #faad14;
    font-size: 21px;
  }
}
// 存为协定方样式
::v-deep .xd-dia {
  // height: 285px;
  .el-dialog__title {
    font-weight: 600;
  }
  .el-form-item__label {
    color: #575757;
    font-size: 14px;
  }
  .el-dialog__body {
    border-top: 1px solid #dfe1e7;
    border-bottom: 1px solid #dfe1e7;
    padding: 20px 30px 0 30px;
  }
  .read {
    background: #f7f8f9;
    padding: 14px 15px;
    height: 90px;
    // max-height: 200px;
    overflow-y: scroll;
    color: #999999;
    p {
      line-height: 24px;
      // margin-bottom: 12px;
    }
    span {
      margin-left: 10px;
    }
  }
  .read::-webkit-scrollbar {
    display: none;
  }
  .el-button--primary {
    padding: 10px 30px;
  }
}
</style>
<style lang="less" scoped>
.flex {
  display: flex;
}
.flex-c {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.row {
  margin-bottom: 10px;
}
.noData {
  text-align: center;
  margin-top: 30px;
  font-size: 16px;
  color: #333333;
}
.dot {
  width: 8px;
  height: 8px;
  background: #e5634b;
  border-radius: 50%;
  opacity: 1;
  position: absolute;
  top: 5px;
  right: 5px;
}
.white-space {
  white-space: pre-wrap;
}
// 竖线条样式
.vertical-line {
  width: 2px;
  height: 12px;
  background: #0245cc;
  border-radius: 4px;
}
.current-theme-bcolor {
  color: #0245cc !important;
}
.current-theme-ocolor {
  color: #e5634b !important;
}
.print-btn {
  width: 120px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid #0245cc;
  border-radius: 4px;
  color: #0245cc;
}
.print-btn:hover {
  opacity: 0.5;
}
.recipel-btn {
  border: 1px solid #999999;
  color: #999999;
  padding: 5px 20px;
  background: rgba(255, 255, 255, 0.5);
}
.recipel-btn:hover {
  opacity: 0.5;
}
.empty-show {
  height: 100%;
  height: 400px;
  img {
    width: 136px;
    height: 130px;
  }
  p {
    color: #999999;
    margin-top: 24px;
    font-size: 14px;
  }
}
// 手写icon
.icon {
  position: relative;
  p {
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 6px;
    background: #333;
    box-sizing: border-box;
  }
  .two-icon {
    position: relative;
    top: -3px;
    left: 3px;
    border: 1px solid rgba(255, 255, 255, 0.5019607843137255);
  }
}
::v-deep .el-radio-button__inner {
  background: #fff !important;
  color: #969799 !important;
  min-width: 98px;
}
::v-deep .is-active {
  .el-radio-button__inner {
    color: #0245cc !important;
  }
}
::v-deep .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 8px 0px 0px 2px;
}
::v-deep .el-radio-button:last-child .el-radio-button__inner {
  border-radius: 0px 8px 2px 0px;
}
// 图片圆角美观设置
::v-deep .el-image__inner {
  border-radius: 4px !important;
}
::v-deep .el-loading-spinner {
  top: 100px !important;
}
</style>

<style lang="less" scoped>
// 随访-drop
.visit-tip-text {
  margin-top: 20px;
  height: 15px;
  font-size: 14px;
  font-weight: 400;
  line-height: 0px;
  color: #999999;
}
.visit-label {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  min-width: 100px;
  text-align: right;
}
.visit-text {
}
.visit-tag {
  background: #c4c4c4;
  border-radius: 14px;
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  padding: 6px 14px;
}
.small-visit-tag {
  border-radius: 12px;
  background: #e3e4e5;
  color: #666666;
  font-size: 12px;
  font-weight: 300;
  padding: 6px 14px;
  width: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 26px;
}
.small-visit-tag-active {
  background: #cbd9f7 !important;
  color: #1157e5 !important;
}
.mr10 {
  margin-right: 10px;
}
.mt6 {
  margin-top: 6px;
}
.mt10 {
  margin-top: 10px;
}
.mt24 {
  margin-top: 24px;
}
p {
  margin: 0;
}

// v2
.img-wrapper {
  .custom-img,
  img {
    width: 68px;
    height: 68px;
    border-radius: 5px;
    margin-right: 10px;
    margin-bottom: 12px;
  }
}

.small-block {
  width: 30%;
}
.mb12 {
  margin-bottom: 12px;
}
.small-title {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
}
.ml46 {
  margin-left: 46px;
}
.key {
  font-size: 13px;
  display: inline-block;
  width: 60px !important;
  min-width: 60px !important;
  text-align: right !important;
  line-height: 22px;
  color: #aaa;
}
.remark {
  margin-top: 20px;
  background: #fafbff;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 400;
  color: #666b6f;
  padding: 10px 20px;
  word-break: break-all;
}
</style>
