<template>
  <div class="patient-record-wrapper">
    <el-dialog
      title="患者病历"
      :visible.sync="dialogVisible"
      width="1200px"
      lock-scroll
      :append-to-body="true"
      :before-close="handleClose"
    >
      <div class="content" v-if="dialogVisible">
        <PatientInfoCom :isModal="true" :titList="titList" :pt_id="pt_id"></PatientInfoCom>
      </div>
    </el-dialog>
  </div>
</template>

<script type="text/javascript">
import PatientInfoCom from './RePatientInfoCom';

export default {
  name: 'k-patientRecord-modal',
  components: {
    PatientInfoCom,
  },
  mixins: [],
  props: {
    dialogVisible: {
      type: Boolean,
      default: () => false,
    },
    pt_id: {
      type: [Number, String],
      default: () => '',
    },
  },
  data() {
    return {
      // tab切换数据
      titList: [
        { id: 1, name: '既往病历' },
        // { id: 2, name: 'AI诊断' },
        // { id: 3, name: '中医体质' },
        { id: 5, name: '食养信息' },
        { id: 4, name: '智能舌诊' },
      ],
    };
  },
  computed: {},
  watch: {
    dialogVisible(val) {
      if (!val && this.$router.history.current.path === '/his/outpatient/diagnosis') {
        this.$router.replace({ query: { ...this.$route.query, btn: false } });
      }
    },
  },
  created() {},
  mounted() {},
  methods: {
    handleClose() {
      this.$emit('update:dialogVisible', false);
    },
  },
  filters: {},
};
</script>
<style lang="less" scoped>
::v-deep .content {
  height: 620px;
  overflow-y: scroll;
  //margin-top: -20px;
  // 去除x轴的滚动条样式
  &::-webkit-scrollbar {
    display: none;
  }

  .visit-information {
    height:600px !important;
    padding-bottom: 0px !important;

    .left-nav, .right-content {
      //height: 100% !important;
    }
  }
}
</style>
<style lang="less" scoped>
::v-deep .el-dialog {
  // margin-top: 5vh !important;
  height: 680px;
  min-height: 680px;
  overflow: hidden;
  min-width: 1200px;
  // overflow-y: scroll;
  // &::-webkit-scrollbar {
  //   display: none;
  // }
}

::v-deep .el-dialog__header {
  // height: 40px;
  // padding: 20px 0 0px 0px !important;
}

::v-deep .el-dialog__title {
  font-size: 18px;

  &:before {
    display: inline-block;
    content: '';
    width: 2px;
    height: 12px;
    background: #1157e5;
    margin-right: 6px;
  }
}

::v-deep .el-dialog__body {
  border-top: none;
  padding: 0px 0px 20px 20px;
}

::v-deep .visit-information {
  padding: 0px 16px 20px 20px !important;
}
</style>
