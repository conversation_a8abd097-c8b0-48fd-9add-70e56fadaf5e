<template>
  <div class="programModal-wrapper">
    <el-dialog
        :visible="value"
        title="辨证结果分析"
        width="920"
        lock-scroll
        @close="handleClose"
        append-to-body
        :destroy-on-close="true"
    >
      <div class="top-result-block flex">
        <div class="result-block-left">
          <chart-view
            height="200px"
            :chart-option="circular_nesting_options">
          </chart-view>

<!--          <div class="progress-block">-->
<!--            <div class="progress-item" v-for="( check_item, check_index ) in disease_cause" :key="check_item+check_index">-->
<!--              <span class="progress-name mr4">{{ check_item.title }}</span>-->
<!--&lt;!&ndash;              <el-progress&ndash;&gt;-->
<!--&lt;!&ndash;                  class="progress-content" :stroke-width="4" text-inside :format="() => {return ''}"&ndash;&gt;-->
<!--&lt;!&ndash;                  :percentage='Number(check_item.score)'&ndash;&gt;-->
<!--&lt;!&ndash;                  :color='colorList[check_index]'></el-progress>&ndash;&gt;-->
<!--              <p class="progress-score">{{ check_item.score }}%</p>-->
<!--            </div>-->
<!--          </div>-->
        </div>
        <div class="result-block-right hidden-scroll">
          <div class="result-blcok-right-item"
            v-for="(item, index) in disease_cause"
            :key="'disease_cause'+index"
          >
            <div class="item-title">{{ item.title }}</div>
            <span class="item-effect" v-for="(item_effect, index) in item.symptom" :key="item_effect+index">{{ item.symptom.length-1 == index ? `${item_effect}` : `${item_effect}，` }}</span>
          </div>
        </div>
      </div>

      <el-table :data="dataList" v-loading="loading" element-loading-spinner="el-icon-loading" element-loading-text="加载中.." stripe style="width: 100%" :border="true" class="mt12">
        <el-table-column prop="" label=""  :resizable="false" align="center" :width="dataList.length ? '100' : ''">
          <template slot-scope="{row, $index}">
            <span>{{ fields[$index] }}</span>
          </template>
        </el-table-column>

          <el-table-column prop="" :resizable="false" v-for="(column_item, column_index) in columns" :key="'columns'+column_index">
            <template slot-scope="scope" slot="header">
              <span>{{ column_item.title }}【<span style="color: #E5634B">置信度：{{ column_item.score }}%</span>】</span>
            </template>
            <template slot-scope="scope">
              <span>{{ scope.row[`field${column_index}`] || '-' }}</span>
            </template>
          </el-table-column>
      </el-table>

    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "analysisModal",
  components: {

  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    mr_id: {
      type: String,
      default: ''
    },
    circular_nesting_options: {
      type: Object,
      default: () => {}
    },
    disease_cause: {
      type: Array,
      default: () => []
    },
    colorList: {
      type: Array,
      default: () => []
    }
  },
  data () {
    return {
      dataList: [],
      columns: [],
      fields: [],
      loading: false,
    }
  },
  computed: {

  },
  watch: {
    value (visible) {
      if ( visible ) {
        this.$router.onReady(() => {
          this.getAnalysisData(this.mr_id || this.$route.query.mr_id)
        })
      }
    }
  },
  created() {

  },
  mounted() {
  },
  methods: {
    // 关闭弹窗
    emitClose () {
      this.$emit('input', false)
    },
    handleClose () {
      this.emitClose()
    },
    //  获取辨证分析数据
    getAnalysisData (mr_id) {
      this.loading = true
      this.dataList = []
      this.columns = []
      this.fields= []
      this.$api.getDiagnosisdialecticalanalysis({mr_id}).then( res => {
        this.dataList = this.handleList(res.data, res.field)
      }).finally(() => this.loading = false)
    },

    handleList (list, field) {
      let resultList = []
      this.columns = []
      this.fields = field
      field && field.forEach( (field_item, field_index) => {
        let obj = {}
        list.forEach( (list_item, list_index) => {
          if (field_index == 0) {
            this.columns.push({
              title: list_item.header.title,
              score: list_item.header.score
            })
          }
          obj['field'+list_index] = list_item.body[field_index].value
        })
        resultList.push(obj)
      } )
      return resultList
    }
  },
  filters: {

  },
}
</script>

<style lang="less" scoped>
::v-deep .el-dialog {
  max-width: 920px;
  min-width: 920px;
  height: 640px;
  overflow: hidden;
  border-radius: 6px;
  .el-dialog__header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    .el-dialog__title {
      font-size: 14px;
      font-weight: 600;
      color: #000000;
      line-height: 10px;
    }
  }


  .el-dialog__body {
    height: 540px;
    padding: 10px 20px 30px;
    overflow-y: scroll;
  }
}
</style>

<style lang="less" scoped>
.top-result-block {
  width: 100%;

  .result-block-left {
    width: 280px;
    .progress-block {
      display: flex;
      align-items: center;
      margin-top: 20px;
      padding-left: 6px;
      .progress-item {
        display: flex;
        align-items: center;
        flex: 1;
        .progress-name {
          font-size: 12px;
          font-weight: 400;
          color: #000000;
          line-height: 20px;
          margin-right: 4px;
        }

        .progress-score {
          font-size: 12px;
          font-weight: 500;
          color: #E5634B;
          line-height: 17px;
        }
      }
    }
  }

  .result-block-right {
    flex: 1;
    margin-left: 20px;
    height: 205px;
    overflow: scroll;
    background: #F9FAFD;
    border-radius: 4px;
    padding: 12px 10px;

    .result-blcok-right-item {
      margin-bottom: 10px;
      .item-title {
        font-size: 12px;
        font-weight: 400;
        color: #000000;
        line-height: 17px;
        margin-bottom: 2px;
      }

      .item-effect {
        font-size: 12px;
        font-weight: 300;
        color: #666666;
        line-height: 18px;
      }
    }
  }
}

::v-deep .el-table {
  overflow: unset;
  .el-table__header-wrapper {
    position: sticky;
    top: -10px;
    z-index: 100;
  }
  .cell {
    line-height: 18px;
    white-space:pre-wrap;
    text-align: justify;
  }
}

.analysis-table-wrapper {
  display: flex;
  .table-item {
    flex: 1;
    .table-item-cell {
      min-height: 20px;
      padding: 6px 10px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.mt12 {
  margin-top: 12px;
}
</style>
