<template>
  <!-- 辨证参考模块 -->
  <div class="dialectical-wrapper hidden-scroll">
    <!-- 查看辨证结果分析 -->
    <div class="result-wrapper" v-if="showAnalysisModal">
      <a class="result" @click="analysisVisibleShow">
        <svg-icon iconClass="eyes"></svg-icon>
        辨证结果分析</a>
    </div>

  <!--  <p class="header&#45;&#45;currency">辨证参考</p>-->
    <div class="dialectical-content--model hidden-scroll" v-loading="dialecticalLoading" element-loading-spinner="el-icon-loading"
							element-loading-text="加载中..">
      <div v-show="disease_cause && disease_cause.length" class="mt20 flex">
        <div class="flex flex-item-align flex-item-center">
          <div class="flex-1 flex flex-item-center">
            <chart-view
              height="250px"
              width="300px"
              :chart-option="circular_nesting_options">
            </chart-view>
          </div>

          <!-- 症状多选框 -->
          <div class="flex-1 flex flex-item-center" style="width: 300px;margin-left: 50px;">
            <div>
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled :label="check_item.title" v-for="( check_item, check_index ) in disease_cause" :key="check_item+check_index">
                  <div class="custom-checkbox--style flex  flex-item-between flex-item-align">
                    <div style="width: 225px" class=" flex flex-item-between">
                  <span class="medicine-model-card-medicine">
                  <el-popover
                    width="200"
                    :open-delay="500"
                    trigger="hover">
                    <p slot="reference">
                      <span class="progress-name mr4">{{ check_item.title }}</span>
                      <svg-icon iconClass="question"></svg-icon>
                    </p>
                    <div class="medicine-description--currency">
                      <p class="title--medicine">{{ `"${check_item.title}"的常见诊断症状为:` }} </p>
                      <p class="character--medicine"></p>
                      <span class="effect--medicine" v-for="(item, index) in check_item.symptom" :key="item+index">{{ check_item.symptom.length-1 == index ? `${item}` : `${item}，` }}</span>
                    </div>
                  </el-popover>
                </span>

                      <p style="width: 163px" class="ml6 mt2">
                        <el-progress class="margin-left12" :stroke-width="11" text-inside :format="() => {return ''}"
                                     :percentage='Number(check_item.score)'
                                     :color='colorList[check_index]'></el-progress>
                      </p>
                    </div>
                    <p class="progress-score">{{ check_item.score }}%</p>
                  </div>
                </el-checkbox>
              </el-checkbox-group>

              <div class="dialectical-description mb16">
              <span v-if="dialectical.default_disease_cause && dialectical.default_disease_cause.length">
                系统判断患者可能存在“
                <span class="outstanding-description">
                  <span class="bold" v-for="(item, index) in dialectical.default_disease_cause" :key="index">
                    {{ dialectical.default_disease_cause && dialectical.default_disease_cause.length-1 !== index ? `${item},` : `${item}` }}
                  </span>
                </span>
                ”，
              </span>
                <span>可根据实际情况选择调整</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <analysis-modal v-model="analysisVisible" :mr_id="mr_id" :circular_nesting_options="circular_nesting_options" :disease_cause="disease_cause" :colorList="colorList"></analysis-modal>
  </div>
</template>

<script>
import analysisModal from './analysisModal.vue';
  export default {
    name: "dialectical",
    components: {
      analysisModal
    },
    mixins: [],
    props: {
      mr_id: {
        type: String,
        default: ''
      },
      dialectical: {
        type: [Object, Array],
        default: () => {}
      },
      loading: {
        type: Boolean,
        default: () => false
      },
      showAnalysisModal: {
        type: [Boolean, String],
        default: () => false
      }
    },
    data () {
      return {
        // colorList: '#BBA9AF', // 进度条颜色组
        colorList: ['#707CC2','#EE7A5F','#EDC760','#6BCE99', '#7DD4E8','#BE7BD5','#CBD6A2','#C87181'],
        circular_nesting_options: {}, // 环形嵌套图配置
        checkList: [],
        checkeddiseasList: [], // 用来展示的病症数据
        disease_cause: [], // 病症列表

        flag: false,

        analysisVisible: false,
      }
    },
    computed: {
      dialecticalLoading () {
        return false
      }
    },
    watch: {
      'dialectical.disease_cause' : {
        immediate:true,
        deep: true,
        handler ( val ) {
          // console.log("-> %c val  === %o", "font-size: 15px;color: green;", val)
          // 获取病症列表
          this.disease_cause = this.$lodash.cloneDeep(val)
          let checkList_copy = []
          this.disease_cause && this.disease_cause.map( item => {
            // console.log("-> %c item  === %o", "font-size: 15px;color: green;", item)
            if ( item.is_chose == 1 ) {
              checkList_copy.push(item.title)
              // console.log("-> %c checkList_copy  === %o", "font-size: 15px;color: green;", checkList_copy)
            }
          } )
          // console.log(this.$attrs)
          if ( !this.$attrs.isFromGrammer ) {
            this.checkList = checkList_copy
          }
          this.$emit('getCheckList', this.checkList)
        }
      },
      'dialectical.disease_cause_label' : {
        immediate:true,
        deep: true,
        handler ( val ) {
          if ( !this.$attrs.isFromGrammer ) {
            this.setCirclePieData()
          }
        }
      },
      'dialectical.disease_location_label' : {
        immediate:true,
        deep: true,
        handler ( val ) {
          if ( !this.$attrs.isFromGrammer ) {
            this.setCirclePieData()
          }
        }
      },
    },
    created() {

    },
    mounted() {
    },
    methods: {
      analysisVisibleShow () {
        this.analysisVisible = true
      },

      getNormalSymptomName (item, index) {
        let allLength = this.$attrs.normalSymptomList.length
        if ( index < 2 ) {
          return allLength - 1 !== index ? `${item.name},` : item.name
        }
        if ( index == 2 ) {
          return `${item.name}...`
        }
        return
      },

      // 处理环形图数据
      handlerCirclePieData ( ) {
        let innerData = this.descToArrHandle(this.dialectical.disease_location_label)
        let outData = this.descToArrHandle(this.dialectical.disease_cause_label)
        let circlePieData = {
          series: [
            { data: innerData },
            { data: outData }
          ]
        }
        return circlePieData
      },
      setCirclePieData () {
        this.circular_nesting_options = this.$eChartFn.innerPie(this.handlerCirclePieData())
      },

      // 将对象转换为指定数组
      descToArrHandle (obj) {
        if ( obj == null ) return []
        let arr = []
        let kArr = Object.keys(obj)
        kArr.map((item, i) => {
          arr.push({
            name: item,
            value: obj[item]
          })
        })
        return arr
      },
    },
    filters: {

    },
  }
</script>

<style lang="less" scoped>
// 辨证参考样式
/* 内部模块样式 */
.dialectical-wrapper {
  position: relative;
  height: 100%;
  background: #fff;

  /* 主体内容 */
  .dialectical-content--model {
    padding: 0 20px 0px 20px;
    margin-left: -50px;
    .progress-name {
      width: 50px;
    }

    .dialectical-description{
      width: 100%;
      box-sizing: border-box;
      padding: 8px 16px;
      background: rgba(247, 247, 249, 0.8);
      border-radius: 4px;
      font-size: 14px;
      font-weight: 300;
      color: #000000;
      line-height: 22px;
      .outstanding-description {
        font-weight: 600;
      }
    }

    .progress-score {
      width: 50px;
      text-align: right;
      font-size: 13px;
      font-weight: 500;
      color: #9c9c9d;
      line-height: 20px;
    }



    /* 自定义多选框样式 */
    ::v-deep .el-checkbox-group {
      display: flex;
      flex-wrap: wrap;
      &>.is-checked {
        .check-item--title {
          color: #1157E5 !important;
        }
      }
    }

    ::v-deep .el-checkbox {
      padding: 12px 11px 6px;
      width: 100%;
      margin-right: 0px !important;
      // background: rgba(255, 255, 255, 0);
      border-radius: 4px;
      // border: 1px solid #DFE1E6;
      // margin-bottom: 12px;
      position: relative;
      z-index: 0;

      .el-checkbox__label {
        width: 98%;
      }

      .custom-checkbox--style {
        .check-item--title {
          font-size: 15px;
          font-weight: 400;
          color: #000000;
          line-height: 21px;
        }

        .check-item--content {
          font-size: 12px;
          font-weight: 300;
          color: #666666;
          line-height: 17px;
        }
      }

      /* 方框位置 */
      .el-checkbox__input {
        // top: 17px;
        // right: -95%;
        // display: flex;
        // width: 16px;

        /* 方框的圆角 */
        .el-checkbox__inner {
          border-radius: 4px;
        }
      }
    }
  }
}
</style>

<style lang="less" scoped>
.result-wrapper {
  .result{
    cursor: pointer;
  }
}

.el-empty {
  text-align: left;
  // margin-top: 100px;
}
// 自定义空状态文本样式
.custom-description-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  .description-content-title {
    width: 286px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.35);
    line-height: 22px;
    margin-bottom: 16px;
    text-align: center;
  }
  .description-content-heightLightTitle {
    color: rgba(17, 87, 229, 0.75);
  }

  .description-content {
    width: 256px;
    background: rgba(247, 247, 249, 0.54);
    border-radius: 4px;
    padding: 16px;

    .description-content-block {
      margin-bottom: 16px;
      .content-block-title {
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        line-height: 12px;
        margin-bottom: 6px;
      }

      .content-block-text {
        font-size: 12px;
        font-weight: 300;
        color: #909399;
        line-height: 21px;
      }

      &:last-of-type {
        margin-bottom: 0px;
      }
    }
  }
}

</style>
