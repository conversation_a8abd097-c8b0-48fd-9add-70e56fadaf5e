<template>
  <el-popover
    placement="bottom-start"
    width="150"
    @show="show"
    @hide="hide"
    trigger="click">
    <div slot="reference" class="popover-tag">
      <p class="tag-text ecs">
        <svg-icon
          icon-class="filter"
          :class="{ lineHeight: isLineHeight }"
        ></svg-icon>
        <span style="margin-left: 4px;" :class="{ lineHeight: isLineHeight }">{{ title }}</span>
      </p>
      <svg-icon
        icon-class="filter-down-arrow"
        class="downSelect"
        :class="{ rotateSvg: optionVisible, lineHeight: isLineHeight }"
      ></svg-icon>
    </div>
    <div>
      <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>
      <div style="margin: 15px 0;"></div>
      <el-checkbox-group v-model="checkedOptions" @change="handleCheckedCitiesChange">
        <el-checkbox v-for="item in optionsList" :label="item" :key="item.id">{{item.desc}}</el-checkbox>
      </el-checkbox-group>
    </div>
  </el-popover>
</template>

<script>
import {debounce} from 'lodash'

export default {
  name: 'filterType',
  components: {},
  mixins: [],
  props: {
    optionsList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      checkAll: false,
      checkedOptions: [],
      isIndeterminate: true,
      optionVisible: false,
    };
  },
  computed: {
    title () {
      return this.checkedOptions.length === 1 ? this.checkedOptions[0].desc : '筛选类型'
    },
    isLineHeight () {
      if ( this.checkedOptions.length < this.optionsList.length ) {
        return true
      }
      return false
    },
  },
  watch: {
    optionsList (val) {
      this.checkedOptions = val
      this.isIndeterminate = false
      this.checkAll = true
    }
  },
  created() {},
  mounted() {
  },
  methods: {
    handleCheckAllChange(val) {
      this.checkedOptions = val ? this.optionsList : [];
      this.isIndeterminate = false;
      this.optionsChange()
    },
    handleCheckedCitiesChange(value) {
      let checkedCount = value.length;
      this.checkAll = checkedCount === this.optionsList.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.optionsList.length;
      this.optionsChange()
    },
    show () {
      this.optionVisible = true
    },
    hide () {
      this.optionVisible = false
    },
    optionsChange: debounce( function () {
      this.$emit('change', this.checkedOptions)
    }, 500 )
  },
};
</script>

<style lang="less" scoped>
.popover-tag {
  //height: 20px;
  padding: 0px 10px 10px;
  width:184px;
  max-width:184px;
  cursor: pointer;
  display: flex;
  align-items: center;
  background: #FFFFFF;
  box-shadow: 0px 1px 1px 0px #CED9EB;
  margin-bottom: 6px;

  .tag-text {
    flex: 1;
  }
}
.el-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  .el-checkbox {
    width: 100%;
    word-break: break-all;
    white-space: pre-line;
    margin-right: 0px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0px;
    }
  }
}
.downSelect {
  //width: 10px;
  transition: transform 0.3s;
  cursor: pointer;
  margin-left: 6px;
  color: #000;
}

.rotateSvg {
  transition: transform 0.3s;
  transform: rotate(-180deg);
}
.lineHeight {
  color: #155BD4 !important;
}
p {
  margin: 0px;
}
</style>
