<template>
  <!-- 非弹窗的患者详情 -->
  <div class="patient-info-com-wrapper">
    <!-- 就诊信息 -->
    <div class="visit-information">
      <div class="header-tab position-relative" v-if="isShowHeaderTab">
        <div class="tab-nav flex" :class="{ 'flex-item-l-center': getTitList.length > 1 }">
          <div class="singleTitle" v-if="getTitList.length === 1">{{ getTitList[0].name }}</div>
          <div
            v-else
            v-for="(item, index) in getTitList"
            :key="index + item.name"
            :class="{ cur: tabId == item.id }"
            class="title"
            @click="changeTab(item.id)"
          >
            <div>
              {{ item.name }}
            </div>
            <div v-if="tabId == item.id" class="line"></div>
          </div>

          <el-button
            class="inquiry-btn ui-btn-small-h"
            type="primary"
            size="medium"
            @click="inquiry"
            v-if="isCanUseEventBtn"
          >
            <img src="@/assets/image/quick.png" alt="" />
            <span>快捷问诊</span>
          </el-button>
        </div>
      </div>
      <div class="content flex">
        <div v-show="tabId != 5">
          <filter-type :optionsList="medicalListStatusDesc" @change="filterChange" v-show="tabId == 1"></filter-type>
          <div class="left-nav" :class="{ 'filter-reset-height': tabId == 1 }" v-if="navList.length > 0">
            <div
              v-for="(item, index) in navList"
              :key="'nav' + index"
              :class="{ 'stage-active': navActiveIndex === index }"
              class="stage-w"
              @click="change(item, index)"
            >
              <div :class="{ 'stage-content-active': navActiveIndex === index }" class="stage-content">
                <div class="stage-time" style="display: flex">
                  <!-- 在开具处方页面，患者病历新增由上一步带出的病历信息-->
                  <div v-if="$route.path.includes('treatment') && index === 0">今天</div>
                  <div v-else style="letter-spacing: 1px">{{ item && item.create_time | data_format('YY/MM/DD') }}</div>
                  <div class="flex flex-item-align">
                    <template v-if="item.visit_status === '1'">
                      <svg-icon
                        style="width: 16px"
                        v-if="navActiveIndex === index"
                        icon-class="follow-up-active"
                      ></svg-icon>
                      <svg-icon v-else style="width: 16px" icon-class="follow-up"></svg-icon>
                    </template>
                    <svg-icon
                      icon-class="hospital"
                      v-show="item.source_from === 'hospital'"
                      style="marginleft: 4px; width: 24px; height: 14px"
                    ></svg-icon>
                    <svg-icon
                      v-if="item.contain_herbs == '1'"
                      icon-class="pres"
                      :style="{ color: navActiveIndex === index ? '#155BD4' : '#999', marginLeft: '4px' }"
                    ></svg-icon>
                  </div>
                </div>
                <!--                <p v-show="+tabId !== 3 || +tabId === 4" class="stage-info">-->
                <!--                  {{ item && item.name }} / {{ item && item.sex_text }} {{ item && item.age && `/ ${item.age}` }}-->
                <!--                </p>-->

                <!--                <p v-show="+tabId === 3" class="stage-info">{{ item && item.name }} / {{ item && item.sex_text }}</p>-->
                <!--                <p-->
                <!--                  class="small-visit-tag mt6"-->
                <!--                  :class="{ 'small-visit-tag-active': navActiveIndex === index }"-->
                <!--                  v-if="item.status == 'FOLLOW_UP'"-->
                <!--                >-->
                <!--                  已随访-->
                <!--                </p>-->
                <div class="stage-info-re ecs" :class="{ 'stage-info-re-active': navActiveIndex === index }">
                  <span>{{ formatTime(item.create_time) }}</span>
                  <span class="flex flex-item-align" v-if="tabId == '1'"
                    ><span class="stage-vertical-line"></span
                    ><span
                      class="stage-dot"
                      :style="{ backgroundColor: getMedicineStatusColor(item.order_status) }"
                    ></span
                    >{{ item.order_status_desc }}</span
                  >
                  <span class="flex flex-item-align" v-if="item.exist_return_desc">
                    <span class="stage-vertical-line"></span>
                    <span>{{ item.exist_return_desc }}</span>
                  </span>
                </div>
              </div>
            </div>
            <div v-if="+navList.length === 0 && tabId != 5" class="noData">暂无数据</div>
          </div>
        </div>
        <!-- right -->
        <div
          v-loading="rightLoading"
          class="right-content"
          element-loading-spinner="el-icon-loading"
          element-loading-text="加载中.."
          ref="content"
        >
          <!-- // *就诊信息 -->
          <div v-show="tabId === 1" class="basic-info">
            <div v-show="+navList.length !== 0">
              <!-- //*患者病历 -->
              <div class="basic-info-header flex-b flex-item-v-center">
                <div class="flex flex-item-v-center">
                  <p class="vertical-line"></p>
                  <p class="basic-distance basic-title">患者病历</p>
                </div>
                <div>
                  <el-button
                    v-if="isCanUseEventBtn && jzObj.prescriptionData && jzObj.prescriptionData.length"
                    size="medium"
                    type="text"
                    @click="editMedicalRecord"
                    >修改病历
                  </el-button>

                  <!--  该字段原逻辑为限制打印病历按钮展示    && currentData.contain_herbs === '1'-->
                  <el-button
                    v-if="isCanUseEventBtn && jzObj.prescriptionData && jzObj.prescriptionData.length"
                    class="print-btn"
                    size="medium"
                    @click="medicalRecordPrint"
                    >打印病历
                  </el-button>

                  <el-button
                    type="ghost"
                    style="height: 36px; border: 1px solid #1157e5; color: #1157e5"
                    class="flex-center"
                    @click="importBtn(jzObj.info.id, jzObj.info?.record_image_details?.length > 0)"
                    v-if="isShowImportBtn"
                    >导入历史问诊
                  </el-button>
                </div>
              </div>

              <div class="basic-personal-info flex flex-item-v-center">
                <!-- 就诊二级标题 -->
                <!-- icon -->
                <div class="icon">
                  <p class="one-icon"></p>
                  <p class="two-icon"></p>
                </div>
                <p class="second-level-tip">
                  <span>患者信息</span>
                  <svg-icon v-if="his_version === '4.0'" icon-class="patient-his4.0" class="his4-tag"></svg-icon>
                </p>
              </div>
              <el-row v-for="(item, index) in basicDetail" :key="index">
                <p class="ml46 small-title mb8">{{ item.title }}</p>
                <el-col :span="6" class="flex" v-for="(info, infoIndex) in item.contents" :key="infoIndex">
                  <p class="basic-label" :style="{ minWidth: infoIndex !== 0 ? '60px' : '100px' }">
                    {{ info.type_text }}
                  </p>
                  <p class="basic-value">{{ info.type_val }}</p>
                </el-col>
              </el-row>

              <!-- 病历信息 -->
              <div class="basic-personal-info flex flex-item-v-center">
                <!-- 就诊二级标题 -->
                <!-- icon -->
                <div class="icon">
                  <p class="one-icon"></p>
                  <p class="two-icon"></p>
                </div>
                <p class="second-level-tip">病历信息</p>
              </div>
              <!-- medical_details -->

              <!-- 问诊参考（影像参考） -->
              <div v-if="record_image_details.length">
                <div class="flex mb12">
                  <p class="ml46 small-title">影像资料</p>
                </div>
                <div
                  class="mb12 flex"
                  v-for="(images_item, images_index) in record_image_details"
                  :key="'images' + images_index"
                >
                  <div class="basic-label key ml46 mr10" v-if="images_item.type_val.length">
                    {{ images_item.type_text }}
                  </div>
                  <div class="img-wrapper" v-if="images_item.type_val.length">
                    <el-image
                      v-for="(img_item, img_index) in images_item.type_val"
                      :key="'img_index' + img_index"
                      :src="img_item"
                      class="cursor custom-img"
                      :preview-src-list="images_item.type_val"
                    >
                    </el-image>
                    <!-- <img class="cursor" :src="img_item" alt="" v-for="( img_item, img_index ) in images_item.type_val" :key="'img_index'+img_index"> -->
                  </div>
                </div>
              </div>
              <!-- 症状与主诉 -->
              <el-row v-if="jzObj.info.symptom_details && jzObj.info.symptom_details.length">
                <div class="flex mb12">
                  <p class="ml46 small-title">症状与主诉</p>
                </div>
                <el-col
                  v-for="(item, index) in jzObj.info.symptom_details || []"
                  :key="'md' + index"
                  :span="24"
                  class="flex"
                >
                  <p class="basic-label key ml46">{{ item && item.type_text }}</p>
                  <p class="basic-value">
                    {{ item.type_val || '-' }}
                  </p>
                </el-col>
              </el-row>

              <!-- 辨证参考 -->
              <div class="flex" v-if="currentData.dialectic_analysis === '1'">
                <p class="basic-label key ml46">辨证参考</p>
                <p class="basic-value">
                  <dialectical showAnalysisModal :mr_id="mr_id" :dialectical="jzDialecticalData"></dialectical>
                </p>
              </div>

              <!-- 既往史与身体指标，体格检查与身体指标并入循环 -->
              <div class="block ml46">
                <div class="mt12" v-if="medical_details.length">
                  <div
                    class="flex"
                    v-for="(medical_item, medical_index) in medical_details"
                    :key="'medical' + medical_index"
                  >
                    <span class="key">{{ medical_item.type_text }}</span>
                    <span class="basic-value">{{ medical_item.type_val || '-' }}</span>
                  </div>
                </div>
              </div>

              <!-- 处方 -->
              <div v-if="jzObj.prescriptionData && jzObj.prescriptionData.length > 0">
                <div class="basic-info-header flex-b flex-item-v-center">
                  <div class="flex flex-item-v-center">
                    <p class="vertical-line"></p>
                    <p class="basic-distance basic-title">处方/治疗</p>
                  </div>
                  <div>
                    <el-button
                      v-if="isCanUseEventBtn && jzObj.info.pay_order && jzObj.info.pay_order.order.status == 'WAIT_PAY'"
                      class="print-btn"
                      size="medium"
                      @click="cancelOrder"
                      >整单作废
                    </el-button>

                    <el-button
                      v-if="isCanUseEventBtn && jzObj.prescriptionData && jzObj.prescriptionData.length"
                      class="print-btn"
                      size="medium"
                      @click="dispensePrint"
                      >打印处方/治疗单
                    </el-button>
                  </div>
                </div>
                <div
                  v-for="(item, index) in jzObj.prescriptionData"
                  :key="'pre' + index"
                  class="pharmacy-table-wrapper"
                >
                  <div class="pharmacy-th flex-b">
                    <div class="flex">
                      <div class="flex-c">
                        <!-- icon -->
                        <div class="icon">
                          <p class="one-icon"></p>
                          <p class="two-icon"></p>
                        </div>
                      </div>
                      <div class="pharmacy-th-name flex-c" style="font-size: 15px">
                        {{ item && item.type_text }}
                      </div>
                      <div class="pharmacy-th-order flex-c">单号:{{ item && item.pres_code }}</div>
                      <div v-show="item" class="pharmacy-th-tag-w flex">
                        <p v-if="item.his_pres_status === 'WAIT_PAY'" class="pharmacy-th-tag flex-c tag1">
                          {{ item.his_pres_status_text }}
                        </p>
                        <p v-if="item.his_pres_status === 'HAS_PAY'" class="pharmacy-th-tag flex-c tag3">
                          {{ item.his_pres_status_text }}
                        </p>
                        <p v-if="item.his_pres_status === 'HAS_MDC'" class="pharmacy-th-tag flex-c tag3">
                          {{ item.his_pres_status_text }}
                        </p>
                        <p v-if="item.his_pres_status === 'ABORT'" class="pharmacy-th-tag flex-c tag4">
                          {{ item.his_pres_status_text }}
                        </p>
                        <p v-if="item.his_pres_status === 'WAIT_PAY_CLOSED'" class="pharmacy-th-tag flex-c tag4">
                          {{ item.his_pres_status_text }}
                        </p>
                      </div>
                    </div>
                    <div>
                      <el-button
                        v-show="isCanUseEventBtn && item.type === 'HERBS'"
                        plain
                        class="recipel-btn-ck"
                        type="default"
                        style="font-size: 12px"
                        @click="saveXDF(item)"
                      >
                        存为协定方
                      </el-button>

                      <!-- :disabled="!(item.his_pres_status === 'HAS_PAY' || item.his_pres_status === 'HAS_MDC')" -->
                      <!-- :class="{'recipel-btn-disabled':(!(item.his_pres_status === 'HAS_PAY' || item.his_pres_status === 'HAS_MDC'))}" -->
                      <el-button
                        v-show="isCanUseEventBtn && item.his_pres_status !== 'WAIT_PAY_CLOSED'"
                        :disabled="item.his_pres_status === 'ABORT' || item.his_pres_status === 'WAIT_PAY_CLOSED'"
                        class="recipel-btn"
                        style="font-size: 12px"
                        @click="deleteEvent(item, item.his_pres_status, jzObj)"
                      >
                        {{ item.his_pres_status === 'ABORT' ? '已' : '' }}{{ getInvalidateText(item.type) }}
                      </el-button>
                      <!-- <el-button class="recipel-btn" :disabled="jzObj.chargeData.pres_list[index+2] && jzObj.chargeData.pres_list[index+2].his_pay_status !== 'HAS_PAY'" @click="deleteEvent(item)">作废处方单</el-button> -->
                    </div>
                  </div>
                  <el-table
                    v-if="item && item.attrs"
                    :cell-style="{ textAlign: 'center' }"
                    :data="item && item.attrs"
                    :header-cell-style="{
                      background: '#F7F7F8',
                      color: '#333333',
                      textAlign: 'center',
                    }"
                    style="width: 100%"
                  >
                    <el-table-column align="center" label="项目图片" prop="image" v-if="item.type === 'TREAT_BJG'">
                      <template v-slot="{ row }">
                        <el-image
                          fit="cover"
                          style="width: 80px"
                          :src="row.image"
                          :preview-src-list="[row.image]"
                        ></el-image>
                      </template>
                    </el-table-column>
                    <!-- 中药饮片 -->
                    <el-table-column
                      v-if="
                        item.type === 'HERBS' ||
                        item.type === 'herbs_info' ||
                        item.type === 'MEDICINE' ||
                        item.type === 'medicine_info'
                      "
                      label="药品"
                      prop="name"
                      key="1"
                      :width="{ 60: item.type !== 'MEDICINE' && item.type !== 'medicine_info' }"
                    ></el-table-column>
                    <el-table-column
                      v-if="
                        item.type === 'TREAT' ||
                        item.type === 'TREAT_TJL' ||
                        item.type === 'TREAT_AJ' ||
                        item.type === 'TREAT_QS' ||
                        item.type === 'PLASTER' ||
                        item.type === 'TREAT_BJG' ||
                        item.type === 'PHYSICAL' ||
                        item.type === 'goods_info' ||
                        item.type === 'zl_info'
                      "
                      label="项目"
                      prop="name"
                      key="2"
                    ></el-table-column>

                    <el-table-column
                      v-if="item.type === 'goods_info' || item.type === 'zl_info'"
                      label="类型"
                      prop="goods_type_text"
                      :key="'goods_type_text' + item.goods_type_text"
                    >
                      <template slot-scope="{ row }">{{ row.goods_type_text || '-' }}</template>
                    </el-table-column>

                    <!-- 西药增加字段 -->
                    <template v-if="item.type == 'MEDICINE' || item.type === 'medicine_info'">
                      <el-table-column
                        align="center"
                        :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                        label="类型"
                        prop="prod_type_text"
                      ></el-table-column>
                      <el-table-column align="center" width="80" label="处方药" prop="is_otc"></el-table-column>
                      <el-table-column align="center" width="60" label="规格" prop="prod_spec"></el-table-column>
                      <el-table-column align="center" label="厂商" prop="manufacturer">
                        <template slot-scope="{ row }">
                          {{ row.manufacturer || '-' }}
                        </template>
                      </el-table-column>
                    </template>

                    <el-table-column
                      v-if="item.type === 'HERBS' || item.type === 'herbs_info'"
                      label="剂量"
                      prop="medicine_info.unit_text"
                      key="3"
                    ></el-table-column>
                    <el-table-column v-if="item.type === 'PHYSICAL'" label="规格" prop="spec" key="3"></el-table-column>
                    <el-table-column
                      v-if="item.type === 'MEDICINE' || item.type === 'medicine_info'"
                      label="单次剂量"
                      prop="medicine_info.unit_text"
                      width="80"
                      key="unit_text"
                    ></el-table-column>
                    <el-table-column
                      v-if="item.type === 'MEDICINE' || item.type === 'medicine_info'"
                      width="60"
                      label="用法"
                      prop="medicine_info.usage"
                      key="5"
                    ></el-table-column>
                    <el-table-column
                      v-if="item.type === 'MEDICINE' || item.type === 'medicine_info'"
                      width="100"
                      label="频次"
                      prop="medicine_info.freq"
                      key="6"
                    ></el-table-column>

                    <!-- 处方单价展示（单价/单位）：中药，中成/药西药展示接口返回的单位，膏方写死瓶，非膏方，中药，中成/药西药写死次 -->
                    <el-table-column
                      label="单价"
                      :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                      prop="price"
                      key="8"
                    >
                      <template slot-scope="scope">
                        <span v-if="item.type === 'HERBS' || item.type === 'herbs_info'"
                          >￥{{ Number(scope.row.price).toFixed(4) }} /
                          {{ scope.row.medicine_info && scope.row.medicine_info.unit_name }}</span
                        >
                        <span
                          v-if="item.type === 'MEDICINE' || item.type === 'PLASTER' || item.type === 'medicine_info'"
                          >￥{{ scope.row.price }} /
                          {{ scope.row.medicine_info && scope.row.medicine_info.sales_unit }}</span
                        >
                        <!-- 商品和诊疗项目 -->
                        <span v-if="item.type === 'goods_info' || item.type === 'zl_info'"
                          >￥{{ scope.row.price }}</span
                        >
                        <span
                          v-if="
                            item.type !== 'HERBS' &&
                            item.type !== 'herbs_info' &&
                            item.type !== 'goods_info' &&
                            item.type !== 'zl_info' &&
                            item.type !== 'MEDICINE' &&
                            item.type !== 'PLASTER' &&
                            item.type !== 'medicine_info' &&
                            scope.row.price
                          "
                          >￥{{ scope.row.price }} /
                          {{ item.type === 'PHYSICAL' || item.type === 'TREAT' ? '个' : '次' }}</span
                        >
                      </template>
                    </el-table-column>

                    <el-table-column
                      v-if="
                        item.type === 'MEDICINE' ||
                        item.type === 'PLASTER' ||
                        item.type === 'goods_info' ||
                        item.type === 'zl_info' ||
                        item.type === 'medicine_info'
                      "
                      label="数量"
                      :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                      prop="quantity_text"
                      key="7"
                    ></el-table-column>
                    <el-table-column
                      v-if="
                        item.type === 'TREAT' ||
                        item.type === 'TREAT_TJL' ||
                        item.type === 'TREAT_AJ' ||
                        item.type === 'TREAT_QS' ||
                        item.type === 'TREAT_BJG' ||
                        item.type === 'PHYSICAL'
                      "
                      width="60"
                      label="数量"
                      prop="quantity"
                      key="9"
                    ></el-table-column>

                    <!-- 单位：4种情况  1:中药：用接口unit_name字段，2：中成/药西药: 用接口sales_unit字段，3：膏方：写死瓶，4：非膏方，中药，中成/药西药: 写死次-->
                    <el-table-column
                      v-if="item.type === 'HERBS' || item.type === 'herbs_info'"
                      label="单位"
                      key="unit"
                      column-key="HERBS_UNIT"
                    >
                      <template slot-scope="{ row }">
                        <span>{{ row.medicine_info && row.medicine_info.unit_name }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.type === 'MEDICINE' || item.type === 'medicine_info'"
                      label="单位"
                      key="unit"
                      column-key="MEDICINE_UNIT"
                    >
                      <template slot-scope="{ row }">
                        <span>{{ row.medicine_info && row.medicine_info.sales_unit }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="item.type === 'goods_info' || item.type === 'zl_info'"
                      label="单位"
                      key="unit"
                      column-key="MEDICINE_UNIT"
                    >
                      <template slot-scope="{ row }">
                        <span>{{ item.type === 'goods_info' ? '件' : '次' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="
                        (item.type !== 'PLASTER' && item.type === 'TREAT') ||
                        item.type === 'TREAT_TJL' ||
                        item.type === 'TREAT_AJ' ||
                        item.type === 'TREAT_QS' ||
                        item.type == 'PHYSICAL'
                      "
                      label="单位"
                      key="10"
                      >{{ item.type === 'PHYSICAL' || item.type === 'TREAT' ? '个' : '次' }}
                    </el-table-column>
                    <el-table-column v-if="item.type === 'PLASTER'" label="单位" key="unit">
                      <template slot-scope="{ row }">
                        <span>{{ row.medicine_info && row.medicine_info.sales_unit }}</span>
                      </template>
                    </el-table-column>
                    <!-- <el-table-column label="折扣" prop="discount_rate" key="11"></el-table-column> -->
                    <el-table-column
                      label="总价"
                      :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                      prop="payment_fee"
                      key="12"
                    >
                      <template slot-scope="scope">
                        <span>￥{{ Number(scope.row.payment_fee).toFixed(2) }}</span>
                      </template>
                    </el-table-column>
                    <!-- <el-table-column v-if="item.type === 'TREAT' || item.type === 'TREAT_TJL' || item.type === 'TREAT_AJ' || item.type === 'TREAT_QS' || item.type === 'PLASTER' " label="总价" prop="total_fee" key="13"></el-table-column> -->
                    <!-- <el-table-column label="备注" prop="remark" show-overflow-tooltip key="14">
                      <template slot-scope="scope">
                        <span>{{ scope.row.remark || '-' }}</span>
                      </template>
                    </el-table-column> -->
                  </el-table>
                  <div class="remark" v-if="item.remark || item.medical_advice">
                    <p v-if="item.medical_advice">医嘱和注意事项：{{ item.medical_advice }}</p>
                    <p v-else>方案备注：{{ item.remark }}</p>
                  </div>
                  <div class="flex-b">
                    <div v-show="item.usage_text" class="pharmacy-tip">
                      <span v-show="his_version !== '4.0'">{{ item.usage_text }}</span>
                    </div>
                    <div v-show="!item.usage_text" class="pharmacy-tip"></div>
                    <div class="total-count">
                      共
                      {{ item.count_entry }}种 总计：￥{{ Number(item.payment_fee).toFixed(2) }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- // *收费 -->
              <div v-if="jzObj.chargeData && jzObj.chargeData.pres_list">
                <div class="basic-info-header flex-b flex-item-v-center">
                  <div class="flex flex-item-v-center">
                    <p class="vertical-line"></p>
                    <p class="basic-distance basic-title">收费</p>
                  </div>
                  <el-button
                    v-if="isCanUseEventBtn && jzObj.chargeData && jzObj.chargeData.pres_list"
                    class="print-btn"
                    size="medium"
                    @click="orderPrint"
                    >打印收费单
                  </el-button>
                </div>
                <template v-if="jzObj.chargeData">
                  <div
                    v-for="(item, index) in jzObj.chargeData.pres_list"
                    :key="'order' + index"
                    class="pharmacy-table-wrapper"
                  >
                    <div class="pharmacy-th flex-b">
                      <div class="flex">
                        <div class="flex-c">
                          <!-- icon -->
                          <div class="icon">
                            <p class="one-icon"></p>
                            <p class="two-icon"></p>
                          </div>
                        </div>
                        <div class="pharmacy-th-name flex-c">
                          {{ item && item.type_text }}
                        </div>
                        <div v-show="item && item.pres_code" class="pharmacy-th-order flex-c">
                          单号:{{ item && item.pres_code }}
                        </div>
                        <div v-show="item && item.his_pay_status_text" class="pharmacy-th-tag-w flex">
                          <p :style="getFeeStatusTagStyle(item.his_pay_status)" class="pharmacy-th-tag flex-c">
                            {{ item.his_pay_status_text }}
                          </p>
                        </div>
                      </div>
                      <div class="overstock-tip" v-if="item.stock_out_list && item.stock_out_list.length">
                        <span style="font-size: 12px; color: red; margin-right: 10px"
                          >该中药饮片收费单存在库存不足发药情况</span
                        >
                        <el-button type="text" size="mini"
                          ><span style="text-decoration: underline" @click="checkOverStockDetail(item)">查看详情</span>
                        </el-button>
                      </div>
                    </div>
                    <el-table
                      v-if="item && item.attrs"
                      :cell-style="{ textAlign: 'center' }"
                      :data="item && item.attrs"
                      :header-cell-style="{
                        background: '#F7F7F8',
                        color: '#333333',
                        textAlign: 'center',
                      }"
                      style="width: 100%"
                    >
                      <el-table-column
                        label="项目"
                        prop="name"
                        :width="item.type === 'MEDICINE' || item.type === 'medicine_info' ? 100 : 180"
                      ></el-table-column>
                      <el-table-column
                        align="center"
                        label="规格"
                        prop="spec"
                        width="60"
                        v-if="item.type === 'PHYSICAL'"
                      ></el-table-column>
                      <el-table-column
                        v-if="item.type !== 'goods_info' && item.type !== 'zl_info'"
                        label="状态"
                        prop="his_status_text"
                        :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                      ></el-table-column>

                      <el-table-column
                        v-if="
                          item.type === 'PHYSICAL' ||
                          item.type === 'TREAT' ||
                          item.type === 'goods_info' ||
                          item.type === 'zl_info'
                        "
                        align="center"
                        label="类型"
                        width="80"
                        prop="goods_type_text"
                      >
                      </el-table-column>

                      <el-table-column
                        label="自费"
                        :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                        >是</el-table-column
                      >

                      <!-- 西药增加字段 -->
                      <template v-if="item.type == 'MEDICINE' || item.type === 'medicine_info'">
                        <el-table-column align="center" width="60" label="类型" prop="prod_type_text"></el-table-column>
                        <el-table-column align="center" width="60" label="处方药" prop="is_otc"></el-table-column>
                        <el-table-column align="center" label="规格" prop="prod_spec"></el-table-column>
                        <el-table-column align="center" label="厂商" prop="manufacturer">
                          <template slot-scope="{ row }">
                            {{ row.manufacturer || '-' }}
                          </template>
                        </el-table-column>
                      </template>

                      <!-- <el-table-column label="单价" prop="price"></el-table-column> -->
                      <!-- 单价展示（单价/单位），中药，膏方，中成/药西药 -->
                      <!-- 中药饮片单价统一为4位小数-->
                      <el-table-column
                        label="单价"
                        :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                        prop="price"
                      >
                        <template slot-scope="scope">
                          <div v-if="scope.row.price">
                            ￥<span v-if="item.type === 'HERBS'">{{ Number(scope.row.price).toFixed(4) }}</span>
                            <span v-else>{{ scope.row.price }}</span>
                            <span v-if="item.type !== 'goods_info' && item.type !== 'zl_info'"
                              >/{{
                                item.type === 'PHYSICAL' || item.type === 'TREAT' ? '个' : scope.row.unit_name || '次'
                              }}</span
                            >
                          </div>
                          <div v-else>-</div>
                        </template>
                      </el-table-column>

                      <!-- 单位：中药，膏方，中成/药西药，加工费用接口返回的单位，其余写死次 -->
                      <el-table-column
                        v-if="
                          item.type === 'HERBS' ||
                          item.type === 'herbs_info' ||
                          item.type === 'PLASTER' ||
                          item.type === 'MEDICINE' ||
                          item.type === 'DECO' ||
                          item.type === 'medicine_info'
                        "
                        :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                        label="单位"
                        prop="unit_name"
                      >
                        <template slot-scope="scope">
                          <span>{{ scope.row.unit_name || '-' }}</span>
                        </template>
                      </el-table-column>
                      <el-table-column
                        v-else-if="item.type === 'goods_info' || item.type === 'zl_info'"
                        label="单位"
                        :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                        >{{ item.type === 'goods_info' ? '个' : '次' }}
                      </el-table-column>
                      <el-table-column
                        v-else
                        label="单位"
                        :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                        >{{ item.type === 'PHYSICAL' || item.type === 'TREAT' ? '个' : '次' }}
                      </el-table-column>

                      <el-table-column
                        label="数量"
                        :width="{ 60: item.type === 'MEDICINE' || item.type === 'medicine_info' }"
                        prop="quantity"
                      ></el-table-column>

                      <el-table-column label="总价" prop="total_fee">
                        <template slot-scope="scope">
                          <span>￥{{ Number(scope.row.total_fee).toFixed(2) }}</span>
                        </template>
                      </el-table-column>

                      <!-- <el-table-column label="总价" v-if="item.type !== 'TREAT'" prop="total_fee"></el-table-column>
                      <el-table-column label="总价" v-if="item.type === 'TREAT'" prop="payment_fee"></el-table-column> -->

                      <!-- <el-table-column label="备注" prop="remark" show-overflow-tooltip>
                        <template slot-scope="scope">
                          <span>{{ scope.row.remark || '-' }}</span>
                        </template>
                      </el-table-column> -->
                    </el-table>
                    <div class="remark" v-if="item.remark || item.medical_advice">
                      <p v-if="item.medical_advice">医嘱和注意事项：{{ item.medical_advice }}</p>
                      <p v-else>方案备注：{{ item.remark }}</p>
                    </div>
                  </div>
                </template>
                <div
                  v-if="jzObj.chargeData && jzObj.chargeData.pres_list && jzObj.chargeData.pres_list.length > 0"
                  class="flex-b"
                >
                  <div class="pharmacy-tip"></div>
                  <div class="total-count">
                    共{{ jzObj.chargeData.order && jzObj.chargeData.order.count_entry }}种 总计：￥{{
                      jzObj.chargeData.order && Number(jzObj.chargeData.order.payment_fee).toFixed(2)
                    }}
                  </div>
                </div>
              </div>

              <!-- 随访记录 -->
              <div v-for="(visit_item, visit_index) in visit_list" :key="visit_index + 'visit'">
                <div class="basic-info-header flex flex-item-v-center">
                  <p class="vertical-line"></p>
                  <p class="basic-distance basic-title">随访记录{{ visit_index + 1 }}</p>
                </div>

                <template>
                  <div class="flex mt24">
                    <div class="flex flex-1">
                      <p class="basic-label mr10">随访人</p>
                      <p class="basic-value">{{ visit_item.up_info.operator }}</p>
                    </div>
                    <div class="flex flex-1">
                      <p class="basic-label mr10">随访时间</p>
                      <p class="basic-value">{{ visit_item.up_info.time | data_format('MM/DD HH:mm') }}</p>
                    </div>
                  </div>

                  <template v-if="isRstClinic && jzObj.info.is_new_follow_up === '1'">
                    <div class="flex">
                      <div class="flex flex-1">
                        <p class="basic-label mr10">随访类型</p>
                        <p class="basic-value">{{ visit_item.type_text || '-' }}</p>
                      </div>
                      <div class="flex flex-1">
                        <p class="basic-label mr10">随访目标</p>
                        <p class="basic-value">{{ visit_item.mr_info.target || '-' }}</p>
                      </div>
                    </div>
                    <div class="flex">
                      <div class="flex flex-1">
                        <p class="basic-label mr10">随访方式</p>
                        <p class="basic-value">{{ visit_item.mode_text || '-' }}</p>
                      </div>
                      <div class="flex flex-1">
                        <p class="basic-label mr10">随访操作</p>
                        <p class="basic-value">{{ visit_item.mr_info.operation_type_text || '-' }}</p>
                      </div>
                    </div>
                    <div class="flex">
                      <div class="flex flex-1">
                        <p class="basic-label mr10">下次随访时间</p>
                        <p class="basic-value">{{ visit_item.mr_info?.next_date || '-' }}</p>
                      </div>
                      <div class="flex flex-1">
                        <p class="basic-label mr10">预约单</p>
                        <p class="basic-value">{{ visit_item.mr_info.reserve_desc || '-' }}</p>
                      </div>
                    </div>
                    <div class="flex">
                      <div class="flex flex-1">
                        <p class="basic-label mr10">随访备注</p>
                        <p class="basic-value">{{ visit_item.mr_info?.remark || '-' }}</p>
                      </div>
                      <div class="flex flex-1">
                        <!-- <p class="basic-label mr10">预约单</p>
                        <p class="basic-value">{{ visit_item.mode_text }}</p> -->
                      </div>
                    </div>
                  </template>
                  <template v-else>
                    <div class="flex">
                      <div class="flex flex-1">
                        <p class="basic-label mr10">随访类型</p>
                        <p class="basic-value">{{ visit_item.type_text || '-' }}</p>
                      </div>
                      <div class="flex flex-1">
                        <p class="basic-label mr10">随访方式</p>
                        <p class="basic-value">{{ visit_item.mode_text || '-' }}</p>
                      </div>
                    </div>

                    <div class="flex">
                      <div class="flex flex-1">
                        <p class="basic-label mr10">随访情况</p>
                        <p class="basic-value" v-if="visit_item.mr_info.visit_situation == 'NOT_REACHED'">
                          {{ visit_item.mr_info.visit_situation_text }},{{ visit_item.mr_info.next_plan_time }}继续跟进
                        </p>
                        <p class="basic-value" v-else>{{ visit_item.mr_info.visit_situation_text || '-' }}</p>
                      </div>
                      <div class="flex flex-1">
                        <p class="basic-label mr10">复诊时间</p>
                        <p class="basic-value">{{ visit_item.mr_info.return_visit_date || '-' }}</p>
                      </div>
                    </div>

                    <div class="flex">
                      <p class="basic-label mr10">用户反馈</p>
                      <p class="basic-value">{{ visit_item.mr_info.feedback_desc || '-' }}</p>
                    </div>

                    <div class="flex">
                      <p class="basic-label mr10">随访记录</p>
                      <p class="basic-value">{{ visit_item.mr_info.visit_record || '-' }}</p>
                    </div>

                    <div class="flex flex-item-align">
                      <p class="visit-label mr10">全身舒适度</p>
                      <p
                        class="visit-text visit-tag flex flex-item-center"
                        :style="{ background: tagColor(visit_item.mr_info.comfort) }"
                      >
                        {{ visit_item.mr_info.comfort_text }}
                      </p>
                    </div>
                  </template>
                </template>

                <!-- 随访表格 -->
                <template>
                  <el-table
                    :data="visit_item.mr_info.diag_result"
                    :cell-style="{ textAlign: 'center' }"
                    :header-cell-style="{
                      background: '#F7F7F8',
                      color: '#333333',
                      textAlign: 'center',
                    }"
                    class="mt10"
                    style="width: 100%"
                  >
                    <el-table-column prop="name" label="初始症状"></el-table-column>
                    <el-table-column prop="feedback_type_text" label="疗效反馈">
                      <template slot-scope="scope">
                        <span>{{ scope.row.feedback_type_text || '-' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="take_medicine_day" label="疗效时间(服药后时间)">
                      <template slot-scope="scope">
                        <span
                          >{{ scope.row.take_medicine_day || '-'
                          }}<span v-if="scope.row.take_medicine_day">天</span></span
                        >
                      </template>
                    </el-table-column>
                  </el-table>
                </template>
              </div>
            </div>
            <div v-show="!rightLoading && +navList.length === 0" class="empty-show flex-c">
              <el-empty></el-empty>
            </div>
          </div>

          <!-- // *AI诊断 -->
          <div v-show="tabId === 2" class="basic-info">
            <div v-show="+navList.length !== 0">
              <!-- // *基础信息 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">基础信息</p>
              </div>
              <div>
                <el-row class="basic-personal-info">
                  <el-col :span="8" class="flex">
                    <p class="basic-label">姓名</p>
                    <p class="basic-value">
                      {{
                        (aiObj.allData[this.navActiveIndex] && aiObj.allData[this.navActiveIndex].patient.name) || '-'
                      }}
                    </p>
                  </el-col>
                  <el-col :span="8" class="flex">
                    <p class="basic-label">性别</p>
                    <p class="basic-value">
                      {{
                        (aiObj.allData[this.navActiveIndex] && aiObj.allData[this.navActiveIndex].patient.sex_text) ||
                        '-'
                      }}
                    </p>
                  </el-col>
                  <el-col :span="8" class="flex">
                    <p class="basic-label">出生日期</p>
                    <p class="basic-value">
                      {{
                        (aiObj.allData[this.navActiveIndex] && aiObj.allData[this.navActiveIndex].patient.birthday) ||
                        '-'
                      }}
                    </p>
                  </el-col>
                  <el-col :span="24" class="flex">
                    <p class="basic-label">自选症状</p>
                    <p class="basic-value">
                      {{ (aiObj.allData[this.navActiveIndex] && aiObj.allData[this.navActiveIndex].symptoms) || '-' }}
                    </p>
                  </el-col>
                </el-row>
              </div>

              <!-- // *辨证结果 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">辨证结果</p>
              </div>

              <el-row v-if="aiRadioList.length" class="basic-personal-info dialectical">
                <el-col :span="24">
                  <el-radio-group
                    v-model="aiActiveRadio"
                    fill="#0245CC"
                    size="mini"
                    style="margin-bottom: 30px"
                    @change="radioChange"
                  >
                    <el-radio-button v-for="(item, index) in aiRadioList" :key="'radio' + index" :label="item.code">
                      {{ item.name }}
                    </el-radio-button>
                  </el-radio-group>
                </el-col>
                <!-- <el-col :span="24" class="flex">
                  <p class="basic-label">置信值</p>
                  <p class="basic-value current-theme-bcolor">
                    {{ aiObj.dialecticalData.score || '-' }}
                  </p>
                </el-col> -->
                <el-col :span="24" class="flex">
                  <p class="basic-label">辨证占比</p>
                  <p class="basic-value current-theme-bcolor">
                    {{ aiObj.dialecticalData.percent || '-' }}
                  </p>
                </el-col>
                <el-col
                  v-for="(item, index) in this.aiObj.dialecticalData.explain"
                  :key="'ai' + index"
                  :span="24"
                  class="flex"
                >
                  <p class="basic-label">{{ item.title }}</p>
                  <p class="basic-value">
                    {{ item.desc }}
                  </p>
                </el-col>
              </el-row>
            </div>
            <div v-show="!rightLoading && +navList.length === 0" class="empty-show flex-c">
              <!--							<div class="flex-c">-->
              <!--								<img alt="" src="@/assets/images/consu_recommend_page.png">-->
              <!--								<p>暂无数据</p>-->
              <!--							</div>-->
              <el-empty></el-empty>
            </div>
          </div>

          <!-- // *中医体质 -->
          <div v-show="tabId === 3" class="basic-info">
            <div v-show="+navList.length !== 0">
              <!-- 基础信息 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">基础信息</p>
              </div>

              <el-row class="basic-personal-info">
                <el-col :span="24" class="flex">
                  <p class="basic-label">主体质</p>
                  <p class="basic-value">
                    {{
                      (cmObj.allData[this.navActiveIndex] && cmObj.allData[this.navActiveIndex].main_type_info.type) ||
                      '-'
                    }}
                  </p>
                </el-col>
                <el-col :span="24" class="flex">
                  <p class="basic-label">主体质特征</p>
                  <p class="basic-value">
                    {{
                      (cmObj.allData[this.navActiveIndex] &&
                        cmObj.allData[this.navActiveIndex].main_type_info.type_text) ||
                      '-'
                    }}
                  </p>
                </el-col>
                <el-col :span="24" class="flex">
                  <p class="basic-label">倾向体质</p>
                  <p class="basic-value">
                    {{
                      (cmObj.allData[this.navActiveIndex] && cmObj.allData[this.navActiveIndex].main_type_info.desc) ||
                      '-'
                    }}
                  </p>
                </el-col>
              </el-row>

              <!-- 总体特征 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">总体特征</p>
              </div>

              <el-row v-show="cmXT.length" class="basic-personal-info">
                <el-col v-for="(item, index) in cmXT" :key="'cmXT' + index" :span="24" class="flex">
                  <p class="basic-label">
                    {{ item.title }}
                  </p>
                  <p class="basic-value">
                    {{ item.content }}
                  </p>
                </el-col>
              </el-row>

              <!-- //*药膳调理 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">药膳调理</p>
              </div>

              <el-row class="basic-personal-info">
                <el-col v-for="(item, index) in cmYS" :key="'cmYS' + index" :span="24" class="flex">
                  <p class="basic-label">{{ item.title }}</p>
                  <p class="basic-value">
                    {{ item.content }}
                  </p>
                </el-col>
              </el-row>
            </div>
            <div v-show="!rightLoading && +navList.length === 0" class="empty-show flex-c">
              <el-empty></el-empty>
            </div>
          </div>

          <!-- // *智能舌诊 -->
          <div v-show="tabId === 4" class="basic-info">
            <div v-show="+navList.length !== 0">
              <!-- // *基础信息 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">基础信息</p>
              </div>

              <el-row class="basic-personal-info">
                <el-col :span="24" class="flex">
                  <p class="basic-label">诊断结果</p>
                  <p class="basic-value">
                    {{
                      (igObj.allData[this.navActiveIndex] && igObj.allData[this.navActiveIndex].body_mass_text) || '-'
                    }}
                  </p>
                </el-col>
                <el-col :span="24" class="flex">
                  <p class="basic-label">我的照片</p>
                  <div class="basic-value image-wrapper">
                    <!-- v-for="(item, index) in urlList"
                      :key="'url' + index" -->
                    <el-image
                      v-if="
                        igObj.allData[this.navActiveIndex] &&
                        igObj.allData[this.navActiveIndex].tongue_analysis.detect_img
                      "
                      :preview-src-list="urlList"
                      :src="
                        igObj.allData[this.navActiveIndex] &&
                        igObj.allData[this.navActiveIndex].tongue_analysis.detect_img
                      "
                      class="basic-distance"
                      style="width: 66px; height: 66px"
                    >
                    </el-image>
                    <span v-else>-</span>
                  </div>
                </el-col>
              </el-row>

              <!-- // *诊断分析 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">诊断分析</p>
              </div>

              <el-row v-if="igzdRadioList.length" class="basic-personal-info dialectical">
                <el-col :span="24">
                  <el-radio-group
                    v-model="igzdActiveRadio"
                    fill="#0245CC"
                    size="mini"
                    style="margin-bottom: 30px"
                    @change="igzdRadioChange"
                  >
                    <el-radio-button v-for="(item, index) in igzdRadioList" :key="'radio' + index" :label="index">
                      {{ item.title }}
                    </el-radio-button>
                  </el-radio-group>
                </el-col>

                <el-col
                  v-for="(item, index) in this.igObj.igzdData.items"
                  :key="'aiys' + index"
                  :span="24"
                  class="flex"
                >
                  <p class="basic-label">{{ item.name }}</p>
                  <p class="basic-value">
                    {{ item.content }}
                  </p>
                </el-col>
              </el-row>

              <!-- // *舌像分析 -->
              <div class="basic-info-header flex flex-item-v-center">
                <p class="vertical-line"></p>
                <p class="basic-distance basic-title">
                  舌像分析
                  <span v-show="unHealthText" class="current-theme-ocolor">({{ unHealthText }})</span>
                </p>
              </div>

              <el-row class="basic-personal-info">
                <el-col :span="24">
                  <el-radio-group
                    v-model="igsxActiveRadio"
                    fill="#0245CC"
                    size="mini"
                    style="margin-bottom: 30px"
                    @change="igsxRadioChange"
                  >
                    <el-radio-button v-for="(item, index) in igsxRadioList" :key="'radio' + index" :label="index"
                      >{{ item.title }}
                      <p :class="{ dot: unHealth.includes(item.title) }"></p
                    ></el-radio-button>
                  </el-radio-group>
                </el-col>

                <el-col v-for="(item, index) in this.igObj.igsxData" :key="'aisx' + index" :span="24" class="flex">
                  <p class="basic-label">{{ item.name }}</p>
                  <p
                    :class="{ abnormal: index === 0 && unHealth.includes(igsxRadioList[igsxActiveRadio].title) }"
                    class="basic-value"
                  >
                    {{ item.content }}
                    <span v-show="index === 0 && unHealth.includes(igsxRadioList[igsxActiveRadio].title)"
                      >( 异常 )</span
                    >
                  </p>
                </el-col>
              </el-row>
            </div>
            <div v-show="!rightLoading && +navList.length === 0" class="empty-show flex-c">
              <!--							<div class="flex-c">-->
              <!--								<img alt="" src="@/assets/images/consu_recommend_page.png">-->
              <!--								<p>暂无数据</p>-->
              <!--							</div>-->
              <el-empty></el-empty>
            </div>
          </div>

          <!-- 慢病信息 -->
          <div v-if="tabId == 5" class="basic-info">
            <chronic-disease :pt_id="pt_id" @resetScroll="$refs.content.scrollTo(0, 0)"></chronic-disease>
          </div>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="deleteVisible" append-to-body custom-class="del-dia" width="480px" lock-scroll>
      <div class="dia-header">
        <div class="flex" style="padding: 0 0 14px 14px">
          <i class="el-icon-warning" icon-color="red" style="font-size: 24px"></i>
          <h4>作废治疗单</h4>
        </div>
      </div>
      <p>该治疗单里，有部分治疗服务已被使用，是否继续作废？</p>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" type="default" @click="deleteVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="continueDelete">继续作废</el-button>
      </div>
    </el-dialog>
    <!-- 协定方弹窗 -->
    <!--    <el-dialog-->
    <!--      :visible.sync="xdVisible"-->
    <!--      append-to-body-->
    <!--      lock-scroll-->
    <!--      custom-class="xd-dia"-->
    <!--      title="存为协定方"-->
    <!--      width="480px"-->
    <!--    >-->
    <!--      <div class="xd-content">-->
    <!--        <el-form ref="form" :model="form" label-width="100px">-->
    <!--          <el-form-item label="协定方名称">-->
    <!--            <el-input v-model="form.name"></el-input>-->
    <!--          </el-form-item>-->
    <!--          <el-form-item label="协定方描述">-->
    <!--            &lt;!&ndash; <el-input :disabled="true" type="textarea" v-model="form.desc"></el-input> &ndash;&gt;-->
    <!--            <div class="read white-space">-->
    <!--              <p>{{ form.desc }}</p>-->
    <!--            </div>-->
    <!--          </el-form-item>-->
    <!--        </el-form>-->
    <!--      </div>-->
    <!--      <span slot="footer" class="dialog-footer">-->
    <!--        <el-button size="small" type="default" @click="xdVisible = false">取 消</el-button>-->
    <!--        <el-button size="small" type="primary" @click="saveAgreement">确 定</el-button>-->
    <!--      </span>-->
    <!--    </el-dialog>-->
    <!--    <div style="display: none">-->
    <!--      <PrintPres ref="patientPrinter"></PrintPres>-->
    <!--    </div>-->
    <!--    <div style="display: none">-->
    <!--      <PrintBill ref="patientBillPrinter"></PrintBill>-->
    <!--    </div>-->
    <!--    <div style="display: none">-->
    <!--      <PrintMedicalRecord ref="medicalRecordPrinter"></PrintMedicalRecord>-->
    <!--    </div>-->
    <!--    <OutStockTable :no-stock-visible.sync="noStockVisible" :over-stock-list="overStockList"></OutStockTable>-->
    <!--    &lt;!&ndash; 取消订单 &ndash;&gt;-->
    <!--    <CancelOrder-->
    <!--      v-model="cancalVisible"-->
    <!--      @cancelSuccess="cancelSuccess"-->
    <!--      :mr_id="jzObj.info.id"-->
    <!--    ></CancelOrder>-->
  </div>
</template>

<script type="text/javascript">
// import PrintPres from '_c/Print/print-prescription';
// import PrintBill from '_c/Print/print-bill';
// import PrintMedicalRecord from '_c/Print/print-medical-record';
// import OutStockTable from './OutStockTable';
// import CancelOrder from '_c/CancelOrder';
// 食养信息
import ChronicDisease from '../chronic/chronic-disease';
// 辨证参考
import dialectical from './components/dialectical';
import RePatientInfoMixin from './mixins/RePatientInfoMixin';
import filterType from './components/filterType.vue';
import { isRstClinic } from '@/libs/runtime';
export default {
  name: 'patientInfoCom',
  components: {
    // PrintPres,
    // PrintBill,
    // OutStockTable,
    // CancelOrder,
    ChronicDisease,
    // PrintMedicalRecord,
    dialectical,
    filterType,
  },
  mixins: [RePatientInfoMixin],
  props: {
    titList: {
      type: Array,
      default: () => [],
    },
    pt_id: {
      type: [String, Number],
      default: () => '',
    },
    // 是否是弹窗里面调用
    isModal: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    isRstClinic() {
      return isRstClinic();
    },
  },
  watch: {},
  created() {},
  mounted() {
    // this.params.pt_id = this.pt_id
    // 初始状态下，默认调取就诊信息数据
  },
  methods: {},
  filters: {},
};
</script>
<style lang="less" scoped>
p {
  margin: 0px;
}
.patient-info-com-wrapper {
  // *就诊流程信息样式
  .visit-information {
    background: #fff;
    border-radius: 4px;
    // margin-top: 30px;
    padding: 20px 16px 20px 20px;
    // height: calc(100vh - 482px);
    height: ~'calc(100vh - 210px)';
    min-height: 400px;
    overflow: hidden;

    .content {
      height: 100%;
      min-height: 400px;
      // height: calc(100% - 100px);
      // overflow-y: scroll ;
    }

    // tab切换
    .tab-nav {
      height: 36px;
      line-height: 36px;
      margin-bottom: 31px;
      padding-bottom: 42px;
      border-bottom: 1px solid #f5f5f6;

      .singleTitle {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }

      .title {
        font-size: 18px;
        margin-right: 50px;
        cursor: pointer;
      }

      .title:hover {
        color: #155bd5;
      }

      .line {
        height: 2px;
        background-image: linear-gradient(to right, #0043cb, #357aff);
        border-radius: 1px;
        margin: 5px auto;
        animation: lineMove 0.2s linear;
      }

      @keyframes lineMove {
        0% {
          width: 0;
        }
        20% {
          width: 20%;
        }
        50% {
          width: 50%;
        }
        100% {
          width: 100%;
        }
      }

      .cur {
        color: #155bd5;
      }
    }

    .left-nav {
      background: #f7f8f9;
      // min-width: 220px;
      min-width: 184px;
      max-width: 184px;
      min-height: 400px;
      height: ~'calc(100% - 70px)';
      overflow-y: scroll;

      &::-webkit-scrollbar {
        display: none;
      }

      .stage-active {
        background: #eff4ff;
      }

      .stage-content-active {
        border-left-color: #0245cc !important;
        color: #155bd5;
      }

      .stage-w:hover {
        background: #eff4ff;
        cursor: pointer;
      }

      .stage-w {
        //min-height: 92px;
        padding: 20px 0;
        box-sizing: border-box;
        display: flex;
        align-items: center;

        .stage-content {
          padding: 0 12px;
          border-left: 2px solid transparent;
          width: 100%;

          .stage-time {
            display: flex;
            justify-content: space-between;

            .time {
              font-weight: 500;
              font-size: 15px;
              color: #999999;
              line-height: 19px;
              letter-spacing: 1px;
            }
          }
          .stage-info-re-active {
            color: #919fbf !important;
          }
          .stage-info-re {
            margin-top: 6px;
            font-size: 12px;
            color: #999999;
            transform: scale(0.9);
            width: 110%;
            margin-left: -10px;
            display: flex;
            align-items: center;
            .stage-dot {
              width: 5px;
              height: 5px;
              background: #bbbbbb;
              border-radius: 50%;
              display: inline-block;
              margin-bottom: 2px;
              margin-right: 2px;
            }

            .stage-vertical-line {
              display: inline-block;
              height: 12px;
              width: 1px;
              font-weight: 300;
              font-size: 10px;
              background: #999999;
              line-height: 12px;
              margin: 0 4px;
            }
          }

          .stage-info {
            word-break: break-all;
            margin-top: 10px;
            line-height: 21px;
          }
        }
      }
    }

    .right-content {
      padding: 0px 0px 0px 16px;
      width: 100%;
      // min-height: 400px;
      height: calc(~'100% - 80px');
      overflow-y: scroll;

      &::-webkit-scrollbar {
        display: none;
      }

      .basic-info {
        // margin-bottom: 40px;
        // basic公共样式
        color: #333;
        min-height: 400px;

        .basic-title {
          font-size: 16px;
          font-weight: 600;
          color: #333333;
        }

        .basic-distance {
          margin-left: 6px;
        }

        .basic-info-header {
          line-height: 50px;
          border-bottom: 1px solid #dadde7;
        }

        .basic-label {
          color: #aaa;
          min-width: 100px;
          text-align: right;
          line-height: 22px;
          margin-bottom: 16px;
          font-size: 13px;
        }

        .basic-value {
          margin-left: 10px;
          margin-bottom: 16px;
          color: #000;
          line-height: 22px;
          //white-space: pre-wrap;
          font-size: 13px;
          text-indent: -4px;
        }

        .abnormal {
          color: #e5634b;
        }

        // 带icon得二级标题
        .second-level-tip {
          margin-left: 10px;
          font-size: 15px;
          line-height: 27px;
          display: flex;
          align-items: center;

          .his4-tag {
            width: 38px;
            height: 14px;
            margin-left: 5px;
          }
        }

        // 个人信息数据展示
        .basic-personal-info {
          margin: 31px 0 20px 0;
        }
      }
    }
  }

  // *处方样式
  .pharmacy-table-wrapper {
    margin-top: 13px;

    .pharmacy-th {
      padding: 18px 0;

      > div {
        vertical-align: center;
      }

      .pharmacy-th-name {
        margin-left: 10px;
        font-size: 15px;
        font-weight: 400;
        color: #333333;
      }

      .pharmacy-th-order {
        margin-left: 26px;
        color: #999999;
        font-size: 12px;
      }

      .pharmacy-th-tag-w {
        margin-left: 20px;

        .pharmacy-th-tag {
          font-size: 12px;
          margin-right: 10px;
          min-width: 46px;
          background: #36a21f;
          padding: 4px 4px;
          border-radius: 16px;
        }

        .tag1 {
          background: rgba(54, 162, 31, 0.1);
          color: rgba(54, 162, 31, 1);
        }

        .tag2 {
          background: rgba(229, 99, 75, 0.1);
          color: rgba(229, 99, 75, 1);
        }

        .tag3 {
          background: rgba(2, 69, 204, 0.1);
          color: rgba(2, 69, 204, 1);
        }

        .tag4 {
          background: rgba(102, 102, 102, 0.1);
          color: rgba(102, 102, 102, 1);
        }
      }
    }
  }

  .pharmacy-tip {
    padding: 20px;
    color: #999999;
    max-width: 70%;
    font-size: 12px;
  }

  .total-count {
    font-weight: 600;
    margin-right: 20px;
  }
}

// 当显示过滤组件时，重置高度
.filter-reset-height {
  height: ~'calc( 100% - 36px )' !important;
}

// AI诊断
.dialectical {
  p {
    // margin-bottom: 20px !important;
  }
}

// 作废处方单样式
.del-dia {
  p {
    margin-left: 54px;
    width: 376px;
    color: rgba(0, 0, 0, 0.65);
    line-height: 22px;
  }

  .dia-header {
    display: flex;

    h4 {
      margin-left: 17px;
      font-size: 16px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.85);
      line-height: 24px;
    }
  }

  .el-icon-warning {
    color: #faad14;
    font-size: 21px;
  }
}

// 存为协定方样式
::v-deep .xd-dia {
  // height: 285px;
  .el-dialog__title {
    font-weight: 600;
  }

  .el-form-item__label {
    color: #575757;
    font-size: 14px;
  }

  .el-dialog__body {
    border-top: 1px solid #dfe1e7;
    border-bottom: 1px solid #dfe1e7;
    padding: 20px 30px 0 30px;
  }

  .read {
    background: #f7f8f9;
    padding: 14px 15px;
    height: 90px;
    // max-height: 200px;
    overflow-y: scroll;
    color: #999999;

    p {
      line-height: 24px;
      // margin-bottom: 12px;
    }

    span {
      margin-left: 10px;
    }
  }

  .read::-webkit-scrollbar {
    display: none;
  }

  .el-button--primary {
    padding: 10px 30px;
  }
}
</style>
<style lang="less" scoped>
.flex {
  display: flex;
}

.flex-c {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-b {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.row {
  margin-bottom: 10px;
}

.noData {
  text-align: center;
  margin-top: 30px;
  font-size: 16px;
  color: #333333;
}

.dot {
  width: 8px;
  height: 8px;
  background: #e5634b;
  border-radius: 50%;
  opacity: 1;
  position: absolute;
  top: 5px;
  right: 5px;
}

.white-space {
  white-space: pre-wrap;
}

// 竖线条样式
.vertical-line {
  width: 2px;
  height: 12px;
  background: #0245cc;
  border-radius: 4px;
}

.current-theme-bcolor {
  color: #0245cc !important;
}

.current-theme-ocolor {
  color: #e5634b !important;
}

.print-btn {
  //width: 140px;
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid #0245cc;
  border-radius: 4px;
  color: #0245cc;
}

.print-btn:hover {
  opacity: 0.5;
}

.recipel-btn {
  font-size: 12px;
  border: 1px solid #999999;
  color: #999999;
  padding: 5px 10px;
  background: rgba(255, 255, 255, 0.5);
}

.recipel-btn-ck {
  padding: 5px 10px;
}

.recipel-btn:hover {
  opacity: 0.5;
}

.recipel-btn-disabled {
  opacity: 0.5;
  color: #c0c4cc;
  cursor: not-allowed;
  background-image: none;
  background-color: #ffffff;
  border-color: #ebeef5;
}

.empty-show {
  height: 100%;
  height: 400px;

  img {
    width: 136px;
    height: 130px;
  }

  p {
    color: #999999;
    margin-top: 24px;
    font-size: 14px;
  }
}

// 手写icon
.icon {
  position: relative;

  p {
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 6px;
    background: #333;
    box-sizing: border-box;
  }

  .two-icon {
    position: relative;
    top: -3px;
    left: 3px;
    border: 1px solid rgba(255, 255, 255, 0.5019607843137255);
  }
}

::v-deep .el-radio-button__inner {
  background: #fff !important;
  color: #969799 !important;
  min-width: 98px;
}

::v-deep .is-active {
  .el-radio-button__inner {
    color: #0245cc !important;
  }
}

::v-deep .el-radio-button:first-child .el-radio-button__inner {
  border-radius: 8px 0px 0px 2px;
}

::v-deep .el-radio-button:last-child .el-radio-button__inner {
  border-radius: 0px 8px 2px 0px;
}

// 图片圆角美观设置
::v-deep .el-image__inner {
  border-radius: 4px !important;
}

::v-deep .el-loading-spinner {
  top: 100px !important;
}
</style>

<style lang="less" scoped>
// 随访-drop
.visit-tip-text {
  margin-top: 20px;
  height: 15px;
  font-size: 14px;
  font-weight: 400;
  line-height: 0px;
  color: #999999;
}

.visit-label {
  font-size: 14px;
  font-weight: 400;
  color: #333333;
  min-width: 100px;
  text-align: right;
}

.visit-text {
}

.visit-tag {
  background: #c4c4c4;
  border-radius: 14px;
  color: #fff;
  font-size: 14px;
  font-weight: 400;
  padding: 6px 14px;
}

.small-visit-tag {
  border-radius: 12px;
  background: #e3e4e5;
  color: #666666;
  font-size: 12px;
  font-weight: 300;
  padding: 7px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.small-visit-tag-active {
  background: #cbd9f7 !important;
  color: #1157e5 !important;
}

.position-relative {
  position: relative;
}

.inquiry-btn {
  position: absolute;
  right: 0;
  margin-left: 8px;
  height: 36px;

  img {
    margin-right: 6px;
    vertical-align: top;
    width: 16px;
    height: 16px;
  }
}

.mr10 {
  margin-right: 10px;
}

.mt6 {
  margin-top: 6px;
}

.mt10 {
  margin-top: 10px;
}

.mt24 {
  margin-top: 24px;
}

// v2
.img-wrapper {
  .custom-img,
  img {
    width: 68px;
    height: 68px;
    border-radius: 5px;
    margin-right: 10px;
    margin-bottom: 12px;
  }
}

.small-block {
  width: 30%;
}

.mb12 {
  margin-bottom: 12px;
}

.small-title {
  font-size: 14px;
  font-weight: 500;
  color: #333333;
}

.ml46 {
  margin-left: 46px;
}

.mb8 {
  margin-bottom: 8px;
}

.key {
  font-size: 13px;
  display: inline-block;
  width: 60px !important;
  min-width: 60px !important;
  text-align: right !important;
  line-height: 22px;
}

.remark {
  margin-top: 20px;
  background: #fafbff;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #666b6f;
  line-height: 18px;
  padding: 10px 20px;
  word-break: break-all;
}

.view-report {
  color: #1157e5;
  cursor: pointer;

  .view-report-text {
    margin-left: 6px;
  }
}
</style>
<style lang="less">
.el-message-box__status {
  top: 14px;
}
</style>
