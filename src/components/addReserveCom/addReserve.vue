<template>
  <div>
    <Spin v-if="pageLoad" class="spin"></Spin>
    <div v-else-if="!pageLoad || !row.id">
      <!-- v2版详情展示 -->
      <reserve-detail
        v-if="row.id && disabled"
        :type="type"
        :detailInfo="detailInfo"
        :showEditBtn="showEditBtn"
        :isVip="isVip"
        @edit="changeEditChange"
      ></reserve-detail>
      <Form
        style="padding: 0 20px 0px 20px"
        v-else
        :label-width="112"
        ref="queryFormData"
        :label-colon="true"
        :disabled="disabled"
        :model="queryFormData"
        :rules="ruleValidate"
        @submit.native.prevent
      >
        <div class="flex flex-item-align flex-item-between">
          <div class="header" v-if="row.id">
            <div class="header-line"></div>
            <div>修改预约单</div>
          </div>

          <div v-if="row.id && isBoard && type == 1 && showEditBtn" class="update-action" @click="changeEditChange">
            <img class="update-icon" src="https://static.rsjxx.com/image/2025/0616/142147_20879.png" />
            <a>取消修改</a>
          </div>
        </div>

        <!-- 到店人信息 -->
        <div v-if="row.id" class="info-content-box">
          <div class="info-item" style="align-items: center">
            <div class="info-label" style="width: 76px">到店人：</div>
            <div class="info-value" style="width: 520px">
              <reserve-user-info :userInfo="detailInfo?.reserve_user || {}" :isVip="isVip"></reserve-user-info>
            </div>
          </div>
        </div>

        <!-- 选择到店人 -->
        <FormItem label="" style="margin-bottom: 0px" v-if="!row.id">
          <div class="label" slot="label">
            <div class="mark">*</div>
            <div>到店人信息:</div>
          </div>
          <div class="search-box" ref="searchBoxRef">
            <div class="current-user" v-if="current_user_info.uid">
              <div class="current-user-left">
                <div
                  class="avatar-box"
                  :class="{ 'vip-avatar-box': current_user_info?.vip_info?.length > 0 }"
                  :style="{ borderColor: getVipBorderColor(current_user_info.vip_info) }"
                >
                  <img
                    v-if="current_user_info?.vip_info?.length"
                    class="vip-icon"
                    :src="getVipIcon(current_user_info.vip_info)"
                  />
                  <img
                    class="avatar"
                    :src="
                      current_user_info.avatar
                        | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
                    "
                  />
                </div>

                <div class="user-info">
                  <div class="user-info-top">
                    <div class="user-info-name">{{ current_user_info.real_name }}</div>
                    <div class="user-info-sex">
                      <span>{{ current_user_info.sex_text }}</span>
                      <span v-if="current_user_info.sex_text && current_user_info.age">｜</span>
                      <span>{{ current_user_info.age ? `${current_user_info.age}岁` : current_user_info.age }}</span>
                    </div>
                  </div>
                  <div class="user-info-mobile-box">
                    <div class="info-mobile">{{ current_user_info.mobile }}</div>
                    <div class="info-stage-mobile" v-if="current_user_info.show_staging_mobile == '1'">
                      <span class="stage-tag">暂存</span>
                      <span class="stage-mobile">{{ current_user_info.staging_mobile }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="delete-user-icon-box" v-if="!isNotRemoveUser">
                <Tooltip content="移除" placement="top">
                  <img
                    class="delete-user-icon"
                    @click="deleteUserInfo()"
                    src="https://static.rsjxx.com/image/2025/0108/163541_57316.png"
                  />
                </Tooltip>
              </div>
            </div>
            <el-autocomplete
              v-else
              class="custom-user-autocomplete"
              ref="custom"
              v-model="nickname"
              :popper-append-to-body="false"
              :fetch-suggestions="querySearchAsync"
              :trigger-on-focus="true"
              @blur="blur"
              placeholder="输入用户姓名、手机号搜索"
              @select="handleSelect"
            >
              <template slot-scope="{ item }">
                <div class="autocomplete" v-if="!item.empty" style="white-space: pre-wrap">
                  <div
                    class="avatar-box"
                    :class="{ 'vip-avatar-box': item.vip_info.length > 0 }"
                    :style="{ borderColor: getVipBorderColor(item.vip_info) }"
                  >
                    <img v-if="item.vip_info.length > 0" class="vip-icon" :src="getVipIcon(item.vip_info)" />

                    <img
                      class="avatar-icon"
                      :src="
                        item.avatar
                          | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
                      "
                    />
                  </div>
                  <div class="info-content">
                    <span class="name">{{ item.patient_name }}</span>
                    <span class="info">
                      <span>{{ item.sex_text }}</span>
                      <span v-if="item.age && item.sex_text"> | </span>
                      <span>{{ item.age ? `${item.age}岁` : '' }}</span>
                    </span>

                    <span class="mobile">{{ item.mobile }}</span>
                    <span class="stage-mobile-box" v-if="item.show_staging_mobile === '1'">
                      <span class="stage-icon">暂存</span>
                      <span class="stage-mobile">{{ item.staging_mobile }}</span>
                    </span>
                  </div>
                </div>
                <div class="flex flex-item-between flex-item-align" @click.stop="creatConsumer" v-else>
                  <p class="flex flex-c">
                    <span>{{ nickname }}</span>
                    <span class="tip">尚无该用户</span>
                  </p>
                  <a>创建用户</a>
                </div>
              </template>
            </el-autocomplete>
          </div>
        </FormItem>

        <!-- 原先的基本信息详情展示，现已抽离 -->
        <!--      <div v-else class="info-content-box">-->
        <!--        <div class="info-item">-->
        <!--          <div class="info-label">预约单号：</div>-->
        <!--          <div class="info-value" style="min-width: 170px">{{ detailInfo.code || '-' }}</div>-->
        <!--        </div>-->

        <!--        <div class="info-item">-->
        <!--          <div class="info-label">预约状态：</div>-->
        <!--          <div class="info-value" style="min-width: fit-content">-->
        <!--            {{ (detailInfo.reserve_status === '5' && detailInfo.reserve_status_desc) || detailInfo.status_desc || '-' }}-->
        <!--          </div>-->
        <!--        </div>-->

        <!--        <div class="info-item">-->
        <!--          <div class="info-label">下单人：</div>-->
        <!--          <div class="info-value">-->
        <!--            <span>{{ detailInfo.reserve_user?.name }}</span-->
        <!--            ><span style="margin-left: 10px">{{ detailInfo.reserve_user?.mobile }}</span>-->
        <!--          </div>-->
        <!--        </div>-->
        <!--      </div>-->

        <!-- 原先的预约信息 -->
        <!--      <div class="header">-->
        <!--        <div class="header-line"></div>-->
        <!--        <div>-->
        <!--          预约信息-->
        <!--          <a v-if="showEditBtn" style="font-size: 11px; font-weight: 400; margin-left: 3px" @click="changeEditChange">-->
        <!--            {{ disabled ? '修改预约单' : '取消修改' }}-->
        <!--            <Icon style="margin-left: -3px" type="md-create" :size="11" />-->
        <!--          </a>-->
        <!--        </div>-->
        <!--      </div>-->

        <!-- 原先的预约信息，包含创建和详情展示，现在此处仅仅用于创建使用，详情抽离组件了-->
        <add-reserve-com
          v-if="!disabled"
          ref="addReserveComRefs"
          v-model="addReserveData"
          @close="closeModal"
          :isVip="isVip"
          :uid="current_user_info.uid"
          :can_use="detailInfo.can_use_card"
          :disabled="disabled"
          :type="type"
          :row="row"
          :options="options"
        ></add-reserve-com>

        <!-- 原先的备注信息，现进行详情抽离 -->
        <template>
          <div class="header">
            <div class="header-line"></div>
            <div>其他信息</div>
          </div>
          <FormItem label="顾客备注" prop="user_remark" v-if="detailInfo.source === 'rst_weapp'">
            <div>{{ user_remark || '-' }}</div>
          </FormItem>
          <FormItem label="门店备注" prop="remark">
            <Input
              style="width: 520px"
              v-model="queryFormData.remark"
              type="textarea"
              :autosize="{ minRows: 2, maxRows: 6 }"
              maxlength="300"
              show-word-limit
              :placeholder="disabled ? '' : '请输入门店备注'"
            />
          </FormItem>
        </template>
      </Form>

      <!-- 创建用户 -->
      <create-user-modal
        :showCreateInfo="optionChange"
        :source-list="sourceList"
        :visible.sync="consumerVisibleDia"
        :level-list="levelList"
        :name="creatName"
        :default-source="defaultSource"
      ></create-user-modal>
    </div>
  </div>
</template>

<script>
import debounce from 'lodash.debounce';
import S from '@/libs/util';
import cloneDeep from 'lodash.clonedeep';
import CreateUserModal from '@/components/k-create-user/CreateUserModal.vue';
import addReserveCom from '@/components/addReserveCom/index.vue';
import moment from 'moment';
import { getPhysioName, isRstClinic } from '@/libs/runtime';
import reserveDetail from '@/components/addReserveCom/detailComponents/reserveDetail.vue';
import reserveUserInfo from '@/components/addReserveCom/detailComponents/reserveUserInfo.vue';
export default {
  name: 'addReserve',
  components: {
    CreateUserModal,
    addReserveCom,
    reserveDetail,
    reserveUserInfo,
  },
  props: {
    row: {
      type: Object,
      default: () => ({}),
    },
    userInfo: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否是看板
    isBoard: {
      type: Boolean,
      default: false,
    },
    showEditBtn: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '1',
    },
    options: {
      type: Object,
      default: () => ({
        out_sales_channel_desc: [],
      }),
    },
    isNotRemoveUser: Boolean,
  },
  mixins: [],
  data() {
    return {
      confirmLoading: false,
      nickname: '',
      current_user_info: {}, // 当前用户
      queryFormData: {
        remark: '',
      },
      user_remark: '', // 顾客备注
      ruleValidate: {},

      // 创建用户
      name: '',
      creatName: '',
      consumerVisibleDia: false, // 创建用户的弹窗
      sourceList: [],
      levelList: ['A', 'B', 'C', 'D', 'E', 'F'],

      addReserveData: {
        deductible_flag: '',
        services: [{ goods_service_id: '', physio_id: '', physical_name: '', physical_avatar: '' }],
        // reserve_date: this.$moment().format('YYYY-MM-DD'),
        reserve_date: '',
        reserve_time: '',
        reserve_time_flag: '',
        reserve_time_flag_text: '',
        out_sales_channel: '',
        duration: {
          // hour: 0,
          minute: null,
        },
      },

      defaultSource: '', // 默认预约
      detailInfo: {},
      pageLoad: false,
    };
  },
  computed: {
    getPhysioName() {
      return getPhysioName();
    },
    getVipIcon() {
      return vipInfo => {
        // 980会员与榕粉会员合并使用相同icon
        let isVip = vipInfo?.findIndex(item => item.user_type === '1' || item.user_type === '4') > -1;
        if (isVip) {
          return require(`@/assets/image/order/vip_980.png`);
        }
        let is9800Vip = vipInfo?.findIndex(item => item.user_type === '3') > -1;
        if (is9800Vip) {
          return require(`@/assets/image/order/vip_9800.png`);
        }

        return '';
      };
    },
    isVip() {
      console.log('=>(addReserve.vue:353) 1', 1);
      return this.current_user_info.vip_info?.findIndex(item => item.user_type === '1' || item.user_type === '4') > -1;
    },
    getVipBorderColor() {
      return vipInfo => {
        return vipInfo?.findIndex(item => item.user_type === '3') > -1 ? '#FAD88F' : '#B0C3DD';
      };
    },
  },
  watch: {
    consumerVisibleDia(val) {
      if (!val) {
        this.nickname = '';
      }
    },
    confirmLoading() {
      this.$emit('getLoading', this.confirmLoading);
    },
  },
  created() {
    const uid = this.$route.query.uid;
    if (uid) {
      this.handleDefaultUID(uid);
    }
  },
  mounted() {},
  methods: {
    // 选中指定的用户
    handleDefaultUID(uid = '') {
      this.getUserList({ uid }).then(res => {
        this.handleSelect(res?.users[0] || {});
      });
    },
    getUserInfo(uid) {
      this.$api.getUserInfo({ uid }).then(res => {
        this.uid = uid;
        res?.user && this.handleSelect(res?.user);
      });
    },
    /**
     * 比较两个时间段的大小 time1 < time 2
     * time1 预约时间
     * time2 系统时间
     * */
    compareTimes(time, symbol) {
      const system_date = moment().format('YYYY-MM-DD HH:mm');
      const system_date_timestamp = moment(system_date).valueOf();
      const current_date = time;
      const current_timestamp = moment(current_date).valueOf();
      if (symbol === '<=') {
        return current_timestamp <= system_date_timestamp;
      } else {
        return current_timestamp < system_date_timestamp;
      }
    },
    // 检测是否是过去的时间满足条件，或者外来的时间，可不选
    checkLastTime() {
      const reserve_date = `${this.addReserveData.reserve_date} ${this.addReserveData.reserve_time}`;
      let physio_id = this.addReserveData.services[0].physio_id;
      let goods_service_id = this.addReserveData.services[0].goods_service_id;
      if (this.compareTimes(reserve_date)) {
        if (!Number(physio_id || 0) && !Number(goods_service_id || 0)) {
          this.$Message.error(`请选择服务和${this.getPhysioName}`);
          return false;
        }
      }
      return true;
    },
    clearData() {
      this.current_user_info = {};
      this.name = '';
      this.mobile = '';
      this.nickname = '';
      this.addReserveData = {
        services: [{ goods_service_id: '', physio_id: '' }],
        reserve_date: '',
        reserve_time: '',
        reserve_time_flag: '',
        reserve_time_flag_text: '',
        duration: {
          // hour: 0,
          minute: null,
        },
      };
      this.queryFormData.remark = '';
      this.$nextTick(() => {
        this.$refs?.addReserveComRefs?.resetAllInfo();
      });
    },
    deleteUserInfo() {
      this.$Modal.confirm({
        title: '更换到店人将会清除预约信息，是否确认更换？',
        content: '<p></p>',
        onOk: () => {
          this.clearData();
        },
        onCancel: () => {},
      });
    },
    // 创建用户返回的数据
    optionChange(item) {
      this.name = item.patient_name;
      this.mobile = item.mobile;
      this.nickname = item.patient_name;
      this.current_user_info = {
        nickname: this.nickname,
        ...item,
      };
    },
    /**
     * @description:远程搜索用户信息
     * */
    querySearchAsync(keyword, cb) {
      this.creatName = cloneDeep(keyword);
      if (this.searchTimes && !keyword) {
        cb(this.userList);
      } else {
        let copyName = this.name || 'none';
        if (keyword !== copyName) {
          this.getUserList({ search: keyword }, cb);
        } else {
          cb(this.userList);
        }
        this.getUserList({ search: keyword }, cb);
      }
    },
    // 点击创建用户，显示弹窗
    creatConsumer(val) {
      this.consumerVisibleDia = true;
      this.$refs.custom.close();
    },
    handleSelect(item) {
      this.current_user_info = item;
      this.name = item.patient_name;
      this.mobile = item.mobile;
      this.nickname = item.patient_name;
    },
    // 当搜索的人不存在时,失焦清除绑定数据,不允许自建
    blur() {
      setTimeout(() => {
        if (!this.name) {
          if (!this.consumerVisibleDia) {
            this.nickname = '';
            this.$refs.custom?.getData();
          }
        } else {
          this.nickname = this.name;
        }
      }, 200);
    },
    // api-获取用户列表-用户手机号带出用户信息
    getUserList: debounce(function ({ search = '', uid = '' }, cb) {
      console.log('search');
      return new Promise(resolve => {
        let params = {
          page: 1,
          pageSize: 20,
          search,
          uid: search ? '' : uid,
        };
        this.searchTimes++;
        if (search) {
          this.searchTimes = 0;
        }
        this.$api.getUserList(params).then(res => {
          resolve(res);
          // 获取用户数据
          this.handleUserList(res.users, cb);
        });
      });
    }, 400),
    // 处理用户数据
    handleUserList(data, cb) {
      this.userList = data;
      if (!data.length) {
        cb([{ empty: true }]);
        return [];
      }
      typeof cb === 'function' && cb(data);
    },
    // 初始化
    init() {
      return new Promise((resolve, reject) => {
        this.getArrivalOptions();
        if (this.row.id) {
          this.getReservev2ReserveShow().then(resolve).catch(reject);
        } else if (this.userInfo.uid) {
          this.$set(this.current_user_info, 'uid', this.userInfo.uid);
          this.$set(this.current_user_info, 'real_name', this.userInfo.name);
          this.$set(this.current_user_info, 'avatar', this.userInfo.avatar);
          this.$set(this.current_user_info, 'mobile', this.userInfo.mobile);
          this.$set(this.current_user_info, 'sex_text', this.userInfo.sex_desc);
        } else {
          // 由于日期和时刻合并了，此处不做日期的初始化了
          // this.addReserveData.reserve_date = this.row?.reserve_date || this.addReserveData.reserve_date;
          // 理疗师的时刻和医生的时间段默认不初始化
          // this.addReserveData.reserve_time = this.row?.start || this.addReserveData.reserve_time;

          let services_item = {
            physio_id: this.row?.physio_id || this.addReserveData.services[0].physio_id,
            physical_name: this.row?.name || this.addReserveData.services[0].physical_name,
            physical_avatar: this.row?.avatar || this.addReserveData.services[0].physical_avatar,
          };
          this.$set(this.addReserveData.services, 0, { ...this.addReserveData.services[0], ...services_item });
          console.log('=>(addReserve.vue:349) this.addReserveData', this.addReserveData);
          resolve({});
        }
      });
    },

    // 获取详情信息
    getReservev2ReserveShow() {
      return new Promise((resolve, reject) => {
        let params = {
          id: this.row.id,
        };
        this.pageLoad = true;
        this.$api
          .getReservev2ReserveShow(params)
          .then(res => {
            this.handleEchoData(res);
            resolve(res);
          })
          .finally(() => (this.pageLoad = false))
          .catch(reject);
      });
    },

    handleEchoData(data = {}) {
      this.detailInfo = data;
      let info = data.reserve_user || {};
      this.$set(this.current_user_info, 'uid', info.uid);
      this.$set(this.current_user_info, 'real_name', info.name);
      this.$set(this.current_user_info, 'avatar', info.avatar);
      this.$set(this.current_user_info, 'mobile', info.mobile);
      this.$set(this.current_user_info, 'sex_text', info.sex_desc);
      this.$set(this.current_user_info, 'vip_info', info.vip_info);
      let services = [];
      data?.services?.map(item => {
        services.push({
          serve_name: item.goods_service.name,
          physical_name: item.physio.name,
          physical_avatar: item.physio.avatar,
          goods_service_id: item.goods_service_id,
          physio_id: item.physio_id,
          duration: data.duration.minute,
          service_duration: data.service_duration.minute,
          prepare_duration: data.prepare_duration.minute,
          role_name: item.physio.role_name,
          level_name: item.physio.level_name,
          out_sales_channel_list: data.out_sales_channel_list, // 服务绑定了哪些外部渠道
          bind_sales_channel: data.bind_sales_channel, // 服务是否绑定了外部渠道
          price: item?.goods_service?.price,
          vip_price: item?.goods_service?.vip_price,
        });
      });
      console.log('=>(addReserve.vue:556) services', services);
      this.addReserveData = {
        deductible_flag: data.deductible_flag,
        services: services || [],
        reserve_date: data.reserve_date,
        reserve_time: data.reserve_time,
        reserve_time_flag: data.reserve_time_flag,
        reserve_time_flag_text: data.reserve_time_flag_text,
        out_sales_channel: data.out_sales_channel,
        duration: {
          // hour: Number(data?.duration?.hour || 0) || null,
          minute: Number(data?.duration?.minute || 0) || null,
        },
      };
      this.queryFormData.remark = data.remark;
      this.user_remark = data.user_remark;
    },

    confirm() {
      if (this.validFields()) {
        if (this.row.id) {
          this.getReservev2ReserveUpdate();
        } else {
          this.getReservev2ReserveStore();
        }
      }
    },

    /**
     * @desc 创建预约校验
     * */
    validFields() {
      let service = this.addReserveData.services[0] || {};
      console.log('=>(addReserve.vue:624) this.addReserveData', this.addReserveData);
      if (!this.current_user_info.uid) {
        this.$Message.error('请选择到店人信息');
        return false;
      }

      if (
        !this.addReserveData.reserve_date &&
        (!this.addReserveData.reserve_time || !this.addReserveData.reserve_time_flag_text)
      ) {
        this.$Message.error('请选择预约到店时间');
        return false;
      }

      if (this.type == '1') {
        // 当前理疗师和服务是绑在一起的，判断是否选择了理疗师即可
        if (!Number(service.physio_id || 0)) {
          this.$Message.error('请预约服务');
          return false;
        }
        // 选择的服务绑定了外部渠道，没有选择选项
        if (service.bind_sales_channel == '1' && !this.addReserveData.out_sales_channel) {
          this.$Message.error('请选择外部预约渠道');
          return false;
        }

        // 可以使用卡券抵扣，没有选择选项，一般默认都有值，此处添加一层意外判断
        if (this.$refs.addReserveComRefs?.isShowCardUseModel && !this.addReserveData.deductible_flag) {
          this.$Message.error('请选择是否使用卡券抵扣');
          return false;
        }
      }

      if (this.type == '2') {
        if (!Number(service.physio_id || 0)) {
          this.$Message.error('请预约医生');
          return false;
        }
      }
      return true;
    },

    // api-获取options
    getArrivalOptions() {
      this.$api.getArrivalOptions().then(res => {
        // 用户来源
        this.sourceList = S.descToArrHandle(res.userFromDesc);
        this.defaultSource = 'RESERVE';
      });
    },

    handlerParams() {
      let params = {
        name: this.current_user_info.real_name,
        mobile: this.current_user_info.mobile,
        uid: this.current_user_info.uid,
        ...this.addReserveData,
        ...this.queryFormData,
        services: this.handlerServices(),
        duration: {
          hour: 0,
          minute: this.addReserveData.duration.minute,
        },
      };

      if (this.row.id) {
        this.$set(params, 'id', this.row.id);
      }

      let services = this.handlerServices();
      //  预约医生
      if (this.type == '2') {
        this.$set(params, 'type', 'DOCTOR');
        let physio_id = services[0]?.physio_id || '';
        this.$set(params, 'physio_id', physio_id);
        this.$delete(params, 'reserve_time');
        this.$delete(params, 'services');
        this.$delete(params, 'deductible_flag');
      }

      // 特殊处理，如果没有选择服务，确保服务占用时长为空(加一层保障)
      if (this.type == '1') {
        let goods_service_id = services[0]?.goods_service_id || '';
        if (!Number(goods_service_id)) {
          this.$set(params.duration, 'minute', '');
        }
      }

      return params;
    },

    // 创建预约
    getReservev2ReserveStore() {
      this.confirmLoading = true;
      let params = {
        ...this.handlerParams(),
      };
      this.$api
        .getReservev2ReserveStore(params)
        .then(res => {
          params.id = res.id;
          params.reserve_data = this.addReserveData || {};
          this.$emit('success', true, false, params);
          this.closeModal();
        })
        .finally(() => (this.confirmLoading = false));
    },

    // 修改预约
    getReservev2ReserveUpdate() {
      this.confirmLoading = true;
      let params = {
        ...this.handlerParams(),
      };
      this.$api
        .getReservev2ReserveUpdate(params)
        .then(() => {
          this.$emit('success', true, false, params);
          this.closeModal();
        })
        .finally(() => (this.confirmLoading = false));
    },

    handlerServices() {
      let list = [];
      this.addReserveData?.services?.forEach(item => {
        list.push({ goods_service_id: item.goods_service_id, physio_id: item.physio_id, quantity: 1 });
      });
      return list;
    },

    closeModal() {
      this.$emit('close');
    },
    changeEditChange() {
      this.$emit('changeEditChange');
    },
  },
};
</script>

<style lang="less" scoped>
.info-content-box {
  margin-left: 30px;
  margin-bottom: 16px;
  display: flex;
  margin-right: 57px;
  min-height: 32px;
  .info-item {
    //flex: 1;
    display: flex;
    align-items: flex-start;
    margin-right: 10px;
    margin-left: 7px;

    .info-label {
      font-weight: 400;
      font-size: 12px;
      color: #333333;
      line-height: 16px;
      min-width: 60px;
      text-align: right;
    }

    .info-value {
      font-weight: 400;
      font-size: 12px;
      color: #999999;
      line-height: 16px;
      word-break: break-all;
    }
  }
}

.header {
  display: flex;
  align-items: center;

  .header-line {
    width: 2px;
    height: 14px;
    background: #175bd4;
    margin-right: 8px;
  }
  padding: 10px;
  position: relative;
  font-weight: 500;
  font-size: 14px;
  color: #000000;
  line-height: 20px;
  text-align: left;
}
.update-action {
  display: flex;
  align-items: center;
  font-weight: 400;
  font-size: 12px;
  color: #155bd4;
  line-height: 18px;
  .update-icon {
    width: 16px;
    min-width: 16px;
    height: 16px;
    margin-right: 3px;
    cursor: pointer;
  }
}
</style>
<style lang="less" scoped>
::v-deep .ivu-select-visible .ivu-select-selection {
  box-shadow: none;
}
.spin {
  height: 460px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
<!--用户样式-->
<style lang="less" scoped>
// 搜索用户的样式优化
::v-deep .custom-user-autocomplete {
  width: 520px;
  height: 32px;
  border-radius: 4px;
  margin-top: -10px;

  .el-input__inner {
    height: 32px;
    padding: 0 12px;
    border-color: #dcdcdc;
    font-size: 12px;
    border-radius: 4px !important;
  }

  .is-disabled {
    .el-input__inner {
      background: #f3f3f3;
      color: #ccc;
    }
  }
}

::v-deep .el-input__inner:focus {
  //box-shadow: 0 0 0 1px rgba(68, 124, 221, 0.2)
  box-shadow: 0 0 0 1.5px rgba(21, 91, 212, 0.2);
}

.search-box {
  margin-top: -10px;
  min-height: 64px;
  min-width: fit-content;
  width: 100%;
  display: flex;
  //justify-content: center;
  align-items: center;

  .current-user {
    width: 520px;
    height: 62px;
    background: #ffffff;
    border-radius: 4px;
    border: 1px solid #dcdcdc;
    padding: 10px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .current-user-left {
      display: flex;
      align-items: center;

      .avatar-box {
        width: 38px;
        height: 38px;
        border-radius: 50%;
        position: relative;
        box-sizing: content-box;
        //border: 1px solid #EBEDF0;
        //background: #EBEDF0;
        .vip-icon {
          width: 30px;
          min-width: 30px;
          height: 12px;
          position: absolute;
          bottom: -6px;
          right: 4px;
        }

        .avatar {
          width: 38px;
          min-width: 38px;
          height: 38px;
          border-radius: 50%;
        }
      }

      .vip-avatar-box {
        border: 1px solid #e7c1a6;
      }

      .user-info {
        margin-left: 16px;

        .user-info-top {
          display: flex;
          align-items: center;

          .user-info-name {
            //font-weight: 600;
            font-size: 14px;
            color: #303133;
            //line-height: 24px;
          }

          .user-info-sex {
            margin-left: 12px;
            font-weight: 400;
            font-size: 14px;
            color: #909399;
            //line-height: 18px;
          }
        }

        .user-info-mobile-box {
          display: flex;
          align-items: center;

          .info-mobile {
            font-weight: 400;
            font-size: 14px;
            color: #999999;
            line-height: 20px;
          }

          .info-stage-mobile {
            margin-left: 20px;
            font-weight: 400;
            font-size: 13px;
            color: #909399;
            line-height: 20px;
            display: flex;
            align-items: center;

            .stage-tag {
              background: #fff3df;
              border-radius: 2px;
              padding: 1px 4px;
              font-weight: 400;
              font-size: 12px;
              color: #ffa300;
              line-height: 18px;
              min-width: fit-content;
            }

            .stage-mobile {
              margin-left: 6px;
              font-weight: 400;
              font-size: 13px;
              color: #909399;
              line-height: 20px;
            }
          }
        }
      }
    }

    .delete-user-icon {
      display: block;
      width: 10px;
      height: 10px;
      cursor: pointer;

      &:hover {
        transform: scale(1.3);
      }
    }

    .delete-user-icon-box {
      //margin-top: -23px;
      //margin-left: -3px;
      //background: url('https://img-sn-i01s-cdn.rsjxx.com/image/2025/0108/163541_57316.png') no-repeat;
      //background-size: 10px 10px;
      //width: 16px;
      //height: 16px;
      //cursor: pointer;

      //&:hover {
      //  .delete-user-icon {
      //    display: block;
      //  }
      //}
    }
  }

  .autocomplete {
    display: flex;
    align-items: center;
    padding: 8px 0px;

    .avatar-box {
      box-sizing: content-box;
      width: 38px;
      min-width: 38px;
      height: 38px;
      //background: #D8D8D8;
      border-radius: 50%;
      position: relative;

      .vip-icon {
        width: 30px;
        min-width: 30px;
        height: 12px;
        position: absolute;
        bottom: -6px;
        right: 4px;
      }

      .avatar-icon {
        width: 38px;
        min-width: 38px;
        height: 38px;
        border-radius: 50%;
        margin-left: 0px;
        margin-top: 0px;
      }
    }

    .vip-avatar-box {
      border: 1px solid #e7c1a6;
    }

    .info-content {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .name {
        margin-left: 16px;
        //font-weight: 600;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
      }

      .info {
        margin-left: 12px;
        font-weight: 400;
        font-size: 12px;
        color: #909399;
        line-height: 18px;
      }

      .mobile {
        margin-left: 12px;
        font-weight: 400;
        font-size: 13px;
        color: #999999;
        line-height: 18px;
      }

      .stage-mobile-box {
        margin-left: 12px;

        .stage-icon {
          padding: 1px 4px;
          background: #fff3df;
          border-radius: 2px;
          font-weight: 400;
          font-size: 12px;
          color: #ffa300;
          line-height: 18px;
          transform: scale(0.8);
        }

        .stage-mobile {
          margin-left: 6px;
          font-weight: 400;
          font-size: 13px;
          color: #606266;
          line-height: 18px;
        }
      }
    }
  }
}

::v-deep .ivu-input {
  border: 1px solid #dcdcdc;
}

.label {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  font-weight: 400;
  font-size: 12px;
  color: #333333;
  line-height: 16px;

  .mark {
    color: #fa4f4f;
  }
}

:deep(.ivu-input-disabled) {
  background: #f3f3f3;
}

:deep(.date-picker .ivu-input-disabled) {
  background: #f3f3f3;
}

::v-deep textarea.ivu-input {
  padding: 8px 12px;
}
</style>
