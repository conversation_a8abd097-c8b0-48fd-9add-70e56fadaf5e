<template>
  <Poptip
    popper-class="custom-radius-poptip"
    trigger="click"
    width="550"
    ref="pop"
    :disabled="disabled"
    @on-popper-show="popShow"
    @on-popper-hide="popHide"
  >
    <div slot="title" class="title">
      <Input
        v-model="name"
        style="width: 100%; max-width: unset; margin-right: 10px"
        @on-change="inputChange"
        placeholder="请输入服务名称搜索"
        @keyup.enter.native="debounceSearch"
      >
        <div slot="suffix" class="cursor" @click="debounceSearch">
          <Icon type="ios-search" />
        </div>
      </Input>
      <a @click="reset" style="margin-right: 10px">重置</a>
      <Icon class="close-icon" color="#999" type="md-close" size="20" @click="closePopTip" />
    </div>
    <div slot="content" class="reserve-content">
      <div class="empty" v-if="!loading && !list.length">当前时间或当前{{ getPhysioName }}没有可提供服务</div>
      <Spin v-if="loading" class="spin"></Spin>
      <template v-else>
        <div
          class="reserve-item"
          :class="{ 'reserve-item--active': id === item.id }"
          @click="checkedServe(item)"
          v-for="(item, index) in list"
          :key="index"
        >
          <div class="flex item-top">
            <Tooltip :content="item.name" :disabled="item.name?.lenght < 7">
              <div class="serve-name ecs">{{ item.name }}</div>
            </Tooltip>
            <div class="serve-tag-box">
              <div class="serve-tag">约{{ item.duration || 0 }}分钟</div>
              <div class="serve-tag" v-if="is_rst && item.can_use_card == '1'">卡券抵扣</div>
            </div>
          </div>
          <div class="serve-info">
            <div class="serve-price">¥{{ Number(item.price || 0).toFixed(2) }}</div>
            <!--  <Tooltip theme="light" placement="top" v-if="item.sale_promotion_price > 0 && is_rst">-->
            <!--    <div slot="content">-->
            <!--      980专享 <span style="color: red">￥{{ item.sale_promotion_price }}</span>-->
            <!--    </div>-->
            <!--    <div class="vip-tag-box">-->
            <!--      <svg-icon name="980_service_tag" class="vip-tag"></svg-icon>-->
            <!--      <span class="vip-price">￥{{ item.sale_promotion_price }}</span>-->
            <!--    </div>-->
            <!--  </Tooltip>-->
            <Tooltip theme="light" placement="top" v-if="is_rst && isVip">
              <div slot="content">
                会员专享 <span style="color: red">￥{{ item.vip_price || 0 }}</span>
              </div>
              <div class="vip-tag-box">
                <img src="https://img-sn01.rsjxx.com/image/2025/0616/175205_87738.png" alt="" class="vip-tag" />
                <span class="rst-vip-price">￥{{ item.vip_price || 0 }}</span>
              </div>
            </Tooltip>
            <div
              v-if="item.out_goods_name && ['1', '2'].includes(out_sales_channel)"
              style="flex: 1; text-align: right; padding-left: 8px"
              class="text-ellipsis"
              v-overflow-tooltip="{ content: item.out_goods_name }"
            >
              {{ out_sales_channel === '1' ? '美团商品名称：' : '抖音商品名称：' }}{{ item.out_goods_name }}
            </div>
          </div>
        </div>
      </template>
    </div>
    <slot></slot>
  </Poptip>
</template>

<script>
import debounce from 'lodash.debounce';
import { getPhysioName, isRstClinic } from '@/libs/runtime';

export default {
  name: 'servePoptip',
  components: {},
  mixins: [],
  props: {
    physical_id: {
      type: [String, Number],
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    uid: {
      type: String,
      default: '',
    },
    out_sales_channel: {
      type: String,
      default: '',
    },
    isVip: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      name: '',
      list: [],
      loading: true,
    };
  },
  computed: {
    is_rst() {
      return isRstClinic();
    },
    getPhysioName() {
      return getPhysioName();
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    inputChange(val) {
      this.debounceSearch();
    },
    reset() {
      this.name = '';
      this.debounceSearch();
    },
    closePopTip() {
      this.$refs.pop.handleClose();
    },
    popShow() {
      this.getPhysicalTServiceList();
    },
    popHide() {
      this.name = '';
    },
    // 选择服务
    checkedServe(item) {
      this.$emit('change', item);
      this.closePopTip();
    },

    debounceSearch: debounce(function () {
      this.getPhysicalTServiceList();
    }, 300),

    // 获取服务
    getPhysicalTServiceList() {
      this.loading = true;
      let params = {
        uid: this.uid,
        id: this.physical_id,
        name: this.name,
        out_sales_channel: this.out_sales_channel,
        page: 1,
        pageSize: 2000,
      };
      this.$api
        .getPhysicalTServiceList(params)
        .then(res => {
          this.list = res.list;
        })
        .finally(() => (this.loading = false));
    },
  },
};
</script>

<style lang="less" scoped>
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.title {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .close-icon {
    cursor: pointer;

    &:hover {
      color: #333 !important;
    }
  }
}

.spin {
  width: 100%;
  display: flex;
  justify-content: center;
  align-content: center;
  margin-top: 120px;
}

.reserve-content {
  margin: 10px 0px;
  padding: 0 10px;
  height: 260px;
  overflow-y: auto;
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;

  &::-webkit-scrollbar {
    display: none;
  }

  .reserve-item--active {
    background: #efedfb !important;
  }

  .reserve-item {
    cursor: pointer;
    width: 49%;
    margin-bottom: 10px;
    margin-right: 2%;
    position: relative;
    outline: none;
    border-radius: 2px;
    line-height: 20px;
    box-sizing: border-box;
    padding: 10px 12px;
    background: #fff;
    border-radius: 2px;
    border: 1px solid #ebedf0;

    &:hover {
      background: #efedfb;
    }

    &:nth-child(2n) {
      margin-right: 0px;
    }

    .item-top {
      margin-bottom: 11px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .serve-name {
      font-weight: 500;
      font-size: 12px;
      color: #333333;
      line-height: 20px;
      text-align: left;
    }

    .serve-tag-box {
      margin-top: -4px;
      display: flex;
    }
    .serve-tag {
      font-weight: 300;
      font-size: 12px;
      color: #8b8f97;
      line-height: 18px;
      background: #f5f6f8;
      border-radius: 2px;
      padding: 0 2px;
      margin-right: 4px;
      &:last-child {
        margin-right: 0px;
      }
    }

    .serve-info {
      display: flex;
      align-items: baseline;
      justify-content: space-between;

      .serve-price {
        font-weight: 500;
        font-size: 14px;
        color: #fa4f4f;
        line-height: 11px;
        text-align: right;
      }
      .vip-tag-box {
        position: relative;
        .vip-price {
          position: absolute;
          right: 6px;
          top: 0;
          color: #fff;
        }
        .rst-vip-price {
          position: absolute;
          right: 4px;
          top: 1px;
          color: #fff;
        }
      }
    }
  }
}

.cursor {
  cursor: pointer;
}

.empty {
  width: 100%;
  text-align: center;
}

.vip-tag {
  width: 90px;
  height: 18px;
}
</style>
