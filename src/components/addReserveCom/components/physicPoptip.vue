<template>
  <Poptip
    popper-class="custom-radius-poptip"
    trigger="click"
    width="300"
    ref="pop"
    :disabled="disabled"
    v-model="popVisible"
    @on-popper-show="popShow"
  >
    <div slot="title" class="title">
      <div>请选择{{ type == '2' ? '医生' : getPhysioName }}</div>
      <Icon class="close-icon" color="#999" type="md-close" size="20" @click="closePopTip" />
    </div>
    <div slot="content">
      <div class="physic-content">
        <Spin v-if="loading" class="spin"></Spin>
        <div v-else>
          <div v-if="list.length" class="physic-item cursor" style="justify-content: center" @click="checkedPhysic()">
            不选择{{ type == '2' ? '医生' : getPhysioName }}
          </div>
          <div v-else class="physic-item--empty">暂无{{ type == '2' ? '医生' : getPhysioName }}可选择</div>
          <div v-for="(item, index) in list">
            <can-reserve-time-poptip
              :date="date"
              style="width: 100%"
              :reserve_time="reserve_time"
              :disabled="item.select_check != '0'"
              :list="item.range_list"
              class="can-reserve-poptip"
            >
              <div
                class="physic-item cursor"
                :class="{ 'physic-item--active': id === item.id, 'physic-item--disabled': item.select_check == '0' }"
                @click="checkedPhysic(item)"
                :key="index"
              >
                <img
                  class="avatar"
                  :src="
                    item.avatar
                      | imageStyle('B.w300', 'http://img-sn-i01s-cdn.rsjxx.com/rsjxx/2023/1220/151459_81411.png')
                  "
                />
                <div class="physic-name ecs">{{ item.name }}</div>
                <div class="physic-time" v-if="item.schedule_msg">({{ item.schedule_msg }})</div>
              </div>
            </can-reserve-time-poptip>
          </div>
          <div class="add-box" v-if="list.length === 0">
            <!-- 榕树堂没有添加技师入口，只展示文案 -->
            <div style="color: #ccc" v-if="type !== '2' && is_rst">暂无可用技师</div>
            <Button v-else type="primary" @click="toAddPhysical"
              >去添加{{ type == '2' ? '医生' : getPhysioName }}</Button
            >
          </div>
        </div>
      </div>
    </div>
    <slot></slot>
  </Poptip>
</template>

<script>
import { getPhysioName, isRstClinic } from '@/libs/runtime';
import canReserveTimePoptip from './canReserveTimePoptip.vue';
export default {
  name: 'physicPoptip',
  components: { canReserveTimePoptip },
  mixins: [],
  props: {
    service_id: {
      type: [String, Number],
      default: '',
    },
    id: {
      type: String,
      default: '',
    },
    reserve_id: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: '1',
    },
    date: {
      type: [String, Date],
      default: '',
    },
    reserve_time: {
      type: String,
      default: '',
    },
    duration: {
      type: [String, Number],
      default: '',
    },
    row: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      list: [],
      loading: false,
      popVisible: false,
    };
  },
  computed: {
    getPhysioName() {
      return getPhysioName();
    },
    is_rst() {
      return isRstClinic();
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    toAddPhysical() {
      if (this.type == '2') {
        this.$router.push('/setting/physician/list');
      } else {
        this.$router.push('/setting/member/list');
      }
      this.$emit('close');
      this.popVisible = false;
      this.closePopTip();
    },
    closePopTip() {
      this.$refs.pop.handleClose();
    },
    popShow() {
      if (this.type === '2') {
        this.getR2SPhysioeSelectDoctor();
      } else {
        this.getR2SPhysiolist();
      }
    },
    checkedPhysic(item) {
      if (item?.select_check == '0') {
        return;
      }
      this.$emit('change', item || {});
      this.closePopTip();
    },
    // 获取医生
    getR2SPhysioeSelectDoctor() {
      this.list = [];
      let params = {};
      this.loading = true;
      this.$api
        .getR2SPhysioeSelectDoctor(params)
        .then(res => {
          this.list = res.list;
        })
        .finally(() => (this.loading = false));
    },
    // 获取服务
    getR2SPhysiolist() {
      console.log('row', this.row);
      this.list = [];
      let params = {
        reserve_id: this.row?.id,
        reserve_date: this.date,
        reserve_time: this.reserve_time,
        duration: this.duration,
        status: 'ON',
        goods_service_id: this.service_id,
      };
      if (this.reserve_id) {
        this.$set(params, 'reserve_id', this.reserve_id);
      }
      this.loading = true;
      this.$api
        .getR2SPhysiolist(params)
        .then(res => {
          this.list = res || [];
        })
        .finally(() => (this.loading = false));
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
  font-size: 14px;
  color: #000000;
  line-height: 20px;
  text-shadow: 0px 0px 13px rgba(0, 0, 0, 0.12);
  .close-icon {
    cursor: pointer;
    &:hover {
      color: #333 !important;
    }
  }
}
.physic-content {
  padding: 0px 10px 10px;
  height: 260px;
  overflow-y: auto;
  position: relative;
  .spin {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%);
    transform: translateY(-50%);
  }
  .physic-item--empty {
    background: #f6f6f6;
    border-radius: 4px;
    margin: 10px 15px;
    display: flex;
    justify-content: center;
    padding: 12px 10px;
  }
  .physic-item {
    list-style-type: none;
    margin-top: 10px;
    display: flex;
    padding: 10px;
    background: #f9f9f9;
    //box-shadow: 0px 0px 13px 0px rgba(0, 0, 0, 0.12);
    display: flex;
    align-items: center;
    justify-content: flex-start;

    &:hover {
      background: #efedfb;
    }
  }
  .physic-item--active {
    background: #efedfb !important;
  }
  .physic-item--disabled {
    background: #f2f2f2 !important;
    color: #ccc !important;
    div,
    span,
    p {
      color: #ccc !important;
    }
  }
}
.add-box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
}
.cursor {
  cursor: pointer;
}
.avatar {
  width: 24px;
  min-width: 24px;
  height: 24px;
  border-radius: 50%;
  object-fit: cover;
}
.physic-name {
  margin-left: 10px;
  font-weight: 400;
  font-size: 12px;
  color: #333333;
  line-height: 17px;
  text-align: left;
}
.physic-time {
  margin-left: 6px;
  font-weight: 400;
  font-size: 12px;
  color: #666666;
  line-height: 17px;
}
.can-reserve-poptip {
  ::v-deep .ivu-poptip-rel {
    width: 100%;
  }
}
</style>
