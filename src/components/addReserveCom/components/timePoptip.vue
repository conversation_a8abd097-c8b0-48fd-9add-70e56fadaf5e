<template>
  <Poptip
    popper-class="custom-radius-poptip"
    trigger="click"
    width="460"
    ref="pop"
    @on-popper-show="popShow"
    @on-popper-hide="popHide"
  >
    <div slot="content" class="time-content">
      <Spin v-if="loading" class="spin"></Spin>
      <div class="content" v-else>
        <div class="header">
          <div @click="expandEvent">{{ expand ? '收起过去时间' : '展开过去时间' }}</div>
          <Icon v-if="!expand" type="md-arrow-dropdown" color="#8558fa" size="20" />
          <Icon v-else type="md-arrow-dropup" color="#8558fa" size="20" />
        </div>
        <div class="item-wrapper">
          <div
            v-show="expand || compareTimes(item)"
            class="time-item"
            :class="{ 'time-item--active': item === time }"
            v-for="(item, index) in list"
            :key="index"
            @click="changeTime(item)"
          >
            {{ item }}
          </div>
        </div>
      </div>
      <div class="footer-wrapper">
        <div class="checked-time">
          预约时间：<span class="time">{{ time }}</span>
        </div>
        <Button v-if="!this.disabled" type="primary" style="width: 80px" @click="confirm">确定</Button>
      </div>
    </div>
    <slot></slot>
  </Poptip>
</template>

<script>
import moment from 'moment';
export default {
  name: 'timePoptip',
  components: {},
  mixins: [],
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    reserve_time: {
      type: String,
      default: '',
    },
    date: {
      type: [String, Date],
      default: '',
    },
  },
  data() {
    return {
      expand: false,
      time: '',
      loading: false,
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    confirm() {
      this.$emit('change', this.time || '');
      this.closePopTip();
    },
    closePopTip() {
      this.$refs.pop.handleClose();
    },
    changeTime(item) {
      this.time = item;
      this.confirm();
    },
    popShow() {
      if (this.reserve_time) {
        this.time = this.reserve_time;
      }
    },
    popHide() {
      this.time = '';
    },
    expandEvent() {
      this.expand = !this.expand;
    },
    // 比较两个时间段的大小 time1 < time 2
    compareTimes(time, symbol) {
      const today_date = moment().format('YYYY-MM-DD');
      // 当选择的日期不是当天的，直接放出所有日期
      if (this.date && moment(this.date).isAfter(today_date)) {
        return true;
      }
      const today_timestamp = moment().valueOf();
      const current_date = `${today_date} ${time}`;
      const current_timestamp = moment(current_date).valueOf();
      if (symbol === '<=') {
        return today_timestamp <= current_timestamp;
      } else {
        return today_timestamp < current_timestamp;
      }
    },
  },
};
</script>

<style lang="less" scoped>
::v-deep .ivu-poptip-title {
  display: none;
}
.time-content {
  height: 300px;
  .content {
    height: 250px;
    overflow-y: auto;
    position: relative;
    .header {
      font-size: 14px;
      padding: 10px 0px;
      width: 100%;
      color: #8558fa;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: sticky;
      top: 0px;
      background: #fff;
      border-bottom: 1px solid #f2f2f2;
    }
    .item-wrapper {
      display: flex;
      flex-wrap: wrap;
      margin-top: 12px;
      .time-item {
        cursor: pointer;
        width: 14%;
        margin-bottom: 24px;
        text-align: center;
        line-height: 100%;
        &:hover {
          color: #8558fa;
        }
      }
      .time-item--active {
        color: #8558fa;
      }
    }
  }
  .footer-wrapper {
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.2);
    position: absolute;
    bottom: 0px;
    left: 0px;
    background: #fff;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-top: 1px solid #f2f2f2;
    color: #333;
    .checked-time {
    }
  }
}
.spin {
  flex-wrap: wrap;
  width: 100%;
  display: flex;
  justify-content: center;
  align-content: center !important;
}
.time {
  font-size: 12px;
  color: #999;
  line-height: 17px;
}
</style>
