<template>
  <Poptip
    trigger="hover"
    width="300"
    placement="right-end"
    ref="pop"
    :disabled="disabled"
    v-model="popVisible"
    @on-popper-show="popShow"
  >
    <div slot="title" class="title">
      <div>预约到店时间：</div>
      <div>{{ formatDate }}</div>
      <div style="margin-left: 10px">{{ reserve_time }}</div>
    </div>
    <div slot="content" class="content">
      <Table class="mt10" stripe :columns="columns" :data="list" :height="200">
        <template slot-scope="{ row }" slot="time">{{ row.range }}</template>
      </Table>
      <!--      <div class="physic-content"></div>-->
    </div>
    <slot></slot>
  </Poptip>
</template>

<script>
import moment from 'moment';
import { getPhysioName } from '@/libs/runtime';
export default {
  name: 'canReserveTimePoptip',
  components: {},
  mixins: [],
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    date: {
      type: [String, Date],
      default: '',
    },
    reserve_time: {
      type: String,
      default: '',
    },
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loading: false,
      popVisible: false,
      columns: [
        {
          title: '可预约时间段',
          slot: 'time',
          align: 'center',
        },
      ],
    };
  },
  computed: {
    formatDate() {
      return moment(this.date).format('YYYY-MM-DD');
    },
    getPhysioName() {
      return getPhysioName();
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    closePopTip() {
      this.$refs.pop.handleClose();
    },
    popShow() {},
    checkedPhysic(item) {
      this.$emit('change', item || {});
      this.closePopTip();
    },
  },
};
</script>

<style lang="less" scoped>
.title {
  display: flex;
  align-items: center;
  .close-icon {
    cursor: pointer;
    &:hover {
      color: #333 !important;
    }
  }
}
.content {
  padding: 0px 10px;
}
::v-deep .ivu-table-header thead tr th {
  padding: 3px 0px;
}
::v-deep td {
  height: 30px;
}
.mt10 {
  margin-top: 10px;
}
::v-deep .ivu-table-tbody {
  .ivu-table-row > td {
    background: #f8f8f8;
  }
  .ivu-table-row:hover {
    td {
      background: #f8f8f8 !important;
    }
  }
}
</style>
