<template>
  <Poptip
    popper-class="custom-radius-poptip"
    trigger="click"
    width="230"
    ref="pop"
    :disabled="disabled"
    v-model="popVisible"
    @on-popper-show="popShow"
  >
    <div slot="content">
      <div class="physic-content">
        <Spin v-if="loading" class="spin"></Spin>
        <div v-else>
          <div v-if="!list.length" class="physic-item--empty">暂无时间可选择</div>
          <div class="add-box" v-if="!list.length">
            <Button type="primary" @click="toScheduling">去排班</Button>
          </div>
          <div
            class="physic-item cursor"
            :class="{ 'physic-item--active': flag === item.flag, 'physic-item--disabled': item.residue == 0 }"
            @click="checkedPhysic(item)"
            v-for="(item, index) in list"
            :key="index"
          >
            <div>{{ item.desc }}</div>
            <div style="margin-left: 10px">{{ item.p_flag_text }} {{ item.st }} - {{ item.et }}</div>
            <!--            <div class="surplus-box">剩{{ item.residue }}</div>-->
          </div>
        </div>
      </div>
    </div>
    <slot></slot>
  </Poptip>
</template>

<script>
import { getPhysioName } from '@/libs/runtime';
export default {
  name: 'doctorPoptip',
  components: {},
  mixins: [],
  props: {
    doc_id: {
      type: String,
      default: '',
    },
    date: {
      type: [String, Date],
      default: '',
    },
    flag: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      list: [],
      loading: false,
      popVisible: false,
    };
  },
  computed: {
    getPhysioName() {
      return getPhysioName();
    },
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    toScheduling() {
      this.$router.push('/setting/member/list?componentsName=management');
      this.$emit('close');
      this.popVisible = false;
      this.closePopTip();
    },
    closePopTip() {
      this.$refs.pop.handleClose();
    },
    popShow() {
      this.getR2SPhysioeTimeSelect();
    },
    checkedPhysic(item) {
      console.log('🚀 ~ checkedPhysic ~ item: ', item);
      // if (item.residue == 0) return;
      this.$emit('change', item || {});
      this.closePopTip();
    },
    // 获取服务
    getR2SPhysioeTimeSelect() {
      this.list = [];
      let params = {
        date: this.date,
        doctor_id: this.doc_id,
      };
      this.loading = true;
      this.$api
        .getR2SPhysioeTimeSelect(params)
        .then(res => {
          this.list = res.list;
        })
        .finally(() => (this.loading = false));
    },
  },
};
</script>

<style lang="less" scoped>
.physic-content {
  padding: 0px 10px;
  height: 170px;
  overflow-y: auto;
  position: relative;
  .spin {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translateX(-50%);
    transform: translateY(-50%);
  }
  .physic-item--empty {
    background: #f6f6f6;
    border-radius: 4px;
    margin: 10px 0px;
    display: flex;
    justify-content: center;
    padding: 12px 10px;
  }
  .physic-item {
    //width: 280px;
    height: 44px;
    background: #f9f9f9;
    //list-style-type: none;
    margin-top: 10px;
    display: flex;
    padding: 14px;
    &:hover {
      background: #efedfb;
    }
  }
  .physic-item--active {
    background: #efedfb !important;
  }
  .physic-item--disabled {
    cursor: not-allowed !important;
    background: #fdfafa !important;
    color: #ccc !important;
    div {
      color: #ccc;
    }
  }
}
.add-box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
}
.cursor {
  cursor: pointer;
}

.surplus-box {
  margin-left: 10px;
  color: #ffad33;
}
</style>
