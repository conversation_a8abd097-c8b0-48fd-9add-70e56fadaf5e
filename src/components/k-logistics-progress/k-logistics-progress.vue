<template>
  <Modal :value="value" width="950" footer-hide lock-scroll :mask-closable="false" @on-visible-change="visibleChange">
    <div slot="header" class="flex auto-p">
      <p class="tabs mr30" :class="{ active: tab === 1 }" @click="changeTab(1)" v-show="isLogisticsDetail">
        物流包裹发货详情
      </p>
      <p class="tabs" :class="{ active: tab === 2 }" @click="changeTab(2)">物流进度</p>
      <div class="text-center">
        <p>{{ progressList[0]?.express_name }} {{ progressList[0]?.express_no }}</p>
      </div>
      <!-- <Select v-model="progress" style="width: 200px" class="ml10 custom-select" size="small" @on-change="changeSelect"> -->
      <!--   <Option -->
      <!--     v-for="(item, index) in progressList" -->
      <!--     :value="item.express_no + index" -->
      <!--     :key="index" -->
      <!--     :label="`${item.express_name} ${item.express_no}`" -->
      <!--     style="white-space: normal; max-width: 300px; word-break: break-all; line-height: 18px" -->
      <!--   > -->
      <!--     <p slot style="color: #ccc">物流{{ index + 1 }}</p> -->
      <!--     <p>{{ item.express_name }} {{ item.express_no }}</p> -->
      <!--   </Option> -->
      <!-- </Select> -->
    </div>
    <div class="content" v-if="tab === 1">
      <Table :columns="columns1" :data="list" :loading="tableLoading">
        <!-- 商品批号 -->
        <template slot-scope="{ row, index }" slot="batch_code">
          <span>{{ row.batch_code || '-' }}</span>
        </template>

        <!-- 生产时间 -->
        <template slot-scope="{ row, index }" slot="prod_date">
          <span>{{ row.prod_date | date_format('YYYY-MM-DD') }}</span>
        </template>

        <!-- 过期时间 -->
        <template slot-scope="{ row, index }" slot="expire_date">
          <span>{{ row.expire_date | date_format('YYYY-MM-DD') }}</span>
        </template>
      </Table>
    </div>
    <div class="content mt14" v-else-if="tab === 2">
      <Timeline v-if="progressDetail.length > 0">
        <TimelineItem v-for="(item, index) in progressDetail" :key="index" :color="index === 0 ? 'red' : 'blue'">
          <div slot="dot" class="dot" :style="{ backgroundColor: index === 0 ? '#D63232' : '#D8D8D8' }"></div>
          <p :style="{ color: index === 0 ? '#D63232' : '#333' }" class="time">
            {{ item.time }} <span v-if="index === 0" class="latest ml10">最新</span>
          </p>
          <p :style="{ color: index === 0 ? '#D63232' : '#333' }" class="content">{{ item.context }}</p>
          <Divider class="mt20" />
        </TimelineItem>
      </Timeline>
      <div class="empty" v-else>暂无物流数据</div>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'logisticsProgress',
  components: {},
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    logisticsData: {
      type: Object,
      default: () => {
        return {
          pack_index: '',
          order_code: ''
        };
      }
    },

    express_no: {
      type: String,
      default: ''
    },

    progress_no: {
      type: String,
      default: ''
    },
    progress_name: {
      type: String,
      default: ''
    },
    // 是否展示发货详情
    isLogisticsDetail: {
      type: Boolean,
      default: true
    },
    progress_code: {
      type: String,
      default: ''
    },
    express_detail: {
      type: Array,
      default: () => []
    },
    // 实时订阅，需要个人信息，默认不需要
    needPersonInfo: {
      type: Boolean,
      default: false
    },
    // 个人信息
    personDetail: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      // 物流包裹详情弹窗
      columns1: [
        { title: '商品编号', key: 'sku_code', align: 'center', minWidth: 70 },
        { title: '商品', key: 'generic_name', align: 'center' },
        { title: '商品类型', key: 'prod_type_desc', align: 'center' },
        { title: '规格', key: 'prod_spec', align: 'center' },
        { title: '商品单位', key: 'pack_unit', align: 'center', width: 80 },
        { title: '商品批号', slot: 'batch_code', align: 'center', minWidth: 70 },
        { title: '生产日期', slot: 'prod_date', align: 'center' },
        { title: '过期时间', slot: 'expire_date', align: 'center' },
        { title: '发货数量', key: 'num', align: 'center' }
      ],
      list: [],
      tableLoading: false,
      progressList: [],
      progress: '',
      progressDetail: [],
      tab: 1
    };
  },
  computed: {},
  watch: {
    express_detail: {
      immediate: true,
      handler(val) {
        if (val.length > 0) {
          val.forEach((item, index) => {
            item.express_total = item.express_no + index; //保证取值唯一
          });
          console.log('-> val', val);
        }
      }
    }
  },
  created() {},
  mounted() {},
  methods: {
    init() {
      this.tab = '';
      this.progressList = [];
      this.progressDetail = [];
      this.progress = '';
    },
    /* 弹窗 */
    closeable() {
      this.$emit('input', false);
    },
    visibleChange(val) {
      if (val) {
        this.progressList = this.express_detail;

        if (this.progressList.length === 0) {
          return;
        }

        if (this.express_no) {
          // 有选中单号
          let currentKey = this.progressList.findIndex(item => item.express_no === this.express_no); // 回显选中的单号
          this.progress = this.progressList[currentKey].express_no + currentKey;
        } else {
          this.progress = this.progressList[0].express_no + 0; // 默认带出第一个
        }

        if (this.isLogisticsDetail) {
          // 是否展示物流详情
          this.changeTab(1);
        } else {
          this.changeTab(2);
        }
      } else {
        this.init();
        this.closeable();
      }
    },

    /*api - 发货详情 */ //todo 后续同步其他
    getList() {
      this.tableLoading = true;
      let params = {
        order_code: this.logisticsData.order_code,
        pack_index: this.logisticsData.pack_index
      };
      this.$api
        .getPackinfo(params)
        .then(res => {
          this.list = res.info.list;
        })
        .catch(err => {
          {};
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },

    /*api - erp物流详情 */
    getErpLogisticsInfo() {
      let params = { id: this.progress_id };
      this.$api
        .getErpLogisticsInfo(params)
        .then(res => {
          this.progressDetail = res.data;
        })
        .catch(error => {
          {};
        });
    },

    changeTab(id) {
      this.tab = id;
      if (this.tab === 1) {
        // 发货详情
        this.getList();
      }

      if (this.tab === 2) {
        // 物流进度
        let currentExpress = this.express_detail.find(item => item.express_total === this.progress);
        if (this.progress_id) {
          this.getErpLogisticsInfo();
        } else if (this.needPersonInfo) {
          // 需要后端实时订阅时，需传个人信息
          this.getErpLogisticsQuerySubscribe(currentExpress.express_no, currentExpress.express_code);
        } else {
          this.getErpLogisticsQuery(currentExpress.express_no, currentExpress.express_code);
        }
      }
    },

    getErpLogisticsQuery(express_no, express_code) {
      let params = {
        express_no,
        express_code
      };
      this.$api
        .getErpLogisticsQuery(params)
        .then(res => {
          this.progressDetail = res.data;
        })
        .catch(error => {
          {};
        });
    },

    /*api - 查询物流轨迹并订阅 */
    getErpLogisticsQuerySubscribe(express_no, express_code) {
      let params = {
        ...this.personDetail,
        express_no,
        express_code
      };
      this.$api
        .getErpLogisticsQuerySubscribe(params)
        .then(res => {
          this.progressDetail = res.data;
        })
        .catch(err => {});
    },

    changeSelect(val) {
      if (val) {
        let currentExpress = this.progressList.find(item => item.express_total === val);
        // if(!currentExpress){
        //   return
        // }
        if (this.needPersonInfo) {
          // 需要后端实时订阅时，需传个人信息
          this.getErpLogisticsQuerySubscribe(currentExpress.express_no, currentExpress.express_code);
        } else {
          this.getErpLogisticsQuery(currentExpress.express_no, currentExpress.express_code);
        }
      }
    }
  },
  filters: {}
};
</script>
<style lang="less" scoped>
::v-deep .ivu-select-item-focus {
  background-color: transparent !important;
}

::v-deep .ivu-select-item-selected {
  background-color: #f3f3f3 !important;
}

p {
  margin: 0;
}
.auto-p {
  height: 100%;
  p {
    width: auto;
  }
}
.tip-text {
  color: #ccc;
  font-size: 14px;
}
.ml10 {
  margin-left: 10px;
}
.ml30 {
  margin-left: 30px;
}
.mt20 {
  margin-top: 20px;
}
.mr30 {
  margin-right: 20px;
}

::v-deep .ivu-modal-body {
  padding: 20px 30px 0;
  height: calc(~'100% - 80px');
  overflow-y: auto;
}
::v-deep .ivu-modal {
  height: calc(~'100% - 100px') !important;
}
::v-deep .ivu-modal-content {
  height: calc(~'100% - 100px');
}

::v-deep .ivu-modal-header {
  height: 48px;
  padding: 14px 16px 0;
}

.mt14 {
  margin-top: 14px;
}

.dot {
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background-color: red;
  margin: auto;
}

.latest {
  display: inline-block;
  width: 30px;
  height: 14px;
  text-align: center;
  line-height: 14px;
  font-size: 12px;
  color: #fff;
  background-color: #d63232;
  border-radius: 2px;
}

.tabs {
  color: #999999;
  cursor: pointer;
  height: 100%;
  line-height: 24px;
  &:hover {
    color: #155bd4;
  }
}

.active {
  position: relative;
  color: #155bd4;
  //text-underline: #155BD4;
  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #155bd4;
  }
}
.text-center {
  text-align: center;
  line-height: 37px;
  padding-left: 20px;
}
</style>
