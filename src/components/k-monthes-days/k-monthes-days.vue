<template>
<div class="popover">
    <el-popover
        placement="bottom"
        v-model="visible">
        <el-card class="box-card">
           <div slot="header" class="box-header">
                 <div class="el-icon-arrow-left cursor" @click="upMonth"></div>
                 <div @click="showMonthes">{{nowMonth}}</div>
                 <div class="el-icon-arrow-right cursor" @click="nextMonth"></div>
            </div>
            <div v-if="isShowMonthes" class="show">
                <div v-for="(month,index) in monthes" :key="index" class="monthes">
                    <span v-bind:class="{ active : month === nowMonth }"
                    @click="changNowMonth(month)">{{month}}</span>
                </div>
            </div>
             <div v-if="isShowDays" class="show">
                <div v-for="(day,index) in days" :key="index" class="days">
                    <span v-bind:class="{ active : (day == nowDay && activeMonth == nowMonthArab) }"
                    @click="changNowDay(day)">{{day}}</span>
                </div>
            </div>
        </el-card>
        
        <el-input 
            class="mouth-days-value"
            slot="reference"
            :placeholder="placeholder"
            suffix-icon="el-icon-date"
            v-model="monthDaysValue">
        </el-input>
        
    </el-popover>
</div>
</template>

<script>
import dataSelect from './dataSelect.json'
export default {
  name: 'monthesDays',
  data(){
      return{
          monthDaysValue:'',
          monthes:[],
          days:[],
          nowMonth:'',
          nowDay:'',
          nowMonthArab:'',
          monthesArab:[],
          isShowMonthes:false,
          isShowDays:true,
          visible:false,

          activeMonth : '',
      }
  },
  props: {
      value: {
        type: String,
        default: () => ''
      },
      placeholder: {
        type: String,
        default: '请选择日期'
      }
  },
  watch:{
      value: {
          immediate: true,
          handler ( val ) {
            if (val) {
                let list = val.split('-')
                if ( (list.length == 2 ) && (0 <Number(list[0] < 13) && (0< Number(list[1]) < 32) )) {
                    this.monthDaysValue = val
                    this.nowMonth = this.activeMonth = Number(list[0]);
                    this.nowDay = Number(list[1]);
                    this.getNowMonth();
                    this.getDays()
                }
            }else {
                let date = new Date();
                this.nowMonth = date.getMonth() + 1;
                this.nowDay = date.getDate();
                this.getNowMonth();
                this.getDays()
                this.activeMonth = this.nowMonthArab
            }
          },
      },
      nowMonth(){
         this.getDays()
      }
  },
  mounted(){
    if ( !this.value ) {
        
    }
  },
  methods:{
    getDays () {
        let feature = '大';
         for (let index = 0; index < dataSelect.month.length; index++) {
            if(dataSelect.month[index].option == this.nowMonth){
                feature = dataSelect.month[index].feature;
                break;
            }
        }
         for (let index = 0; index < dataSelect.days.length; index++) {
            if(dataSelect.days[index].feature == feature){
                this.days = dataSelect.days[index].selectValues;
                break;
            }
        }
    },
    getmonthesArab(){
        this.monthes = [];
        for (let index = 0; index < dataSelect.month.length; index++) {
          this.monthes.push(dataSelect.month[index].option);
        }
    },
    getNowMonth(){
        for (let index = 0; index < dataSelect.month.length; index++) {
            if(dataSelect.month[index].arab == this.nowMonth || `0${dataSelect.month[index].arab}` == this.nowMonth){
                this.nowMonth = dataSelect.month[index].option;
                this.nowMonthArab = dataSelect.month[index].arab;
                break;
            }
        }
    },
    showMonthes(){
        this.isShowMonthes = !this.isShowMonthes;
        this.isShowDays = !this.isShowDays;
        this.getmonthesArab();
    },
    upMonth(){
        for (let index = 0; index < dataSelect.month.length; index++) {
            if(dataSelect.month[index].arab == this.nowMonthArab){
                if(index === 0 ){
                    index = dataSelect.month.length;
                }
                this.nowMonth = dataSelect.month[index-1].option;
                this.nowMonthArab = dataSelect.month[index-1].arab;
                break;
            }
        }
    },
    nextMonth(){
        for (let index = 0; index < dataSelect.month.length; index++) {
            if(dataSelect.month[index].arab == this.nowMonthArab){
                if(index === dataSelect.month.length-1){
                    index = -1;
                }
                this.nowMonth = dataSelect.month[index+1].option;
                this.nowMonthArab = dataSelect.month[index+1].arab;
                break;
            }
        }
    },
    changNowMonth(month){
        this.isShowMonthes = !this.isShowMonthes;
        this.isShowDays = !this.isShowDays;
        for (let index = 0; index < dataSelect.month.length; index++) {
            if(dataSelect.month[index].option === month){
                this.nowMonth = dataSelect.month[index].option;
                this.nowMonthArab = dataSelect.month[index].arab;
                break;
            }
        }
    },
    changNowDay(day){
        this.nowDay = day;
        let nowMon1,nowDay1;
        this.activeMonth = this.nowMonthArab
        if(this.nowMonthArab > 0 && this.nowMonthArab < 10){
            nowMon1 = '0'+ this.nowMonthArab;
        }else{
            nowMon1 = this.nowMonthArab;
        }
         if(this.nowDay > 0 && this.nowDay < 10){
            nowDay1 = '0'+ this.nowDay;
        }else{
            nowDay1 = this.nowDay;
        }
        this.monthDaysValue = nowMon1 +'-' + nowDay1;
        this.visible = false;
        this.$emit('getValue',this.monthDaysValue);
    }
  }
}
</script>
<style>
 .el-popover{
    padding:0;
  }
</style>
<style lang="less"  scoped>
  .box-card {
    width: 300px
  }
  .mouth-days-value {
      /* width: 200px; */
      width: 100%;
  }
  .box-header{
      text-align: center;
      display: flex;
      justify-content: space-between;
  }
  .show{
      display: flex;
      flex-wrap: wrap;
  }
  .monthes{
      width: 25%;
      text-align: center;
      margin-top: 4%;
      margin-bottom: 4%;
  }
  .days{
        width: 13%;
        text-align: center;
        margin-top: 2%;
        margin-bottom: 2%;
        margin-left: 1%;
        height: 34px;
        span {
            // width: 100%;
            display: inline-block;
            width: 30px;
            height: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        &:hover {
            background: #e1f0fe;
            color: #333;
            cursor: pointer;
        }
        .active {
            background: #115bd4;
            color: #fff;
            border-radius: 4px;
            &:after {
                content: ' ';
                height: 6px;
                width: 6px;
                display: inline-block;
                border-radius: 50%;
                background: #fff;
                color: #115bd4;
                position: relative;
                right: -1px;
                top: -8px;
            }
        }
  }
  .active{
      color:#409EFF;
  }

  .cursor {
      cursor: pointer;
  }
</style>>
