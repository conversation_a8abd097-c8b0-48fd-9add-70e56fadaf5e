<template>
  <div class="message-container">
    <div class="tips-header" v-if="!uid">
      <svg-icon name="tips"></svg-icon>
      <span class="tips-text">用户使用临时号注册时，无法接收到短信通知 </span>
    </div>
    <div :class="['message-wrapper', uid ? 'message-wrapper-user' : '']">
      <div class="message-item" v-for="item in messageList" :key="item.id">
        <h3 class="message-title">
          {{ item.name }}
        </h3>
        <div :class="['message-content']">
          <div class="message-info">
            <h4 class="sub-title">{{ item.title }}</h4>
            <div class="message-desc">
              {{ item.desc }}
            </div>
          </div>
          <div class="checkbox">
            <Checkbox :value="item.checked" :disabled="followSys" @click.prevent.native="checkNotice(item)">
              <span class="checkbox-text ml-4">短信通知</span>
            </Checkbox>
          </div>
        </div>
      </div>
    </div>
    <message-notice-modal
      v-model="noticeVisible"
      :msg-item="currentMsgItem"
      @refreshMsgSetting="getMsgSetting"
      :uid="uid"
    ></message-notice-modal>
  </div>
</template>

<script>
import MessageNoticeModal from './components/message-notice-modal.vue';

export default {
  name: 'SettingMessage',
  props: {
    uid: {
      type: String,
      default: '',
    },
    followSys: {
      type: Boolean,
      default: false,
    },
    messageList: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    MessageNoticeModal,
  },
  data() {
    return {
      noticeVisible: false,
      currentMsgItem: {},
    };
  },
  mounted() {},
  methods: {
    checkNotice(item) {
      if(this.followSys)return
      this.currentMsgItem = item;
      this.noticeVisible = true;
    },
    getMsgSetting() {
      this.$emit('refreshMsgSetting');
    },
  },
};
</script>

<style lang="less" scoped>
.message-container {
  .tips-header {
    padding: 13px 16px;
    background: #fff8eb;
    border-radius: 4px;

    .tips-text {
      font-size: 14px;
      color: #606266;
      line-height: 22px;
      margin-left: 10px;
    }
  }

  .message-wrapper {
    padding: 24px 52px;

    .message-item {
      margin-bottom: 24px;

      &:last-child {
        .message-content {
          border-bottom: none;
        }
      }

      .message-title {
        font-weight: 600;
        font-size: 16px;
        color: #303133;
        line-height: 24px;
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        &:before {
          content: '';
          display: inline-block;
          width: 3px;
          height: 16px;
          background: #3088ff;
          margin-right: 12px;
          vertical-align: baseline;
        }
      }

      .message-content {
        border-bottom: 1px solid #ebedf0;
        margin: 0 31px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 24px;

        .message-info {
          .sub-title {
            font-weight: 500;
            font-size: 15px;
            color: #303133;
            line-height: 22px;
            margin-bottom: 8px;
          }

          .message-desc {
            color: #a8abb2;
            line-height: 18px;
          }
        }
      }
    }
  }

  .message-wrapper-user {
    padding: 16px;
    background: #f9fafb;
    border-radius: 4px;

    .message-item {
      margin-bottom: 16px;

      .message-content {
        padding-bottom: 16px;
      }
    }
  }
}
</style>
