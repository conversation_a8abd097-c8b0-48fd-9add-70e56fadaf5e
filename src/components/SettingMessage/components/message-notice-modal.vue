<template>
  <Modal
    :value="visible"
    :title="getCurrentTab.name"
    footer-hide
    width="720"
    :mask-closable="false"
    @on-visible-change="onVisibleChange"
  >
    <div class="setting-wrapper">
      <div class="setting-item">
        <div class="n-title">通知时机</div>
        <div class="setting-content">
          <div v-if="getCurrentTab.daySelect">
            {{ getCurrentTab.frontNotice }}
            <Select v-model="selectDay" placeholder="通知时机" style="width: 100px" @on-change="changeDay">
              <Option v-for="item in dayOptions" :key="item.value" :value="item.value" :label="item.label"></Option>
            </Select>
            {{ getCurrentTab.endNotice }}
          </div>
          <div v-else>{{ getCurrentTab.notice }}</div>
        </div>
      </div>
      <div class="setting-item">
        <div class="n-title">短信通知</div>
        <div class="setting-content">
          是否开启短信通知
          <i-switch
            class="ml-16"
            v-model="openMessage"
            :before-change="handleOpenMessageChange"
            size="small"
            :loading="saveLoading"
          />
        </div>
      </div>
      <div class="msg-tabs">
        <Tabs :value="activeTab">
          <TabPane :label="item.name" :name="item.name" v-for="item in getCurrentTab.tabs" :key="item.id">
            <div class="notice-example" v-if="Array.isArray(item.desc) && item.desc.length">
              <div class="example-item" v-for="(desc, index) in item.desc" :key="'desc' + index">
                <div class="notice-title" v-show="!desc.isRst">{{ desc.isRst }}</div>
                <div class="notice-content flex-1 flex flex-item-align" v-show="!desc.isRst">
                  <div>{{ desc.desc }}</div>
                  <div class="divider"></div>
                </div>
              </div>
            </div>
            <div class="notice-example single-notice" v-else>
              <div class="notice-content">{{ item.desc }}</div>
            </div>
          </TabPane>
        </Tabs>
      </div>
    </div>
  </Modal>
</template>
<script>
import { isRstClinic } from '@/libs/runtime';
export default {
  name: 'message-notice-modal',
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    msgItem: {
      type: Object,
      default: () => ({
        type: '',
      }),
    },
    visible: {
      type: Boolean,
      default: false,
    },
    uid: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      openMessage: false,
      selectDay: '',
      dayOptions: [
        { label: '3天', value: '3' },
        { label: '7天', value: '7' },
        { label: '15天', value: '15' },
      ],
      tabsEnum: {
        1: {
          name: '储值余额变动通知',
          notice: '会员储值卡充值、储值余额消费、储值余额退款、消费订单退款后储值余额回滚时，将给用户发短信通知',
          tabs: [
            {
              id: 1,
              name: '充值',
              desc: `【榕树家】储值余额充值：你于2019-07-30 12:30:21 充值1000.00元${
                isRstClinic() ? '' : '，获赠金50元'
              }，当前余额2000.35元。发起门店：XXXX`,
            },
            {
              id: 2,
              name: '消费',
              desc: [
                {
                  title: '本人消费时：',
                  desc: `【榕树家】储值余额消费：你于2019-07-30 12:30:21 消费1000.00元${
                    isRstClinic() ? '，其中实充扣款1000元' : '，其中实充扣款899.99元，赠金扣款101.01元'
                  }，当前余额2000.35元。发起门店：XXXX`,
                },
                {
                  title: '家庭组成员消费时：',
                  isRst: isRstClinic(),
                  desc: `【榕树家】储值余额消费：你的家庭组成员“张三”于2019-07-30 12:30:21 消费1000.00元${
                    isRstClinic() ? '，其中实充扣款1000元' : '，其中实充扣款899.99元，赠金扣款101.01元'
                  }，当前余额2000.35元。发起门店：XXXX`,
                },
              ],
            },
            {
              id: 3,
              name: '退款',
              desc: `【榕树家】储值余额退款：你于2019-07-30 12:30:21 退款1000.00元${
                isRstClinic() ? '，其中实充扣除1000元' : '，其中实充扣除899.99元，赠金扣除101.01元'
              }，扣除后余额共计剩余333元。退款方式：${
                isRstClinic() ? '原路返回' : '现金'
              }，实际退还金额888.35元。发起门店：XXXX`,
            },
            {
              id: 4,
              name: '余额回滚',
              desc: [
                {
                  title: '本人消费订单余额回滚',
                  desc: '【榕树家】储值余额回滚：你于2019-07-30 12:30:21 消费的订单已发生退款，300.00元已原路退还至你的储值余额，当前余额888.35元。发起门店：XXXX',
                },
                {
                  title: '家庭组成员消费订单余额回滚',
                  isRst: isRstClinic(),
                  desc: '【榕树家】储值余额消费:你的家庭组成员“张三”于2019-07-30 12:30:21 消费的订单已发生退款，300.00元已原路退还至你的储值余额，当前余额888.35元。发起门店:XXXX',
                },
              ],
            },
          ],
        },
        2: {
          name: '卡券核销/到期提醒',
          frontNotice: '服务卡券核销、服务卡券到期前',
          endNotice: '自动提醒用户到店消费',
          daySelect: true,
          tabs: [
            {
              id: 1,
              name: '核销',
              desc: '【榕树家】卡券核销：你于2019-07-30 12:30:21 完成1次服务「xxx」核销，还剩3次。发起门店：XXXX',
            },
            {
              id: 2,
              name: '到期提醒',
              isMultiDesc: true,
              desc: [
                {
                  title: '当天只有1张卡券到期：',
                  desc: '【榕树家】卡券到期提醒：你的「卡券名」将于2019-07-30 12:30:21 到期，卡券共x次，剩余Y次，请及时到店使用。发起门店：XXXX',
                },
                {
                  title: '当天有超过1张卡券到期：',
                  desc: '【榕树家】卡券到期提醒：你有x张卡券将于2019-07-30到期，请及时到店使用。发起门店：XXXX',
                },
              ],
            },
          ],
        },
        3: {
          name: '通兑券/到期提醒',
          frontNotice: '服务卡券兑换、通兑券到期前',
          endNotice: '自动提醒用户到店消费',
          daySelect: true,
          tabs: [
            {
              id: 1,
              name: '兑换',
              desc: '【榕树家】通兑券兑换：你于2019-07-30 12:30:21 完成1次通兑券「xxx」核销，还剩3次。发起门店：XXXX',
            },
            {
              id: 2,
              name: '到期提醒',
              isMultiDesc: true,
              desc: [
                {
                  title: '当天只有1张通兑券到期：',
                  desc: '【榕树家】通兑券到期提醒：你的「通兑券券名」将于2019-07-30 12:30:21 到期，通兑券共x次，剩余Y次，请及时到店使用。发起门店：XXXX',
                },
                {
                  title: '当天有超过1张通兑券到期：',
                  desc: '【榕树家】通兑券到期提醒：你有x张通兑券将于2019-07-30到期，请及时到店使用。发起门店：XXXX',
                },
              ],
            },
          ],
        },
        4: {
          name: '家庭组加入/退出通知',
          notice: '用户加入或退出某一个家庭组时，将给该用户发确认通知',
          tabs: [
            {
              id: 1,
              name: '加入',
              desc: '【榕树家】加入家庭组：你已加入由张三创建的家庭组。发起门店：XXXX',
            },
            {
              id: 2,
              name: '退出',
              desc: '【榕树家】退出家庭组：你已退出由张三创建的家庭组。发起门店：XXXX',
            },
          ],
        },
      },
      activeTab: '',
      saveLoading: false,
    };
  },
  watch: {},
  methods: {
    show() {
      this.visible = true;
    },
    onVisibleChange(v) {
      if (v) {
        this.activeTab = this.getCurrentTab.tabs[0].name;
        this.init();
      } else {
        this.$emit('changeVisible', false);
        this.activeTab = '';
      }
    },
    init() {
      console.log(this.msgItem);
      this.selectDay = this.msgItem.day;
      this.openMessage = this.msgItem.sms.status === '1';
    },
    changeDay() {
      if (!this.openMessage) {
        return;
      }
      this.handleOpenMessageChange(true);
    },
    handleOpenMessageChange(isChangeDate = false) {
      return new Promise(resolve => {
        if (this.getCurrentTab.daySelect && !this.selectDay) {
          this.$Message.error('请选择到期时限天数');
          return;
        }
        this.saveLoading = true;
        const api = this.uid ? 'saveUserMsgSetting' : 'saveMsgSetting';
        const params = {
          type: this.msgItem.type,
          day: this.selectDay,
          id: this.msgItem.id,
          status: !this.openMessage ? '1' : '0',
        };
        isChangeDate && (params.status = '1');
        this.uid && (params.uid = this.uid);
        this.$api[api](params)
          .then(res => {
            this.$Message.success('设置成功');
            this.$emit('refreshMsgSetting');
            resolve(true);
          })
          .finally(() => {
            this.saveLoading = false;
          });
      });
    },
  },
  computed: {
    getCurrentTab() {
      return this.msgItem?.type ? this.tabsEnum[this.msgItem?.type] : {};
    },
  },
};
</script>

<style scoped lang="less">
.setting-wrapper {
  .setting-item {
    margin-bottom: 20px;

    .n-title {
      font-size: 13px;
      color: #909399;
      line-height: 18px;
      margin-bottom: 12px;
    }

    .setting-content {
      font-size: 14px;
      color: #303133;
      line-height: 22px;
    }
  }

  .msg-tabs {
    .notice-example {
      margin-top: 6px;
      display: flex;
      flex-wrap: wrap;
      align-items: stretch;

      .example-item {
        flex: 1;
        display: flex;
        flex-direction: column;
      }

      .notice-title {
        font-weight: 500;
        font-size: 14px;
        color: #303133;
        line-height: 20px;
        margin-bottom: 12px;
      }

      .notice-content {
        background: #f5f6f8;
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        color: #606266;
        line-height: 22px;
        margin-right: 45px;
        display: flex;
      }
    }

    .single-notice {
      width: 50%;
    }

    padding-bottom: 20px;
  }
}
</style>
