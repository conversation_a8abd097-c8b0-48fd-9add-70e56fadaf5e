<template>
  <Modal
    ref="customModal"
    :value="value"
    width="520px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <p class="tip">以同步形式创建用户的过程中，不允许修改用户基础资料</p>
      <Form ref="formData" :model="formData" :label-width="80" :label-colon="true" :rules="ruleValidate">
        <FormItem label="用户手机" prop="mobile">
          <Input v-model="formData.mobile" maxlength="11" placeholder="请输入用户手机号" disabled> </Input>
        </FormItem>
        <FormItem label="用户姓名" prop="real_name">
          <Input
            v-model="formData.real_name"
            placeholder="请输入用户姓名"
            maxlength="16"
            :disabled="!!info?.profile?.real_name"
            show-word-limit
          ></Input>
        </FormItem>
        <FormItem label="用户性别" prop="sex">
          <Select v-model="formData.sex" placeholder="请选择用户性别" :disabled="!!Number(info?.profile?.gender)">
            <Option :value="sex_item.value" v-for="(sex_item, sex_index) in sexList" :key="sex_index + 'sex'"
              >{{ sex_item.label }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="出生日期" prop="birthday">
          <DatePicker
            :disabled="!!info?.profile?.birthday"
            :options="disabledTime"
            :start-date="new Date('1972-01-01')"
            type="date"
            placeholder="请选择出生日期"
            @on-change="changeDate"
            :value="formData.birthday"
            style="width: 100%"
          ></DatePicker>
        </FormItem>
        <FormItem label="用户年龄">
          <Input disabled v-model="userAge" placeholder="根据出生日期自动生成"></Input>
        </FormItem>

        <FormItem label="用户来源" prop="source">
          <Select v-model="formData.source" placeholder="请选择用户来源">
            <Option
              :value="source_item.id"
              v-for="(source_item, source_index) in sourceList"
              :key="source_index + 'source'"
              >{{ source_item.desc }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="用户等级" prop="offline_level">
          <Select v-model="formData.offline_level" placeholder="请选择用户等级">
            <Option :value="level" v-for="(level, source_index) in levelList" :key="source_index + 'level'"
              >{{ level }}
            </Option>
          </Select>
        </FormItem>

        <FormItem label="验证码" prop="authcode">
          <div class="flex">
            <Input v-model="formData.authcode" :maxlength="6" placeholder="请输入验证码"> </Input>
            <vac
              ref="vac"
              :auto-start="false"
              :left-time="60000"
              @finish="onCountDownFinish"
              class='custom-vac'
              style="width: 92px; text-align: center;"
            >
              <Button disabled slot="process" slot-scope="{ timeObj }" style="width: 100%">
                {{ timeObj.ceil.s }} 重新获取
              </Button>
              <Button slot="before" type="primary" :disabled="!formData.mobile" @click="sendAuthCode"
                >获取验证码
              </Button>
              <Button slot="finish" type="primary" :disabled="!formData.mobile" @click="sendAuthCode"
                >获取验证码
              </Button>
            </vac>
          </div>
        </FormItem>
      </Form>
    </div>
    <div slot="footer">
      <Button @click="closeModal">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="createNewConsumer">确认同步并创建</Button>
    </div>
  </Modal>
</template>

<script>
import S from '@/libs/util';
export default {
  name: 'SynAccountModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '同步账号并创建到本店'
    },
    levelList: {
      type: Array,
      default: () => []
    },
    sourceList: {
      type: Array,
      default: () => []
    },
    info: {
      type: Object,
      default: () => {}
    },
    is_exist: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      sexList: [
        { label: '男', value: '1' },
        { label: '女', value: '2' }
      ],
      confirmLoading: false,
      disabledTime: {
        disabledDate(date) {
          return date && date.valueOf() > Date.now();
        }
      },
      formData: {
        real_name: '',
        mobile: '',
        authcode: '',
        birthday: '',
        sex: '',
        source: '',
        offline_level: ''
      }, // 创建用户的数据
      ruleValidate: {
        real_name: [{ required: true, message: '请输入用户姓名', trigger: 'change' }],
        sex: [{ required: true, message: '请选择用户性别', trigger: 'change' }],
        birthday: [{ required: true, message: '请选择用户出生日期', trigger: 'change' }],
        mobile: [
          { required: true, message: '请输入用户手机号', trigger: 'change' },
          {
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (!this.regRole(value)) {
                callback(new Error('请输入正确的手机号'));
              } else {
                callback();
              }
            }
          }
        ],
        authcode: [{ required: true, message: '请输入验证码', trigger: 'change' }]
      },
      countdowning: false,
      userAge: ''
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeDate(date) {
      this.formData.birthday = date;
      let age = S.formatDuration(date);
      let index = age?.indexOf('岁');
      this.userAge = age?.substring(0, index + 1);
    },
    // 创建新用户
    createNewConsumer() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          this.confirmLoading = true;
          let params = {
            ...this.formData,
            is_exist: this.is_exist
          };
          this.$api
            .createUser(params)
            .then(
              res => {
                if (res.is_change === '1') {
                  this.checkUserIsExist();
                } else {
                  this.$Message.success('创建用户成功');
                  this.$emit('success', res.user);
                  this.closeModal();
                }
              },
              err => {}
            )
            .finally(() => {
              this.confirmLoading = false;
            });
        }
      });
    },
    // 检测用户中心的的信息是否发生了变更
    checkUserIsExist() {
      this.$Modal.confirm({
        title: '更新数据',
        loading: true,
        okText: '获取更新数据',
        content: '<p>当前要同步的账号信息已发生变更，你需要获取最新的的信息然后再同步</p>',
        onOk: () => {
          this.getUserGetucuser();
        }
      });
    },
    sendAuthCode() {
      if (!this.regRole(this.formData.mobile)) {
        this.$Message.error('请输入正确的手机号');
        return;
      }
      let params = {
        mobile: this.formData.mobile
      };
      this.$api
        .sendAuthCode(params)
        .then(res => {
          this.$Message.success('发送成功');
          this.$refs.vac.startCountdown(true);
        })
        .catch(err => {});
    },
    // 拉取用户信息
    getUserGetucuser() {
      let params = {
        mobile: this.formData.mobile
      };
      this.$api
        .getUserGetucuser(params)
        .then(
          res => {
            let uc_user = res.uc_user;
            this.formData.mobile = uc_user.mobile;
            this.formData.birthday = uc_user.birthday;
            this.formData.real_name = uc_user.real_name;
            this.formData.sex = uc_user.sex;
            this.$Modal.remove();
            this.$Message.success('数据已更新');
          },
          err => {}
        )
        .finally(() => this.$Modal.remove());
    },
    onCountDownFinish() {
      this.countdowning = false;
    },
    /**
     * @description: 弹窗状态检测
     * @params  { Boolean } visible true: 弹窗打开 false:弹窗关闭
     * */
    changeVisible(visible) {
      if (visible) {
        this.synData();
      } else {
        this.closeModal();
      }
    },

    // 手机号校验
    regRole(tel) {
      let flag;
      let reg = /^1[3456789]\d{9}$/;
      flag = reg.test(tel);
      return flag;
    },

    synData() {
      let copy_info = this.$lodash.cloneDeep(this.info || {});
      const profile = copy_info?.profile || {};
      this.formData.mobile = copy_info.mobile;
      this.formData.real_name = profile.real_name;
      this.formData.sex = Number(profile.gender) == 0 ? '' : profile.gender;
      this.formData.birthday = profile.birthday;
      this.changeDate(profile.birthday);
    },

    clearData() {
      this.$refs.formData.resetFields();
      this.$refs.vac.finishCountdown();
      this.userAge = '';
    },

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    }
  }
};
</script>

<style scoped lang="less">
.content {
  .tip {
    color: #999;
  }
}
::v-deep .custom-vac {
  margin-top: -1px;
  button {
    height: 32px;
  }
}
</style>

<style scoped lang="less">
::v-deep .ivu-modal-body {
  max-height: 500px;
  min-height: 150px;
  overflow-y: auto;
}
</style>
