<template>
  <Page
    :total="p_total"
    :page-size="p_pageSize"
    :current="p_current"
    :class-name="className"
    :styles="styles"
    :page-size-opts="pageSizeOpts"
    :size="sizeType"
    @on-change="onPageChange"
    @on-page-size-change="onPageSizeChange"
    show-sizer
    show-elevator
    show-total
    transfer
  >
  </Page>
</template>

<script>
import S from '@/libs/util';
import ConfirmModal from '../confirmModal/confirmModal.vue';

export default {
  name: 'k-page',
  components: { ConfirmModal },
  props: {
    current: {
      type: [Number, String],
      default: 1,
    },
    total: {
      type: [Number, String],
      default: 0,
    },
    pageSize: {
      type: [Number, String],
      default: 20,
    },
    pageSizeOpts: {
      type: Array,
      default() {
        return [10, 20, 50, 80, 100, 200];
      },
    },
    className: {
      type: String,
    },
    styles: {
      type: Object,
    },
    sizeType: {
      type: String,
      default: 'default',
    },
  },
  data() {
    return {
      p_current: 0,
      p_pageSize: 0,
      p_total: 0,
    };
  },

  methods: {
    onPageChange: function (page) {
      //解决表格滚动条分页问题
      // setTimeout(() => {
      //   //这里要延迟下滚动
      //   document.getElementsByClassName('ivu-table-body')[0].scroll(0, 0)
      // }, 400)
      this.p_current = page;
      this.$emit('on-change', page, this.p_pageSize);
    },
    onPageSizeChange: function (pageSize) {
      this.p_pageSize = pageSize;
      if (this.p_current == 1) {
        this.$emit('on-change', this.p_current, pageSize);
      }
    },
  },

  watch: {
    current: {
      immediate: true,
      handler: function (val) {
        if (typeof val == 'string') val = Number(val);
        this.p_current = val;
      },
    },
    total: {
      immediate: true,
      handler: function (val) {
        if (typeof val == 'string') val = Number(val);
        this.p_total = val;
      },
    },
    pageSize: {
      immediate: true,
      handler: function (val) {
        if (typeof val == 'string') val = Number(val);
        this.p_pageSize = val;
      },
    },
  },
};
</script>

<style lang="less">
.ivu-page-item:hover {
  color: #fff;
}
.ivu-page-item-active {
  background-color: #155bd4;
}
.ivu-page-item-active a,
.ivu-page-item-active:hover a {
  color: #fff;
}
</style>
