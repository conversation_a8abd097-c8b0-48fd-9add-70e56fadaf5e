<template>
  <Modal
    :value="value"
    :mask-closable="false"
    class-name="vertical-center-modal custom-pay-tip-modal"
    :title="title"
    width="1080px"
    :closable="false"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="top-box">
        <div class="item">
          <div class="label">当前用户储值余额：</div>
          <div class="money">¥{{ Number(info.balance || 0).toFixed(2) }}</div>
        </div>
        <div class="item ml20">
          <div class="label">可退金额：</div>
          <div class="money">¥{{ Number(info.can_refund_amount || 0).toFixed(2) }}</div>
        </div>
      </div>
      <Table :columns="columns" :data="list" height="350">
        <template v-slot:total_fee="{ row, index }">
          <span>¥{{ Number(row.total_fee || 0).toFixed(2) }}</span>
        </template>

        <template v-slot:account_fee="{ row, index }">
          <span>¥{{ Number(row.account_fee || 0).toFixed(2) }}</span>
        </template>

        <template v-slot:refund_fee="{ row, index }">
          <span>¥{{ Number(row.refund_fee || 0).toFixed(2) }}</span>
        </template>

        <template v-slot:custom_refund_fee="{ row, index }">
          <InputNumber
            style="width: 100%"
            v-model="list[index].custom_refund_fee"
            :active-change="false"
            @on-change="v => changeRefundNum(v, index)"
            :min="0"
            :max="+row.can_refund_fee"
            :precision="2"
            :disabled="+row.can_refund_fee == 0 || row.refund_nums == info.max_refund_nums"
            placeholder="请输入"
          />
        </template>
      </Table>
      <k-widget label="退款方式" required :label-width="70" class="mt-40 mb-20" placeholder="请选择退款方式">
        <Select v-model="formData.method" style="width: 280px">
          <Option
            :value="item.kw"
            :label="item.refund_desc"
            :key="item.kw"
            v-for="item in offlineRefundPayPlatformDesc"
          ></Option>
        </Select>
      </k-widget>
      <k-widget label="退款原因" :label-width="70">
        <Input
          v-model="formData.description"
          type="textarea"
          style="width: 80%; max-width: 80%"
          placeholder="请输入退款原因"
          :autosize="{
            minRows: 4,
            maxRows: 4,
          }"
        />
      </k-widget>
    </div>
    <div slot="footer">
      <Button @click="cancelHandler">取消</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirmHandler">确认退款</Button>
    </div>
  </Modal>
</template>

<script>
import { $operator } from '@/libs/operation';
export default {
  name: 'RefundModal',
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '储值退款',
    },
    out_trade_no: {
      type: String,
      default: '',
    },
    // 用户id
    uid: {
      type: String,
      default: '',
    },
    // 订单id
    orderId: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      confirmLoading: false,
      offlineRefundPayPlatformDesc: {
        refund_old_way: { refund_desc: '原路返回', kw: 'refund_old_way' },
      },
      formData: {
        method: 'refund_old_way',
        description: '',
      },
      columns: [
        { title: '订单编号', key: 'out_trade_no', align: 'center', minWidth: 140 },
        { title: '外部流水号', key: 'out_transaction_no', align: 'center', minWidth: 140 },
        { title: '支付时间', key: 'finished_time_format', align: 'center', minWidth: 140 },
        { title: '支付方式', key: 'pay_type_desc', align: 'center', minWidth: 100 },
        { title: '实充金额', slot: 'total_fee', align: 'center', minWidth: 100 },
        // { title: '储值金额', slot: 'account_fee', align: 'center', minWidth: 100 },
        { title: '已退金额', slot: 'refund_fee', align: 'center', minWidth: 100 },
        { title: '退款金额', slot: 'custom_refund_fee', align: 'center', minWidth: 100 },
      ],
      list: [],
      info: {},
    };
  },
  watch: {},
  computed: {},
  created() {},
  methods: {
    init() {
      this.getAccountRefundInfo();
      this.getROrderList();
    },
    changeRefundNum(v, index) {
      const max = this.list[index].can_refund_fee || 0;
      this.list[index].custom_refund_fee = v >= Number(max) ? +max : +v;
    },

    changeVisible(visible) {
      if (visible) {
        this.init();
      } else {
        this.list = [];
        this.formData = {
          method: 'refund_old_way',
          description: '',
        };
      }
    },

    confirmHandler() {
      if (!this.formData.method) {
        this.$Message.error('请选择退款方式');
        return;
      }
      let flag = this.list.every(item => !item.custom_refund_fee);
      if (flag) {
        this.$Message.error('请输入退款金额');
        return;
      }

      if (this.getLimitOrderList().length) {
        this.getTip();
      } else {
        this.getReRefundApplybyorder();
      }
    },
    cancelHandler() {
      this.closeModal();
    },
    closeModal() {
      this.$emit('input', false);
    },

    // 退款时，获取订单次数达到上限制的订单
    getLimitOrderList() {
      let list = [];
      this.list.forEach(item => {
        if (Number(item.custom_refund_fee) > 0 && +item.refund_nums == +this.info.max_refund_nums - 1) {
          list.push(item.out_trade_no);
        }
      });
      return list || [];
    },
    // 超出五次退款提示
    getTip() {
      this.$Modal.confirm({
        okText: '确认',
        onOk: () => {
          this.getReRefundApplybyorder();
        },
        render: h => {
          return h('div', [
            h('h2', '提示'),
            h(
              'div',
              { style: { margin: '10px 0px' } },
              `单笔储值订单【${this.getLimitOrderList().join('、')}】最多进行${this.info?.max_refund_nums}次退款，超出${
                this.info?.max_refund_nums
              }次后将无法继续退款，请确认退款金额`
            ),
          ]);
        },
      });
    },

    getROrderList() {
      let params = {
        out_trade_no: this.out_trade_no,
        uid: this.uid,
        wallet_type: 'USER_RECHARGE_YZT',
      };
      this.$api.getROrderList(params).then(res => {
        this.list = (res.orders || []).map(item => ({
          ...item,
          custom_refund_fee: null,
        }));
      });
    },

    // 获取储值订单退款信息
    getAccountRefundInfo() {
      let params = {
        uid: this.uid,
        order_id: this.orderId,
      };
      this.$api.getAccountRefundInfo(params).then(res => {
        this.info = res;
      });
    },

    handlerRefundList() {
      let list = [];
      this.list.forEach(item => {
        if (+item.custom_refund_fee) {
          list.push({
            order_id: item.id,
            amount: item.custom_refund_fee,
          });
        }
      });
      return list || [];
    },

    // 根据充值记录发起退款
    getReRefundApplybyorder() {
      this.confirmLoading = true;
      let params = {
        uid: this.uid,
        ...this.formData,
        refund_list: this.handlerRefundList(),
      };
      this.$api
        .getReRefundApplybyorder(params)
        .then(res => {
          this.$emit('success');
          this.closeModal();
        })
        .finally(() => (this.confirmLoading = false));
    },
  },
};
</script>
<style lang="less" scoped>
.top-box {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  .item {
    display: flex;
    align-items: center;
    .label {
      color: #333;
    }
    .money {
      color: red;
    }
  }
}
.ml20 {
  margin-left: 20px;
}
</style>
<style lang="less">
.ivu-modal-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  .ivu-modal {
    top: 0px;
  }
}
</style>
