<template>
  <Modal
    :value="visible"
    title="选择优惠券"
    :mask-closable="false"
    :width="1080"
    class-name="vertical-center-modal"
    @on-visible-change="onVisibleChange"
  >
    <div style="position: relative">
      <Button to="/store/coupon/list" target="_blank">优惠券管理</Button>
      <Dvd />
      <Dvd />
      <a @click="onRefresh">刷新</a>
      <div style="position: absolute; right: 0px; top: 0px" class="flex">
        <!-- <Select
          v-model="queryFormData.status"
          placeholder="请选择状态"
          style="width: 150px"
          class="mr10"
          @on-change="getList"
          clearable
        >
          <Option v-for="item in statusDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
        </Select>

        <Select
          v-model="queryFormData.type"
          placeholder="请选择类型"
          style="width: 150px"
          class="mr10"
          @on-change="getList"
          clearable
        >
          <Option v-for="item in typeDesc" :value="item.id" :key="item.id">{{
            item.desc
          }}</Option>
        </Select> -->
        <Input
          v-model="queryFormData.keyword"
          placeholder="搜索优惠券名称或ID"
          clearable
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          style="width: 180px"
        >
          <Icon type="ios-search" slot="suffix" />
        </Input>
      </div>
    </div>
    <div class="block_10"></div>
    <div>
      <Table
        ref="selection"
        @on-select="onSelect"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-select-all-cancel="onSelectAllCancel"
        :columns="tableCols"
        :data="list"
        :loading="tableLoading"
        height="400"
      >
      </Table>

      <div class="block_20"></div>

      <KPage
        v-if="total > 0"
        :total="total"
        :page-size.sync="queryFormData.pageSize"
        :page-size-opts="[5, 10, 20]"
        :current.sync="queryFormData.page"
        @on-change="onPageChange"
        style="text-align: center"
      />
    </div>
    <div slot="footer">
      <div v-if="Object.keys(selected_items).length > 0" style="display: inline-block" class="lr15 text-muted">
        已选: 优惠券(<span class="text-error">{{ Object.keys(selected_items).length }}</span
        >)
      </div>
      <Button @click="closeModal">取消</Button>
      <Button type="primary" @click="onConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
let init_query_from_data = {
  page: 1,
  pageSize: 10,
  keyword: '',
  status: '',
  type: ''
};

export default {
  name: 'k-coupon-select-multiple',
  model: {
    prop: 'visible',
    event: 'changeVisible'
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectedItems: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      queryFormData: { ...init_query_from_data },
      modalValue: false,
      tableCols: [
        { type: 'selection', width: 60 },
        { title: 'ID', key: 'id', width: 50, align: 'center' },
        { title: '卡券名称', key: 'name', align: 'center' },
        { title: '优惠金额', key: 'discount_amount_desc', align: 'center' },
        // { title: '卡券类型', key: 'type_desc', align: 'center', width: 100 },
        { title: '适用范围', key: 'range_desc', align: 'center' },
        { title: '限制类型', key: 'limit_type_desc', align: 'center',width: 80 },
        { title: '卡券有效期', key: 'period_desc', align: 'center' },
        // { title: '领取方式', key: 'status' },
        // { title: '发放规则', key: 'status' },
        // { title: '领取上限', key: 'status' },
        { title: '卡券库存', key: 'stock_num', align: 'center', width: 80 },
        { title: '卡券状态', key: 'status_desc', align: 'center', width: 80 }
      ],
      tableLoading: false,
      list: [],
      total: 0,
      selected_items: {},
      statusDesc: [],
      typeDesc: []
    };
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.getList();
    },

    onPageChange: function (page, pageSize) {
      // console.log(page,pageSize,'asd')
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.getList();
    },

    onRefresh: function () {
      this.getList();
    },

    onSelect: function (selection, row) {
      this.$set(this.selected_items, row.id, row);
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        this.$set(this.selected_items, item.id, item);
      });
    },

    onSelectCancel: function (selection, row) {
      this.$delete(this.selected_items, row.id);
    },

    onSelectAllCancel: function (selection) {
      for (let k in this.list) {
        this.$delete(this.selected_items, this.list[k].id);
      }
    },

    onConfirm: function () {
      let items = [];
      for (let key in this.selected_items) {
        items.push(this.selected_items[key]);
      }
      this.$emit('on-selected', items);
      this.selected_items = {};
    },

    getList() {
      this.tableLoading = true;

      this.$api
        .getUsableCouponBatch(this.queryFormData)
        .then(data => {
          this.list = this.handler(data.list);
          this.total = data.total;

          this.tableLoading = false;
        })
        .catch(error => {
          {};
        });
    },

    handler: function (list) {
      for (let k in list) {
        for (let j in this.selected_items) {
          if (list[k].id == this.selected_items[j].id) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }
      }
      return list;
    },

    clearQuery: function () {
      this.queryFormData = { ...init_query_from_data };
      this.queryFormData.page = 1;
      this.list = [];
      this.total = 0;
    },
    onVisibleChange(v) {
      if (!v) {
        this.closeModal();
      }
    },
    closeModal() {
      this.$emit('changeVisible', false);
    },
    // getCouponOption() {
    //   this.$api
    //     .getCouponOption()
    //     .then(data => {
    //       console.log('%c [ data ]-217', 'font-size:13px; background:#d215f6; color:#ff59ff;', data);
    //       this.statusDesc = this.descToArrHandle(data.status);
    //       this.typeDesc = this.descToArrHandle(data.type);
    //     })
    //     .catch(error => {
    //       {};
    //     });
    // },
    descToArrHandle(obj) {
      let arr = [];
      let kArr = Object.keys(obj);
      kArr.map((item, i) => {
        arr.push({
          id: item,
          desc: obj[item].desc || '',
          kw: obj[item].kw || '',
          ...obj[item]
        });
      });
      return arr;
    }
  },
  watch: {
    visible: function (val) {
      if (val) {
        console.log('-> %c val  ===    %o', 'font-size: 15px;color: #fa8c16 ;', val);
        // this.getCouponOption();
        this.clearQuery();
        this.getList();
        if (this.selectedItems.length) {
          this.selectedItems.forEach(item => {
            this.$set(this.selected_items, item.id, item);
          });
        }
      }
    }
  }
};
</script>

<style lang="less">
.clip {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 90%;
  display: inline-block;
}
</style>
