<template>
  <div class="excel-upload-wrapper">
    <div class="buttonBox">
      <el-upload
        ref="upload"
        action
        accept=".xlsx, .xls"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handle"
        :disabled="disabled"
        class="flex flex-item-align custom-upload"
      >
        <Button v-if="btnType === 'button' && !filename" type="primary" class="space6" :loading="excelUploadLoading">{{
          btnText
        }}</Button>
        <a v-if="btnType === 'link'">{{ btnText }}</a>
        <p>
          <a v-if="btnType === 'text' && !filename">{{ btnText }}</a>
          <span v-if="filename" style="color: #155bd4" class="ecs">{{ filename }}</span>
        </p>
      </el-upload>
    </div>
  </div>
</template>

<script>
import XLSX from 'xlsx';
export default {
  name: 'RExcelUploadV2',
  mixins: [],

  components: {},

  props: {
    btnText: {
      type: String,
      default: '上传,',
    },
    btnType: {
      type: String,
      default: 'button',
    },
    isStock: {
      type: Boolean,
      default: false,
    },
    showFilename: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      filename: '',
      excelUploadLoading: false,
    };
  },
  inject: {
    getFileName: {
      default: () => function () {},
    },
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    // 将流数据转化为json数据
    async handle(file) {
      console.log('-> file', file);
      //读取FILE中的数据（变为JSON格式）
      let excelJsonList = await this.readFile(file.raw);

      let workbook = XLSX.read(excelJsonList, { type: 'binary' }),
        worksheet = workbook.Sheets[workbook.SheetNames[0]];
      // this.html = XLSX.utils.sheet_to_html(worksheet);
      excelJsonList = XLSX.utils.sheet_to_json(worksheet);
      // 获取导入excel数据
      this.filename = file.name;
      // 用于获取
      this.getFileName(this.filename);
      this.$emit('excel-upload', excelJsonList);
    },
    // 读取数据
    readFile(file) {
      return new Promise(resolve => {
        let reader = new FileReader();
        reader.readAsBinaryString(file);
        reader.onload = ev => {
          resolve(ev.target.result);
        };
      });
    },

    readWorkbookFromLocalFile(file, cb) {
      const reader = new FileReader();
      reader.onload = function (e) {
        const data = e.target.result;
        const workbook = XLSX.read(data, { type: 'binary' });
        cb && cb(workbook);
      };
      reader.readAsBinaryString(file);
    },
    outputWorkbook(workbook) {
      const sheetNames = workbook.SheetNames; // 工作表名称集合
      sheetNames.forEach(name => {
        const worksheet = workbook.Sheets[name]; // 只能通过工作表名称来获取指定工作表
        for (const key in worksheet) {
          // v是读取单元格的原始值
          console.log(key, key[0] === '!' ? worksheet[key] : worksheet[key].v);
        }
      });
    },
    //读取远程EXCEL
    readWorkbookFromRemoteFile(url, callback) {
      const xhr = new XMLHttpRequest();
      xhr.open('get', url, true);
      xhr.responseType = 'arraybuffer';
      xhr.onload = function (e) {
        console.log('-> %c e  === %o', 'font-size: 15px;color: green;', e);
        if (xhr.status == 200) {
          const data = new Uint8Array(xhr.response);
          const workbook = XLSX.read(data, { type: 'array' });
          if (callback) callback(workbook);
        }
      };
      xhr.send();
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
p {
  margin: 0;
}
</style>
