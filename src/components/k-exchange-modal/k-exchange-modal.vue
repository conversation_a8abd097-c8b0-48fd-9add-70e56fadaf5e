<template>
  <Modal :value="value"
         title="兑换服务"
         :mask-closable="false"
         :width="600"
         @on-visible-change="visibleChange"
  >
    <div class="exchange-box">
      <div class="exchange-item" style="border-bottom: 1px solid #EFEEF1;">
        <div class="title">兑换商品</div>
        <div class="value">
          <Select v-model="queryFormData.gs_id" placeholder="兑换商品" style="width: 200px" @on-change="changeService">
            <Option v-for="(item, key) in serviceList" :value="item.id" :key="key" :disabled="item.surplus_num === '0'">{{item.card_name}}</Option>
          </Select>
        </div>
      </div>
      <div class="exchange-item">
        <div class="title">兑换次数</div>
        <div class="value">
          剩余最多可兑 <span style="color:#E5634B">{{ maxTimes }}</span> 次
          <InputNumber v-model="queryFormData.times" style="width: 134px" :min="0" :max="Number(maxTimes)" placeholder="兑换次数" controls-outside></InputNumber>
        </div>
      </div>
    </div>
    <div class="notice">服务兑换完成后，无法撤回。请确认无误后再进行兑换</div>
    <div slot="footer">
      <Button @click="cancel">取消</Button>
      <Button type="primary" @click="confirm">确认兑换</Button>
    </div>
  </Modal>
</template>

<script>
let init_query_from_data = {
  page: 1,
  pageSize: 5,
  gs_id: '',
  times: null,
  id: '',
}

export default {
  name: "exchange-modal",
  props: {
    value: {
      type: Boolean,
      default: false
    },
    exchangeId: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      queryFormData: {...init_query_from_data},
      maxTimes: '0',
      serviceList:[]
    }
  },

  created() {},

  methods: {
    visibleChange(val){
      if (val === true) {
        this.queryFormData.id = this.exchangeId
        this.getExchangeCardInfo()
      }else{
        this.cancel()
        this.clearQuery()
      }
    },

    cancel(){
      this.$nextTick(() => {
        this.$emit('input', false)
      })
    },

    confirm(){
      if(!this.queryFormData.times){
        this.$Message.error('兑换次数不得低于1次')
        return
      }
      let params = {...this.queryFormData, id: this.exchangeId}
       this.$api.exchangeCard(params).then(res => {
         this.cancel()
         this.$emit('refresh')
         this.$Message.success('兑换成功')
       })
    },
    // 重置数据
    clearQuery () {
      this.queryFormData = {...init_query_from_data}
      this.queryFormData.page = 1
      this.maxTimes = 0
    },

    changeService(id){
      if(this.serviceList.length > 0 && id){
        let service = this.serviceList.find(item => item.id === id)
        if(service.service_status !== 'ALL'){
          this.maxTimes = service.surplus_num
        }else{
          this.maxTimes = 0
        }
        if(this.maxTimes === 0){
          this.queryFormData.times = this.maxTimes
        }else{
          this.queryFormData.times = 1
        }
      }

    },

    getExchangeCardInfo(){
      let params = {id: this.exchangeId}
      this.$api.getExchangeCardInfo(params).then(res => {
        console.log("-> res", res);
        this.serviceList = res.services_list
        let defaultExchange = this.serviceList.find(item => item.surplus_num !== '0') // 默认通兑券
        this.queryFormData.gs_id = defaultExchange.id
        if(defaultExchange.service_status !== 'ALL'){
          this.maxTimes = defaultExchange.surplus_num
        }else{
          this.maxTimes = 0
        }
        if(this.maxTimes === 0){
          this.queryFormData.times = this.maxTimes
        }else{
          this.queryFormData.times = 1
        }
      })
    }
  },

  watch: {}
}
</script>

<style lang="less" scoped>
::v-deep .ivu-modal{
  top: 20%;
  .ivu-modal-body{
    padding: 20px 30px 0;
  }
  .ivu-modal-footer{
    border: 0;
  }
}
.disabled{
  cursor: not-allowed;
  color: #999999;
}

.notice{
  margin-top: 10px;
  color: #E5634B;
}

::v-deep .ivu-input-number-input{
  text-align: center;
}

::v-deep .ivu-input-number-controls-outside-btn i{
  font-size: 26px;
  color: #656c7c;
  font-weight: 600;
  margin-top: 2px;
}

::v-deep .ivu-input-number-controls-outside-btn-disabled i, .ivu-input-number-controls-outside-btn-disabled:hover i{
  color: #ccc !important;
}

.exchange-box{
  height: 152px;
  background: #F8F9FD;
  border-radius: 4px;
  border: 1px solid #EFEEF1;
  padding:0 20px ;
  .exchange-item{
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    color: #80868C;
    //border: 1px solid #999999;
    //border-bottom: 0;
    .title{

    }
    .value{
    }
  }
  .exchange-item:last-child{
    //border-bottom: 1px solid #999999;
  }
}
</style>
