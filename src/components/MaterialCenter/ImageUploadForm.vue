<template>
  <div class="image-upload-form">
    <Form ref="imageUploadForm" :rules="rules" :model="formData" :label-width="120" class="image-form">
      <FormItem label="上传方式" prop="upload_type" required>
        <RadioGroup v-model="formData.upload_type" @on-change="onUploadTypeChange">
          <Radio v-for="item in uploadTypes" :key="item.value" :label="item.value">{{ item.label }}</Radio>
        </RadioGroup>
      </FormItem>

      <FormItem label="所在分组" required prop="parent_id">
        <div class="flex">
          <Select ref="categorySelect" v-model="formData.parent_id" style="width: 346px" placeholder="请输入分组名称">
            <Option :value="formData.parent_id || ''" :label="getNodesPath" v-show="false"></Option>
            <category-tree
              :category-tree="categoryTree"
              :current-category-id="formData.parent_id"
              :category-select="selectCategory"
              is-upload
            />
          </Select>
          <!--          <k-link class="mr-16 ml-16" target="_blank" to="/goods/material-center?createCategory=1&type=image">-->
          <!--            添加分组-->
          <!--          </k-link>-->
          <a class="ml-16" @click="$emit('refreshCategoryList')">刷新</a>
        </div>
      </FormItem>
      <FormItem v-if="formData.upload_type === 1" label="本地图片" :prop="formData.upload_type === 1 ? 'images' : ''">
        <div style="max-height: 320px; overflow-y: auto; min-height: 92px" class="hidden-scroll">
          <image-upload v-model="formData.images" ref="imageUpload" :limit="limit"></image-upload>
        </div>
      </FormItem>
      <FormItem v-if="formData.upload_type === 2" label="网络图片" prop="net_url">
        <Input v-model.trim="formData.net_url" placeholder="请输入网络图片地址" style="width: 346px" />
      </FormItem>
    </Form>
    <div class="submit-btn">
      <Button @click="cancelUploadAll">取消</Button>
      <Button type="primary" :loading="submitLoading" class="ml-16" @click="submit">确定</Button>
    </div>
  </div>
</template>

<script>
import CategoryTree from './CategoryTree.vue';
import ImageUpload from './Upload/ImageUpload.vue';
import axios from 'axios';
import { getImageKey } from '@/libs/imageUrl';
import { getAssetAppKey, getUlid } from '@/libs/runtime';

export default {
  name: 'ImageUploadForm',
  components: { CategoryTree, ImageUpload },
  props: {
    categoryTree: {
      type: Array,
      default: () => [],
    },
    treeMap: {
      type: Map,
      default: () => null,
    },
    findPathToRootById: {
      type: Function,
      default: () => {},
    },
    currentCategoryId: {
      type: [Number, String],
      default: '',
    },
    storePrefix: {
      type: String,
      default: '',
    },
    assetType: {
      type: Number,
      default: 1,
    },
    limit: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      rules: {
        upload_type: [{ required: false, message: '请选择上传方式', trigger: ['change'] }],
        parent_id: [{ required: false, message: '请选择所在分组', trigger: ['change'] }],
        name: [{ required: true, message: '请输入分组名称', trigger: ['change'] }],
        images: [{ required: true, type: 'array', min: 1, message: '请上传图片', trigger: 'change' }],
        net_url: [{ required: true, message: '请输入图片地址', trigger: 'change' }],
      },
      formData: {
        parent_id: '',
        upload_type: 1,
        images: [],
        net_url: '',
      },
      categoryNodes: [],
      uploadTypes: [
        {
          value: 1,
          label: '本地上传',
        },
        {
          value: 2,
          label: '网络图片',
        },
      ],
      qiniuInfo: {
        domain: '',
        scope: '',
        token: '',
      },
      submitLoading: false,
      successImages: [],
      cancelTokens: {},
    };
  },
  computed: {
    getNodesPath() {
      return this.categoryNodes.map(node => node.name).join('/');
    },
  },
  watch: {
    currentCategoryId: {
      handler(val) {
        if (val) {
          this.formData.parent_id = val;
          this.categoryNodes = this.findPathToRootById(val);
        }
      },
      immediate: true,
    },
    'formData.images.length': {
      handler(val) {
        this.$refs.imageUploadForm.validateField('images');
      },
    },
  },
  methods: {
    onUploadTypeChange(v) {
      this.formData.upload_type = v;
      let field = this.formData.upload_type === 1 ? 'images' : 'net_url';
      this.formData.images = [];
      this.formData.net_url = '';
      this.$nextTick(() => {
        this.$refs.imageUploadForm.validateField(field);
      });
    },
    selectCategory({ id }) {
      this.$refs.categorySelect.visible = false;
      this.categoryNodes = this.findPathToRootById(id);
      this.formData.parent_id = id;
      localStorage.setItem(this.storePrefix, id);
    },
    closeModal() {
      this.formData.images = [];
      this.$emit('close');
    },
    submit() {
      console.log(this.formData);
      this.$refs.imageUploadForm.validate(valid => {
        console.log('%c [ valid ]-114', 'font-size:13px; background:#a52093; color:#e964d7;', valid);
        if (valid) {
          if (this.formData.upload_type === 1) {
            this.submitForm();
            return;
          }
          this.submitLoading = true;
          this.$api
            .uploadNetImg({
              url: this.formData.net_url,
              category_id: this.formData.parent_id,
            })
            .then(res => {
              console.log('%c=>(ImageUploadForm.vue:166) res', 'color: #ECA233;font-size: 16px;', res);
              const img = res.domain + '/' + res.key;
              this.successImages = [img];
              this.emitSuccess(this.successImages);
              this.$refs.imageUploadForm.resetFields();
            })
            .catch(err => {
              this.$Message.error('上传失败,请检查网络或图片地址');
            })
            .finally(() => {
              this.submitLoading = false;
            });
        } else {
          this.$Message.error('请检查表单内容');
        }
      });
    },
    async submitForm() {
      const data = await this.$api.getAssetsUploadToken({ asset_type: this.assetType });
      this.qiniuInfo = data;
      this.submitLoading = true;
      let formDataList = [];
      for (let i = 0; i < this.formData.images.length; i++) {
        const file = this.formData.images[i];
        const formData = new FormData();
        formData.append('key', getImageKey(file.name, this.qiniuInfo.scope));
        formData.append('token', this.qiniuInfo.token);
        formData.append('file', file);
        formData.append('x:assetType', this.assetType);
        formData.append('x:categoryId', this.formData.parent_id);
        formData.append('x:appKey', getAssetAppKey());
        formData.append('x:deviceId', getUlid());
        formDataList.push(formData);
      }
      const promises = formDataList.map(formData => {
        const fileId = formData.get('key');
        const cancelTokenSource = axios.CancelToken.source();
        this.$set(this.cancelTokens, fileId, cancelTokenSource);
        return axios.post('https://upload.qiniup.com/', formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
          cancelToken: cancelTokenSource.token,
        });
      });
      Promise.all(promises)
        .then(res => {
          this.successImages = res.map(item => this.qiniuInfo.domain + '/' + item.data.data.key);
          this.emitSuccess(this.successImages);
        })
        .catch(err => {
          console.log('%c=>(ImageUploadForm.vue:160) err', 'font-size: 18px;color: #FF7043;', err);
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
    cancelUpload(fileId) {
      if (this.cancelTokens[fileId]) {
        this.cancelTokens[fileId].cancel('Upload canceled by the user.');
        this.$delete(this.cancelTokens, fileId);
      }
    },
    cancelUploadAll() {
      for (let fileId in this.cancelTokens) {
        this.cancelUpload(fileId);
      }
      this.closeModal();
    },
    emitSuccess(data) {
      console.log('%c [ data ]-196', 'font-size:13px; background:#1e9b05; color:#62df49;', data);
      this.$Message.success('上传成功');
      this.$emit('uploadSuccess', data);
      this.closeModal();
    },
    //获取图片key
  },
};
</script>

<style lang="less" scoped>
.image-upload-form {
  height: 100%;
  width: 100%;
  position: relative;

  .submit-btn {
    position: absolute;
    bottom: 0;
    right: 0;
  }
}
</style>
