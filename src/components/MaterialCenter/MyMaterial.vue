<template>
  <div>
    <Modal
      :value="visible"
      class-name="vertical-center-modal material-modal"
      width="902px"
      footer-hide
      @on-visible-change="visibleChange"
    >
      <div class="modal-header" slot="header">
        {{ `我的${getAssetTypeDesc}` }}
        <span class="desc">({{ checkedMaterials.length }}/{{ limit }})</span>
      </div>
      <div class="picture-container">
        <div class="action-bar">
          <Button class="mr-16" type="primary" @click="toUpload">上传{{ getAssetTypeDesc }}</Button>
          <k-link target="_blank" to="/goods/material-center">前往素材中心</k-link>
        </div>
        <div class="flex">
          <div class="picture-sidebar">
            <div class="category-filter">
              <a v-if="isSearching" class="mr-8" @click="cancelSearch">返回</a>
              <input v-show="false" type="text" name="username" />
              <Input
                v-model="categoryName"
                class="flex-1"
                placeholder="搜索分组名称"
                @keyup.enter.native="searchCategory"
              >
                <Icon v-slot:suffix type="ios-search" @click="searchCategory" />
              </Input>
            </div>
            <div class="category-tree-wrapper">
              <category-tree
                :current-category-id="currentCategoryId"
                :category-tree="renderCategoryTree"
                :category-select="categorySelect"
                show-count
                is-upload
              />
            </div>
          </div>
          <div class="asset-list-wrapper">
            <div class="asset-list hidden-scroll">
              <template v-if="mediaList.length">
                <template v-if="assetType === 'image'">
                  <div v-for="item in mediaList" :key="item.id" class="asset-item" @click="handleSelect(item)">
                    <div class="image-box" :style="{ backgroundImage: `url(${item.url}-B.450)` }"></div>
                    <div class="image-title-wrap" v-overflow-tooltip="{ content: item.name }">{{ item.name }}</div>
                    <div v-if="getCheckedIndex(item.id) > -1" class="image-box-selected">
                      <div class="image-box-selected__right-angle"></div>
                      <div class="image-box-selected__text">{{ getCheckedIndex(item.id) + 1 }}</div>
                    </div>
                  </div>
                </template>
                <template v-else>
                  <div
                    v-for="item in mediaList"
                    :key="item.id"
                    class="asset-item video-asset-item"
                    @click="handleSelectVideo(item)"
                  >
                    <div class="image-box video-box" :style="{ backgroundImage: `url(${item.cover_url}-B.450)` }">
                      <div class="video-meta">
                        <span>{{ getDuration(item.meta_info.duration) }}</span>
                        <span>{{ getSize(item.size) }}</span>
                      </div>
                    </div>
                    <div class="image-title-wrap">{{ item.name }}</div>
                    <div v-if="getCheckedIndex(item.id) > -1" class="image-box-selected">
                      <div class="image-box-selected__right-angle"></div>
                      <div class="image-box-selected__text">
                        <Icon type="md-checkmark" />
                      </div>
                    </div>
                  </div>
                </template>
              </template>
              <div class="empty-tip" v-if="!mediaList.length">
                暂无数据，可点击左上角“上传{{ getAssetTypeDesc }}”按钮添加
              </div>
            </div>
            <div class="pagination-wrap mt-12">
              <div class="checked-count">
                <span>已选{{ checkedMaterials.length }}项</span>
              </div>
              <div class="flex flex-item-align">
                <span class="pagination-text"> 共{{ total }}条，每页{{ pagination.page_size }}条 </span>
                <Page
                  :current="pagination.page"
                  :page-size="pagination.page_size"
                  @on-change="onPageChange"
                  :total="total"
                  simple
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <Button class="mr-16" @click="closeModal">取消</Button>
        <Button type="primary" :disabled="!checkedMaterials.length" @click="confirmSelect">确定</Button>
      </div>
    </Modal>
    <upload-modal
      v-model="uploadVisible"
      :limit="limit"
      :current-category-id="currentCategoryId"
      :asset-type="mediaTypeMap[assetType]"
      @uploadSuccess="uploadSuccess"
    />
  </div>
</template>

<script>
import CategoryTree from './CategoryTree.vue';
import { cloneDeep } from 'lodash';
import { getUid } from '@/libs/runtime';
import UploadModal from './UploadModal.vue';

export default {
  name: 'MyMaterial',
  components: { UploadModal, CategoryTree },
  model: {
    prop: 'visible',
    event: 'visibleChange',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    assetType: {
      type: [String, Number],
      default: 'image',
    },
    limit: {
      type: Number,
      default: 9,
    },
  },
  data() {
    return {
      mediaTypeMap: {
        image: 1,
        video: 2,
      },
      checkedMaterials: [],
      isSearching: false,
      currentCategoryId: '',
      categoryName: '',
      renderCategoryTree: [],
      categoryList: [],
      categoryListTree: [],
      defaultCategoryId: '',
      currentCategoryName: '',
      mediaList: [],
      storePrefixKey: '',
      pagination: {
        page: 1,
        page_size: 15,
      },
      total: 0,
      uploadVisible: false,
    };
  },
  computed: {
    getCheckedIndex() {
      return id => this.checkedMaterials.findIndex(item => item.id === id);
    },
    getCategoryType() {
      return this.mediaTypeMap[this.assetType];
    },

    getAssetTypeDesc() {
      return this.assetType === 'image' ? '图片' : '视频';
    },
    // formatVideoMetaInfo() {
    //   return (metaInfo, key) => {
    //     switch (key) {
    //       case 'duration':
    //         return this.getDuration(metaInfo.duration);
    //       case 'size':
    //         return this.getSize(metaInfo.size);
    //     }
    //   };
    // },

    getDuration() {
      return seconds => {
        seconds = Math.floor(seconds);
        // 计算分钟和剩余秒数
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;

        // 格式化为两位数
        const formattedMinutes = String(minutes).padStart(2, '0');
        const formattedSeconds = String(remainingSeconds).padStart(2, '0');
        return `${formattedMinutes}:${formattedSeconds}`;
      };
    },
    getSize() {
      return size => (size / 1024 / 1024).toFixed(2) + 'MB';
    },
  },
  created() {
    this.storePrefixKey = `document-attachment-${this.assetType}-category-id-${getUid()}`;
  },

  methods: {
    uploadSuccess(data) {
      this.$emit('uploadSuccess', data);
      this.uploadVisible = false;
    },
    confirmSelect() {
      const urls = this.checkedMaterials.map(item => item.url)
      this.$emit('uploadSuccess', this.limit === 1 ? urls[0] : urls)
      this.closeModal()
    },
    handleSelectVideo(item) {
      console.log(
        '%c=>(MyMaterial.vue:207) item',
        'background-color: green; color: #fff; padding: 4px 20px; border-radius: 4px;',
        item
      );
      if (this.getCheckedIndex(item.id) > -1) {
        this.checkedMaterials.splice(this.getCheckedIndex(item.id), 1);
      } else {
        this.checkedMaterials = [];
        this.checkedMaterials = [item];
      }
    },
    handleSelect(item) {
      const isInvert = this.getCheckedIndex(item.id) > -1;

      if (!isInvert && this.checkedMaterials.length >= this.limit) {
        this.$Message.error(`最多只能选择${this.limit}张图片`);
        return;
      }
      this.manageCheckedItem(item);
    },
    manageCheckedItem(item) {
      if (this.getCheckedIndex(item.id) > -1) {
        this.checkedMaterials.splice(this.getCheckedIndex(item.id), 1);
      } else {
        this.checkedMaterials.push(item);
      }
    },
    categorySelect(category) {
      this.currentCategoryId = category.id;
      localStorage.setItem(this.storePrefixKey, category.id);
      this.currentCategoryName = category.name;
      this.pagination.page = 1;
      this.getMediaList();
    },
    toUpload() {
      this.uploadVisible = true;
      console.log(this.limit);
      this.$emit('visibleChange', false);
    },
    visibleChange(visible) {
      this.pagination.page_size = this.assetType === 'image' ? 15 : 12;
      if (visible) {
        this.initCategoryData();
      }
      if (!visible) {
        this.checkedMaterials = [];
        this.$emit('visibleChange', visible);
      }
    },
    searchCategory() {
      if (!this.categoryName) {
        this.isSearching = false;
        this.renderCategoryTree = this.buildTreeAndSearch(this.categoryList);
        return;
      }
      this.isSearching = true;
      this.renderCategoryTree = this.buildTreeAndSearch(this.categoryList, this.categoryName);
      if (this.renderCategoryTree.length) {
        this.categorySelect(this.renderCategoryTree[0]);
      }
    },
    cancelSearch() {
      this.categoryName = '';
      this.isSearching = false;
      this.searchCategory();
    },
    initCategoryStatus(data, categoryId) {
      data.forEach(item => {
        if (item.level === 0 && item.type === 2 && item.id) {
          item.isOpen = true;
        }
        if (item.id === categoryId) {
          this.currentCategoryName = item.name;
          item.isOpen = true;
          if (item.parent_id) {
            this.initCategoryStatus(data, item.parent_id);
          }
        }
      });
    },
    buildTreeAndSearch(data, rootName = null) {
      const map = new Map();
      let rootNode = null; // 用于存储搜索到的根节点
      // 初始化 Map，并附加 children 属性
      data.forEach(item => {
        item.isOpen = item.isOpen || false;
        map.set(item.id, { ...item, children: [] });
      });
      // 构建树结构
      data.forEach(item => {
        if (map.has(item.parent_id)) {
          const parent = map.get(item.parent_id);
          parent.children.push(map.get(item.id));
        }
      });
      // 如果指定了 rootName，搜索该节点
      if (rootName) {
        rootNode = Array.from(map.values()).filter(node => node.name.includes(rootName));
      }

      // 如果未指定 rootName 或未找到目标节点，返回完整的树
      if (!rootName || !rootNode) {
        return Array.from(map.values()).filter(node => !map.has(node.parent_id));
      }
      // 返回以指定节点为根的子树
      console.log('%c=>(CategoryManager.vue:144) rootNode', 'font-size: 18px;color: #FF7043;', rootNode);
      return rootNode;
    },
    initCategoryData() {
      this.$api
        .getMtCategoryAllList({
          asset_type: this.getCategoryType,
        })
        .then(res => {
          this.categoryList = res.list;
          const list = this.categoryList;
          this.initDefaultId();
          const originTree = this.buildTreeAndSearch(list);
          this.categoryListTree = cloneDeep(originTree);
          this.renderCategoryTree = originTree;
        })
        .catch(err => {
          this.$Message.error(err.message);
        });
    },
    initDefaultId() {
      const categoryId = localStorage.getItem(this.storePrefixKey);
      const isExist = this.categoryList.some(item => item.id === Number(categoryId));
      if (!isExist) {
        localStorage.removeItem(this.storePrefixKey);
      }
      this.defaultCategoryId = this.categoryList.find(item => item.type === 1)?.id;
      this.currentCategoryId = isExist ? Number(categoryId) : this.defaultCategoryId;
      this.getMediaList();
      const category = this.categoryList.find(item => item.id === this.currentCategoryId);
      this.currentCategoryName = category?.name;
      this.initCategoryStatus(this.categoryList, this.currentCategoryId);
    },
    handleSuccess() {
      this.initCategoryData();
    },
    getMediaList() {
      this.$api
        .getMaterialList({
          category_id: this.currentCategoryId,
          type: this.mediaTypeMap[this.assetType],
          ...this.pagination,
        })
        .then(res => {
          this.mediaList = res.list;
          this.total = res.total;
        })
        .catch(err => {
          this.$Message.error(err.message);
        });
    },
    onPageChange(page) {
      this.pagination.page = page;
      this.getMediaList();
    },
    closeModal() {
      this.$emit('visibleChange', false);
      this.checkedMaterials = [];
    },
  },
};
</script>

<style lang="less" scoped>
.modal-header {
  font-size: 16px;

  .desc {
    font-size: 14px;
    color: #969799;
  }
}

.picture-container {
  .action-bar {
    margin-bottom: 8px;
  }
}

.modal-footer {
  text-align: right;
  margin-top: 16px;
  padding-bottom: 16px;
}

.picture-sidebar {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex: none;
  width: 230px;
  border-right: 1px solid #dcdee0;
  height: 508px;

  .category-filter {
    display: flex;
    align-items: center;
    padding: 0 8px 8px 0;
    box-shadow: 0 -1px 4px 0 rgba(0, 0, 0, 0.1);
    font-size: 14px;
  }

  .category-tree-wrapper {
    flex: auto;
    overflow-y: auto;
    padding: 0 8px 2px 12px;
    box-sizing: border-box;

    &::-webkit-scrollbar {
      display: none;
    }
  }
}

.asset-list-wrapper {
  flex: 1;
  margin-left: 8px;

  .asset-list {
    overflow: auto;
    height: 457px;

    .asset-item {
      display: inline-block;
      position: relative;
      margin-right: 8px;
      margin-bottom: 8px;
      font-size: 14px;
      vertical-align: middle;
      cursor: pointer;

      &:nth-of-type(5n) {
        margin-right: 0;
      }

      &:nth-of-type(n + 10) {
        margin-bottom: 0;
      }

      .image-box {
        display: flex;
        align-items: flex-end;
        width: 120px;
        height: 120px;
        box-sizing: border-box;
        background: #ddd;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: 50% 50%;
        border-radius: 2px;
      }

      .image-box-selected {
        position: absolute;
        box-sizing: border-box;
        top: 0;
        left: 0;
        width: 120px;
        height: 120px;
        border: 2px solid #155bd4;
        color: #fff;
        overflow: hidden;
        pointer-events: none;

        .image-box-selected__right-angle {
          position: absolute;
          top: -21px;
          right: -21px;
          width: 42px;
          height: 42px;
          transform: rotate(45deg);
          background: #155bd4;
        }

        .image-box-selected__text {
          position: absolute;
          top: -2px;
          right: 3px;
        }
      }

      .image-title-wrap {
        margin-top: 5px;
        padding-bottom: 1px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 110px;
        color: #323233;
      }
    }

    .video-asset-item {
      .video-box {
        width: 160px;
        height: 90px;

        .video-meta {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #fff;
          height: 28px;
          width: 100%;
          line-height: 28px;
          background-color: rgba(0, 0, 0, 0.4);
          padding: 0 4px;
        }
      }

      .image-box-selected {
        width: 160px;
        height: 90px;
      }
    }
  }
}

.pagination-wrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;

  .checked-count {
    flex: none;
    color: #969799;
  }
}

.empty-tip {
  text-align: center;
  font-size: 14px;
  margin: 60px 0;
}
</style>

<style lang="less">
.material-modal {
  .ivu-modal-body {
    padding: 12px 16px;
  }
}
</style>
