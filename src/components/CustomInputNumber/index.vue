<template>
  <div class="custom-input-number">
    <div v-if="!isEmpty(prefix)" :style="`height: ${preHeight}px`" class="prefix">{{ prefix }}</div>
    <template v-else-if="$slots.prepend">
      <div :style="`height: ${preHeight}px`" class="prefix">
        <slot name="prepend" />
      </div>
    </template>
    <InputNumber
      ref="custom-input-number"
      v-model="newValue"
      class="custom-input-number-input flex-1"
      v-bind="$attrs"
      :active-change="false"
      @on-change="change"
      @on-focus="focus"
      @on-blur="blur"
    />
    <div v-if="!isEmpty(suffix)" :style="`height: ${preHeight}px`" class="prefix">{{ suffix }}</div>
    <template v-else-if="$slots.append">
      <div :style="`height: ${preHeight}px`" class="prefix">
        <slot name="append" />
      </div>
    </template>
  </div>
</template>

<script>
import { isEmpty } from '../../utils/helper';

export default {
  name: 'CustomInputNumber',
  props: {
    value: {
      type: [String, Number],
      default: null,
    },
    prefix: {
      type: String,
      default: '',
    },
    suffix: {
      type: String,
      default: '',
    },
    isShowZero: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isEmpty,
      preHeight: 0,
    };
  },
  computed: {
    newValue: {
      get() {
        if (this.isShowZero) {
          return this.value;
        } else {
          return +this.value || null;
        }
      },
      set(newValue) {
        if (this.isShowZero) {
          this.$emit('input', newValue);
        } else {
          this.$emit('input', newValue || null);
        }
      },
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.preHeight = this.$refs?.['custom-input-number']?.$el?.offsetHeight;
    });
  },
  methods: {
    change(value) {
      this.$emit('on-change', value);
    },
    focus(event) {
      event.currentTarget.select();
      this.$emit('on-focus', event);
    },
    blur() {
      this.$emit('on-blur');
    },
  },
};
</script>

<style scoped lang="less">
.custom-input-number {
  width: 100%;
  display: flex;
  border: 1px solid #dcdee2;
  border-radius: 2px;
  .prefix {
    padding: 0 10px;
    background: #dcdee2;
    line-height: unset;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 40px;
    :deep(.ivu-select-selection) {
      width: 85px;
      background: #dcdee2 !important;
      border: none !important;
      color: #333;
      height: 28px;
      margin-top: -2px;
    }
  }
  .custom-input-number-input {
    border: none;
  }
  .custom-input-number-input:focus {
    box-shadow: none;
  }
  :deep(.ivu-input-number-input) {
    border: none;
    border-radius: unset;
  }
}
.custom-input-number:focus-within {
  border-color: #447cdd;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(21, 91, 212, 0.2);
}
.custom-input-number :deep(.ivu-select-selection) {
  background: #dcdee2;
  border-color: #dcdee2;
  box-shadow: unset;
}
.custom-input-number :deep(.ivu-select-selection .ivu-select-selected-value) {
  padding: 0;
}
.custom-input-number :deep(.ivu-select-selection:hover) {
  border-color: #dcdee2 !important;
  box-shadow: unset;
}
</style>
