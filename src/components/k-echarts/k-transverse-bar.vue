<template>
		<div :id='id' :ref="id" style="height:300px;width: 100%"></div>
</template>
<script>
export default {
	name: "KTransverseBar",
	props: {
		id: {
			type: String,
			default: () => 'transverselBar'
		},
    options: {
      type: Object,
      default: () => {
        return {
          seriesData: {
            order_num: 0, // 总订单
            his_pres_num: 0, // his订单
            shop_order_num: 0, // 商城订单
          }
        }
      }
    }
	},
	data() {
		return {
		};
	},
	watch: {
    options: {
      deep: true,
      immediate: true,
      handler (val) {
        this.init()
      }
    }
	},
	created() {
	},
  mounted() {
		// this.init()
	},
	methods: {
		init() {
			this.$nextTick(() => {
				this.drawLine(this.options);
			})
		},
		drawLine(options) {
			var myChart = this.$echarts.init(document.getElementById(this.id));
      window.addEventListener("resize", function() {
        myChart.resize();
      });
			myChart.setOption({
        grid: {
          top: '10%',
          left:'4%',
          right: '10%',
          bottom: '8%'
        },
        tooltip: {
          show: true,
        },
				yAxis: {
          type: 'category',
          offset: 0,
          position: 'left',
          data: [ '商城购物订单', 'HIS诊疗处方单', '总订单',],
          // offset: -100,
          axisTick: {
            show: false, // 去除x轴的刻度线
          },
          axisLabel: {
            interval: 0,
            color: '#666666', // 自定义x轴label的颜色
            fontSize: 12,
            verticalAlign: "bottom",
            align: 'left',
            padding: [0,0,12,10],
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(204, 204, 204, .5)',
            }
          },
        },
        xAxis: {
          type: 'value',
          name: "",
          nameTextStyle: {
            color: '#999999', // 设置y轴单位的颜色
            fontSize: 14,
            padding: [0,0,-30,60], // 设置单位的位置
          },
          axisTick: {
            show: false, // 去除y轴的刻度线
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(204, 204, 204, .5)'
            }
          },
          splitLine: {
            show: true, // 显示y轴的网格线
            lineStyle: {
              color: '#CCCCCC',
              opacity: .3,
            }
          },
          axisLabel: {
            formatter: ''
          }
        },
				series: [
					{
            data: [
              {
                value: options.seriesData.shop_order_num,
                itemStyle: {
                  color: '#E88E6D'
                }
              },
              {
                value: options.seriesData.his_pres_num,
                itemStyle: {
                  color: '#9CA4FF'
                }
              },
              {
                value: options.seriesData.order_num,
                itemStyle: {
                  color: '#5AC5B1'
                }
              },
            ],
            type: 'bar',
            color: 'rgba(17, 87, 229, .3)', // 更改柱体的颜色
            barWidth: '20%', // 设置柱子的宽度
            // itemStyle: {
                label: {
                  show: true,
                  formatter: '{c}',
                  position: 'right',
                  textStyle: {
                    color: '#000',
                    fontSize: 14
                  }
                // }
            },
          }
				],
			});
		}
	},
};
</script>

<style lang="less" scoped>
</style>
