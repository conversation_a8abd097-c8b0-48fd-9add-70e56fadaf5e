<template>
  <div :id='id' :ref="id" style="height:300px;width: 100%"></div>
</template>
<script>
export default {
  name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  props: {
    id: {
      type: String,
      default: () => 'pie'
    },
    options: {
      type: Object,
      default: () => {
        return {
          seriesData: [],
          legendData: [],
          name: '',
          tooltip: {},
          color: []
        }
      }
    }
  },
  data() {
    return {};
  },
  watch: {
    options: {
      deep: true,
      handler(val) {
        if(val&&val.seriesData&&val.name){
          this.init(val)
        }
      },immediate: true
    }
  },
  created() {

  },
  methods: {
    init(options) {
      this.$nextTick(() => {
        this.drawLine(options);
        let myChart = this.$echarts.getInstanceByDom(document.getElementById(this.id))
        // 已有实例，不再重复创建
        if ( myChart == null ) {
          myChart = this.$echarts.init(document.getElementById(this.id));
        }
        window.addEventListener("resize", function () {
          myChart.resize();
        });
      })
    },
    // 将辨证参考的数据格式转换为饼图所需要的格式
    drawLine(data) {
      const myChart = this.$echarts.init(document.getElementById(this.id));
      // var data = this.genData(3);
      let option = {
        title: {
          text: data.name || '',
          subtext: "",
          left: 'center',
          top: "bottom",
          padding: [30, 0],
          textStyle: {
            fontSize: 12,
            color: '#000'
          }
        },
        tooltip: {
          trigger: "item",
          formatter: (a) => {
            return `${a.data.name}：${a.data.rate?a.data.rate:a.data.value}`
          },
          confine: true
        },
        series: [
          {
            name: "",
            type: "pie",
            center: ["50%", "50%"], // ?!饼图的位置
            // roseType: 'pie',
            itemStyle: {
              borderRadius: 4
            },
            data: data.seriesData,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)"
              }
            },
            radius: [15, '60%'],
            label: {
              show: false,
              padding: [0, -5],
              overflow: 'none',
            },
            labelLine: {
              length: 0,
              length2: 0,
            },
            color: data.color
          },
        ],
      }
      myChart.setOption(option);
    }
  }
}
</script>

<style lang="less" scoped>
</style>
