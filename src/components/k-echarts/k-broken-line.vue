<template>
		<div :id='id' :ref="id" style="height:300px;width: 100%"></div>
</template>
<script>
export default {
	name: "KBrokenLine",
	props: {
		id: {
			type: String,
			default: () => 'BrokenLine'
		},
    options: {
      type: Object,
      default: () => {
        return {
          xAxisData: [],
          seriesData: [],
          tooltip: {title: 'title'},
          color: {lineColor: '#E88E6D', areaColor: '#E88E6D'},
        }
      }
    }
	},
	data() {
		return {
		};
	},
	watch: {
    options: {
      immediate: true,
      deep: true,
      handler (val) {
        if (val.xAxisData.length) {
          this.init()
        }else{
          // removeAttribute('_echarts_instance_');
        }
      }
    }
	},
	created() {
	},
  mounted() {
	},
	methods: {
		init() {
			this.$nextTick(() => {
				this.drawLine();
				// var myChart = this.$echarts.init(document.getElementById(this.id));
				// window.addEventListener("resize", function() {
				// 	myChart.resize();
				// });
			})
		},
    // 这是测试数据，便于观看图的形状
    handleOptions () {
      let options = {
        xAxisData: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        seriesData: [820, 932, 901, 934, 1290, 1330, 1320],
        tooltip: {title: '成功退款金额（元)', content: '40,129.23'},
        color: {lineColor: '#E88E6D', areaColor: '#E88E6D'},
      }
      return options
    },
		drawLine() {
			var myChart = this.$echarts.init(document.getElementById(this.id));
      window.addEventListener("resize", function() {
        myChart.resize();
      });
      // let data = this.handleOptions()
      let data = this.options
      myChart.clear()
			myChart.setOption({
				title: {
					text: "",
					subtext: "",
					left: 'center',
					top: "bottom"
				},
        grid: {
          top: '4%',
          left:'4%',
          right: '4%',
          bottom: '10%'
        },
				xAxis: {
          type: 'category',
          boundaryGap: false,
          splitLine: {
            show: true, // 开启x轴的网格线
            lineStyle: {
              color: '#ccc',
              opacity: .2,
            }
          },
          axisTick: {
            show: false, // 去除轴刻度线,
          },
          axisLabel: {
            color: '#999999', // 自定义x轴label颜色
          },
          axisLine: {
            show: false, // 去除x轴
          },
          data: data.xAxisData
        },
        yAxis: {
          type: 'value',
          show: false, // 去除y轴整体
          splitLine: {
            show: false, // 关闭y轴的网格线
          },
          axisTick: {
            show: false, // 去除轴刻度线,
          },
          axisLine: {
            show: false, // 去除y轴
          },
        },
        tooltip: {
          show: true, // 开启悬浮框
          trigger: 'axis',
          position: function(point, params, dom, rect, size) {
            // 其中point为当前鼠标的位置，size中有两个属性：viewSize和contentSize，分别为外层div和tooltip提示框的大小
            var x = point[0] //
            var y = point[1]
            var viewWidth = size.viewSize[0]
            var viewHeight = size.viewSize[1]
            var boxWidth = size.contentSize[0]
            var boxHeight = size.contentSize[1]
            var posX = 0 // x坐标位置
            var posY = 0 // y坐标位置

            if (x < boxWidth) { // 左边放不开
              posX = 5
            } else { // 左边放的下
              posX = x - boxWidth/2
            }

            if (y < boxHeight) { // 上边放不开
              posY = 5
            } else { // 上边放得下
              posY = y - boxHeight - 25
            }

            return [posX, posY]
          },
          padding: 1, // 边框距离内容的距离
          left: 'center',
          backgroundColor: '#CCCCCC', // 边框色
          borderRadius:4,
          formatter: function (param) { // 自定义tooltip
            var text = '';
            const value = data.tooltip.title === '访问支付转化率' ? `${param[0].value}%` : `${param[0].value}`
            text += `<div style="background: #fff;padding: 8px;border-radius:4px;position: relative;min-width: 100px">
                  <div style="color:#999999;height:auto;width:100%;margin-right:5px;font-size:10px">${data.tooltip.title}</div>
                  <div style="color:#000000;font-size:14px;font-weight:600;margin-top:4px;line-height:20px"><span>${value}</span></div>
                  <div style="position: absolute;bottom:-11px;left:43%;width:0;height:0;border-top:11px solid #ccc;border-left:11px solid transparent;border-right:11px solid transparent">
                  <div style="position: absolute;top:-11px;left:-10px;width:0;height:0;border-top:10px solid #fff;border-left:10px solid transparent;border-right:10px solid transparent"><div>
                  <div>
                  </div>`
            return text;
          },
        },
				series: [
					{
            itemStyle: {
              normal: {
                color: data.color.lineColor, // 拐点颜色
                borderColor: data.color.lineColor, // 拐点边框色
                borderWidth: 3, // 拐点边框大小
                lineStyle:{
                  color: data.color.lineColor, // 设置线条颜色
                  width: 2
                }
              }
            },
            data: data.seriesData,
            type: 'line',
            symbolSize: 10, // 设置拐点大小
            showSymbol:false, // 鼠标选中悬浮才会显示拐点
            areaStyle: {
              color: data.color.areaColor, // 设置面积色
              opacity: .1, // 设置面积色透明度
            },  
            smooth: true
          }
				],
			});
		}
	}
};
</script>

<style lang="less" scoped></style>
