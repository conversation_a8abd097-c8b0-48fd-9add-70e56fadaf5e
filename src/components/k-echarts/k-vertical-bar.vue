<template>
		<div :id='id' :ref="id" style="width: 100%" :style="{height: height + 'px'}"></div>
</template>
<script>
export default {
	name: "KVerticalBar",
	props: {
		id: {
			type: String,
			default: () => 'vertivalBar'
		},
    dataSource: {
      type: Object,
      default: ()=>{}
    },
    height: {
      type: [String,Number],
      default: 300
    }
	},
	data() {
		return {
		};
	},
	watch: {
    dataSource: {
      handler(val){
        if(val.xData&&val.xData.length){
          this.init(val)
        }
      },
      deep: true,
      immediate: true,
    }
	},
	created() {
	},
	methods: {
		init(data) {
			this.$nextTick(() => {
				this.drawLine(data);
				var myChart = this.$echarts.init(document.getElementById(this.id));
				window.addEventListener("resize", function() {
					myChart.resize();
				});
			})
		},
		drawLine({xData = [],yData=[]}) {
			const myChart = this.$echarts.init(document.getElementById(this.id));
      let options = {
        grid: {
          top: '10%',
          left:'2%',
          right: '2%',
          bottom: '6%'
        },
        tooltip: {
          show: true,
        },
        xAxis: {
          type: 'category',
          data: xData,
          axisTick: {
            show: false, // 去除x轴的刻度线
          },
          axisLabel: {
            color: '#999', // 自定义x轴label的颜色
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(204, 204, 204, .5)'
            }
          },
        },
        yAxis: {
          type: 'value',
          name: "人数",
          nameTextStyle: {
            color: '#999999', // 设置y轴单位的颜色
            fontSize: 14,
            padding: [0,0,-30,30], // 设置单位的位置
          },
          axisTick: {
            show: false, // 去除y轴的刻度线
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(204, 204, 204, .5)'
            }
          },
          splitLine: {
            show: false, // 去除y轴的网格线
          },
          axisLabel: {
            formatter: ''
          }
        },
        series: [
          {
            data: yData,
            type: 'bar',
            color: 'rgba(17, 87, 229, .3)', // 更改柱体的颜色
            label: {
              show: true,
              formatter: '{c}',
              position: 'top',
              textStyle: {
                color: '#1157E5',
                fontSize: 14
              }
            },
            barWidth: '30%', // 设置柱子的宽度
          }
        ],
      }
			myChart.setOption(options);
		}
	},

};
</script>

<style lang="less" scoped>
</style>
