<template>
  <Modal v-model="modalValue" title="选择服务" :mask-closable="false" :width="760" class-name="vertical-center-modal">
    <div style="position: relative">
      <Button to="/goods/service/list" target="_blank">服务列表</Button>
      <Dvd />
      <Dvd />
      <a @click="onRefresh">刷新</a>
      <div style="position: absolute; right: 0px; top: 0px" class="flex">
        <Select
          v-model="queryFormData.serv_type"
          placeholder="请选择类型"
          style="width: 150px"
          class="mr10"
          @on-change="onSearch"
          clearable
        >
          <Option v-for="item in optionsList.servTypeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
        </Select>

        <Select
          v-model="queryFormData.source_platform"
          placeholder="请选择来源"
          style="width: 150px"
          class="mr10"
          @on-change="onSearch"
          clearable
        >
          <Option v-for="item in optionsList.sourcePlatformDesc" :value="item.id" :key="item.id"
            >{{ item.desc }}
          </Option>
        </Select>

        <Input
          v-model="queryFormData.keyword"
          placeholder="输入服务名称"
          clearable
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          style="width: 150px"
        >
          <Icon type="ios-search" slot="suffix" />
        </Input>
      </div>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; overflow: auto">
      <Table
        ref="selection"
        height="415"
        @on-select="onSelect"
        @on-select-cancel="onSelectCancel"
        @on-select-all="onSelectAll"
        @on-select-all-cancel="onSelectAllCancel"
        :columns="tableCols"
        :data="list"
        :loading="tableLoading"
      >
        <template slot-scope="{ row }" slot="info">
          <span class="clip">
            <KLink :to="{ path: '/goods/service/list', query: { id: row.id } }" target="_blank">{{ row.name }}</KLink>
          </span>
        </template>
        <template slot-scope="{ row }" slot="price">
          <p v-if="row.price">￥{{ row.price | number_format }}</p>
          <p v-else>-</p>
        </template>
      </Table>
    </div>
    <div class="block_20"></div>

    <KPage
      v-if="total > 0"
      :total="total"
      :page-size.sync="queryFormData.pageSize"
      :page-size-opts="[5, 10, 15, 20]"
      :current.sync="queryFormData.page"
      @on-change="onPageChange"
      style="text-align: center"
    />
    <div slot="footer">
      <div v-if="Object.keys(selected_items).length > 0" style="display: inline-block" class="lr15 text-muted">
        已选: 服务(<span class="text-error">{{ Object.keys(selected_items).length }}</span
        >)
      </div>
      <Button @click="modalValue = false">取消</Button>
      <Button type="primary" @click="onConfirm">确定</Button>
    </div>
  </Modal>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */

let init_query_from_data = {
  page: 1,
  pageSize: 20,
  serv_type: '',
  source_platform: '',
  keyword: '',
  status: 'ON'
};

export default {
  name: 'k-goods-services',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    disabledItemIds: {
      type: Array,
      default() {
        return [];
      }
    },
    checkedService: {
      type: Array,
      default() {
        return [];
      }
    },
    optionsList: {
      type: Object,
      default() {
        return {
          servTypeDesc: [],
          sourcePlatformDesc: []
        };
      }
    }
  },

  data() {
    return {
      queryFormData: { ...init_query_from_data },
      modalValue: false,
      tableCols: [
        { type: 'selection', width: 60 },
        { title: '服务', slot: 'info' },
        { title: '类型', key: 'serv_type_text' },
        { title: '来源', key: 'source_platform_text' },
        { title: '价格', slot: 'price', width: 100 }
      ],
      tableLoading: false,
      list: [],
      servTypeDesc: [],
      sourcePlatformDesc: [],
      total: 0,
      statusDesc: {},

      selected_items: {}
    };
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.get();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.get();
    },

    onRefresh: function () {
      this.get();
    },

    onSelect: function (selection, row) {
      this.$set(this.selected_items, row.id, row);
    },

    onSelectAll: function (selection) {
      selection.forEach(item => {
        this.$set(this.selected_items, item.id, item);
      });
    },

    onSelectCancel: function (selection, row) {
      this.$delete(this.selected_items, row.id);
    },

    onSelectAllCancel: function (selection) {
      for (let k in this.list) {
        this.$delete(this.selected_items, this.list[k].id);
      }
    },

    onConfirm: function () {
      let items = [];
      for (let key in this.selected_items) {
        items.push(this.selected_items[key]);
      }
      this.$emit('on-selected', items);
    },

    get: function () {
      this.tableLoading = true;

      io.get('clinic/goods.goodsservice.list', { data: { ...this.queryFormData } })
        .then(data => {
          this.list = this.handler(data.list);
          this.total = data.total;

          this.tableLoading = false;
        })
        .catch(error => {
          {};
        });
    },

    handler: function (list) {
      for (let k in list) {
        for (let j in this.selected_items) {
          if (list[k].id == this.selected_items[j].id) {
            list[k]['_checked'] = true; // 选中已选项
          }
        }

        if (S.inArray(Number(list[k].id), this.disabledItemIds)) {
          list[k]['_disabled'] = true; // 选中已选项
        }
      }
      return list;
    },

    clearQuery: function () {
      this.queryFormData = { ...init_query_from_data };
      this.queryFormData.page = 1;
      this.list = [];
      this.total = 0;
      this.selected_items = {};
    }
  },

  watch: {
    value: function (val) {
      this.modalValue = val;
      if (val) {
        console.log(this.optionsList);
        this.clearQuery();
        // 选中渲染
        this.onSelectAll(this.checkedService);
        this.get();
      }
    },

    modalValue: function (val) {
      this.$emit('input', val);
    },

    selected_items: function (val) {
      // S.log(this.selected_items)
    }
  }
};
</script>

<style lang="less">
//::v-deep .ivu-modal-body {
//  padding: 20px 30px;
//  height: calc( ~"100% - 110px" );
//  overflow-y: auto;
//}
//::v-deep .ivu-modal {
//  height: calc( ~"100% - 100px" ) !important;
//}
//::v-deep .ivu-modal-content {
//  height: calc( ~"100% - 100px" );
//}

.clip {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 90%;
  display: inline-block;
}
</style>
