<template>
  <div :id="chartRef" :ref="chartRef" :style="{ height: height, width: width }"></div>
</template>

<script>
import { debounce } from 'lodash'
import { addListener, removeListener } from 'resize-detector'

export default {
  name: 'chart-view',
  mixins: [],
  components: {},
  props: {
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    autoResize: {
      type: Boolean,
      default: true
    },
    chartOption: {
      type: Object,
      required: true
    },
    type: {
      type: String,
      default: 'canvas'
    },
    chartRef: {
      type: String,
      default: 'myChart'
    }
  },
  data() {
    return {
      chart: null,
      resizefn: null
    }
  },

  computed: {},

  watch: {
    height(val){
      // console.log("-> val", val)
      this.setOptions(this.chartOption)
    },
    chartOption: {
      deep: true,
      handler( newVal ) {
        // console.log("-> newVal", newVal)
        this.setOptions( newVal )
      },
    },
  },

  created() {
    this.resizefn = debounce(this.resize<PERSON><PERSON><PERSON>, 30)
  },
  mounted() {
    this.initChart()
    if ( this.autoResize ) {
      addListener(this.$refs[this.chartRef], this.resizefn)
    }
  },
  beforeDestroy() {
    if ( this.autoResize ) {
      removeListener(this.$refs[this.chartRef], this.resizefn)
    }
    this.chart.dispose()
    this.chart = null
  },
  destroyed() {
  },

  methods: {
    resizeHandler() {
      this.chart?.resize()
    },
    initChart() {
      // this.$nextTick(()=>{
      this.chart = this.$echarts.init( this.$refs[this.chartRef], '', {
        renderer: this.type
      } )
      if(this.chartOption){
        this.chart.setOption( this.chartOption )
      }
      // this.chart.on('click', function(params) {
      //   this.$emit('onClick', params)
      // })
      // })
    },
    handleClick( params ) {
      this.$emit( 'click', params )
    },
    setOptions( option ) {
      // console.log("-> option", option)
      this.clearChart()
      if ( this.chart ) {
        this.resizeHandler()
        this.chart.setOption( option )
      }
    },
    refresh() {
      this.setOptions( this.chartOption )
    },
    clearChart() {
      this.chart && this.chart.clear()
    }
  },
}
</script>

<style scoped lang="less">

</style>
