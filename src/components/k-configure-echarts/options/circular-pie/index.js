import { merge } from 'lodash';

/**
 * 环形嵌套图
 */
let colorList = ['#707CC2', '#EE7A5F', '#EDC760', '#6BCE99', '#7DD4E8', '#BE7BD5', '#CBD6A2', '#C87181'];

let defaultOptions = {
  series: [
    {
      data: [
        { value: 100, name: '肾' },
        { value: 100, name: '心包' },
        { value: 100, name: '脾' },
        { value: 100, name: '肝' },
      ],
    },
    {
      data: [
        { value: 18, name: '阴虚' },
        { value: 18, name: '热' },
        { value: 18, name: '湿' },
        { value: 46, name: '寒' },
      ],
    },
  ],
};
/**
 * innerIndex: 内环数量
 * outIndex: 外环数量
 * @description: 颜色匹配
 * */
const innerPie = (options = defaultOptions, innerIndex, outIndex) => {
  const defaultConfig = {
    grid: {
      bottom: '30%',
    },
    tooltip: {
      trigger: 'item',
      formatter: a => {
        return `${a.name}:${a.percent}%`;
      },
    },
    // legend: {
    // },
    series: [
      {
        name: 'pie',
        type: 'pie',
        // selectedMode: 'single', // 点击内环脱离效果
        radius: [0, '29%'], // 更改内环的大小
        center: ['52%', '50%'],
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          position: 'inner',
          fontSize: 12,
        },
        labelLine: {
          show: false,
        },
        color: colorList,
        data: [], // 内环数据
      },
      {
        name: 'pie',
        type: 'pie',
        radius: ['42%', '55%'], // 更改外环的大小
        center: ['52%', '50%'],
        // labelLine: {
        //   length: 30,
        // },
        itemStyle: {
          borderRadius: 6,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          borderWidth: 1,
          borderRadius: 4,
          formatter: a => {
            return `{a|${a.value}%}\n\n{b|}\n\n{c|${a.name}}`;
          },
          rich: {
            a: {
              padding: [0, -50, -20, -30],
            },
            b: {
              height: 10,
              width: 10,
              lineHeight: 10,
              marginBottom: 10,
              padding: [0, -20, 0, 20],
              borderRadius: 10,
              backgroundColor: {
                image: require('@/assets/image/echarts_circle.png'),
              },
            },
            c: {
              padding: [-10, -50, 0, -30],
            },
          },
        },
        labelLine: {
          // 引导线设置
          length: 22,
          length2: 28,
          cap: 'round',
          align: 'right',
          lineStyle: {
            type: 'dashed',
          },
        },
        color: colorList,
        data: [], // 外环数据
      },
    ],
  };
  const opt = merge(defaultConfig, options);
  return opt;
};

export default {
  innerPie,
};
