import { merge } from 'lodash'
import { number_format } from '@/libs/filters'

let colorList = ['#546FC8', '#A2D37F', '#FFD267', '#EB6B67', '#67C8EB', '#22A232', '#AA166F','#696969','#FFD700','#D2B48C']

// 文案居于左侧的横向柱状图
const horizontalBar = ( data ) => {
  const defaultConfig = {
    grid: {
      top: '10%',
      left: '160px',
      right: '10%',
      bottom: '6%'
    },
    tooltip: {
      show: true,
      formatter:(a)=>{
        let val = number_format(a.value,2)
        return `${a.name}：${val}元`
      }
    },
    yAxis: {
      type: 'category',
      position: 'left',
      data: [],
      axisTick: {
        show: false, // 去除x轴的刻度线
      },
      axisLabel: {
        interval: 0,
        inside: false,
        color: '#666', // 自定义x轴label的颜色
        fontSize: 12,
        verticalAlign: "middle",
        // padding: [0,0,0,-200],
        width: 156,
        // margin: '160',
        overflow: 'truncate',
        ellipsis: '...',
        align:'right',
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)',
        }
      },
    },
    xAxis: {
      type: 'value',
      name: '',
      position: 'bottom',
      nameLocation: 'start',
      data: [],
      nameTextStyle: {
        color: '#999999', // 设置y轴单位的颜色
        fontSize: 12,
      },
      axisTick: {
        show: false, // 去除y轴的刻度线
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      },
      splitLine: {
        show: true, // 显示y轴的网格线
        lineStyle: {
          color: '#CCCCCC',
          opacity: .3,
        }
      },
      // axisLabel: {
      //   formatter: ''
      // }
    },
    series: [
      {
        data: [],
        type: 'bar',
        // color: '#9CA4FF', // 更改柱体0的颜色
        barWidth: '14px', // 设置柱子的宽度
        label: {
          show: true,
          position: 'right',
          formatter:(a)=>{
            let val = number_format(a.value,2)
            return `¥ ${val}`
          },
          fontSize: 12,
          fontWeight: 'bolder'
        },
        itemStyle: {
          color: '#9CA4FF',
          label: {
            show: true,
            formatter: '{c}',
            position: 'right',
            textStyle: {
              color: '#1157E5',
              fontSize: 14
            }
          }
        },
      }
    ],
  }
  const opt = merge( defaultConfig, data )
  return opt
}

// 竖向柱状图
const verticalBar = ( data ) => {
  const defaultConfig = {
    grid: {
      top: '11%',
      left: '63',
      right: '4%',
      bottom: '15%',
      show: true,
      borderWidth: 0
    },
    tooltip: {
      show: true,
    },
    xAxis: {
      type: 'category',
      data: [],
      axisTick: {
        show: false, // 去除x轴的刻度线
      },
      axisLabel: {
        color: '#999', // 自定义x轴label的颜色
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      },
    },
    yAxis: {
      type: 'value',
      name: "人数",
      nameTextStyle: {
        color: '#999999', // 设置y轴单位的颜色
        fontSize: 14,
        padding: [0,0,-30,30], // 设置单位的位置
      },
      axisTick: {
        show: false, // 去除y轴的刻度线
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      },
      splitLine: {
        show: false, // 去除y轴的网格线
      },
      axisLabel: {
        formatter: ''
      }
    },
    series: [
      {
        data: [],
        type: 'bar',
        color: 'rgba(17, 87, 229, .3)', // 更改柱体的颜色
        label: {
          show: true,
          formatter: '{c}',
          position: 'top',
          textStyle: {
            color: '#1157E5',
            fontSize: 14
          }
        },
        barWidth: '30%', // 设置柱子的宽度
      }
    ],
  }
  const opt = merge( defaultConfig, data )
  return opt
}

// 文案居于柱状图上方
const customHorizontaBar = (data) => {
  const defaultConfig = {
    grid: {
      top: '10%',
      left:'4%',
      right: '10%',
      bottom: '8%'
    },
    tooltip: {
      show: true,
    },
    yAxis: {
      type: 'category',
      offset: 0,
      position: 'left',
      data: [ '商城购物订单', 'HIS诊疗处方单', '总订单',],
      // offset: -100,
      axisTick: {
        show: false, // 去除x轴的刻度线
      },
      axisLabel: {
        interval: 0,
        color: '#666666', // 自定义x轴label的颜色
        fontSize: 12,
        verticalAlign: "bottom",
        align: 'left',
        padding: [0,0,12,10],
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)',
        }
      },
    },
    xAxis: {
      type: 'value',
      name: "",
      nameTextStyle: {
        color: '#999999', // 设置y轴单位的颜色
        fontSize: 14,
        padding: [0,0,-30,60], // 设置单位的位置
      },
      axisTick: {
        show: false, // 去除y轴的刻度线
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(204, 204, 204, .5)'
        }
      },
      splitLine: {
        show: true, // 显示y轴的网格线
        lineStyle: {
          color: '#CCCCCC',
          opacity: .3,
        }
      },
      axisLabel: {
        formatter: ''
      }
    },
    series: [
      {
        data: [
          {
            value: 11,
            itemStyle: {
              color: '#E88E6D'
            }
          },
          {
            value: 120,
            itemStyle: {
              color: '#9CA4FF'
            }
          },
          {
            value: 14,
            itemStyle: {
              color: '#5AC5B1'
            }
          },
        ],
        type: 'bar',
        color: 'rgba(17, 87, 229, .3)', // 更改柱体的颜色
        barWidth: '20%', // 设置柱子的宽度
        // itemStyle: {
            label: {
              show: true,
              formatter: '{c}%',
              position: 'right',
              textStyle: {
                color: '#000',
                fontSize: 14
              }
            // }
        },
      }
    ],
  }
  const opt = merge( defaultConfig, data )
  return opt
}

/*
* @lowList 瀑布下层柱状图数据
* @dValueList 上层与下层数据相减数据
* @highList 瀑布上层柱状图数据
* */
// 瀑布柱状图
const waterfallBar = (data, lowList=[], dValueList=[], highList=[]) => {
  const defaultConfig =   {
    tooltip: {
      show: true, // 开启悬浮框
      trigger: 'axis',
      position: function(point, params, dom, rect, size) {
        // 其中point为当前鼠标的位置，size中有两个属性：viewSize和contentSize，分别为外层div和tooltip提示框的大小
        var x = point[0] //
        var y = point[1]
        var viewWidth = size.viewSize[0]
        var viewHeight = size.viewSize[1]
        var boxWidth = size.contentSize[0]
        var boxHeight = size.contentSize[1]
        var posX = 0 // x坐标位置
        var posY = 0 // y坐标位置

        if (x < boxWidth) { // 左边放不开
          posX = 5
        } else { // 左边放的下
          posX = x - boxWidth/2
        }

        if (y < boxHeight) { // 上边放不开
          posY = 5
        } else { // 上边放得下
          posY = y - boxHeight - 25
        }

        return [posX, posY]
      },
      padding: 1, // 边框距离内容的距离
      left: 'center',
      backgroundColor: '#CCCCCC', // 边框色
      borderRadius:4,
    },
    dataZoom: [{
      type: 'inside', //1平移 缩放
      throttle: '50', //设置触发视图刷新的频率。单位为毫秒（ms）。
      minValueSpan: 6, //用于限制窗口大小的最小值,在类目轴上可以设置为 5 表示 5 个类目
      maxValueSpan: 6, //用于限制窗口大小的最大值,在类目轴上可以设置为 5 表示 5 个类目
      // start: 1, //数据窗口范围的起始百分比 范围是：0 ~ 100。表示 0% ~ 100%。
      // end: 50, //数据窗口范围的结束百分比。范围是：0 ~ 100。
      zoomLock: true, //如果设置为 true 则锁定选择区域的大小，也就是说，只能平移，不能缩放。
    }],
    grid: {
      top: '11%',
      left: '3%',
      right: '4%',
      bottom: '10%',
      show: true,
      borderWidth: 0
    },
    xAxis: {
      type: 'category',
      axisTick: {
        show: false, // 去除y轴的刻度线
      },
      axisLabel:{
        color: "#999999",
        fontWeight: 400,
      },
      axisLine: {
        show: true, // 去除x轴
        lineStyle: {
          color: "#BFC5D6",
          width: 0.5
        }
      },
      itemStyle : {
        normal : {
          color:'#00FF00',
          lineStyle:{
              color:'#00FF00'
          }
        }
      },
      splitLine: { show: false },
      data: []
    },
    yAxis: {
      type: 'value',
      scale: true,
      min: function (value) {
        return  value.min - 10;
      },
      nameTextStyle: {
        color: '#999999', // 设置y轴单位的颜色
        fontSize: 14,
        align: 'center',
        padding: [0, 10, 10, 0], // 设置单位的位置
      },
      axisTick: {
        show: false, // 去除y轴的刻度线
      },
      axisLabel: {
        margin: 6,
        show: true,
        showMinLabel : false,
        interval: 'auto',
        formatter: '{value}'
      },

      axisLine: {
        show: true, // 去除y轴
        lineStyle: {
          width: 0.5,
          color: '#999999',
        }
      },
      splitLine: {
        show: false, // 关闭y轴的网格线
        lineStyle: {
          type: 'dashed',
          color: '#DDDDDD',
          opacity: 0.2
        }
      },
    },
    series: [
      {
        name: '',
        type: 'bar',
        stack: 'Total',
        itemStyle: {
          borderColor: 'transparent',
          color: 'transparent'
        },// 最下面的柱体颜色为透明色
        label: {
          show: true,
          position: 'insideTop',
          textStyle: {
            color: '#333',
            fontSize: 12
          }
        },
        data: lowList,
        barWidth: '30'
      },
      {
        name: '',
        type: 'bar',
        stack: 'Total',
        label: {
          show: true,
          position: 'top',
          textStyle: {
            color: '#333',
            fontSize: 12
          },
          formatter: function (params) {
            return highList[params.dataIndex] // params中获取index, 再去获取对应的收缩压
          }
        },
        data: dValueList
      }
    ]
  }
  const opt = merge( defaultConfig, data )
  return opt
}


export default {
  horizontalBar, verticalBar,customHorizontaBar, waterfallBar
}
