import { merge } from 'lodash'
//饼图
let colorList = [
  '#546FC8',
  '#A2D37F',
  '#EB6B67',
  '#67C8EB',
  '#22A232',
  '#AA166F',
  '#BACBE6',
  '#FFD700',
  '#D2B48C',
  '#C761E8',
  '#A495D1',
  '#B47E87',
  '#DA859B',
  '#E0E4C5',
  '#87529B',
  '#F6B8BB'
];
const orderPie = ( options, colorParamter ) => {
  const defaultConfig = {
    grid: {
      top: '10%',
    },
    // legend: {
    //   type: 'scroll',
    //   bottom: '26px',
    //   left: 'center'
    // },
    title: {
      text: '',
      subtext: '',
      left: 'center',
      top: 'bottom',
      padding: [10, 0],
      textStyle: {
        fontSize: 12,
        color: '#333',
        fontWeight:400,
      }
    },
    tooltip: {
      show: true,
      extraCssText: `z-index:2`,
      trigger: 'item',
      // formatter: ( a ) => {
      //   return `<div class="flex-col">
      //             <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${ a.data.title }</span>
      //             <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${ a.data.num || a.data.order_num }单</span>
      //             <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">¥${ a.data.turnover || a.data.money}</span>
      //             <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">占比</span>
      //             <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${ a.data.rate || a.data.percent}%</span>
      //           </div>
      //           `
      // },
      confine: false
    },
    series: [
      {
        name: '',
        color: colorParamter || colorList,
        type: 'pie',
        center: ['50%', '50%'], // ?!饼图的位置
        top: -40,
        stillShowZeroSum: true,
        showEmptyCircle: true,
        // minAngle: 10, // 此属性用来设置最小的占比，
        itemStyle: {
          borderRadius: 4
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgb(85, 114, 197,.6)'
          }
        },
        radius: [15, '65%'],
        label: {
          show: false,
          overflow: 'none',
          position: 'outer',
          formatter: function ( e ) {
            let data = e.data
            return `{a|${ data.title }}\n{v|${ data.value }单}\n{v|¥${ data.turnover || data.money }}\n{v|金额占比}\n{v|${ data.rate || data.percent }%}`
          },
          rich: {
            a: {
              color: '#333333',
              fontSize: 13,
              lineHeight: 16,
              fontWeight:'bold',
              align: 'left'
            },
            v: {
              fontSize: 11,
              lineHeight: 16,
              align: 'left',
              fontWeight: 300,
              color: '#A7AFC0'
            },

          }
        },
        labelLine: {
          show: false,   //引导线显示
          minTurnAngle: 140,
          length: 12,
          length2: 18,
          emphasis: {
            show: true
          }
        },
      },

    ],
  }
  const opt = merge( defaultConfig, options )
  return opt
}

export default {
  orderPie
}
