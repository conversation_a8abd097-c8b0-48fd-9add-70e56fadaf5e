<template>
  <Modal
    :value="visible"
    :title="title"
    width="430px"
    :mask-closable="false"
    @on-visible-change="changeVisible"
    :class-name="className"
    transfer
    :closable="false"
  >
    <div class="content" v-if="isCustomContent">
      <slot name="content"></slot>
    </div>
    <div class="content" v-else>
      <!--      <div class="title" v-if="contentTitle">-->
      <!--        <Icon type="ios-help-circle" size="26" color="#ff9900"></Icon>-->
      <!--        <span class="h-title">{{ contentTitle }}</span>-->
      <!--      </div>-->
      <div class="content-text">
        <p>{{ contentText }}</p>
      </div>
    </div>
    <div slot="footer">
      <Button v-if="showCancel" @click="cancel">{{ cancelText }}</Button>
      <Button v-if="showConfirm" type="primary" @click="confirmPass">{{ confirmText }}</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'TipsModal',
  mixins: [],

  components: {},
  model: {
    prop: 'visible',
    event: 'update:visible',
  },
  props: {
    title: {
      type: String,
      default: '温馨提示',
    },
    confirmText: {
      type: String,
      default: '确定',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    showConfirm: {
      type: Boolean,
      default: true,
    },
    visible: {
      type: Boolean,
      default: false,
    },
    contentText: {
      type: String,
      default: '确定要通过审核吗？',
    },
    isCustomContent: {
      type: Boolean,
      default: false,
    },
    className: {
      type: String,
      default: 'tips-modal vertical-center-modal',
    },
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.changeVisible(false);
      this.$emit('onCancel');
    },
    confirmPass() {
      this.$emit('onOk');
      this.changeVisible(false);
    },
    changeVisible(val) {
      !val && this.$emit('update:visible', val);
    },
  },
};
</script>

<style scoped lang="less">
.tips-modal {
  .content {
    padding: 0px;
  }
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

.title {
  display: flex;
  align-items: center;
  padding-left: 10px;

  .h-title {
    font-size: 15px;
    font-weight: 500;
    margin-left: 6px;
    line-height: 26px;
  }
}

.content-text {
  padding: 16px;
  line-height: 21px;
  font-size: 13px;
}
</style>
