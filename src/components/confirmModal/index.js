import Vue from 'vue';
import ConfirmModal from './confirmModal.vue';

const auditRefuseConstructor = Vue.extend(ConfirmModal);
let instance = null;

const TipModel = function (params) {
  return new Promise((resolve, reject) => {
    instance = new auditRefuseConstructor({
      propsData: {
        ...params,
      },
      methods: {
        confirmPass: res => {
          unmount();
          resolve(res);
          instance.$el.remove();
        },
        cancel: () => {
          unmount();
          reject();
          instance.$el.remove();
        },
      },
    });

    // 卸载组件
    const unmount = () => {
      instance.confirmVisible = false;
    };
    // 完成初始化
    instance.$mount();

    // 将组件添加到根节点body
    document.body.appendChild(instance.$el);
    instance.confirmVisible = true;
  });
};

export default TipModel;
