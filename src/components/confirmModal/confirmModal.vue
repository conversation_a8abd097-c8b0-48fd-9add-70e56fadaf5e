<template>
  <Modal
    :value="confirmVisible"
    :title="title"
    width="430px"
    :closable="false"
    @on-visible-change="changeVisible"
    class-name="confirm-modal"
    :mask-closable="maskClosable"
    transfer
  >
    <div slot="header"></div>
    <div class="content">
      <div class="title">
        <Icon type="ios-help-circle" size="26" color="#ff9900"></Icon>
        <span class="h-title">{{ content }}</span>
      </div>
      <div class="content-text">
        <p>{{ contentText }}</p>
      </div>
      <slot name="contentTxt"></slot>
    </div>
    <div slot="footer">
      <Button v-if="showCancel" size="default" @click="cancel">{{ cancelText }}</Button>
      <Button v-if="showOk" type="primary" @click="confirmPass" :loading="loading">{{ confirmText }}</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'confirmModal',
  mixins: [],

  components: {},

  props: {
    title: {
      type: String,
      default: '提示',
    },
    content: {
      type: String,
      default: '通过审核',
    },
    confirmVisible: {
      type: Boolean,
      default: false,
    },
    contentText: {
      type: String,
      default: '确定要通过审核吗？',
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    confirmText: {
      type: String,
      default: '确定',
    },
    maskClosable: {
      type: Boolean,
      default: false,
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    showOk: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {};
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.changeVisible(false);
      this.$emit('cancel');
    },
    confirmPass() {
      this.$emit('ok');
      this.$emit('update:confirmVisible', false);
    },
    changeVisible(val) {
      !val && this.$emit('update:confirmVisible', val);
    },
  },
};
</script>

<style scoped lang="less">
.confirm-modal {
  .content {
    padding: 0px;
  }
}

::v-deep .ivu-modal-header {
  display: none;
}

::v-deep .ivu-modal-footer {
  border-top: none;
}

.title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  padding-left: 10px;
  padding-top: 10px;

  .h-title {
    font-size: 15px;
    font-weight: 500;
    margin-left: 16px;
    line-height: 26px;
  }
}

.content-text {
  padding-left: 52px;
}
</style>
