<template>
  <Button @click="back" v-bind="$attrs" v-on="$listeners">
    <slot>返回</slot>
  </Button>
</template>
<script>
import store from '@/store';
export default {
  name: 'back-button',
  components: {},
  mixins: [],
  props: {},
  data() {
    return {};
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 针对外部跳转进详情页面，存在的返回按钮做统一处理
    back() {
      if (window.history.length === 1) {
        let routes = store.state.router.routes;
        let currentRoutePid = this.$route.meta.p_id;
        console.log('-> currentRoutePid', currentRoutePid);
        let ParentRoute = {};
        routes.find(route => {
          return route.children.find(child => {
            if (child.id === currentRoutePid) {
              ParentRoute = child;
              return child;
            }
          });
        });
        if (!ParentRoute.path) {
          return this.$Message.error('无法返回，请联系开发人员');
        }
        this.$router.replace(`${ParentRoute.path}`);
        return;
      }
      const curRoutePath = this.$route.path;
      if (curRoutePath === '/user/detail') {
        this.$router.replace('/user/list');
        return;
      }
      // if (curRoutePath === '/service/card/verification') {
      //   this.$router.replace('/service/card/list');
      //   return;
      // }
      this.$router.back();
    },
  },
  filters: {},
};
</script>
<style lang="scss" scoped></style>
