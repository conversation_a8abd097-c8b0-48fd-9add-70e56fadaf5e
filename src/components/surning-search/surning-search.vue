<template>
  <div class="flex" style="margin-top: 1px;">
    <Select v-if="showType" v-model="ent_type" style="width: 80px" @on-change="changeEntType" placeholder="全部" clearable>
      <Option value="CLI">诊所</Option>
      <Option value="RXJ">养疗馆</Option>
    </Select>
    <Select
        ref="surning"
        transfer
        :value="value"
        :clearable="isClearable"
        :loading="searchLoading"
        :remote-method="search"
        filterable
        @on-clear="clearSub"
        @on-query-change="queryChange"
        class="filterable-select"
        :placeholder="placeholder"
        @on-select="selectSup">
      <Option value="" v-if="showAll">全部</Option>
      <Option v-for="(option, index) in surning_list" :key="index" :value="option.ent_code">{{ option.ent_name }}</Option>
    </Select>
  </div>

</template>

<script>
import util from '@/libs/util'
export default {
	name: 'surning-search',
	components: {},
	mixins: [],
	props: {
		isClearable: {
			type: Boolean,
			default: true,
		},
		value: {
			type: String,
			default: ''
		},
    showAll: {
      type: Boolean,
      default: true
    },
		type: {
			type: String,
			default: ''
		},
		// prepayment: 表示放出省公司，默认不放
		source: {
			type: String,
			default: ''
		},
    placeholder: {
      type: String,
      default: '请输入搜索采购主体'
    },
    // 展示采购主体类型，目前只开放采购单，后续全部放开可移除该属性
    showType: {
      type: Boolean,
      default: false
    },
    p_ent_type: {
      type: String,
      default: ''
    }

	},
	data() {
		return {
			searchLoading: false,
			surning_list: [],
			query: '',
      currentLabel: "20155b65-2ea3-44c5-9897-d0efaefbffae",
      ent_type: ''
		}
	},
	computed: {
	},
	watch: {
    p_ent_type: {
      handler: function (val, oldVal) {
        console.log("-> %c val, oldVal  === %o", "font-size: 15px;color: green;", val, oldVal)
       this.ent_type = val
      },immediate: true
    },
  },
	created() {
		if (!this.$route.query.ent_code) {
			this.searchMethod()
		}else{
			let list = JSON.parse(localStorage.getItem('surning_list')) || []
			this.surning_list = list
			this.$emit('input', list[0] && list[0].ent_code)
			this.$emit('getType', list[0] && list[0].ent_type)
		}
	},
	mounted() {},
	methods: {
		searchMethod: util.debounce(function (query) {
			console.log('query', query)
			this.searchLoading = true
			let params = {
				name: query || '',
        ent_type: this.ent_type,
				source: this.source || ''
			}
			this.$api.searchPurchasesubject(params).then(res => {
				this.searchLoading = false
				this.surning_list = res.list
        console.log("-> %c this.surning_list  === %o ", "font-size: 24px;color:#67C23A ", this.surning_list)
				localStorage.setItem('surning_list', JSON.stringify(this.surning_list))
			})
		},200),
		search () {},
		selectSup(val) {
			let type = ''
			this.surning_list.some( item => {
				if ( item.ent_code == val.value ) {
					type = item.ent_type
				}
			} )
			this.$emit('input', val.value)
			this.$emit('getType', this.ent_type || type)
      this.$emit('change', val)
      this.currentLabel = val.label
    },
		queryChange (val) {
      if(val === this.currentLabel)return
      this.searchMethod(val)
		},
		clear () { // 列表页重置时必须使用
			this.$refs['surning'].clearSingleSelect()
      this.ent_type = ''
		},
		clearSub(){
			this.searchMethod()
			this.$emit('input','')
			this.$emit('getType', '')
      this.$emit('change', '')
    },
    changeEntType(){
      this.$emit('input','')
      this.$emit('getType', this.ent_type)
      this.searchMethod()
    }
	},
}
</script>

<style lang="less" scoped>
.filterable-select{
	::v-deep .ivu-select-input{
		margin-top: -1px;
	}
}
</style>
