<template>
  <Select
    v-model="selValue"
    v-bind="$attrs"
    v-on="$listeners"
    transfer
    filterable
    clearable
    :loading="loading"
    :remote-method="search"
    @on-query-change="handleSearch"
    @on-select="handleChange"
    @on-clear="clear"
    @focus.native.capture="focus"
  >
    <Option v-for="option in data" :key="option.value" :value="option[dictValue]" :label="option[dictLabel]">
      <slot :option="option">
        {{ option[dictLabel] }}
      </slot>
    </Option>
  </Select>
</template>

<script>
import { debounce } from 'lodash'
export default {
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    extraParams: {
      type: Object,
      default: () => ({}),
    },
    dictLabel: {
      type: String,
      default: 'label',
    },
    dictValue: {
      type: String,
      default: 'value',
    },
    // 传入的接口方法
    apiName: {
      type: String,
      default: '',
      required: true,
    },
  },
  data() {
    return {
      page: 1,
      loading: false,
      data: [],
      keyword: '',
      loadLoading: false,
      selValue: '',
    }
  },
  watch: {
    value(val) {
      console.log('=>(index.vue:60) val', val)
      if (!val) {
        this.page = 1
        this.keyword = ''
        this.request()
      }
      this.selValue = val
    },
  },
  computed: {},
  mounted() {
    // this.selValue = this.value;
    console.log('-> %c this.value  ===    %o', 'font-size: 15px;color: #fa8c16 ;', this.value)
    this.$nextTick(() => {
      if (this.value) {
        this.request(this.value)
        this.selValue = this.value
      } else {
        this.request()
      }
    })
  },
  created() {},
  methods: {
    handleChange(item) {
      this.$emit('input', item.value)
    },
    request(key = '') {
      const params = {
        key,
        page: this.page,
        page_size: 20,
        ...this.extraParams,
      }
      this.$api[this.apiName](params)
        .then(res => {
          this.data = res.list
        })
        .catch(err => {})
        .finally(() => (this.loading = false))
    },
    // 选中下拉框没有数据时，自动请求第一页的数据
    focus() {
      if (!this.data?.length) {
        this.request()
      }
    },
    handleSearch: debounce(function (keyword) {
      console.log('=>(index.vue:107) keyword', keyword)
      console.log('=>(index.vue:109) this.selValue', this.selValue)
      this.keyword = keyword
      this.loading = true
      this.page = 1
      this.request(keyword)
    }, 200),
    // 删除选中时，如果请求了关键字，则清除关键字再请求第一页的数据
    clear() {
      this.page = 1
      this.keyword = ''
      this.request()
    },
    search() {},
  },
}
</script>
