<template>
  <Modal
    :value="visible"
    title="选择商品"
    :mask-closable="false"
    :width="860"
    class-name="vertical-center-modal"
    footer-hide
    @on-visible-change="onVisibleChange"
  >
    <div style="position: relative">
      <Button to="/goods/item/list" target="_blank">商品管理</Button>
      <Dvd />
      <Dvd />
      <a @click="onRefresh">刷新</a>
      <div style="position: absolute; right: 0px; top: 0px" class="flex">
        <!--        <Select-->
        <!--          v-model="queryFormData.serv_type"-->
        <!--          placeholder="请选择类型"-->
        <!--          style="width: 150px"-->
        <!--          class="mr10"-->
        <!--          @on-change="get"-->
        <!--          clearable-->
        <!--        >-->
        <!--          <Option v-for="item in optionsList.servTypeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>-->
        <!--        </Select>-->
        <Select
          v-model="queryFormData.goods_type"
          placeholder="请选择类型"
          style="width: 150px"
          class="mr10"
          @on-change="onSearch"
          clearable
        >
          <Option v-for="item in optionsList.goodsTypeDesc" :value="item.id" :key="item.id">{{ item.desc }}</Option>
        </Select>

        <Select
          v-model="queryFormData.source_platform"
          placeholder="请选择来源"
          style="width: 150px"
          class="mr10"
          @on-change="onSearch"
          clearable
        >
          <Option v-for="item in optionsList.sourcePlatformDesc" :value="item.id" :key="item.id"
            >{{ item.desc }}
          </Option>
        </Select>
        <Input
          v-model="queryFormData.goods_keyword"
          placeholder="搜索商品标题或者商品ID"
          clearable
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          style="width: 180px"
        >
          <Icon type="ios-search" slot="suffix" />
        </Input>
      </div>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; height: 445px; overflow: auto">
      <Table :columns="tableCols" :data="list" :loading="tableLoading" height="380">
        <template slot-scope="{ row }" slot="id">
          {{ row.id }}
        </template>
        <template slot-scope="{ row }" slot="info">
          <div class="media-left media-middle">
            <img
              :src="row.main_img | imageStyle"
              style="width: 35px; margin-right: 5px"
              class="img-rounded"
              :title="'id:' + row.id"
            />
          </div>
          <div class="media-body">
            <div>
              <KLink :to="{ path: '/goods/item/list', query: { id: row.id } }" target="_blank">{{ row.name }}</KLink>
            </div>
            <div>￥ {{ row.price }}</div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="status">
          {{ statusDesc[row.status].desc }}
        </template>
        <template slot-scope="{ row }" slot="operate">
          <Button type="primary" size="small" @click="onSelected(row)">选择商品</Button>
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage
        v-if="total > 0"
        :total="total"
        :page-size.sync="queryFormData.pageSize"
        :page-size-opts="[5, 10, 20]"
        :current.sync="queryFormData.page"
        @on-change="onPageChange"
        style="text-align: center"
      />
    </div>
  </Modal>
</template>

<script>
/* eslint-disable */
import io from '@/libs/io'; // Http request
import S from 'libs/util';
/* eslint-disable */

let init_query_from_data = {
  page: 1,
  pageSize: 10,
  goods_keyword: '',
  xn_scope: '0,1,9',
  status_list: [],
  goods_type: '',
};

export default {
  name: 'k-goods-select',
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      queryFormData: { ...init_query_from_data },
      modalValue: false,
      tableCols: [
        { title: 'ID', slot: 'id', width: 50 },
        { title: '商品信息', slot: 'info' },
        { title: '商品类型', key: 'goods_type_text' },
        { title: '来源', key: 'source_platform_text' },
        { title: '状态', slot: 'status', width: 100 },
        { title: '操作', slot: 'operate', width: 100 },
      ],
      tableLoading: false,

      list: [],
      total: 0,
      statusDesc: {},
      optionsList: {
        servTypeDesc: [],
        sourcePlatformDesc: [],
        goodsTypeDesc: [],
      },
    };
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.get();
    },

    onPageChange: function (page, pageSize) {
      console.log('-> %c page  ===    %o', 'font-size: 15px;color: #fa8c16 ;', page);
      // console.log(page,pageSize,'asd')
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.get();
    },
    getList() {
      this.queryFormData.page = 1;
      this.queryFormData.pageSize = 10;
      this.get();
    },
    onRefresh: function () {
      this.get();
    },

    onSelected: function (item) {
      this.$emit('on-selected', item);
    },

    get: function () {
      this.tableLoading = true;

      io.get('clinic/goods.index.list', { data: this.queryFormData })
        .then(data => {
          this.list = this.handler(data.goods_items);
          this.statusDesc = data.statusDesc;
          this.total = data.total;

          this.tableLoading = false;
        })
        .catch(error => {
          {
          }
        });
    },

    handler: function (list) {
      return list;
    },

    clearQuery: function () {
      this.queryFormData = { ...init_query_from_data };
      this.queryFormData.page = 1;
      this.list = [];
      this.total = 0;
    },
    onVisibleChange(v) {
      if (!v) {
        this.$emit('changeVisible', false);
      }
    },
    // 储值购买枚举值
    getIndexOptions() {
      this.$api.getIndexOptions().then(res => {
        this.optionsList.servTypeDesc = S.descToArrHandle(res.servTypeDesc);
        this.optionsList.goodsTypeDesc = S.descToArrHandle(res.goodsTypeDesc);
        this.optionsList.sourcePlatformDesc = S.descToArrHandle(res.sourcePlatformDesc);
      });
    },
  },
  watch: {
    visible: function (val) {
      if (val) {
        console.log('-> %c val  ===    %o', 'font-size: 15px;color: #fa8c16 ;', val);
        this.getIndexOptions();
        this.clearQuery();
        this.get();
      }
    },
  },
};
</script>

<style lang="less">
.clip {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 90%;
  display: inline-block;
}
</style>
