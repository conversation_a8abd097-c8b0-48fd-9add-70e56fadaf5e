<template>
  <div class="batch-upload">
    <Modal
      :value="importVisible"
      :mask-closable="false"
      :title="modalTitle"
      hide-footer
      @on-visible-change="visibleChange"
      class-name="tips-modal vertical-center-modal"
    >
      <div class="import-content" v-if="importVisible">
        <div class="flex flex-item-align">
          <p style="min-width: fit-content">导入表格：</p>
          <RExcelUpload :disabled="isUploadOnly && (errorList.length > 0 || successList.length > 0)" ref="excelUpload" btnType="text" btnText="选择表格" @excel-upload="excelUpload" />
          <a
            class="download cursor hover"
            @click="templateDownload"
            style="min-width: fit-content"
            v-show="downloadTemplateApiName"
            >下载导入模板</a
          >
        </div>
        <!-- 备注插槽 -->
        <slot name="remark">
          <div style="margin: 10px 0 20px; height: 20px"></div>
        </slot>
        <!-- btn -->
        <div class="flex flex-item-center" style="margin-top: 20px">
          <Button type="default" class="space6" @click="importCancel">取消</Button>
          <Button
            type="primary"
            class="spacce6"
            @click="importConfirm"
            :disabled="!validExcelList.length"
            :loading="excelUploadLoading"
            >导入
          </Button>
        </div>
        <!-- 错误报告 -->
        <div
          class="error-report flex flex-item-center"
          style="color: #aaaaaa"
          v-show="errorList.length || successList.length"
        >
          <p><span></span>本次导入结果：成功{{ succ_num }}条数据，失败{{ fail_num }}条数据</p>
          <!--          ，<span v-if="errorList.length">请修改正确后再导入</span>-->
          <p class="download-error cursor hover" v-show="Number(fail_num) > 0" @click="seeReport">查看错误报告</p>
        </div>
      </div>
      <div slot="footer"></div>
    </Modal>
    <KErrorReport
      :report-visible.sync="reportVisible"
      :report-list="errorList"
      :report-columns="reportColumns"
    ></KErrorReport>
  </div>
</template>

<script>
import RExcelUpload from '@/components/k-excel-upload/RExcelUploadV2.vue'
import KErrorReport from './KErrorReport'
import download from '@/mixins/downloadExcel'

export default {
  name: 'KBatchUpload',
  mixins: [download],

  components: { RExcelUpload, KErrorReport },

  props: {
    modalTitle: {
      type: String,
      default: '导入总部仓出库单'
    },
    importVisible: {
      type: Boolean,
      default: true
    },
    // 原为downloadApiName，此处改名因与mixin里的downloadExcel中的data同名
    downloadTemplateApiName: {
      type: String,
      default: '',
      required: true
    },
    excelKeyMap: {
      type: Object,
      default: () => {},
      required: true
    },
    importApiName: {
      type: String,
      default: '',
      required: true

    },
    // validateApiName: {
    //   type: String,
    //   default: '',
    //   required: true
    // },
    reportColumns: {
      type: Array,
      default: () => [],
      required: true
    },
    chunkNum: {
      type: Number,
      default: 50
    },
    restParams:{
      type: Object,
      default: () => {}
    },
    isImportDrugs:{
      type: Boolean,
      default: false
    },
    // 是否需要检查拦截
    isRequiredCheck: {
      type: Boolean,
      default: false
    },
    // 拦截提示时需展示的字段名
    errorKey: {
      type: String,
      default: '',
    },
    // 前置条件(必须使用promise，提供loading关闭条件)。默认无前置条件
    precondition: {
      type: Function,
      default: () => new Promise(resolve => {
        resolve(true)
      })
    },
    // 是否上传唯一
    isUploadOnly:{
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      reportVisible: false,
      validExcelList: [],
      errorList: [],// 错误报告列表
      isImportSuccess: false,// 是否导入成功
      excelUploadLoading: false,
      succ_num: 0,
      fail_num: 0,
      chunkIndex: 0,// 分片索引
      chunkItemsLength: 0,// 分片长度
      successList: [],// 导入成功列表
    }
  },

  computed: {
    canImport() {
      return !this.validExcelList.length
    },
  },

  watch: {},

  created() {
  },

  mounted() {
  },

  methods: {
    confirmHandle() {

    },
    importConfirm() {
      // this.saveExcel( this.validExcelList )

      // 有导入的前置条件
      this.excelUploadLoading = true;
      this.precondition().then(res => {
        if(res){
          this.initBatchImportExcel()
        }else{
          this.excelUploadLoading = false;
        }
      })

    },
    importCancel() {
      this.initImportNums()
      this.$emit( 'update:importVisible', false )
      this.$emit( 'refresh' )
    },
    seeReport() {
      this.reportVisible = true
    },
    excelUpload( excelList ) {
      console.log( '-> %c excelList  === %o', 'font-size: 15px;color: green;', excelList )
      if ( !excelList.length ) {
        this.$Message.error( '请上传正确的表格' )
        return
      }
      const excelKeys = Object.keys( this.excelKeyMap )
      let requiredKeys = []
      let resultList = []
      excelList.forEach( item => {
        let temObj = {}
        for ( const excelKey of excelKeys ) {
          const { key, type='string' } = this.excelKeyMap[excelKey]
          if ( this.excelKeyMap[excelKey].required && !requiredKeys.includes( key ) ) {
            requiredKeys.push( key )
          }
          if ( item[excelKey] || item[excelKey] === 0 ) { // 兼容为0项
            temObj[key] = item[excelKey]
            if ( type === 'date' ) {
              temObj[key] = this.handleExcelDate( item[excelKey] )
            }
          }else {
            temObj[key] = ''
          }
        }
        resultList.push( temObj )
      } )
      console.log("-> resultList", resultList);
      let falseList = []
      const validExcelList = resultList.filter( item => {
        return requiredKeys.every( key => {
          // 检索必填字段未填的错误数据
          if(this.isRequiredCheck && !(item[key] || item[key] === 0)){
            falseList.push(item)
          }
          return item[key] || item[key] === 0
        } )// 兼容为0项
      } )
      if(this.isRequiredCheck && falseList.length > 0){
        let prodNameList = []
        falseList.forEach(item => {
          prodNameList.push(item[this.errorKey])
        })
        let errorStr = ''
        if(prodNameList.length > 4){
          errorStr = prodNameList.slice(0,4).join(',') + '等......'
        }else{
          errorStr = prodNameList.join(', ')
        }
        this.$Message.error({
          content: `产品信息有误，请检查表格内容。${falseList.length}个错误产品：${errorStr}`,
          duration: 3
        });
        return;
      }
      this.validExcelList = [...validExcelList]
      if ( !this.validExcelList.length ) {
        this.$Message.error( '请上传有效的表格数据' )
        return
      }
      this.$emit('validateList',this.validExcelList)
      this.$nextTick(()=>{
        this.initImportNums()
      })
      // this.initValidateExcel()
    },
    initImportNums() {
      if(this.successList.length||this.errorList.length){
       this.$emit('freshList')
      }
      this.chunkIndex = 0
      this.chunkItemsLength = 0
      this.succ_num = 0
      this.fail_num = 0
      this.successList = []
      this.errorList = []
    },
    // initValidateExcel() {
    //   let chunkItems = this.$lodash.chunk( this.validExcelList, this.chunkNum )
    //   console.log( '-> %c chunkItems  === %o', 'font-size: 15px;color: green;', chunkItems )
    //   this.chunkItemsLength = chunkItems.length
    //   this.batchValidateExcel( chunkItems[this.chunkIndex] )
    // },
    // batchValidateExcel( chunkList ) {
    //   if ( this.chunkIndex < +this.chunkItemsLength ) {
    //     this.$api[this.validateApiName]( { batch_params: chunkList } ).then( res => {
    //       console.log( '-> %c res  === %o', 'font-size: 15px;color: green;', res )
    //       this.succ_num += Number( res.succ_num )
    //       this.fail_num += Number( res.fail_num )
    //       this.errorList = [...this.errorList, ...res.fail_data]
    //       this.successList = [...this.successList, ...res.succ_data]
    //     } ).finally( () => {
    //       this.chunkIndex++
    //       if ( this.chunkIndex === this.chunkItemsLength ) {
    //         this.excelUploadLoading = false
    //         this.chunkIndex = 0
    //         this.chunkItemsLength = 0
    //       } else {
    //         this.initValidateExcel()
    //       }
    //     } )
    //   }
    // },
    initBatchImportExcel() {
      let chunkItems = this.$lodash.chunk( this.validExcelList, this.chunkNum )
      console.log( '-> %c chunkItems  === %o', 'font-size: 15px;color: green;', chunkItems )
      this.chunkItemsLength = chunkItems.length
      console.log("-> this.chunkItemsLength", this.chunkItemsLength);
      this.batchImportExcelList( chunkItems[this.chunkIndex] )
    },
    batchImportExcelList( chunkList ) {
      this.excelUploadLoading = true
      if ( this.chunkIndex < +this.chunkItemsLength ) {
        this.$api[this.importApiName]( { batch_params: chunkList,...this.restParams } )
          .then( res => {
          // if(this.isImportDrugs){
          //   console.log(this.isImportDrugs);
          //   const products = res.products
          //   const import_product = res.import_product.map(item=>{
          //     return {
          //       ...item,
          //       ...products[item.product_code]
          //     }
          //   })
          //   this.successList = [...this.successList, ...import_product]
          // }else {
            this.succ_num += Number( res.succ_num )
            this.fail_num += Number( res.fail_num )
            this.errorList = [...this.errorList, ...res.fail_data]
            this.successList = [...this.successList, ...res.succ_data]
          // }

        } )
          .catch(e=>{})
          .finally( () => {
            this.chunkIndex++
            if ( this.chunkIndex === this.chunkItemsLength ) {
              this.excelUploadLoading = false
              this.chunkIndex = 0
              this.chunkItemsLength = 0
              this.$Message.success( '导入数据完成' )
              this.$emit('emitSuccessList',this.successList)
              // 单独抛出额外事件做其他业务逻辑
              this.$emit('emitFinish', this.successList.length > 0)
              console.log("-> %c this.successList  === %o", "font-size: 15px;color: green;", this.successList)
              this.validExcelList = []
            } else {
              this.initBatchImportExcel()
            }
          } )
      }
    },
    templateDownload() {
      console.log( this.downloadTemplateApiName )
      this.$api[this.downloadTemplateApiName]().then( res => {
        this.download( res.url )
      } )
    },
    visibleChange( visible ) {
      if ( !visible ) {
        this.importCancel()
      }
    },
    handleExcelDate( time ) {
      if ( time == '' || time == undefined ) return ''
      if ( time.toString().includes( '-' ) || time.toString().includes( '/' ) ) {
        return time
      } else {
        return this.formatDate( time, '/' )
      }
    },

    formatDate( numb, format ) {
      const old = numb - 1
      const t = Math.round( (old - Math.floor( old )) * 24 * 60 * 60 )
      const time = new Date( 1900, 0, old, 0, 0, t )
      const year = time.getFullYear()
      const month = time.getMonth() + 1
      const date = time.getDate()
      return (
          year +
          format +
          (month < 10 ? '0' + month : month) +
          format +
          (date < 10 ? '0' + date : date)
      )
    }
  },

  destroyed() {
  },


}
</script>

<style scoped lang="less">
.download {
  margin-left: 10px;
}

.download {
  margin-left: 20px;
  color: rgb(155, 141, 141);
  border-bottom: 1px solid #ccc;
}

.error-report {
  margin-top: 20px;

  .download-error {
    margin-left: 15px;
    color: red;
  }
}

.cursor {
  cursor: pointer;
}

//.hover {
//  &:hover {
//    color: #155BD4 !important;
//  }
//}

p {
  margin-bottom: 0px;
}
</style>
