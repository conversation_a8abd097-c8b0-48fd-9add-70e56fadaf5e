<template>
  <div ref="lodopPrintDiv" class="lodopPrintDiv">
    <div class="order-code">
      处方单号: {{ labelInfo.order_code || '-' }}
    </div>

    <div class="content">
      <div class="content-left">
        <div class="patient-name">姓名:{{ labelInfo.pt_name }}</div>

<!--        <div class="content-left-dose-box">-->
<!--          <div class="dose">{{ labelInfo.package_num }}包（{{ labelInfo.day_num }}天*{{ labelInfo.day_use_times }}次）</div>-->
<!--          <div class="quantity">包装量:{{ quantity }} ml</div>-->
<!--        </div>-->
      </div>

      <div class="content-right">
        <div class="usage_methods">{{ labelInfo.usage_method_text }}</div>
      </div>
    </div>

    <div class="footer">
      <div class="clinic-name">{{ labelInfo.opd_name }}</div>
      <div class="tell">
        <div class="method">冷藏或遵医嘱保存</div>
        <div class="date">{{ labelInfo.print_time }}</div>
      </div>
    </div>

  </div>
</template>
<!-- https://static.rsjxx.com/file/printer_drive/CLodop_Setup_for_Win64NT_6.579EN.zip -->
<!-- https://static.rsjxx.com/file/printer_drive/GP-C200%20%E7%B3%BB%E5%88%97%E9%A9%B1%E5%8A%A8%20V1.1.zip -->

<script>
export default {
  name: 'lodopPrintDiv',
  props: {
    labelInfo: {
      type: Object,
      default: () => {}
    },
    quantity: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      printData: {},
    };
  },
  mounted() {
    this.getPrintData();
  },
  methods: {
    getPrintData() {
    }
  },
};
</script>
