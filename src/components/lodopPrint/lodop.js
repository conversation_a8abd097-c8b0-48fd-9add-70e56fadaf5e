export const definCss = `
<style>
* {
  box-sizing: border-box;
  font-size: 7pt;
}
div p {
  margin: 0;
  padding: 0;
}
.lodopPrintDiv {
 	overflow: hidden;
 	padding:3mm 3mm 3mm 5mm;
}

.order-code {
	font-size: 6pt;
	margin-bottom: 6pt;
	font-weight: 400;
}

.content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 4pt;
}

.content-left {
/*background: red;*/
}

.content-left .patient-name {
	font-size: 12pt;
	/*font-weight: bold;*/
	margin-bottom: 4pt;
	width: 40mm;
	overflow-y: hidden;
	white-space: nowrap;
	/*text-overflow: ellipsis;*/
	
} 
.content-left-dose-box .dose{
	font-size: 14pt;
	margin-bottom: 4pt;
	/*font-weight: 500;*/
}

.content-left-dose-box .quantity {
	/*font-weight: 6pt;*/
}

.content-right {
	padding-right: 10mm;
}
.content-right .usage_methods {
	width: 6mm;
	font-size: 14pt;
	/*font-weight: bold;*/
	border: 1pt solid #000;
	display: flex;
	justify-content: center;
	align-items: center;
}
.footer {
	/*padding-right: 0mm;*/
}
.footer .clinic-name {
	font-size: 8pt;
	/*font-weight: 300;*/
}

.footer .tell {
	margin-top: 3pt;
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	font-size: 6pt;
}
</style>
`;
