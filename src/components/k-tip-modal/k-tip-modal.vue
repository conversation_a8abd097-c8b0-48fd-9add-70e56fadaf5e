<template>
  <Modal
    :value="value"
    title=" "
    width="350"
    :mask-closable="false"
    class-name="tipModalClass"
    @on-visible-change="changeVisible"
  >
    <div class="modal-content">
      <div class="flex flex-item-center" style="padding: 10px 20px;">
       <svg-icon v-if="showIcon" iconClass="success" style="font-size: 20px;margin-right: 8px;"></svg-icon> {{ text }}
      </div>
    </div>

    <div slot="footer" class="edit-footer">
      <Button type="default" @click="closeModal">好的</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: "tip-modal",
  components: {
  },
  mixins: [],
  props: {
    value: {
      type: Boolean,
      default: () => false
    },
    text: {
      type: String,
      default: ''
    },
    showIcon:{
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
    }
  },
  computed: {

  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  methods: {
    changeVisible(visible){
      !visible&&this.closeModal()
    },
    // 关闭弹窗
    closeModal () {
      this.$emit('input', false)
    },
  },
  filters: {

  }
}
</script>
<style lang="less" scoped>
::v-deep .tipModalClass {
  .ivu-modal-body {
    padding: 0px;
    min-height: 10px !important;
  }
}
::v-deep .ivu-modal-header {
  border: none !important;
}
::v-deep .ivu-modal-footer {
  border: none !important;
}
</style>
