<template>
  <div class="audit-btn">
    <Button @click="auditRefuse" type="error">{{ buttonText }}</Button>
    <reason-modal
      :title="title"
      :label="label"
      v-model="refuseModalVisible"
      v-on="$listeners"
      :default-close="defaultClose"
    ></reason-modal>
  </div>
</template>

<script>
import ReasonModal from '../confirmModal/ReasonModal.vue';

export default {
  name: 'AuditRefuse',
  mixins: [],

  components: { ReasonModal },

  props: {
    title: {
      type: String,
    },
    label: {
      type: String,
    },
    buttonText: {
      type: String,
      default: '审核驳回',
    },
    defaultClose: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      refuseModalVisible: false,
      refuseReason: '',
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  methods: {
    auditRefuse() {
      this.refuseModalVisible = true;
    },
  },

  destroyed() {},
};
</script>

<style scoped lang="less">
.audit-btn {
  display: inline-block;
}
</style>
