<template>
  <div class="my-iframe" v-loading="loading">
    <iframe ref="webviewIframe" scrolling="auto" :src="url" :width="width" class="webview-iframe" @load="loadEvent" />
    <report-detail v-model="aiDetailVisible" :iframe-url="aiReportUrl" :title="dialogTitle"></report-detail>
  </div>
</template>

<script>
import ReportDetail from './components/detail.vue';
import * as runtime from '@/libs/runtime';

export default {
  components: {
    ReportDetail,
  },
  props: {
    width: {
      type: String,
      default: '424px',
    },

    url: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      aiDetailVisible: false,
      aiReportUrl: '',
      dialogTitle: '辨证分析',
      loading: true,
    };
  },
  mounted() {
    const iframe = this.$refs.webviewIframe;
    iframe.onload = () => {
      // this.onIframeLoaded(iframe)
      window.addEventListener('message', this.handleMessage);
    };
  },
  methods: {
    loadEvent() {
      this.loading = false;
    },
    onIframeLoaded(iframe) {
      console.log('-> %c iframe  ===    %o', 'font-size: 15px;color: #fa8c16 ;', iframe);
      iframe.style.height = iframe.contentWindow.document.body.scrollHeight + 'px';
      console.log('-> %c iframe  ===    %o', 'font-size: 15px;color: #fa8c16 ;', iframe.style);
      this.$refs.webviewIframe.contentWindow.postMessage('getIframeHeight', '*');
    },
    postMsgToIframe() {
      const iframe = this.$refs.webviewIframe;
      iframe?.contentWindow?.postMessage(
        {
          type: 'getXEnv',
          message: {
            plat: 'admin_hospital',
            uid: runtime.getUid(),
          },
        },
        '*'
      );
    },
    handleMessage(e) {
      if (e.data.type === 'webview') {
        if (e.data.message.event === 'checkMore') {
          this.dialogTitle = e.data.message.fullPath.indexOf('tongue') > -1 ? '舌象分析' : '辨证分析';
          this.aiReportUrl = e.data.message.fullPath;
          this.aiDetailVisible = true;
        } else if (e.data.message.event === 'getXEnv') {
          this.postMsgToIframe();
        }
      }
    },
  },
};
</script>

<style scoped lang="less">
.my-iframe {
  height: 100%;
  width: 425px;
  background: #f6f6f6;
  border-radius: 6px;
}

.webview-iframe {
  height: calc(100vh - 14px) !important;
  border: unset;
}
.webview-iframe::-webkit-scrollbar {
  display: none;
}
.webview-iframe ::v-deep .van-sticky {
  padding-top: 0 !important;
}
.webview-iframe iframe::-webkit-scrollbar {
  display: none;
}
</style>
