<template>
  <Modal
    :value="value"
    :mask-closable="false"
    class-name="vertical-center-modal custom-confirm-tip-modal"
    :title="title"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="text">
        以下商品存在着购买后无法成功赠券的情况，请确认后再进行其他操作：
      </div>

      <div class="mt-10 flex">
        <p v-for="(item, index) in list" :key="index">
          {{ index === list.length - 1 ? `商品【${item.goods_name}】${item.msg}` : `商品【${item.goods_name}】${item.msg}、` }}
        </p>
      </div>
    </div>
    <div slot="footer">
      <Button @click="cancel">返回修改</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">继续提交</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'ConfirmTipModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    },
    list: {
      type: Array,
      default: []
    },
  },

  data() {
    return {
      confirmLoading: false
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.$emit('input', false);
    },

    confirm() {
      this.$emit('success')
      this.cancel()
    },

    changeVisible(visible) {
      if (!visible) {
        this.cancel();
      }
    }
  }
};
</script>

<style scoped lang="less">
.content {
  margin-top: 20px;
  .text {
    font-size: 14px;
  }
}
::v-deep .custom-confirm-tip-modal {
  .ivu-modal-body {
    max-height: 500px;
    min-height: 150px;
    overflow-y: auto;
  }
}
</style>
