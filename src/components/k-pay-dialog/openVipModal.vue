<template>
  <div>
    <Modal
      ref="customModal"
      :value="value"
      width="840px"
      :title="title"
      :footer-hide="false"
      :mask-closable="false"
      :lock-scroll="true"
      class-name="vertical-center-modal"
      @on-visible-change="changeVisible"
    >
      <div class="content">
        <div class="info-box">
          <div class="avatar-box">
            <Avatar
              :src="row.avatar | imageStyle('B.200', 'http://static.rsjxx.com/rsjxx/2023/1220/151459_81411.png')"
              shape="square"
              icon="ios-person"
              class="avatar"
            />
          </div>
          <div class="info-wrapper">
            <div class="name">{{ row.real_name }}</div>
            <div class="mobile">{{ row.mobile || row.user_mobile }}</div>
          </div>
        </div>
        <div class="content-box" v-if="is_rst">
          <div class="content-left">
            <div class="card-name-box">
              <img class="name-img" src="https://img-sn01.rsjxx.com/image/2025/0616/152202_43895.png" />
            </div>
            <div class="card-content">
              <div class="card-style-box rst-card">
                <div class="card-text">榕粉权益卡 尊享三大好礼</div>
              </div>

              <div class="present-box">
                <div class="present-item" v-for="(item, index) in rstPresentList" :key="index">
                  <div class="present-title">
                    <img :src="item.title_img" alt="" class="title-img" />
                  </div>
                  <div class="present-desc">「{{ item.desc }}」</div>
                  <div class="present-service-box">
                    <div
                      class="service-item"
                      v-for="(service_item, service_index) in item.service_list"
                      :key="service_index"
                    >
                      <div class="service-left">
                        <img :src="service_item.icon" class="service-icon" alt="" />
                        <div class="service-name">{{ service_item.name }}</div>
                      </div>
                      <div class="service-right">
                        <img
                          src="https://img-sn01.rsjxx.com/image/2025/0616/160230_38108.png"
                          class="mark-icon"
                          alt=""
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content-box" v-if="!is_rst">
          <div class="content-left">
            <div class="card-name-box">
              <img class="name-img" src="https://static.rsjxx.com/image/2025/0215/140525_81664.png" />
            </div>
            <div class="card-content">
              <div class="card-style-box">
                <div class="card-text">980身份权益卡 尊享下方六大权益</div>
              </div>

              <div class="interest-box">
                <div class="interest-item" v-for="(item, index) in interestList" :key="index">
                  <img class="interest-icon" :src="interestIconObj[index]" />
                  <div class="title">{{ item.title }}</div>
                  <div class="desc">{{ item.desc }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer">
        <Button class="cancel-btn" @click="closeModal">取消</Button>
        <Button class="confirm-btn" :loading="confirmLoading" type="primary" @click="open">立即开通</Button>
      </div>
    </Modal>

    <!-- 支付 -->
    <template v-if="is_rst">
      <rst-pay-dialog
        v-model="rstPayVisible"
        :disabled-discount="disabledDiscount"
        :orderId="order_id"
        :is_rst="is_rst"
        :canUseRecharge="false"
        is_ry_order="1"
      ></rst-pay-dialog>
    </template>
    <template v-else>
      <k-pay-dialog
        v-model="payVisible"
        :order_id="order_id"
        :is_rst="is_rst"
        @changeVisible="changeVisible"
      ></k-pay-dialog>
    </template>
  </div>
</template>

<script>
import KPayDialog from '@/components/k-pay-dialog/index.vue';
import RstPayDialog from '@/components/k-pay-dialog/rst-pay-dialog.vue';
import { isRstClinic } from '@/libs/runtime';

export default {
  name: 'openVipModal',
  mixins: [],

  components: { RstPayDialog, KPayDialog },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '开通会员',
    },
    row: {
      type: Object,
      default: () => {},
    },
    interestList: {
      type: Array,
      default: () => [],
    },
  },

  data() {
    return {
      confirmLoading: false,
      interestIconObj: {
        0: 'https://static.rsjxx.com/image/2025/0215/141403_36450.png',
        1: 'https://static.rsjxx.com/image/2025/0215/143622_31659.png',
        2: 'https://static.rsjxx.com/image/2025/0215/143654_12577.png',
        3: 'https://static.rsjxx.com/image/2025/0215/143654_21446.png',
        4: 'https://static.rsjxx.com/image/2025/0215/143654_21446.png',
        5: 'https://static.rsjxx.com/image/2025/0215/143654_21446.png',
      },

      // 支付逻辑
      payVisible: false, // 支付弹窗显示的标识
      rstPayVisible: false, // 支付弹窗显示的标识
      order_id: '', // 订单id
      real_mobile: '',
      rstPresentList: [
        {
          title_img: 'https://img-sn01.rsjxx.com/image/2025/0616/155452_80873.png',
          desc: '价值424元礼包免费领',
          service_list: [
            { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/160230_48132.png', name: '祛湿除寒1次' },
            { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_75431.png', name: '葫芦灸1次' },
            { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_81065.png', name: '局部推拿1次' },
          ],
        },
        {
          title_img: 'https://img-sn01.rsjxx.com/image/2025/0616/164156_53219.png',
          desc: '健康消费更实惠',
          service_list: [
            { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_58505.png', name: '理疗服务专项会员价' },
            // { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_86755.png', name: '购买商品8折起' },
            // { icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_25053.png', name: '门诊处方9折起' },
          ],
        },
        // {
        //   title_img: 'https://img-sn01.rsjxx.com/image/2025/0616/164156_49497.png',
        //   desc: '消费越多回馈越多',
        //   service_list: [{ icon: 'https://img-sn01.rsjxx.com/image/2025/0616/163730_85272.png', name: '消费积分' }],
        // },
      ],
    };
  },

  computed: {
    is_rst() {
      return isRstClinic();
    },
    disabledDiscount() {
      if (this.is_rst) {
        return true;
      }
      return false;
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    // 检测是否存在开通中的订单
    getUserGetrywaitpayorder(row) {
      let params = {
        uid: row.uid,
      };
      this.$api.getUserGetrywaitpayorder(params).then(res => {
        this.order_id = res.order.id;
        this.real_mobile = res.real_mobile;
      });
    },
    open() {
      if (!this.order_id) {
        this.orderCreate();
      } else {
        this.preCollection();
      }
    },
    preCollection() {
      if (+this.order_id) {
        if (this.is_rst) {
          this.rstPayVisible = true;
        } else {
          this.payVisible = true;
        }
        this.closeModal();
      }
    },
    async init() {
      this.order_id = '';
      await this.getUserGetrywaitpayorder(this.row);
    },
    // 创建榕益卡订单
    orderCreate: async function () {
      this.confirmLoading = true;
      let params = {
        is_ry_order: 1, // 标识榕益卡订单
        version: 2,
        mobile: this.row.real_mobile || this.real_mobile,
        name: this.row.real_name,
        uid: this.row.uid,
        listof: {}, // 写死空对象
        service_of: {}, // 写死空对象,
        need_address: 0, //
        consignee_address: {},
      };
      await this.$api
        .orderCreate(params)
        .then(res => {
          this.order_id = res.id;
          this.preCollection();
        })
        .finally(() => (this.confirmLoading = false));
    },

    changeVisible(visible) {
      if (visible) {
        this.init();
      } else {
        this.closeModal();
      }
    },

    clearData() {},

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .ivu-modal-content {
  overflow: hidden;
  background: url('https://static.rsjxx.com/image/2025/0306/180203_52299.png') no-repeat;
  //background: url('https://static.rsjxx.com/image/2025/0215/125212_68168.png') no-repeat;
  background-size: cover;
  border-radius: 12px;
}
::v-deep .ivu-modal-body {
  max-height: 600px;
  min-height: 600px;
  overflow-y: auto;
}
::v-deep .ivu-modal-header {
  display: none;
}
::v-deep .ivu-modal-footer {
  border-top: none;
}

.content {
  .info-box {
    display: flex;
    align-items: center;
    .avatar-box {
      min-width: 36px;
      height: 36px;
      background: #fff;
      border-radius: 50%;
      margin-right: 6px;
      .avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
      }
    }
    .info-wrapper {
      .name {
        font-weight: 500;
        font-size: 16px;
        color: #333333;
        line-height: 22px;
      }
      .mobile {
        font-weight: 400;
        font-size: 14px;
        color: #827f73;
        line-height: 20px;
      }
    }
  }

  .content-box {
    margin-top: 20px;
    display: flex;
    .content-left {
      flex: 1;
      background: rgba(255, 255, 255, 0.93);
      box-shadow: 0px 0px 15px 0px #c1cfe2;
      border-radius: 12px;
      .card-name-box {
        height: 70px;
        //background: linear-gradient(180deg, #d4e4f7 0%, #ffffff 100%);
        background: linear-gradient(360deg, #d4e4f7 0%, #ffffff 100%);
        border-radius: 12px 12px 0px 0px;
        display: flex;
        justify-content: center;
        align-items: center;
        .name-img {
          width: 306px;
          height: 35px;
        }
      }
      .card-content {
        position: relative;
        padding: 12px;
        background: #fff;
        border-radius: 12px;
        .card-style-box {
          height: 175px;
          background: url('https://img-sn-i01s-cdn.rsjxx.com/image/2025/0227/104612_64307.png') no-repeat;
          background-size: cover;
          position: relative;
          border-radius: 6px;
          .card-text {
            position: absolute;
            left: 24px;
            bottom: 6px;
            font-weight: 400;
            font-size: 16px;
            color: rgba(88, 107, 136, 0.7);
            line-height: 26px;
          }
        }
        .rst-card {
          background: url('https://img-sn01.rsjxx.com/image/2025/0616/163527_14023.png') no-repeat;
          background-size: cover;
          margin-bottom: 12px;
          height: 180px;
        }

        .interest-box {
          display: flex;
          flex-wrap: wrap;
          .interest-item {
            width: 32%;
            margin-top: 10px;
            margin-right: 2%;
            background: #f4f8fe;
            border-radius: 8px;
            padding: 8px;
            &:nth-child(3n) {
              margin-right: 0px;
            }
            .interest-icon {
              width: 27px;
              height: 27px;
            }
            .title {
              margin-top: 10px;
              font-weight: 500;
              font-size: 14px;
              color: #536b93;
              line-height: 20px;
            }
            .desc {
              margin-top: 4px;
              font-weight: 400;
              font-size: 12px;
              color: #849cc2;
              line-height: 18px;
            }
          }
        }
        .present-box {
          display: flex;
          gap: 12px;
          justify-content: space-between;
          .present-item {
            flex: 1;

            border-radius: 8px;
            background-color: #f4f8fe;
            .present-title {
              height: 36px;
              background: linear-gradient(270deg, #d1e0f6 0%, #e2ebf9 100%);
              border-radius: 8px 8px 0 0;
              text-align: center;
              padding-top: 8px;
              .title-img {
                height: 17px;
              }
            }
            .present-desc {
              font-weight: 400;
              font-size: 12px;
              color: #8fa3c4;
              padding: 12px 0;
              text-align: center;
            }
            .present-service-box {
              padding: 0 10px;
              .service-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 14px 0 14px;
                border-top: 1px dashed #ced9e9;
                &:first-child {
                  border-top: 0;
                  padding-top: 0;
                }
                .service-left {
                  display: flex;
                  //align-items: center;
                  .service-icon {
                    width: 16px;
                    height: 16px;
                    margin-right: 5px;
                  }
                  .service-name {
                    font-weight: 500;
                    font-size: 13px;
                    color: #536b93;
                  }
                }
                .service-right {
                  .mark-icon {
                    width: 10px;
                    height: 8px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.cancel-btn {
  width: 104px;
  height: 38px;
  background: #f6faff;
  border-radius: 8px;
  border: 1px solid #b9c5da;
  font-weight: 400;
  font-size: 16px;
  color: #7689a7;
  line-height: 22px;
  text-align: center;
}
.confirm-btn {
  width: 104px;
  height: 38px;
  background: linear-gradient(270deg, #6686b5 0%, #83a4d5 53%, #6786b6 100%);
  border-radius: 8px;
  font-weight: 400;
  font-size: 16px;
  color: #ffffff;
  line-height: 22px;
  text-align: center;
  border: none;
}
</style>
