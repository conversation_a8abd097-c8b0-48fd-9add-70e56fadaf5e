<template>
  <div>
    <Modal
      :value="visible"
      class="k-collect-dialog"
      title="收款"
      class-name="vertical-center-modal"
      :width="840"
      :closable="true"
      :mask-closable="false"
      @on-visible-change="changeVisible"
    >
      <div class="title-wrapper" slot="header">
        收款
        <span v-if="first_pay_order === '1'" class="sales-promotion-text">
          <Icon type="md-alert" :size="16" class="mr-8" color="#FFC107" />
          用户首次到店</span
        >
      </div>
      <div class="content flex" v-if="visible">
        <div class="content-left flex-1">
          <div class="padding-block">
            <div>
              <div class="title-text">订单金额</div>
              <div class="order-money-block flex flex-item-align flex-item-between">
                <div class="label-text">应付金额</div>
                <div class="flex flex-item-align">
                  <span class="pay-money" v-text-format.number="ori_payment"></span>
                </div>
              </div>
            </div>

            <div class="mt-30">
              <div class="flex flex-item-align flex-item-between">
                <div class="title-text">优惠活动</div>
              </div>
              <Select
                :disabled="disabledDiscount"
                class="control_width"
                v-model="formData.sp_id"
                @on-change="changeVoucher"
                clearable
                placeholder="请选择优惠活动"
                size="large"
              >
                <Option v-for="item in activityList" :value="item.id" :key="item.id">
                  {{ item.name }}
                </Option>
              </Select>
            </div>
            <div class="discount-type-box mt-10 mb-10" v-if="discountRules.length > 0">
              <RadioGroup
                v-model="formData.sp_item_id"
                button-style="solid"
                type="button"
                @on-change="changeDiscountType"
              >
                <Radio v-for="item in discountRules" style="margin-bottom: 6px" :label="item.id" :key="item.id">
                  {{ item.name }}
                </Radio>
              </RadioGroup>
            </div>
            <div v-if="showDiscount" class="discount-input-box">
              <span class="discount-label-text flex-1">{{ getDiscountInfo.label }}</span>
              <span class="unit-text" v-if="formData.sp_item_id !== '1'">￥</span>
              <el-input-number
                :class="`control_width custom-number-input ${
                  formData.sp_item_id === '1' ? 'discount-input-box-percent' : ''
                }`"
                ref="discountInput"
                :max="Number(getDiscountInfo.max)"
                :min="Number(getDiscountInfo.min) || 0"
                :precision="getDiscountInfo.precision"
                :active-change="false"
                :placeholder="getDiscountInfo.placeholder"
                style="width: 88px"
                :controls="false"
                v-model="formData.sp_item_value"
                @change="calculateAmount"
                @focus="e => handleFocus(e)"
                @blur="handleLimitValue"
              />
              <span class="unit-text" v-if="formData.sp_item_id === '1'">折</span>
            </div>
            <div class="reduction-money">-<span v-text-format.number="discountAmount"></span></div>
          </div>

          <div class="actual-payment-block">
            <div style="color: #999">
              <span>结算明细</span>
              <el-tooltip effect="dark" placement="top-start" max-width="240">
                <Icon type="ios-arrow-dropright" class="ml-2 cursor" />
                <div class="pay-tooltip-content" slot="content">
                  <div class="pay-item">
                    <span class="pay-label">订单金额</span>
                    <span class="pay-amount" v-text-format.number="ori_payment"></span>
                  </div>
                  <div class="pay-item">
                    <span class="pay-label">优惠活动</span>
                    <span class="pay-amount">{{ getActivityName }}</span>
                  </div>
                  <div class="pay-item" v-for="(item, index) in discountList" :key="'discount' + index">
                    <span class="pay-label">{{ item.name }}</span>
                    <span> -<span class="pay-amount" v-text-format.number="item.discount_fee"></span> </span>
                  </div>

                  <div class="pay-item actual-pay-item">
                    <span class="pay-label">实际支付金额</span>
                    <span class="pay-amount" v-text-format.number="paymentFee"></span>
                  </div>
                </div>
              </el-tooltip>
            </div>
            <div>
              <span class="actual-pay-label">
                <el-tooltip effect="dark" placement="top-start" max-width="240">
                  <svg-icon class="cursor mr-4" name="tip"></svg-icon>
                  <div slot="content" class="discount-tips">注：实际支付金额不能低于订单应付金额的50%</div>
                </el-tooltip>
                实际支付</span
              >
              <span class="actual-pay-amount"
                ><span class="pay-symbol">￥</span>{{ Number(paymentFee).toFixed(2) }}</span
              >
            </div>
          </div>
        </div>

        <div class="content-right hidden-scroll flex-1">
          <div>
            <div class="title-text">收款方式</div>
            <div class="payment-methods-block">
              <div class="flex methods-block-item-wrapper">
                <div
                  v-for="method in paymentMethods"
                  :key="method.value"
                  class="payment-methods-block-item"
                  :class="{
                    'payment-methods-block-item--active': checkoutPayMethod === method.type,
                  }"
                  @click="checkoutPayMethod = method.type"
                >
                  <div>{{ method.name }}</div>
                  <div class="triangle" v-if="checkoutPayMethod === method.type">
                    <svg-icon iconClass="check-mark"></svg-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="payment-section">
            <div class="payment-wrapper" v-if="checkoutPayMethod !== 'other-pay'">
              <svg-icon :name="getPayIcon" class="qr-code-img" />
              <div class="tip-text">{{ getPayTip }}</div>
            </div>
            <div v-else class="offline-pay">
              <RadioGroup v-model="offlinePayMethod">
                <Radio :label="item.id" v-for="item in offlinePayMethods" :value="item.id" :key="item.id">
                  <img :src="item.imageUrl" />
                  <span>{{ item.pay_name }}</span>
                </Radio>
              </RadioGroup>
            </div>
          </div>

          <div>
            <div class="title-text mb-12">收款备注</div>
            <Input
              class="control_width"
              v-model="remark"
              maxlength="50"
              show-word-limit
              type="textarea"
              style="max-width: 100%"
              :autosize="{ minRows: 4, maxRows: 4 }"
              placeholder="请输入内容"
            />
          </div>
        </div>
      </div>
      <div slot="footer">
        <div class="flex flex-item-align flex-item-between">
          <div class="rst-tip">
            <span v-if="is_rst && is_ry_order == '1'">注：榕益健康会员权益卡只能原价购买</span>
          </div>
          <div>
            <Button @click="handleCancel">取消</Button>
            <Button
              type="primary"
              @click="handleNext"
              :loading="loading || blurLoading || calculateLoading"
              :disabled="!canSubmit"
              >{{ checkoutPayMethod === 'other-pay' ? '确认支付' : '下一步' }}
            </Button>
          </div>
        </div>
      </div>
    </Modal>

    <!-- 支付状态弹窗 -->
    <pay-status-dialog
      v-model="payStatusVisible"
      :is-scan-pay="isScanPay"
      :payment_fee="paymentFee"
      :code-url="codeUrl"
      :order_id="orderId"
      :trade_record_id="tradeRecordId"
      :scan-params="scanParams"
      :close-modal="handleCancel"
      @on-success="handlePaySuccess"
    />

    <!-- 支付成功弹窗 -->
    <success-pay-dialog
      v-model="successVisible"
      :success-data="successData"
      v-if="successVisible"
      :order_id="orderId"
    />
  </div>
</template>

<script>
import PayStatusDialog from './pay-status-dialog.vue';
import SuccessPayDialog from './pay-status/SuccessPayDialog.vue';
import { $operator } from '@/libs/operation';

export default {
  name: 'RstPayDialog',
  components: {
    PayStatusDialog,
    SuccessPayDialog,
  },
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    orderId: {
      type: String,
      default: '',
    },
    disabledDiscount: {
      type: Boolean,
      default: false,
    },
    is_rst: {
      type: Boolean,
      default: false,
    },
    is_ry_order: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      loading: false,
      paymentMethods: [
        { name: '扫码枪收款', desc: '使用扫码枪扫描客户付款码', type: 'scan-pay', icon: 'scan-pay-icon' },
        { name: '收款码收款', desc: '客户扫我的收款码向我付款', type: 'collection-pay', icon: 'code-pay-icon' },
        { name: '其他收款', type: 'other-pay' },
      ],
      discountAmount: 0,
      remark: '',
      activityList: [], // 这里可以通过API获取抵扣券列表
      voucherAmount: 0, // 抵扣券金额
      // 支付相关状态
      payStatusVisible: false,
      successVisible: false,
      isScanPay: false,
      paymentFee: '',
      codeUrl: '',
      tradeRecordId: '',
      scanParams: {},
      successData: {},
      ori_payment: 0,
      checkoutPayMethod: 'scan-pay', // scan-pay 扫码枪支付   collection-pay   收款码支付
      discountType: '1', // 1 折扣 2 立减 3 一口价
      amount: 0, // 订单金额
      extra_discount: undefined, // 额外优惠
      formData: {
        order_id: '',
        sp_id: '',
        sp_item_id: '',
        sp_item_value: '',
      },
      discountList: [],
      first_pay_order: '',
      canSubmit: true,
      calculateLoading: false,
      blurLoading: false,
      offlinePayMethods: [], // 线下支付方式列表
      offlinePayMethod: '', // 线下支付方式
      OfflinePaymentIcon: {
        CASH: require('@/assets/image/pay/collection/cash.png'), // 现金收款
        OFFLINE_WXCODE: require('@/assets/image/pay/collection/weixin.png'), // 微信收款
        OFFLINE_ALIPAY: require('@/assets/image/pay/collection/zhifubao.png'), // 支付宝收款
        OFFLINE_LAKALA: require('@/assets/image/pay/collection/lakala.png'), // 拉卡拉收款
        OFFLINE_UNION_PAY: require('@/assets/image/pay/collection/union.png'), // 银联收款
        OFFLINE_MEITUAN: require('@/assets/image/pay/collection/meituan.png'), // 美团团购
        OFFLINE_DOUYIN: require('@/assets/image/pay/collection/douyin.png'), // 抖音团购
        OFFLINE_YB: require('@/assets/image/pay/collection/yibao.png'), // 医保
        OFFLINE_HJK: require('@/assets/image/pay/collection/hjk.png'), // 好聚客
        OFFLINE_CLINIC_SK: require('@/assets/image/pay/collection/gonghu.png'), // 公户收款
        OFFLINE_CI_PAY: require('@/assets/image/pay/collection/shangbao.png'), // 商保收款
      },
    };
  },
  computed: {
    getActivityName() {
      return this.activityList.find(item => item.id === this.formData.sp_id)?.name || '';
    },
    discountRules() {
      if (!this.formData.sp_id) return [];
      return this.activityList.find(item => item.id === this.formData.sp_id)?.rule?.items || [];
    },
    // 实付金额
    getDiscountInfo() {
      const halfPrice = $operator.divide(this.ori_payment, 2);
      const discountMap = {
        1: {
          placeholder: '请输入折扣',
          label: '折扣',
          precision: 1,
          max: 9.9,
          min: 5,
        },
        2: {
          placeholder: '请输入金额',
          label: '立减',
          precision: 2,
          max: halfPrice,
          min: 0,
        },
        3: {
          placeholder: '请输入金额',
          label: '一口价',
          precision: 2,
          max: this.ori_payment,
          min: halfPrice,
        },
      };
      return discountMap[this.formData.sp_item_id];
    },
    showDiscount() {
      return this.formData.sp_id === '2'; // 这里可以判断是否显示抵扣券输入框
    },
    getPayIcon() {
      return this.getCurrentPayMethod.icon;
    },
    getPayTip() {
      return this.getCurrentPayMethod.desc;
    },
    getCurrentPayMethod() {
      return this.paymentMethods.find(item => item.type === this.checkoutPayMethod);
    },
  },
  watch: {},
  methods: {
    handleLimitValue() {
      this.canSubmit = true;
      this.blurLoading = true;
      setTimeout(() => {
        this.blurLoading = false;
      }, 300);
    },
    handleFocus(e) {
      e.target.select();
      this.canSubmit = false;
    },
    calculateAmount(v) {
      if (!v) {
        this.discountList = [];
        this.discountAmount = 0;
        this.paymentFee = this.ori_payment;
        return;
      }
      const params = {
        ...this.formData,
        order_id: this.orderId,
      };
      this.calculateLoading = true;
      this.$api
        .getRstOrderAmount(params)
        .then(res => {
          this.paymentFee = res.payment_fee;
          this.discountAmount = $operator.subtract(res.order_total_fee, res.payment_fee);
          this.discountList = res.discount_list;
        })
        .catch(err => {
          this.formData.sp_item_value = undefined;
        })
        .finally(() => {
          this.calculateLoading = false;
        });
    },
    changeDiscountType(val) {
      this.discountList = [];
      this.formData.sp_item_value = undefined;
      this.resetDiscount();
      this.focusDiscountInput();
    },
    resetDiscount() {
      this.discountAmount = 0;
      this.paymentFee = this.ori_payment;
    },
    changeVoucher(v) {
      console.log('%c=>(rst-pay-dialog.vue:297) v', 'color: #ECA233;font-size: 16px;', v);
      this.discountList = [];
      if (!v) {
        this.formData.sp_item_id = '';
        this.formData.sp_item_value = undefined;
        this.resetDiscount();
        this.discountList = [];
        return;
      }
      this.formData.sp_id = v;
      if (v === '2') {
        this.formData.sp_item_id = '';
        this.formData.sp_item_value = undefined;
        this.resetDiscount();
        if (this.discountRules.length) {
          this.formData.sp_item_id = this.discountRules[0].id;
          this.focusDiscountInput();
        }
        return;
      }
      if (v === '1') {
        this.formData.sp_item_id = '';
      }
      this.calculateAmount(true);
    },
    focusDiscountInput() {
      this.$nextTick(() => {
        console.log(this.$refs.discountInput);
        this.$refs.discountInput.select();
      });
    },
    changeVisible(visible) {
      if (visible) {
        this.checkoutPayMethod = 'scan-pay';
        this.getOfflinePayMethods();
        if (this.orderId) {
          this.getOrderInfo();
        }
        this.scanParams = {};
      } else {
        this.cancel();
        this.clearData();
      }
    },
    getOfflinePayMethods() {
      this.$api.getOrderPayOptions().then(res => {
        console.log('%c=>(rst-pay-dialog.vue:422) res', 'color: #ECA233;font-size: 16px;', res);
        this.offlinePayMethods = [];
        for (let pay_key in res.offline_pay_desc) {
          this.offlinePayMethods.push({
            id: pay_key,
            pay_name: res.offline_pay_desc[pay_key].desc,
            imageUrl: this.OfflinePaymentIcon[pay_key.toUpperCase()],
          });
        }

        this.offlinePayMethod = this.offlinePayMethods[0] && this.offlinePayMethods[0].id;
      });
    },
    cancel() {
      if (this.visible) {
        this.$emit('changeVisible', false);
      }
    },
    // 获取订单信息
    getOrderInfo() {
      // this.orderId
      this.$api.getOrderInfo({ order_id: this.orderId }).then(res => {
        this.ori_payment = res.order.ori_payment;
        this.activityList = res.sales_promotion;
        this.first_pay_order = res.first_pay_order;
        // 禁用优惠时，不默认选中活动
        if (!this.disabledDiscount) {
          this.formData.sp_id = this.activityList.find(item => item.type === '1')?.id || '';
        }
        if (this.formData.sp_id) {
          this.calculateAmount(true);
        }
        this.resetDiscount();
      });
    },
    handleCancel() {
      this.cancel();
      this.clearData();
    },
    async handleNext() {
      if (this.loading) return;
      this.loading = true;

      try {
        const sales_promotion = {
          sp_id: this.formData.sp_id,
          sp_item_id: this.formData.sp_item_id,
          sp_item_value: this.formData.sp_item_value,
        };
        if (!this.formData.sp_item_value) {
          if (sales_promotion.sp_id === '2') {
            sales_promotion.sp_id = '';
          }
          sales_promotion.sp_item_id = '';
          sales_promotion.sp_item_value = '';
        }
        const params = {
          order_id: this.orderId,
          remark: this.remark,
          pay_platform: 'ap',
          sales_promotion,
        };
        if (this.checkoutPayMethod === 'other-pay') {
          params.pay_platform = this.offlinePayMethod;
        } else {
          this.isScanPay = this.checkoutPayMethod === 'scan-pay';
          this.payStatusVisible = true;
          if (this.isScanPay) {
            this.wxpayment_fee = this.collected_amount;
            this.scanParams = params;
            // this.cancel();
            return;
          }
        }

        // 调用支付接口
        const res = await this.$api.orderPayShop(params);
        if (this.checkoutPayMethod !== 'other-pay') {
          this.trade_record_id = res.trade_record_id;
          this.codeUrl = res.pay_params.code_url;
          this.wxpayment_fee = res.payment_fee;
          this.cancel();
          this.payStatusVisible = true;
        } else {
          this.orderPayConfirm();
        }
        // 处理支付结果
      } catch (error) {
        this.$Message.error(error.message || '支付失败');
      } finally {
        this.loading = false;
      }
    },
    // 确认支付获取支付成功信息
    orderPayConfirm() {
      let params = {
        order_id: this.orderId,
      };
      this.$api.orderPayConfirm(params).then(res => {
        this.cancel();
        this.successVisible = true;
        this.successData = res;
      });
    },
    handlePaySuccess(data) {
      this.successData = data;
      this.payStatusVisible = false;
      this.successVisible = true;
    },
    // 清空数据
    clearData() {
      this.coupon_pay = '';
      this.remark = '';
      this.extra_discount = undefined;
      this.formData = {
        order_id: '',
        sp_id: '',
        sp_item_id: '',
        sp_item_value: undefined,
      };
      // this.scanParams = {}
    },
  },
};
</script>

<style lang="less" scoped>
.title-wrapper {
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  display: flex;
  align-items: center;

  .sales-promotion-text {
    font-size: 12px;
    padding: 8px 12px;
    background: rgba(249, 156, 63, 0.1);
    border-radius: 3px;
    color: #f99c3f;
    line-height: 16px;
    margin-left: 16px;
  }
}

.content {
  height: 490px;

  .title-text {
    height: 18px;
    font-size: 14px;
    font-weight: 400;
    color: #333333;
    line-height: 18px;
    margin-bottom: 16px;
  }

  .content-left {
    position: relative;
    height: 100% !important;

    &::after {
      content: ' ';
      background-image: url('~@/assets/image/pay/collection/lace_base.png');
      display: inline-block;
      width: 100%;
      height: 20px;
      bottom: -16px;
      position: absolute;
      background-repeat: round;
    }

    .padding-block {
      padding: 20px;
    }

    background: #fafbfc;
    box-shadow: 0px 2px 4px 0px rgba(236, 236, 236, 0.5);

    .order-money-block {
      width: 100%;
      padding: 20px 16px;
      background: #ffffff;
      border-radius: 4px;
      border: 1px solid #f3f3f5;

      .label-text {
        font-size: 14px;
        font-weight: 400;
        color: #bbbbbb;
        line-height: 18px;
      }

      .pay-money {
        font-size: 22px;
        font-weight: 500;
        color: #333333;
        line-height: 28px;
        margin-left: 12px;
      }
    }

    .reduction-money {
      font-size: 16px;
      font-weight: 400;
      color: #f74441;
      line-height: 18px;
      margin-right: 16px;
      text-align: right;
      margin-top: 4px;
    }

    // 实际支付
    .actual-payment-block {
      margin-right: 16px;
      border-top: 1px solid #f4f4f4;
      display: flex;
      align-items: center;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .actual-pay-label {
        font-size: 14px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        margin-right: 8px;
      }

      .actual-pay-amount {
        font-size: 22px;
        font-weight: 500;
        color: #f74441;
        line-height: 32px;

        .pay-symbol {
          font-size: 16px;
        }
      }
    }
  }

  .content-right {
    overflow-y: auto;
    padding: 20px;

    .payment-methods-block {
      position: relative;
      background: rgba(21, 91, 212, 0.01);
      border-radius: 4px;

      .methods-block-item-wrapper {
        .payment-methods-block-item {
          flex: 1;
          position: relative;
          cursor: pointer;
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          align-items: center;
          width: 110px;
          height: 54px;
          background: #ffffff;
          border-radius: 4px;
          border: 1px solid #dfe1e6;
          color: #666666;
          font-size: 14px;
          margin-right: 12px;

          &:last-child {
            margin-right: 0px;
          }

          &:hover {
            border: 1px solid #1157e5;
            color: #1157e5;
          }

          .triangle {
            width: 0;
            height: 0;
            border-bottom: 25px solid #1157e5;
            border-left: 25px solid transparent;
            position: absolute;
            right: 0px;
            bottom: 0px;

            .svg-icon {
              position: absolute;
              font-size: 14px;
              left: -15px;
              bottom: -25px;
              color: #fff;
            }
          }
        }

        .payment-methods-block-item--active {
          position: relative;
          border: 1px solid #1157e5;
          color: #1157e5;
          background: #f3f7ff;
        }
      }
    }

    .payment-section {
      text-align: center;
      min-height: 226px;
      padding-top: 10px;
      .payment-wrapper {
        display: inline-block;
        background: #ffffff;
        border-radius: 4px;
        margin-top: 20px;

        .qr-code-img {
          width: 280px;
          height: 163px;
          margin-bottom: 2px;
        }

        .tip-text {
          color: #aaaaaa;
        }
      }
    }

    .offline-pay {
      padding: 26px 10px 10px;
      border: 1px solid #f3f3f3;
      border-radius: 4px;

      :deep(.ivu-radio-group) {
        display: flex;
        flex-wrap: wrap;

        .ivu-radio-wrapper {
          margin-right: 4%;
          border-radius: 4px;
          flex: 1;
          min-width: 48%;
          max-width: 48%;
          height: 40px;
          background: #fff;
          border: 1px solid #f0f0f0;
          display: flex;
          align-items: center;
          padding-left: 10px;
          margin-bottom: 10px;
          &:nth-child(2n) {
            margin-right: 0;
          }
        }
      }

      img {
        width: 28px;
        height: 28px;
        margin-right: 8px;
      }
    }
  }
}

.discount-input-box {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #e3e5eb;
  font-size: 16px;
  color: #333333;
  line-height: 22px;

  .discount-label-text {
    color: #bbb;
  }
}

.discount-tips {
  color: #fff;
  line-height: 18px;
  text-align: right;
}

:deep(.custom-number-input) {
  background: #ffffff;
  border-radius: 0px;

  &:focus {
    box-shadow: none;
  }

  box-shadow: none !important;

  .el-input__inner {
    line-height: 32px;
    font-size: 16px;
    border: none;
    text-align: right;
    border-radius: 0px !important;
    border-bottom: 1px solid transparent;
    padding-left: 8px;
    padding-right: 8px;

    &::placeholder {
      font-size: 14px;
      color: #bbbbbb;
    }

    &:hover {
      border-bottom: 1px solid #1157e5;
    }
  }
}

:deep(.discount-input-box-percent) {
  .el-input__inner {
    padding-right: 8px;
  }
}

.k-collect-dialog {
  ::v-deep .ivu-modal-body {
    padding: 10px;
    max-height: 500px;
    min-height: 520px;
    overflow-y: auto;
  }

  ::v-deep .ivu-modal-header {
    padding: 12px 20px;
    border-bottom: 1px solid #e8e8e8;
    font-weight: 500;
    color: #333333;
    line-height: 22px;
  }
}

::v-deep .ivu-input-wrapper,
.ivu-select {
  max-width: unset;
}

::v-deep .ivu-select-selection,
.ivu-checkbox-inner {
  border: 1px solid #dcdee2;
}

.pay-tooltip-content {
  width: 216px;

  .pay-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    line-height: 18px;

    &:last-child {
      margin-bottom: 0;
    }

    .pay-label {
      color: rgba(255, 255, 255, 0.75);
    }

    .pay-value {
      color: #fff;
    }
  }

  .actual-pay-item {
    padding-top: 12px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}
.rst-tip {
  color: red;
}
</style>
