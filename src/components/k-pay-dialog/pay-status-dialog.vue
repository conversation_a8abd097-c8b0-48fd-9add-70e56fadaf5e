<template>
  <div>
    <Modal
      :mask-closable="false"
      :value="visible"
      @on-visible-change="visibleChange"
      :title="getTitle"
      width="840"
      footer-hide
      class-name="vertical-center-modal pay-status-dialog"
    >
      <div class="flex flex-item-center" style="height: 100%">
        <div v-show="isScanPay">
          <pay-loading v-show="payLoading"></pay-loading>
          <pay-failed v-if="isPayFailed" :close-modal="close"></pay-failed>
          <scan-pay
            ref="scanPay"
            :payment_fee="payment_fee"
            @payOk="payOk"
            @payFailed="payFailed"
            @showScanPayLoading="payLoading = true"
            :scanParams="scanParams"
            v-show="!payLoading && !isPayFailed"
            :isStorePay="isStorePay"
            :isLocalClose="isLocalClose"
          ></scan-pay>
        </div>
        <div v-show="!isScanPay" class="collection-pay">
          <collection-code
            ref="collectionCode"
            :codeUrl="codeUrl"
            :order_id="order_id"
            :trade_record_id="trade_record_id"
            :payment_fee="payment_fee"
            @payOk="payOk"
            :isStorePay="isStorePay"
          ></collection-code>
        </div>
      </div>
    </Modal>
    <!-- 支付成功 -->
    <success-pay-dialog
      v-model="successVisible"
      :successData="successData"
      :order_id="order_id"
      v-if="successVisible"
      :isStorePay="isStorePay"
      :isLocalClose="isLocalClose"
    ></success-pay-dialog>
    <Modal v-model="maskVisible" footer-hide :closable="false" class-name="maskClass vertical-center-modal">
      <div style="font-size: 16px; text-align: center">点击任意区域继续支付</div>
    </Modal>
  </div>
</template>

<script>
import ScanPay from './pay-status/scan-pay.vue';
import CollectionCode from './pay-status/collection-code.vue';
import PayLoading from './pay-status/pay-loading.vue';
// import WxCollectionCode from '../../view/trade/order/components/WxCollectionCode.vue';
import successPayDialog from './pay-status/SuccessPayDialog.vue';
import PayFailed from './pay-status/pay-failed.vue';

export default {
  name: 'PayStatusDialog',
  components: {
    // WxCollectionCode,
    successPayDialog,
    ScanPay,
    CollectionCode,
    PayLoading,
    PayFailed,
  },
  model: {
    prop: 'visible',
    event: 'changeVisible',
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },

    codeUrl: {
      type: String,
      default: '',
    },
    // 微信待支付金额
    payment_fee: {
      type: [String, Number],
      default: '',
    },
    order_id: {
      type: String,
      default: '',
    },
    trade_record_id: {
      type: String,
      default: '',
    },
    isScanPay: {
      type: Boolean,
      default: true,
    },
    scanParams: {
      type: Object,
      default: () => ({}),
    },
    isStorePay: {
      type: Boolean,
      default: false,
    },
    isLocalClose: {
      type: Boolean,
      default: false,
    },
    closeModal: {
      type: Function,
      default: () => {},
    },
  },
  data() {
    return {
      successVisible: false,
      successData: {
        payment_fee: 0,
        pay_platform_text: '',
        pay_time: '',
        remark: '',
      },
      payLoading: false,
      isPayFailed: false,
      maskVisible: false,
    };
  },
  watch: {
    successVisible(val) {
      if (val) {
        this.maskVisible = false;
      }
    },
  },
  computed: {
    getTitle() {
      if (this.isPayFailed) return '收款失败';
      return this.isScanPay ? '扫码支付' : '二维码收款';
    },
  },
  methods: {
    handleFocus() {
      this.maskVisible = false;
    },
    handleBlur() {
      if (this.$refs.scanPay?.scanner) {
        this.maskVisible = true;
      }
    },
    visibleChange(v) {
      const collectionCode = this.$refs.collectionCode;
      if (v) {
        this.payLoading = false;
        if (!this.isScanPay) {
          setTimeout(() => {
            collectionCode.waitPay();
          }, 300);
        } else {
          window.addEventListener('focus', this.handleFocus);
          window.addEventListener('blur', this.handleBlur);
          this.$nextTick(() => {
            this.$refs.scanPay.initScanner();
          });
        }
      } else {
        this.isPayFailed = false;
        if (!this.isScanPay) {
          collectionCode.clearIntervalEvent();
        } else {
          window.removeEventListener('focus', this.handleFocus);
          window.removeEventListener('blur', this.handleBlur);
          this.$refs.scanPay?.cancelScan();
        }
        this.$emit('changeVisible', v);
      }
    },
    payOk(successData) {
      this.successData = successData;
      this.payLoading = false;
      this.$emit('changeVisible', false);
      this.closeModal();
      this.successVisible = true;
      this.$emit('on-success')
    },
    payFailed() {
      this.payLoading = false;
      this.isPayFailed = true;
    },
    close() {
      this.$emit('changeVisible', false);
    },
  },
};
</script>

<style lang="less">
.pay-status-dialog {
  .ivu-modal-body {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 580px;
  }
}
</style>
