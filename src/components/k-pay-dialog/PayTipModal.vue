<template>
  <Modal
    :value="value"
    :mask-closable="false"
    class-name="vertical-center-modal custom-pay-tip-modal"
    :title="title"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      <div class="text">
        该订单包含赠券商品，只有通过原价购买（不包含储值支付）时才能正常发放；当前无法满足购买后赠券的条件，请知晓
      </div>
    </div>
    <div slot="footer">
      <Button @click="cancel">返回修改</Button>
      <Button :loading="confirmLoading" type="primary" @click="confirm">继续支付</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'PayTipModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '提示'
    }
  },

  data() {
    return {
      confirmLoading: false
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    cancel() {
      this.$emit('input', false);
    },

    confirm() {
      this.$emit('success')
      this.cancel()
    },

    changeVisible(visible) {
      if (!visible) {
        this.cancel();
      }
    }
  }
};
</script>

<style scoped lang="less">
.content {
  margin-top: 20px;
  .text {
    font-size: 14px;
  }
}

::v-deep .custom-pay-tip-modal {
  .ivu-modal-body {
    max-height: 500px;
    min-height: 150px;
    overflow-y: auto;
  }
}
</style>
