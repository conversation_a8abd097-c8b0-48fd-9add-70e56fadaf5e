<template>
  <div class="pay-failed">
    <svg-icon name="pay-failed" style="width: 156px; height: 110px"></svg-icon>
    <p class="failed-text">支付异常，请重新支付</p>
    <Button  @click="closeModal" type="primary">重新支付</Button>
  </div>
</template>

<script>
export default {
  name: 'PayFailed',
  props: {
    closeModal: {
      type: Function,
      default: () => {},
    },
  },
};
</script>

<style lang="less" scoped>
.pay-failed {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .failed-text {
    margin-top: 24px;
    font-size: 16px;
    color: #f74441;
    margin-bottom: 24px;
  }
}
</style>
