<template>
  <div class="pay-loading">
    <div class="pay-loading-icon">
      <svg-icon name="pay-loading" size="64"></svg-icon>
    </div>
    <div class="loading-text"></div>
  </div>
</template>

<script>
export default {
  name: 'PayLoading',
  props: {
    // props
  },
  data() {
    return {
      // data
    };
  },
  methods: {
    // methods
  },
};
</script>

<style lang="less" scoped>
.pay-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  .pay-loading-icon{
    margin-bottom: 24px;
  }
}

/* HTML: <div class="loader"></div> */
.loading-text {
  width: fit-content;
  font-weight: 500;
  font-family: monospace;
  font-size: 24px;
  clip-path: inset(0 100% 0 0);
  animation: l5 2s steps(11) infinite;
}

.loading-text:before {
  content: '正在支付中...';
}

@keyframes l5 {
  to {
    clip-path: inset(0 -1ch 0 0);
  }
}

</style>
