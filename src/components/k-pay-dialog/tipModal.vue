<template>
  <Modal
    ref="customModal"
    :value="value"
    width="500px"
    :title="title"
    :footer-hide="false"
    :mask-closable="false"
    :lock-scroll="true"
    class-name="custom-tip-modal vertical-center-modal"
    @on-visible-change="changeVisible"
  >
    <div class="content">
      {{ content }}
    </div>
    <div slot="footer">
      <Button :loading="confirmLoading" type="primary" @click="confirm">我知道了</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'emptyModal',
  mixins: [],

  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '提示',
    },
    content: {
      type: String,
      default: '',
    },
  },

  data() {
    return {
      confirmLoading: false,
    };
  },

  computed: {},

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    changeVisible(visible) {
      if (visible) {
      } else {
        this.closeModal();
      }
    },

    clearData() {},

    closeModal() {
      this.clearData();
      this.$emit('input', false);
    },

    confirm() {
      this.$emit('success');
      this.closeModal();
    },
  },
};
</script>
<style lang="less" scoped>
.content {
  padding: 30px;
  font-size: 14px;
}
</style>
<style lang="less">
.custom-tip-modal {
  .ivu-modal-body {
    max-height: 130px !important;
    min-height: 130px !important;
    overflow-y: auto;
  }
}
</style>
