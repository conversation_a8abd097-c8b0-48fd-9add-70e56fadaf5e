<template>
  <!-- 核销弹窗 -->
    <Modal
      :value="value"
      width="980"
      :mask-closable="false"
      @on-visible-change=visibleChange
      footer-hide
      >
      <div slot="header" class="modalHeader">
        <div style="font-size: 14px; margin-right: 5px;">兑换记录</div><span>(总次数{{totalNum}}, 剩余兑换次数{{surplusNum}})</span>
      </div>
      <div class="content">
        <Table :loading="tableLoading" :columns="exchangeColumns" :data="exchangeData" height="400">
          <!-- 生效时间 -->
          <template slot-scope="{row}" slot="create_time">
            {{row.create_time | data_format}}
          </template>

          <template slot-scope="{row}" slot="action">
            <a v-if="row.status == 'WAIT' || row.status == 'PART'" @click="getCardno(row)">核销</a>
            <div v-else>-</div>
          </template>
        </Table>
      </div>
    </Modal>
</template>

<script>
  export default {
    name: "exchange-detail",
    components: {

    },
    mixins: [],
    props: {
      value: {
        type: Boolean,
        default: () => false
      },
      exchangeId: {
        type: String,
        default: ''
      }
    },
    data () {
      return {
        exchangeColumns: [
          {title: '服务项目', key: 'card_name'},
          {title: '兑换数量', key: 'card_num',align:'center'},
          {title: '核销数量', key: 'used_num',align:'center'},
          {title: '兑换人', key: 'operator',align:'center'},
          {title: '兑换时间', slot: 'create_time',align:'center'},
          {title: '核销状态', key: 'status_text',align:'center'},
          {title: '操作', slot: 'action',align:'center'},
        ],// 卡券核销columns

        exchangeData: [], // 卡券核销数据
        tableLoading: false,
        totalNum: 0,
        surplusNum: 0,
      }
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {

    },
    methods: {
      visibleChange (val) {
        if (!val) {
          this.$nextTick(() => {
            this.$emit('input',false)
            this.exchangeData = []
          })
        }else{
          this.getExchangeCardInfo()
        }
      },

      // 跳转到核销页面
      jumpVerification (card_no) {
        this.$router.push({
          path: '/service/card/verification',
          query: {card: card_no}
        })
      },

      // 获取详情
      getCarddetail () {
        this.tableLoading = true
        const { batch_id } = this
        let params = {
          batch_id
        }
        this.$api.getCarddetail(params).then( res => {
          this.exchangeData = res.cards
        } )
        .catch( err => {} )
        .finally( () => {
          this.tableLoading = false
        } )
      },

      // 获取核销的卡券编号
      getCardno (row) {
        let params = {
          batch_id: row.id
        }
        this.$api.getCardno(params).then( res => {
          this.jumpVerification(res.card_no)
          this.$emit('input', false)
        } ).catch( err => {} )
      },

      getExchangeCardInfo(){
        this.tableLoading = true
        let params = {id: this.exchangeId}
        this.$api.getExchangeCardInfo(params).then(res => {
          this.exchangeData = res.batch_list
          this.totalNum = res.num
          this.surplusNum = res.surplus_num
        }).catch( err => {} )
          .finally( () => {this.tableLoading = false} )
      }
    },
    filters: {

    }
  }
</script>
<style lang="less" scoped>
::v-deep .ivu-modal-header{
  padding: 17px 16px
}

.modalHeader{
  display: flex;
  align-items: center;
  color: #17233d;
  font-weight: 500;
}
</style>
