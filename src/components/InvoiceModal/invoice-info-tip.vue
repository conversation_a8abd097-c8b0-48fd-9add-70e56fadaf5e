<template>
  <Modal :value="value" :title="title" :mask-closable="false" width="430px" @on-visible-change="changeVisible" transfer>
    <div class="content">
      <div class="content-text">
        <p>{{ tipsContent }}</p>
      </div>
    </div>
    <div slot="footer">
      <Button type="primary" class="custom-link-btn" v-if="showEditBtn">
        <k-link :to="{ path: '/setting/Invoice' }" target="_blank">{{ getLinkText }}</k-link>
      </Button>
      <Button type="default" @click="confirmPass">好的</Button>
    </div>
  </Modal>
</template>

<script>
export default {
  name: 'invoice-info-tip',
  mixins: [],
  components: {},

  props: {
    title: {
      type: String,
      default: '温馨提示',
    },
    tipsContent: {
      type: String,
      default: '',
    },
    tipsType: {
      type: String,
      default: '',
    },
    value: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {};
  },

  computed: {
    showEditBtn() {
      return this.tipsType === '2' || this.tipsType === '1';
    },
    getLinkText() {
      return this.tipsType === '2' ? '去修改' : '去填写';
    },
  },

  watch: {},

  created() {},

  mounted() {},

  destroyed() {},

  methods: {
    close() {
      this.$emit('input', false);
    },
    changeVisible(flag) {
      if (!flag) {
        this.close();
      }
    },
    confirmPass() {
      this.close();
    },
  },
};
</script>

<style scoped lang="less">
::v-deep .custom-link-btn {
  padding: 0px !important;

  span {
    height: 100%;
  }

  a {
    color: #fff;
    display: inline-block;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 15px;
  }
}
</style>
