<template>
  <div class="global-table-status">
    <div class="status-item" v-if="type" :style="{backgroundColor: `var(--status-${type}-color)`}">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'status',
  mixins: [],

  components: {},

  props: {
    /**
     * @description: 目前支持四种type, success, reject, warn ,gray, type为空则不显示状态标记
     * @note: 若需要状态标记和文字同色展示，可扩展新样式，亦可直接同步状态色到文字色
     * */
    type: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
    };
  },

  computed: {
  },

  watch: {},

  created() {},

  mounted() {},

  methods: {},

  destroyed() {}
};
</script>

<style scoped lang="less">
</style>
