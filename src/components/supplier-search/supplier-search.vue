<template>
  <div>
    <Select
      ref="supplier"
      transfer
      :value="value"
      :clearable="isClearable"
      :loading="searchLoading"
      filterable
      @on-clear="clearSub"
      placeholder="请输入搜索供应商"
      @on-select="selectSup"
      @on-query-change="queryChange"
      :remote-method="search"
    >
      <Option v-for="(option, index) in supplier_list" :key="option.id" :value="option.id">{{ option.name }}</Option>
    </Select>
  </div>
</template>

<script>
import util from '@/libs/util';
export default {
  name: 'supplier-search',
  components: {},
  mixins: [],
  props: {
    isClearable: {
      type: Boolean,
      default: true
    },
    value: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      searchLoading: false,
      supplier_list: [],
      query: '',
    };
  },
  computed: {},
  watch: {
  },
  created() {
  },
  mounted() {},
  methods: {
    searchMethod: util.debounce(function (query) {
      let params = {
        name: query
      }
      this.searchLoading = true;
      this.$api.getSupplierList(params).then(res => {
        this.searchLoading = false;
        this.supplier_list = res.suppliers;
      });
    }, 200),
    search() {
    },
    selectSup(val) {
      this.$emit('input', val.value);
    },
    queryChange(val) {
      this.searchMethod(val);
    },
    clear() {
      this.$refs.supplier.clearSingleSelect();
    },
    clearSub() {
      this.searchMethod('');
      this.$emit('input', '');
    }
  }
};
</script>

<style lang="less" scoped>
.filterable-select {
  ::v-deep .ivu-select-input {
    margin-top: -1px;
  }
}
</style>
