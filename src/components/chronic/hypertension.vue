<template>
  <div class="hypertension-wrapper">
    <div class="hypertension">
      <div class="hypertension-title">
        <h2>总体概览</h2>
      </div>
      <div class="hypertension-top flex flex-item-between">
        <div class="hypertension-box flex">
          <div class="hypertension-title">血压类型占比</div>
          <div class="pie">
            <chart-view height="125px" :chart-option="hypertension_pie_options"></chart-view>
          </div>
          <div class="h-content">
            <Row :gutter="8">
              <Col span="8" v-for="(item, index) in bp_distribution" :key="index">
                <div class="h-content-box">
                  <div class="press">
                    <div class="press-type">{{ item.status_text }}</div>
                  </div>
                  <div class="flex flex-item-between">
                    <div class="press-percent">{{item.percent}}%</div>
                    <div class="press-times"><span class="unit">{{item.num}} 次</span></div>
                  </div>

                </div>
              </Col>
            </Row>
          </div>
        </div>
        <div class="hypertension-box flex">
          <div class="hypertension-title">血压平均值</div>
          <div class="flex" v-if="bp_av.status">
            <div class="pie">
              <div class="circle-wrapper" :style="{background: statusColor(bp_av.status, 'rgb')}">
                <div class="circle" :style="{background: statusColor(bp_av.status)}">
                  <div class="circle-value">
                    {{bp_av.bp_dpr }}/{{bp_av.bp_spr }}
                  </div>
                  <div class="circle-unit">mmHg</div>
                </div>
              </div>
            </div>
            <div class="h-content">
              <Row :gutter="10">
                <Col span="12">
                  <div class="h-content-box">
                    <div class="press flex flex-item-between">
                      <div class="press-type">平均血压值</div>
                    </div>
                    <div class="press-times">{{ bp_av.bp_dpr }}/{{ bp_av.bp_spr }} <span class="unit">mmHg</span></div>
                  </div>
                </Col>
                <Col span="12">
                  <div class="h-content-box">
                    <div class="press flex flex-item-between">
                      <div class="press-type">平均心率</div>
                    </div>
                    <div class="press-times">{{ bp_av.bp_pulse }} <span class="unit">BPM</span></div>
                  </div>
                </Col>
                <Col span="24">
                  <div class="h-content-box">
                    <div class="color-panel" >
                      <div
                        v-for="( item, index ) in colorEnumerations" :key="index"
                        class="color-panel-item"
                        :style="{background: item.color}">
                        <div class="color-panel-item-tooltip" v-if="bp_av.status === item.status"  :style="{background: item.color }">
                          {{ item.label }}
                          <div class="triangle" :style="{borderTopColor: item.color }"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </Col>
              </Row>
            </div>
          </div>
          <div v-else class="empty" style="flex: 1;min-height: 124px">
            暂无数据
          </div>
        </div>
      </div>
      <div class="hypertension-title">
        <h2>图表趋势</h2>
      </div>
      <div class="flex flex-item-between">
        <div class="trend-tag-box">
          <span
            class="trend-tag"
            v-for="(item, index) in pressItems"
            :class="[current_card_index === index ? 'tag-active' : '']"
            :key="item.id"
            @click="changeTrendTag(item, index)"
          >{{ item.desc }}</span>
        </div>
        <div>
          <el-select size="small" v-model="average" style="width:200px;" @change="selectAverage">
            <el-option v-for="item in averageList" :label='item.desc' :value="item.id" :key="item.id">{{ item.desc }}</el-option>
          </el-select>
        </div>
      </div>
      <div class="line-wrapper">
        <chart-view height="400px" :chart-option="hypertension_bar_options"></chart-view>
      </div>
      <div class="hypertension-title flex flex-item-between flex-item-center">
        <h2>录入明细</h2>
       <div class="colorPanel flex flex-item-end ">
         <div class="flex flex-item-align" v-for="( color_item, color_index ) in colorEnumerations" :key="'color'+color_index">
           <div class="colorShow" :style="{background: color_item.color}"></div>
           <div class="colorLabel">{{ color_item.label }}</div>
         </div>
       </div>
      </div>
      <div>
        <custom-detail-table class="mt18" :columns="tableCols" :data="recordList">
          <!-- 日期 -->
          <template slot-scope="{row, $index}" slot="time">
            <div>{{ row.time}}</div>
          </template>

          <!-- 舒张压 -->
          <template slot-scope="{row, $index}" slot="bp_spr">
            <div>{{ row.bp_spr}}</div>
          </template>

          <!-- 收缩压 -->
          <template slot-scope="{row, $index}" slot="bp_dpr">
            <div>{{ row.bp_dpr}}</div>
          </template>

          <!-- 心率 -->
          <template slot-scope="{row, $index}" slot="bp_pulse">
            <div>{{ row.bp_pulse }}</div>
          </template>

          <!-- 脉压差 -->
          <template slot-scope="{row, $index}" slot="bp_pd_pulse">
            <div>{{ row.bp_pd_pulse}}</div>
          </template>

          <!-- 状态 -->
          <template slot-scope="{row, $index}" slot="status_text">
            <div :class="{block: getStatus( row ) }" :style="{background: statusColor(getStatus( row )) }">
              {{ row.status_text }}
            </div>
          </template>

          <!-- 记录时间 -->
          <template slot-scope="{row, $index}" slot="date">
            <div v-if="row.date">{{ row.date | data_format('yyyy-MM-DD HH:mm') }}</div>
          </template>
        </custom-detail-table>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
import {getClinicid} from '@/libs/runtime'
import CustomDetailTable from '@/components/CustomDetailTable'
import S from '@/libs/util'
import { $operator } from '@/libs/operation'
export default {
  name: 'hypertension',
  mixins: [],

  components: {CustomDetailTable},

  props: {
    trendTime: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      hypertension_pie_options: {}, // 饼状图
      hypertension_bar_options: {}, // 柱状图
      average: '',
      averageList: [],
      pressItems: [
        {id: 'BP', desc: '血压', isMoney: false,},
        {id: 'PPD', desc: '脉压差', isMoney: false,},
        {id: 'PULSE', desc: '心率', isMoney: false,},
      ],
      pressType: 'BP',
      colorEnumerations: [
          { label: '低血压', color: '#1162D1', rgb: 'rgba(17, 98, 209, .3)', status: 'LOW', },
          { label: '血压正常', color: '#05B5AC', rgb: 'rgba(5, 181, 172, .3)', status: 'NORMAL', },
          { label: '血压正常偏高', color: '#F1A42C', rgb: 'rgba(241, 164, 44, .3)', status: 'NORMAL_OVER', },
          { label: '轻度高血压', color: '#F1682C', rgb: 'rgba(241, 104, 44, .3)', status: 'OVER_ONE', },
          { label: '中度高血压', color: '#FA4F4F', rgb: 'rgba(250, 79, 79, .3)', status: 'OVER_TWO', },
          { label: '重度高血压', color: '#C82D2D', rgb: 'rgba(200, 45, 45, .3)', status: 'OVER_THREE', },
      ],
      // 图表趋势
      trendItems: [
          {
            id: 'BP',
            desc: '血压',
            isChecked: false,
            isMoney: true,
          },
          {
            id: 'PPD',
            desc: '脉压差',
            isChecked: false,
            isMoney: true,
          },
          {
            id: 'PULSE',
            desc: '心率',
            isChecked: false,
            isMoney: true,
          },
      ],
      current_card_index: 0,
      overviewList: [],
      bp_distribution: [], // 血压类型占比
      bp_av: {}, // 血压平均值
      // 录入明细
      tableCols: [
        {title: '日期', slot: 'time', align: 'center'},
        {title: '舒张压', slot: 'bp_spr', align: 'center'},
        {title: '收缩压', slot: 'bp_dpr', align: 'center'},
        {title: '心率', slot: 'bp_pulse', align: 'center'},
        {title: '脉压差', slot: 'bp_pd_pulse', align: 'center'},
        {title: '状态', slot: 'status_text', align: 'center'},
        {title: '记录时间', slot: 'date', align: 'center', width: 150},
      ],
      recordList: [],
      statisList: [],
      // 血压区间
      BpIndexStatus: '',
      // 脉压差区间
      bpPdDesc: {},
      bpStatus: {
        'LOW': { color: '#1162D1' },
        'NORMAL': { color: '#05B5AC' },
        'OVER': { color: '#FA4F4F' },
      },
      // 心率区间
      bpPulseDesc: {},
      bpPulseStatus: {
        'LOW': { color: '#1162D1' },
        'NORMAL': { color: '#05B5AC' },
        'OVER': { color: '#FA4F4F' },
      },
    }
  },

  computed: {
      // 获取状态
      // LOW:低体重(营养不良),NORMAL:正常范围,OVER:超重,OVER_ONE:一级肥胖,OVER_TWO:二级肥胖,OVER_THREE:三级肥胖
      getStatus () {
        return ( row ) => {
          if ( row.status == 'NORMAL' ) {
            return ''
          }
          return row.status || ''
        }
      },
      // 获取状态颜色
      statusColor () {
        return ( status ) => {
          let index = this.colorEnumerations.findIndex( item => item.status == status )
          return this.colorEnumerations[index] && this.colorEnumerations[index].color || ''
        }
      },
  },

  watch: {
    trendTime: {
      handler(val){
        this.getBpOverview()
        this.getBpStatis()
      }
    },
  },

  created() {},

  mounted() {
    this.getBpOverview()
    this.getBpOptions()
    this.getBpStatis()
  },

  methods: {
    getBpOverview(){
      let params = {
        uid: this.$route.query.uid,
        st: this.trendTime[0],
        et: this.trendTime[1],
      }
      this.$api.getBpOverview(params).then(res => {
        this.overviewList = res
        this.bp_distribution = Object.values(res.bp_distribution)
        let arr = Object.values(res.bp_distribution)
        // todo 优化对象交换
        let lowObj = arr.find(item => item.status === 'LOW')
        let newArr = arr.filter(item => item.status !== 'LOW')
        newArr.splice(2,0,lowObj)
        this.bp_distribution = newArr
        this.bp_av = res.bp_av
        this.handlePieData(res.bp_distribution)
      }).catch(err => {})
    },
    // 组装饼图数据
    handlePieData (data) {
      let resultList = []
      let list = this.descToArrHandle(data)
      console.log("=>(hypertension.vue:276) list", list);

      list.forEach( ( item ) => {
        resultList.push( {
          ...item,
          title: item.status_text,
          value: item.num,
          color: this.statusColor(item.status, 'color', true),
          tootipList: [
            { key: '', value: item.status_text },
            { key: '次数：', value: `${ item.num }次` },
            { key: '占比：', value: `${ item.percent }%` },
          ],
        } )
      } )
      this.setPieConfigure( resultList, '' )
    },

    // 绘制饼图
    setPieConfigure( data, title, isShow = true ) {
      data.map( item => item.name = item.title)
      // 控制饼图的颜色
      let colorList = data.map( item => item.color )
      this.hypertension_pie_options = this.$eChartFn.orderPie( {
        series: [
          {
            data,
            selectedMode: 'single',
            radius: [0, '55%'],
            center: ['50%', '66%'],
            itemStyle: {
              borderRadius: 0
            },
          }
        ],
        title: { text: title },
        tooltip: {
          position:function (pos, params, dom, rect, size) {
          // 鼠标在左侧时 tooltip 显示到右侧，鼠标在右侧时 tooltip 显示到左侧。
          let obj = {top: 100};
          obj[['left', 'right'][+(pos[0] < size.viewSize[0] / 2)]] = 5;
          return obj;
          },
          formatter: ( a ) => {
            let content = ''
            a.data.tootipList &&
            a.data.tootipList.forEach( ( item, index ) => {
              if ( index == 0 ) {
                content =
                  content +
                  `<span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${ item.key }</span>
                  <span style="font-size: 13px;font-weight: bold;color: #333333;line-height: 17px;">${ item.value }</span></br>`
              } else {
                content =
                  content +
                  `<span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;">${ item.key }</span>
                      <span style="font-size: 11px;font-weight: 300;color: #A7AFC0;line-height: 15px;margin-right: 10px">${ item.value }</span>`
              }
            } )
            return `<div class="flex-col">
                        ${ content }
                      </div>
                      `
          },
          show: true,
        },
      },colorList )
    },


    // 解析对象
    descToArrHandle (obj) {
      let arr = []
      let kArr = Object.keys(obj)
      kArr.map((item, i) => {
        arr.push({
          ...obj[item],
          id: item,
        })
      })
      return arr || []
    },

    getBpOptions(){
      this.$api.getBpOptions().then(res => {
        console.log("=>(hypertension.vue:318) res", res);
        // 血压区间
        this.BpIndexStatus = res.BpIndexStatus
        // 脉压差区间
        this.bpPdDesc = res.bpPdDesc
        // 心率区间
        this.bpPulseDesc = res.bpPulseDesc
        this.averageList = S.descToArrHandle(res.typeStatisDesc)
        console.log("🚀 ~ file: hypertension.vue ~ line 408 ~ this.$api.getBpOptions ~ this.averageList", this.averageList)
        // 初始化选项默认值
        this.average = this.averageList[0] && this.averageList[0].id
      })
    },
    getBpStatis(){
      this.getBpRecord()
      let params = {
        uid: this.$route.query.uid,
        st: this.trendTime[0],
        et: this.trendTime[1],
        statis_type: this.average,
        type: this.pressType,
      }
      this.$api.getBpStatis(params).then(res => {
        if ( this.current_card_index == 0 ) {
          // 血压柱状图配置
          this.setWaterfallBar(res)
        }else{
          // 脉压差和心率曲线图配置
          this.setLineOptions(res)
        }
      })
    },


    // 设置柱状图配置
    setWaterfallBar (res) {
        let xAxis = res.statis && res.statis.map(item => item.time)
        let dprList = res.statis && res.statis.map(item => item.bp_dpr)
        let sprList = res.statis && res.statis.map(item => item.bp_spr)
        let subtractionList = res.statis && res.statis.map (item => {
          return {
            value: $operator.subtract(Number(item.bp_dpr), Number(item.bp_spr)),
            status_text: item.status_text,
            itemStyle: {
              color: this.statusColor(item.status)
            }
          }
        })
        this.hypertension_bar_options = this.$eChartFn.waterfallBar(
          {
            tooltip: {
              formatter: function (params) {
                let text = ''
                let spr = params[0]
                let dpr = params[1]
                let dprValue = Number(spr.value) + Number(dpr.value)
                console.log('params', params[1].data);
                let status_text = params[1].data && params[1].data.status_text
                // 背景色
                let bgColor = params[1].data && params[1].data.itemStyle.color || '#fff'
                // title颜色
                let titleColor = bgColor == '#fff' ? '#333' :'#fff'
                // 文本颜色
                let textColor = bgColor == '#fff' ? '#333' :'#fff'

                text += `<div style="background: ${bgColor};padding: 8px;border-radius:4px;position: relative;min-width: 100px">
                <div style="color:${titleColor};height:auto;width:100%;margin-right:5px;font-size:14px">${ (spr.value == 0 || dprValue == 0) ? '暂无记录' : status_text}</div>
                `

                if ( spr.value != 0 && dprValue != 0) {
                  text += `
                  <div style="color:${textColor};font-size:13px;font-weight:600;margin-top:4px;line-height:20px"><span>${dpr.seriesName}:${dprValue} mmHg</span></div>
                  <div style="color:${textColor};font-size:13px;font-weight:600;margin-top:4px;line-height:20px"><span>${spr.seriesName}:${spr.value} mmHg</span></div>
                  `
                }

                text += `
                  <div style="position: absolute;bottom:-11px;left:43%;width:0;height:0;border-top:11px solid ${bgColor};border-left:11px solid transparent;border-right:11px solid transparent">
                  <div style="position: absolute;top:-11px;left:-10px;width:0;height:0;border-top:10px solid ${bgColor};border-left:10px solid transparent;border-right:10px solid transparent"><div>
                  <div>
                  </div>`

                return text;
              }
            },

            xAxis: {
              data: xAxis
            },
            grid: {
              top: '11%',
              left: '63',
              right: '4%',
              bottom: '15%',
              show: true,
              borderWidth: 0
            },
            yAxis: {
              name: 'mmHg',
              min: function (value) {
                let min = value.min
                if ( isNaN(value.min) || min == 0) {
                  min = 60
                }
                return $operator.subtract(Number(min), 10) < 0 ? '0' : $operator.subtract(Number(min), 10);
              },
              max: function (value) {
                let max = value.max
                if ( isNaN(value.max) || max == 0) {
                  max = 140
                }
                return $operator.add(Number(max), 10) < 0 ? '0' : $operator.add(Number(max), 10);
              },
            },
            series: [
              {
                name: '舒张压',
              },
              {
                name: '收缩压',
              }
            ]
          },
          sprList,
          subtractionList,
          dprList
        )
    },

    // 曲线图配置
    setLineOptions (res) {
      // bp_pd:脉压差， bp_pulse:心率

      let key = this.current_card_index == 1 ? 'bp_pd' : 'bp_pulse'

      let currentStatus = key == 'bp_pd' ? 'bpStatus' : 'bpPulseStatus'
      // 根据状态获取颜色
      let getCurrentColor = (status) => {
        return this[currentStatus] && this[currentStatus][status] && this[currentStatus][status].color
      }

      let xData = res.statis.map((item) => item.time);
      let yData = res.statis.map((item) => {
        return {
            value: item[key] == 0 ? '' : item[key],
            color: getCurrentColor(item.status),
            status_text: key == 'bp_pulse' ? item.bp_pulse_status_text : item.status_text
          }
      });

      // 安全区域
      let normal_section = res.normal_section
      // 安全区域差值
      let dValue_sectoin = $operator.subtract(Number(normal_section.max), Number(normal_section.min))

      this.hypertension_bar_options = this.$eChartFn.safeAreaLineOptions(
          {
            tooltip: {
              formatter: (param) => { // 自定义tooltip
                var text = '';
                let value = param[0].value
                let title = key == 'bp_pd' ? '脉压差' : '心率'
                let status_text = param[0].data && param[0].data.status_text
                let unit = key == 'bp_pd' ? 'mmHg' : 'BPM'

                // 背景色
                let bgColor = param[0].data && param[0].data.color || '#fff'
                // title颜色
                let titleColor = bgColor == '#fff' ? '#333' :'#fff'
                // 文本颜色
                let textColor = bgColor == '#fff' ? '#333' :'#fff'

                text += `<div style="background: ${bgColor};padding: 8px;border-radius:4px;position: relative;min-width: 100px">
                          <div style="color:${titleColor};height:auto;width:100%;margin-right:5px;font-size:14px">${value ? status_text : '暂无记录'}</div>

                          `
                if ( value ) {
                  text += `<div style="color:${textColor};font-size:14px;font-weight:600;margin-top:4px;line-height:20px"><span>${value||'-'} <span style="color: ${textColor};margin-left: 4px;">${unit}</span></span></div>`
                }

                text += `<div style="position: absolute;bottom:-11px;left:43%;width:0;height:0;border-top:11px solid ${bgColor};border-left:11px solid transparent;border-right:11px solid transparent">
                          <div style="position: absolute;top:-11px;left:-10px;width:0;height:0;border-top:10px solid ${bgColor};border-left:10px solid transparent;border-right:10px solid transparent"><div>
                          <div>
                        </div>`
                return text;
              },
            },
            xAxis: {
              data: xData,
            },
            yAxis: {
              name: key == 'bp_pd' ? 'mmHg' : 'BPM',
              min: function (value) {
                let min = value.min
                if ( isNaN(value.min)) {
                  min = normal_section.min
                }else{
                  min = Number(min) > Number(normal_section.min) ? normal_section.min : min
                }
                return $operator.subtract(Number(min), Number(dValue_sectoin)) < 0 ? '0' : $operator.subtract(Number(min), Number(dValue_sectoin));
              },
              max: function (value) {
                let max = value.max
                // 当最大值为NaN或者0时，用安全区域最大值替代，确保安全区域可以展示出来
                if ( isNaN(value.max) || max == 0) {
                  max = normal_section.max
                }
                // 如果当前数据的最大值小于等于安全区域的最大致，当前最大值 = 安全区域最大值 + 安全区域的空间值
                if ( max <= Number(normal_section.max)) {
                  max = Number(normal_section.max)
                }
                return  $operator.add(Number(max), Number(dValue_sectoin))
              },
            },
            visualMap: {
              pieces: this.setPiecesColor(key)
            },
            series: [{
              data: yData,
              markArea: { // 设置安全区域
                  data: [
                    [{
                        yAxis: normal_section.min,
                      },
                      {
                        yAxis: normal_section.max,
                      },
                    ],
                  ],
                },
            }],
          },
        this.trendItems[this.current_card_index].desc,
      )
    },

    // 设置折线图不同区间的颜色
    setPiecesColor (key) {
      let currentDesc = key == 'bp_pd' ? 'bpPdDesc' : 'bpPulseDesc'
      let currentStatus = key == 'bp_pd' ? 'bpStatus' : 'bpPulseStatus'

      let resultPieces = []

      // 获取当前状态的阶段区间
      let current_section = this[currentDesc]
      for ( let status in current_section ) {
        let item = current_section[status].between
        console.log('item', item);

        // 根据状态获取颜色
        let getCurrentColor = (status) => {
          return this[currentStatus] && this[currentStatus][status] && this[currentStatus][status].color
        }

        // 当有上限无下限
        if ( item.max && !item.min ) {
          resultPieces.push({
            gt: 0,
            lte: +item.max ,
            color: getCurrentColor(status)
          })
        }

        // 当有下限无上限
        if ( !item.max && item.min ) {
          resultPieces.push({
            // gt: +item.min+2,
            gt: +item.min,
            lte: 10000,
            color: getCurrentColor(status)
          })
        }

        // 当有上限下限区间时,设置线的区间范围和对应状态颜色
        if ( item.max && item.min ) {
          resultPieces.push({
            gt: +item.min - 1,
            lte: +item.max + 1,
            color: getCurrentColor(status)
          })
        }
      }
      console.log('resultPieces', resultPieces)
      return resultPieces
    },
    getBpRecord(){
      let params = {
        uid: this.$route.query.uid,
        st: this.trendTime[0],
        et: this.trendTime[1],
        clinic_id: getClinicid(),
      }
      this.$api.getBpRecord(params).then(res => {
        this.recordList = res.list
      })
    },


    changeTrendTag( item, index ) {
      this.pressType = item.id
      this.current_card_index = index
      this.getBpStatis()
      // this.getBgStatis()
    },

    selectAverage(){
      this.getBpStatis()
    }
  },

  destroyed() {
  },


}
</script>

<style lang="less" scoped>
.hypertension-wrapper{
  margin: 0 16px;
  .hypertension{
  .hypertension-title{
    h2 {
      font-size: 14px;
      color: #333333;
      font-weight: 900;
      margin-bottom: 8px;
    }
  }
  .hypertension-top{
    width: 100%;
    margin-bottom: 24px;
    .hypertension-box{
      position: relative;
      width: 49%;
      height: 144px;
      background: #FCFCFB;
      border-radius: 4px;
      padding:10px 10px;
      box-sizing: border-box;
      .hypertension-title{
        position: absolute;
        top: 12px;
        left: 12px;
        font-size: 12px;
        font-weight: 400;
        color: #83766E;
      }
      .pie{
        position: relative;
        width: 200px;
        height: 100%;
        .circle-wrapper {
          background: rgba(41, 184, 182, .3);
          width: 100px;
          height: 100px;
          border-radius: 50%;
          margin: 40px;
          margin-top: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          .circle {
            width: 88px;
            height: 88px;
            background: #29B8B6;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            .circle-value{
              font-size: 20px;
              font-weight: bold;
              color: #FFFFFF;
              line-height: 27px;
            }
            .circle-unit {
              font-size: 12px;
              color: #FFFFFF;
              line-height: 14px;
            }
          }
        }
      }
      .h-content{
        flex: 1;
        justify-content: space-around;
        .h-content-box{
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          height: 59px;
          background: #FFFFFF;
          border-radius: 4px;
          margin-bottom: 6px;
          padding: 6px 14px;
          .press{
            height: 18px;
            line-height: 18px;
            .press-type{
              font-size: 12px;
              font-weight: 300;
              color: #88887E;

            }
          }
          .press-times{
            font-size: 16px;
            font-weight: 700;
            color: #000000;
            .unit{
              font-size: 12px;
              font-weight: 400;
              color: #88887E;
              line-height: 17px;
            }
          }
          .press-percent{
            font-size: 16px;
            font-weight: 700;
            color: #000000;
          }
        }
      }
    }
  }
}
}


.trend-tag-box {
  display: flex;
  flex:1;
  flex-wrap: wrap;
  height: auto;
  .trend-tag {
    padding: 6px;
    border-radius: 2px;
    opacity: 0.8;
    border: 1px solid #bcc3d7;
    margin-right: 10px;
    cursor: pointer;
    margin-bottom: 8px;
  }

  .tag-active {
    background: #155bd4;
    opacity: 1;
    color: #ffffff;
    border-color: #155bd4;
  }
}

.color-panel {
  display: flex;
  justify-content: center;
  .color-panel-item {
    width: 44px;
    height: 4px;
    background: #5591FF;
    margin-right: 4px;
    &:last-child{
      margin-right: 0;
    }
    position: relative;
    margin-top: 40px;
    .color-panel-item-tooltip {
      min-width: 100px;
      height: 27px;
      background: #76C35E;
      position: absolute;
      bottom: 16px;
      right: -25px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      padding: 4px 8px;
      box-sizing: border-box;
      border-radius: 4px;
      .triangle{
        position: absolute;
        bottom: -5px;
        border-left:6px solid transparent;
        border-right:6px solid transparent;
        border-top:6px solid #333;
        width:0;
        height:0;
        content: '';
      }
    }
  }
}

// 颜色面板
.colorPanel {
  .colorShow {
    width: 11px;
    height: 5px;
    background: #8699FF;
    border-radius: 3px;
    margin-left: 12px;
  }
  .colorLabel {
    font-size: 12px;
    line-height: 11px;
    color: #000000;
    margin-left: 6px;
  }
}

</style>
