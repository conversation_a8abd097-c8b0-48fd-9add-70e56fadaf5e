<template>
  <div class="fat-wrapper">
    <div class="diabetes-wrapper">
      <div class="diabetes-box">
        <div class="content-title">
          <h2>总体概览</h2>
        </div>
        <div class="content-data">
          <div class="content-3">
            <div class="diabetes-item" v-for="(item, index) in weightList" :key="index">
              <p class="chronic-title">{{ item.name }}</p>
              <div v-if="(index === 2 && item.weight) || item.weight != 0">
                <p class="chronic-content">
                  <span v-if="index === 2" style="display: inline-flex">
                    <svg-icon
                      iconClass="reduce"
                      v-show="!isAdd"
                      class="helpIcon cursor"
                      style="width: 16px; height: 16px"
                    ></svg-icon>
                    <svg-icon
                      iconClass="add"
                      v-show="isAdd"
                      class="helpIcon cursor"
                      style="width: 16px; height: 16px"
                    ></svg-icon>
                  </span>
                  {{ item.weight }} <span class="unit">Kg</span>
                  <span
                    class="weight-tag"
                    v-if="item.status && item.status !== 'NORMAL'"
                    :style="{ color: statusColor(item.status), borderColor: statusColor(item.status) }"
                    >{{ item.status_text }}</span
                  >
                </p>
                <p class="chronic-time">
                  <span v-if="item.date">{{ item.date | data_format('MM月DD日') }}</span>
                  <span style="margin-left: 8px" v-if="item.bmi">BMI: {{ item.bmi || '-' }}</span>
                </p>
              </div>
              <div v-else class="empty" style="min-height: 71px">
                <span v-if="index == 3">请先确认身高</span>
                <span v-else>暂无数据</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="diabetes-box">
        <div class="content-title" style="margin-bottom: 16px">
          <h2>图表趋势</h2>
        </div>
        <div class="flex flex-item-end">
          <el-select v-model="average" size="small" style="width: 200px" @on-change="getWeightStatis">
            <el-option v-for="item in averageList" :label='item.desc' :value="item.id" :key="item.id">{{ item.desc }}</el-option>
          </el-select>
        </div>
        <div class="line-wrapper">
          <chart-view height="400px" :chart-option="line_options"></chart-view>
        </div>
      </div>
      <div class="diabetes-box">
        <div class="content-title flex flex-item-between flex-item-center" style="margin-bottom: 16px">
          <h2>录入明细</h2>
          <div class="colorPanel flex flex-item-end">
            <div
              class="flex flex-item-align"
              v-for="(color_item, color_index) in colorEnumerations"
              :key="'color' + color_index"
            >
              <div class="colorShow" :style="{ background: color_item.color }"></div>
              <div class="colorLabel">{{ color_item.label }}</div>
            </div>
          </div>
        </div>
        <div>
          <custom-detail-table class="mt18" :columns="tableCols" :data="recordList">
            <!-- 日期 -->
            <template slot-scope="{ row, $columnIndex }" slot="date">
              <div>{{ row.date | date_format('MM月DD日') }}</div>
            </template>

            <!-- 体重 -->
            <template slot-scope="{ row, $columnIndex }" slot="w_weight">
              <div>{{ row.w_weight }}</div>
            </template>

            <!-- BMI -->
            <template slot-scope="{ row, $columnIndex }" slot="w_bmi">
              <div>
                {{ row.w_bmi }}
              </div>
            </template>

            <!-- 状态 -->
            <template slot-scope="{ row, $columnIndex }" slot="status_text">
              <div
                :class="{ block: getStatus(row, $columnIndex) }"
                :style="{ background: statusColor(getStatus(row)) }"
              >
                {{ row.status_text }}
              </div>
            </template>

            <!-- 记录时间 -->
            <template slot-scope="{ row, $columnIndex }" slot="recordTime">
              <div v-if="row.date">{{ row.date | data_format('YYYY-MM-DD HH:mm') }}</div>
            </template>
          </custom-detail-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CustomDetailTable from '@/components/CustomDetailTable';
import { getUid, getClinicid } from '@/libs/runtime';
import S from '@/libs/util';
import { $operator } from '@/libs/operation';
export default {
  name: 'fat',
  mixins: [],

  components: { CustomDetailTable },

  props: {
    trendTime: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      weightList: [
        { name: '最高体重', weight: 0, time: '10月23日', BMI: '23.7' },
        { name: '最低体重', weight: 0, time: '10月23日', BMI: '23.7' },
        { name: '体重变化', weight: 0 },
        { name: '理想体重', weight: 0 },
        { name: '最新体重', weight: 0, time: '10月23日', BMI: '23.7' },
        { name: '目标距离', weight: 0 }
      ],
      average: '',
      averageList: [],
      line_options: {},
      colorEnumerations: [
        { label: '低体重', color: '#1162D1', status: 'LOW' },
        { label: '体重正常', color: '#05B5AC', status: 'NORMAL' },
        { label: '体重超重', color: '#F1A42C', status: 'OVER' },
        { label: '轻度肥胖', color: '#F1682C', status: 'OVER_ONE' },
        { label: '中度肥胖', color: '#FA4F4F', status: 'OVER_TWO' },
        { label: '重度肥胖', color: '#C82D2D', status: 'OVER_THREE' }
      ],
      overviewList: [],
      // 录入明细
      tableCols: [
        { title: '日期', key: 'time', align: 'center' },
        { title: '体重(Kg)', slot: 'w_weight', align: 'center' },
        { title: 'BMI', slot: 'w_bmi', align: 'center' },
        { title: '状态', slot: 'status_text', align: 'center' },
        { title: '记录时间', slot: 'recordTime', align: 'center' }
      ],
      recordList: [],
      statisList: [
        {
          bp_dpr: '110',
          bp_pulse: '70',
          bp_spr: '70',
          date: '1667386680',
          status: 'NORMAL',
          status_text: '正常',
          time: '11.2'
        },
        {
          bp_dpr: '120',
          bp_pulse: '60',
          bp_spr: '60',
          date: '1667486680',
          status: 'NORMAL',
          status_text: '正常',
          time: '11.2'
        },
        {
          bp_dpr: '130',
          bp_pulse: '30',
          bp_spr: '30',
          date: '1667586680',
          status: 'NORMAL',
          status_text: '正常',
          time: '11.2'
        }
      ]
    };
  },

  computed: {
    // 获取状态
    // LOW:低体重(营养不良),NORMAL:正常范围,OVER:超重,OVER_ONE:一级肥胖,OVER_TWO:二级肥胖,OVER_THREE:三级肥胖
    getStatus() {
      return row => {
        if (row.status == 'NORMAL') {
          return '';
        }
        return row.status || '';
      };
    },
    // 获取状态颜色
    statusColor() {
      return status => {
        let index = this.colorEnumerations.findIndex(item => item.status == status);
        return (this.colorEnumerations[index] && this.colorEnumerations[index].color) || '';
      };
    },

    isAdd() {
      console.log('=>(fat.vue:177) Number(this.weightList[2].weight)', Number(this.weightList[2].weight) > 0);
      return Number(this.weightList[2].weight) > 0;
    }
  },

  watch: {
    trendTime: {
      handler(val) {
        console.log('=>(fat.vue:257) val', val);
        this.getWeightOverview();
        this.getWeightStatis();
      }
    }
  },

  created() {},

  mounted() {
    this.getWeightOverview();
    this.getWeightOptions();
  },

  methods: {
    getWeightOverview() {
      let params = {
        uid: this.$route.query.uid,
        st: this.trendTime[0],
        et: this.trendTime[1]
      };
      this.$api
        .getWeightOverview(params)
        .then(res => {
          this.overviewList = res;
          this.weightList[0].weight = res.max.w_weight;
          this.weightList[0].date = res.max.date;
          this.weightList[0].bmi = res.max.bmi;
          this.weightList[0].status = res.max.status;
          this.weightList[0].status_text = res.max.status_text;
          this.weightList[1].weight = res.min.w_weight;
          this.weightList[1].date = res.min.date;
          this.weightList[1].bmi = res.min.bmi;
          this.weightList[1].status = res.min.status;
          this.weightList[1].status_text = res.min.status_text;
          this.weightList[2].weight = res.change;
          this.weightList[3].weight = res.standard;
          this.weightList[4].weight = res.new.w_weight;
          this.weightList[4].date = res.max.date;
          this.weightList[4].bmi = res.max.bmi;
          this.weightList[4].status = res.new.status;
          this.weightList[4].status_text = res.new.status_text;
          this.weightList[5].weight = res.distance;
        })
        .catch(err => {});
    },
    getWeightStatis() {
      this.getWeightRecord();
      console.log('=>(fat.vue:303) this.trendTime', this.trendTime);
      let params = {
        uid: this.$route.query.uid,
        type: this.average,
        st: this.trendTime[0],
        et: this.trendTime[1]
      };

      this.$api.getWeightStatis(params).then(res => {
        let xData = res.list && res.list.map(item => item.time);
        let yData =
          res.list &&
          res.list.map(item => {
            return {
              value: item.w_weight == 0 ? '' : item.w_weight,
              color: this.statusColor(item.status)
            };
          });
        // 安全区域
        let normal_section = res.normal_section;
        // 安全区域差值
        let dValue_sectoin = $operator.subtract(Number(normal_section.max), Number(normal_section.min));
        this.line_options = this.$eChartFn.safeAreaLineOptions({
          tooltip: {
            formatter: param => {
              // 自定义tooltip
              var text = '';
              let value = param[0].value;
              let w_bmi = res.list[param[0].dataIndex] && res.list[param[0].dataIndex].w_bmi;
              let status_text = res.list[param[0].dataIndex] && res.list[param[0].dataIndex].status_text;
              // 背景色
              let bgColor = (param[0].data && param[0].data.color) || '#fff';
              // title颜色
              let titleColor = bgColor == '#fff' ? '#333' : '#fff';
              // 文本颜色
              let textColor = bgColor == '#fff' ? '#333' : '#fff';
              text += `<div style="background: ${bgColor};padding: 8px;border-radius:4px;position: relative;min-width: 100px">
                      <div style="color:${titleColor};height:auto;width:100%;margin-right:5px;font-size:14px">${
                status_text || '暂无记录'
              }</div>
                      `;

              // 当用户没有BMI数据时，展示暂无记录
              if (w_bmi && w_bmi != 0) {
                text += `<div style="color:${textColor};font-size:14px;font-weight:600;margin-top:4px;line-height:20px"><span>${
                  w_bmi || '-'
                } <span style="color: ${textColor};margin-left: 4px;">BMI</span></span></div>`;
              }

              text += `<div style="position: absolute;bottom:-11px;left:43%;width:0;height:0;border-top:11px solid ${bgColor};border-left:11px solid transparent;border-right:11px solid transparent">
                    <div style="position: absolute;top:-11px;left:-10px;width:0;height:0;border-top:10px solid ${bgColor};border-left:10px solid transparent;border-right:10px solid transparent"><div>
                    <div>
                    </div>`;
              return text;
            }
          },
          xAxis: {
            data: xData
          },
          yAxis: {
            name: 'Kg',
            min: function (value) {
              let min = value.min;
              if (isNaN(value.min)) {
                min = normal_section.min;
              } else {
                min = Number(min) > Number(normal_section.min) ? normal_section.min : min;
              }

              // 当用户没有身高是，y轴默认区间 40 - 80
              if (normal_section.min == 0 && normal_section.max == 0) {
                min = 40;
              }
              return $operator.subtract(Number(min), Number(dValue_sectoin)) < 0
                ? '0'
                : $operator.subtract(Number(min), Number(dValue_sectoin));
            },
            max: function (value) {
              let max = value.max;
              // 当最大值为NaN或者0时，用安全区域最大值替代，确保安全区域可以展示出来
              if (isNaN(value.max) || max == 0) {
                max = normal_section.max;
              }
              // 如果当前数据的最大值小于等于安全区域的最大致，当前最大值 = 安全区域最大值 + 安全区域的空间值
              if (max <= Number(normal_section.max)) {
                max = Number(normal_section.max);
              }

              // 当用户没有身高是，y轴默认区间 40 - 80
              if (normal_section.min == 0 && normal_section.max == 0) {
                max = 80;
              }

              return $operator.add(Number(max), Number(dValue_sectoin));
            }
          },
          visualMap: {
            pieces: this.setPiecesColor()
          },
          series: [
            {
              data: yData,
              showSymbol: true, // 直接显示拐点数值
              itemStyle: {
                normal: {
                  label: { show: true }
                }
              },
              markArea: {
                // 设置安全区域
                data: [
                  [
                    {
                      yAxis: normal_section.min
                    },
                    {
                      yAxis: normal_section.max
                    }
                  ]
                ]
              }
            }
          ]
        });
      });
    },

    // 设置折线图不同区间的颜色
    setPiecesColor() {
      let resultPieces = [];

      // 获取当前状态的阶段区间
      let current_section = this.bmiIndexStatus;
      for (let status in current_section) {
        let item = current_section[status].weight_between;

        // 当有上限无下限
        if (item.max && !item.min) {
          resultPieces.push({
            lt: +item.max,
            color: this.statusColor(status)
          });
        }

        // 当有下限无上限
        if (!item.max && item.min) {
          resultPieces.push({
            gt: +item.min,
            color: this.statusColor(status)
          });
        }

        // 当有上限下限区间时,设置线的区间范围和对应状态颜色
        if (item.max && item.min) {
          resultPieces.push({
            // !最小粒度为0.1，此处减去0.1，防止折线出现断层
            gt: +item.min - 0.1,
            lte: +item.max,
            color: this.statusColor(status)
          });
        }
      }
      return resultPieces;
    },
    getWeightRecord() {
      let params = {
        uid: this.$route.query.uid,
        st: this.trendTime[0],
        et: this.trendTime[1],
        clinic_id: getClinicid()
      };
      this.$api.getWeightRecord(params).then(res => {
        this.recordList = res.list;
        console.log('=>(diabetes.vue:341) this.recordList', this.recordList);
      });
    },
    getWeightOptions() {
      let params = {
        uid: this.$route.query.uid
      };
      this.$api.getWeightOptions(params).then(res => {
        // 肥胖的区间状态
        this.bmiIndexStatus = res.bmiIndexStatus;
        this.averageList = S.descToArrHandle(res.typeStatisDesc);
        this.average = this.averageList[0] && this.averageList[0].id;
        this.getWeightStatis();
      });
    }
  },

  destroyed() {}
};
</script>

<style scoped lang="less">
.fat-wrapper {
  margin: 0 16px;
  .diabetes-box {
    .content-title {
      // border-bottom: 1px solid #F2F3F5;

      h2 {
        font-size: 14px;
        color: #333333;
        font-weight: 900;
        margin-bottom: 8px;
      }
    }

    .content-data {
      min-width: 400px;
      margin: 0 0 20px;

      .content-3 {
        display: flex;
        box-sizing: border-box;
        .diabetes-item:nth-of-type(6) {
          margin-right: 0;
        }
      }

      .diabetes-item {
        flex: 1;
        border-radius: 4px;
        padding: 20px;
        padding-right: 15px;
        color: #000;
        border: 1px solid #f0f0f0;
        margin-right: 8px;
        margin-bottom: 4px;
        box-sizing: border-box;
        .chronic-title {
          margin-bottom: 24px;
          font-size: 12px;
          font-weight: 500;
          color: #999;
          line-height: 17px;
        }

        .chronic-content {
          position: relative;
          height: 36px;
          margin-bottom: 18px;
          font-size: 29px;
          font-weight: 600;
          color: #000;
          line-height: 36px;
          .unit {
            font-size: 12px;
            font-weight: 400;
            color: #000;
            line-height: 36px;
          }
          .weight-tag {
            position: absolute;
            top: 5px;
            right: -10px;
            display: inline-block;
            height: 20px;
            background: rgba(216, 216, 216, 0);
            padding: 0 5px;
            border-radius: 10px;
            color: #000000;
            font-size: 12px;
            line-height: 18px;
            text-align: center;
            border: 1px solid #000000;
          }
        }
        .chronic-time {
          font-size: 12px;
          font-weight: 500;
          color: #aaaaaa;
          line-height: 17px;
          margin-bottom: 0;
        }
      }
    }
  }

  .trend-tag-box {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    height: auto;
    .trend-tag {
      padding: 6px;
      border-radius: 2px;
      opacity: 0.8;
      border: 1px solid #bcc3d7;
      margin-right: 10px;
      cursor: pointer;
      margin-bottom: 8px;
    }

    .tag-active {
      background: #155bd4;
      opacity: 1;
      color: #ffffff;
      border-color: #155bd4;
    }
  }
}

// 颜色面板
.colorPanel {
  .colorShow {
    width: 11px;
    height: 5px;
    background: #8699ff;
    border-radius: 3px;
    margin-left: 12px;
  }
  .colorLabel {
    font-size: 12px;
    line-height: 11px;
    color: #000000;
    margin-left: 6px;
  }
}
</style>
