<template>
  <div class="custom-table-wrapper">
   
    <table class="custom-detail-table" :border="!!border" cellspacing="0">
      <thead>
        <tr>
          <th 
            v-for="(head_item, head_index) in columns" 
            :key="'heat'+head_index">
            <div class="cell"
              :style="{
                textAlign: head_item.align || 'left',
                width: `${head_item.width}px`  || '',
              }"
            >{{ head_item.title }}</div>
          </th>
        </tr>
      </thead>

      <tbody :style="{height: height,overflowY: (height && !!data.length) ? 'scroll' :''}">
        <template v-if="data.length">
          <tr
            v-for="(data_item, data_index) in data" 
            :key="'data'+data_index"
            :class="{ 'hover': !!hover }">
            <td 
              v-for="(head_item, head_index) in columns" 
              :key="'item_head'+head_index">
              <div class="cell"
                :style="{
                  textAlign: head_item.align || 'left',
                  width: `${head_item.width}px` || '',
                }"
              >
                <slot :row="data_item" :$columnIndex="head_index" :$index="data_index" v-if="head_item.slot" :name="head_item.slot"></slot>
                <template v-else>
                  {{ data_item[head_item.key] }}
                </template>
              </div>
            </td>
          </tr>
        </template>
        <tr class="empty" v-else>
          <div>{{ emptyText }}</div>
        </tr>
      </tbody>

    </table>
  </div>
</template>
<script>
  export default {
    name: "custom-detail-table",
    components: {
      
    },
    mixins: [],
    props: {
      /**
       * @description:支持columns内置的一些属性
       * { Number } width 表格宽度
       * { Number } minWidth 表格宽度 -- 暂不支持
       * { String } align cell内容的位置，left: 左, center: 中间, right: 右
       * */ 
      columns: {
        type: Array,
        default: () => []
      },
      data: {
        type: Array,
        default: () => []
      },
      // 是否显示边框
      border: {
        type: [Boolean, String],
        default: ''
      },
      // 鼠标悬浮，行样式是否显示悬浮样式
      hover: {
        type: [Boolean, String],
        default: 'true'
      },
      // 固定表格高度
      height: {
        type: String,
        default: ''
      },
      // 空内容文本显示
      emptyText: {
        type: String,
        default: '暂无数据'
      },
    },
    data () {
      return {

      }
    },
    computed: {

    },
    watch: {

    },
    created() {

    },
    mounted() {

    },
    methods: {

    },
    filters: {

    },
  }
</script>

<style lang="less" scoped>
.custom-detail-table {
  width: 100%;
  thead tr, tbody tr {
    display: flex;
  }

  thead th, tbody td {
    display: flex;
    align-items: center;
    flex: 1;
   
    color: #999999;
    font-size: 14px;
    font-weight: 300;
    padding: 9px 11px;
    width: 100%;
    white-space: normal;
    word-break: break-all;
  }

  thead tr {
    background: rgb(255, 255, 255);
    border-bottom: 1px solid rgba(0,0,0,0.06);
  }
  tbody {
    height: auto;
    display: inline-block;
    width: 100%;
  }
  tbody tr {
    background: #fff;
  }

  tbody td {
    border-bottom: 1px solid rgba(0,0,0,0.06);
    color: #333333;
  }

  // 表格空数据
  .empty {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0px;
    color: #ccc;
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    box-sizing: border-box;
  }

  
  .cell {
    width: 100%;
    display: inline-block;
    box-sizing: border-box;
    position: relative;
    padding-left: 10px;
    padding-right: 10px;
    line-height: 20px;
  }
}

.hover{
  &:hover {
    background: rgb(238, 236, 238) !important;
  }
}

.block {
  display: inline-block;
  padding: 2px 12px;
  color: #fff;
  border-radius: 3px;
}
</style>