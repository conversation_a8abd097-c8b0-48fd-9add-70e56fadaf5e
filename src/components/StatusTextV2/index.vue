<template>
  <div class="index-wrapper">
    <span class="audit-text">
      <i class="status-dot" :style="getStatusTextColor(status)"></i>
      <slot></slot>
    </span>
  </div>
</template>

<script>
export default {
  name: 'statusText',
  mixins: [],

  components: {},

  props: {
    status: {
      type: String,
      default: ''
    }
  },

  data() {
    return {};
  },

  computed: {
    getStatusTextColor() {
      /**
       * @method getStatusTextColor
       * @param {status} type  Number - description
       * @description: 0 -- 待审核  1 -- 审核通过  2 -- 审核驳回
       * @author: yangyi
       * @date: 9/2/22
       *

       #f90
       部分兑换


       #155bd4
       已过期

       #f7f7f7
       已失效
       */
      return status => {
        switch (status) {
          case '10':
          case 'TBP':
          case 'WAIT':
          case 'PROC':
            return {
              background: '#ffad33'
            };
          case '90':
          case 'ON':
          case 'UP':
          case 'SUCC':
          case 'enable':
          case 'UNDERWAY':
            return {
              background: '#19be6b'
            };
          case '70':
          case 'OFF':
          case 'DOWN':
          case 'disable':
          case 'FAIL':
          case 'SOLD_OUT':
          case 'SALE_OUT':
            return {
              background: '#ed4014'
            };
          case 'END':
            return {
              background: '#999999'
            };
        }
      };
    }
  },

  watch: {},

  created() {},

  mounted() {},

  methods: {},

  destroyed() {}
};
</script>

<style scoped lang="less">
.audit-text {
  .status-dot {
    display: inline-block;
    border-radius: 50%;
    width: 10px;
    height: 10px;
    margin-right: 4px;
  }
}
</style>
