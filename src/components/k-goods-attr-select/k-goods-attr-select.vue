<template>
  <Modal
    v-model="modalValue"
    title="选择商品"
    :mask-closable="false"
    :width="800"
    class-name="vertical-center-modal"
    footer-hide
  >
    <div style="position: relative">
      <Button to="/goods/item/list" target="_blank">商品管理</Button>
      <Dvd /><Dvd />
      <a @click="onRefresh">刷新</a>
      <div style="position: absolute; right: 0px; top: 0px">
        <Input
          v-model="queryFormData.goods_keyword"
          placeholder="搜索商品标题或者商品ID"
          clearable
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          style="width: 180px"
        >
          <Icon type="ios-search" slot="suffix" />
        </Input>
        <Dvd />
        <Input
          v-model="queryFormData.attr_keyword"
          placeholder="搜索SKU编码或者属性ID"
          clearable
          @keyup.enter.native="onSearch"
          @on-clear="onSearch"
          style="width: 180px"
        >
          <Icon type="ios-search" slot="suffix" />
        </Input>
      </div>
    </div>
    <div class="block_10"></div>
    <div style="position: relative; height: 445px; overflow: auto">
      <Table :columns="tableCols" :data="list" :loading="tableLoading">
        <template slot-scope="{ row }" slot="info">
          <div class="media-left media-middle">
            <img
              :src="row.img | imageStyle"
              style="width: 35px; margin-right: 5px"
              class="img-rounded"
              :title="'id:' + row.id"
            />
          </div>
          <div class="media-body">
            <div>
              <span class="clip">{{ row.goods.name }}</span>
            </div>
            <div>
              规格:<span class="text-muted">{{ row.spec }}</span> 价格:{{ row.price }}
            </div>
          </div>
        </template>
        <template slot-scope="{ row }" slot="sku">
          {{ row.sku }}
        </template>
        <template slot-scope="{ row }" slot="status">
          {{ statusDesc[row.goods.status].desc }}
        </template>
        <template slot-scope="{ row }" slot="stock">
          {{ row.stock }}
        </template>
        <template slot-scope="{ row }" slot="operate">
          <Button type="primary" @click="onSelected(row)">选择</Button>
        </template>
      </Table>

      <div class="block_20"></div>

      <KPage
        v-if="total > 0"
        :total="total"
        :page-size.sync="queryFormData.pageSize"
        :page-size-opts="[5]"
        :current.sync="queryFormData.page"
        @on-change="onPageChange"
        style="text-align: center"
      />
    </div>
  </Modal>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import io from '@/libs/io'; // Http request
import * as runtime from '@/libs/runtime'; // Runtime information
/* eslint-disable */

let init_query_from_data = {
  page: 1,
  pageSize: 5,
  goods_keyword: '',
  attr_keyword: '',
  status_list: [],
};

export default {
  name: 'k-goods-attr-select',
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },

  data() {
    return {
      queryFormData: { ...init_query_from_data },
      modalValue: false,

      tableCols: [
        { title: '商品名称', slot: 'info' },
        { title: 'SKU编码', slot: 'sku', width: 80 },
        { title: '状态', slot: 'status', width: 80 },
        { title: '库存', slot: 'stock', width: 80 },
        { title: '操作', slot: 'operate', width: 70 },
      ],
      tableLoading: false,

      list: [],
      total: 0,
      statusDesc: {},
    };
  },

  methods: {
    onSearch: function () {
      this.queryFormData.page = 1;
      this.get();
    },

    onPageChange: function (page, pageSize) {
      this.queryFormData.page = page;
      this.queryFormData.pageSize = pageSize;
      this.get();
    },

    onRefresh: function () {
      this.get();
    },

    onSelected: function (item) {
      this.$emit('on-selected', item);
    },

    get: function () {
      this.tableLoading = true;

      io.get('clinic/select.goodssku', { data: this.queryFormData })
        .then(data => {
          this.list = this.handler(data.list);
          this.statusDesc = data.statusDesc;
          this.total = data.total;

          this.tableLoading = false;
        })
        .catch(error => {
          {
          }
        });
    },

    handler: function (list) {
      return list;
    },

    clearQuery: function () {
      this.queryFormData = { ...init_query_from_data };
      this.queryFormData.page = 1;
      this.list = [];
      this.total = 0;
    },
  },

  watch: {
    value: function (val) {
      this.modalValue = val;
      if (val == true) {
        this.clearQuery();
        this.get();
      }
    },

    modalValue: function (val) {
      this.$emit('input', val);
    },
  },
};
</script>

<style lang="less">
.clip {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  width: 90%;
  display: inline-block;
}
</style>
