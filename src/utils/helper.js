// 判断数据类型方法
import router from "@/router";

function isObject(value) {
  const type = typeof value;
  return value !== null && (type === 'object' || type === 'function');
}

function isArray(value) {
  const checkArray = Array.isArray || (_arg => Object.prototype.toString.call(_arg) === '[object Array]');
  return checkArray(value);
}

function isString(value) {
  return Object.prototype.toString.call(value) === '[object String]';
}
export function isNumber(value) {
  // 判断正常数字，NaN， Infinity, -Infinity 返回false
  return Number.isFinite(value);
}
// 简易判空， 日常数据使用， 如果是新出的symbol set weak mapWeak 之类的不建议使用
// 第二个参数, 是否忽略空字符串。 如果为不传， 默认空字符串为空。如果传false， 表示空字符串也属于有值
function isEmpty(value, isIgnoreEmptyStr = true) {
  if (isString(value) && value.trim().length === 0 && isIgnoreEmptyStr) return true;
  if (isNumber(value)) return false;
  if (value === null || value === undefined) return true;
  if (isObject(value)) return Object.keys(value).length === 0;
  if (isArray(value)) return value.length === 0;

  return false;
}

/**
 * @description 根据key从数组查询对应的值， 主要用于表格根据 id 反显 name
 * @property arr { Array } 映射数组
 * @property value { string } 需要匹配的值
 * @property key { string } 需要匹配的字段，默认为id
 * @property name { string } 需要返回的字段，默认为name
 * @return string
 */
function formatMatch(arr, value, code = 'id', name = 'name') {
  // 如果不是数组， 或者数组为空, 直接返回空字符串
  if (!isArray(arr) || arr.length === 0) return '';
  const findName = arr.find(item => item[code] === value);
  return findName?.[name] || '';
}

function deepEqual(obj1, obj2, comparedObjects = new Set()) {
  // 递归函数用于比较两个对象是否相等
  if (obj1 === obj2) {
    return true;
  }

  if (obj1 && typeof obj1 === 'object' && obj2 && typeof obj2 === 'object') {
    // 如果两个对象是非空并且属于对象类型，则进行深度比较
    if (comparedObjects.has(obj1) || comparedObjects.has(obj2)) {
      return false; // 避免循环引用
    }

    comparedObjects.add(obj1);
    comparedObjects.add(obj2);

    const keys1 = Object.keys(obj1).sort();
    const keys2 = Object.keys(obj2).sort();

    // 对象的键集合必须相等才继续比较
    if (keys1.length !== keys2.length || !keys1.every(key => keys2.includes(key))) {
      return false;
    }

    for (const key of keys1) {
      if (!deepEqual(obj1[key], obj2[key], comparedObjects)) {
        return false;
      }
    }

    return true;
  } else if (Array.isArray(obj1) && Array.isArray(obj2)) {
    // 如果两个对象是数组，则进行递归比较
    if (obj1.length !== obj2.length) {
      return false;
    }

    for (let i = 0; i < obj1.length; i++) {
      if (!deepEqual(obj1[i], obj2[i], comparedObjects)) {
        return false;
      }
    }

    return true;
  }

  return obj1 === obj2;
}

function formatOptions(options) {
  if (!isArray(options) || isEmpty(options)) return options;
  return options?.map(option => ({
    ...option,
    code: option?.val?.kw,
    name: option?.val?.desc,
  }));
}
function formatYuan(money, unit = '¥') {
  if (isEmpty(money)) return '-';
  const yuan = money / 100;
  if (isNaN(yuan)) return '-';
  return unit + yuan?.toFixed(2);
}
function openNewPage(path, query) {
  const href = router.resolve({
    path,
    query,
  }).href;
  window.open(href, '_blank');
}

//将blob转换为file
const blobToFile = (theBlob, fileName, type) =>
  new Promise((resolve, reject) => {
    const file = new window.File([theBlob], fileName, { type });
    resolve(file);
  });

export { isEmpty, formatMatch, deepEqual, isString, formatOptions, formatYuan,openNewPage, blobToFile };
