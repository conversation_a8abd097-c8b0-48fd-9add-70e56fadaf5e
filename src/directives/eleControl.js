/**
 * @description:当前指令用户对指定元素进行权限控制
 * v-eleControl="'adwdwatfw'"
 * v-eleControl="(['EomkwKokxB1212', '-'])"
 * @note: 当前指令只绑定一次dom,如果遇到dom的更新，当前指令会不再更新
 */
import store from '@/store';
import Vue from 'vue';
// 获取全局所有的元素编码集合
const eleControl = {
  name: 'eleControl',
  bind: (el, binding, vnode) => {
    Vue &&
    Vue.nextTick(() => {
      let idcodes = store.state.router.idcodes;
      let codeValue = '';
      let replaceText = '';
      if (Array.isArray(binding.value)) {
        replaceText = binding.value[1];
        codeValue = binding.value[0];
      } else {
        codeValue = binding.value;
      }
      // 判断传过来的值是否存在元素编码集合中
      let elementIsShow = idcodes.indexOf(codeValue) > -1 || false;
      if (!elementIsShow) {
        // 创建一个注释元素
        let comment = document.createComment('无权限访问');
        if (replaceText) {
          comment = document.createElement('span');
          comment.innerHTML = replaceText;
          comment.style = 'color: #ccc';
        }
        // 设置value值
        Object.defineProperty(comment, 'setAttribute', {
          value: () => 'undefined',
        });
        // 用注释节点替换 当前页面元素
        vnode.elm = comment;
        // 下面作为初始化操作 赋值为空
        vnode.text = '';
        vnode.isComment = true;
        vnode.context = undefined;
        vnode.tag = undefined;
        vnode.data.directives = undefined;

        // 判断当前元素是否是组件  如果是组件的话也替换成 注释元素
        if (vnode.componentInstance) {
          vnode.componentInstance.$el = comment;
        }

        // 判断当前元素是否是文档节点或者是文档碎片节点
        if (el.parentNode) {
          // 从 DOM 树中删除 node 节点，除非它已经被删除了。
          el.parentNode.replaceChild(comment, el);
        }
      }
    });
  },
  //被绑定元素插入父节点时调用（父节点存在即可调用，不必存在于 document 中）。
  inserted: function () {
    //插入节点
  },
  /**
   * 被绑定元素所在模板完成一次更新周期时调用。
   * @param {*} el 当前元素
   * @param {*} 获取当前绑定的值
   */
  componentUpdated: function (el, binding) {},
  //只调用一次， 指令与元素解绑时调用。
  unbind: function (el, { value }) {},
};

export default eleControl;
