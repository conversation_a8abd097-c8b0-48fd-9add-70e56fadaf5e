import { isEmpty } from 'lodash';
import moment from "moment";

const empty = {
  name: 'empty',
  bind: function (el, binding) {
    init(el, binding);
  },
  //被绑定元素插入父节点时调用（父节点存在即可调用，不必存在于 document 中）。
  inserted: function () {
    //插入节点
  },
  /**
   * 被绑定元素所在模板完成一次更新周期时调用。
   * @param {*} el 当前元素
   * @param {*} 获取当前绑定的值
   */
  componentUpdated: function (el, binding) {
    init(el, binding);
  },
  //只调用一次， 指令与元素解绑时调用。
  // unbind: function (el, { value }) {},
};

const init = (el, binding) => {
  const emptyText = binding?.arg || '-';
  const modifiers = binding.modifiers || {};
  let text = binding?.value || '';
  if (modifiers.time && !isEmpty(text)) {
    const isDate = date => date instanceof Date && !isNaN(date);
    let date = new Date(text);
    if (isDate(date)) {
      el.innerText = moment(date).format('YYYY-MM-DD HH:mm');
      return;
    }
    date = new Date(text * 1000);
    if (isDate(date)) {
      el.innerText = moment(date).format('YYYY-MM-DD HH:mm');
      return;
    }
  }
  if (isEmpty(text)) {
    el.innerText = emptyText;
    return;
  }
  el.innerText = text;
};

export default empty;
