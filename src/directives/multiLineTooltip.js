import Vue from 'vue';
import debounce from 'lodash/debounce';
import { Tooltip } from 'element-ui';

Vue.use(Tooltip);

/**
 * 多行文本省略指令
 * 功能：超出指定行数显示省略号，鼠标悬停时显示完整内容
 *
 * 使用方式：
 * 1. 基本用法：<div v-multi-line-tooltip>长文本内容</div>
 * 2. 配置行数：<div v-multi-line-tooltip="{ line: 3 }">长文本内容</div>
 * 3. 配置tooltip最大宽度：<div v-multi-line-tooltip="{ line: 2, maxWidth: 300 }">长文本内容</div>
 * 4. 配置tooltip位置：<div v-multi-line-tooltip="{ placement: 'bottom' }">长文本内容</div>
 * 5. 配置多个参数：<div v-multi-line-tooltip="{ line: 2, maxWidth: 300, placement: 'left', effect: 'light' }">长文本内容</div>
 */

// 创建Vue实例用于挂载tooltip组件
const ctx = '@@multiLineTooltipStore';

// 生成唯一tooltip ID
const tooltipId = 'multi-line-tooltip-' + Date.now();

// tooltip容器样式
const styleEl = document.createElement('style');
styleEl.textContent = `
  .multi-line-tooltip .el-tooltip__popper {
    max-width: 300px;
    word-break: break-all;
    line-height: 1.5;
  }
`;
document.head.appendChild(styleEl);

// 使用模板创建，而不是JSX，避免h参数的问题
const vm = new Vue({
  data() {
    return {
      content: '',
      tooltipProps: {},
    };
  },
  render(h) {
    return h('el-tooltip', {
      ref: 'multiLineTooltipRef',
      props: {
        content: this.content,
        popperClass: `multi-line-tooltip ${this.tooltipProps.customClass || ''}`,
        ...this.tooltipProps,
      },
    });
  },
}).$mount();

const tooltipVM = vm.$refs.multiLineTooltipRef;
const activateTooltip = debounce(tooltipVM => tooltipVM.handleShowPopper(), 50);

// 处理器函数，判断文本是否溢出并绑定相应事件
const multiLineTooltipHandler = (el, binding) => {
  // 获取配置选项，设置默认值
  const options = binding.value || {};
  const line = options.line || 2; // 默认2行
  const maxWidth = options.maxWidth || 300; // tooltip默认最大宽度

  // 移除已有样式和事件处理
  if (el[ctx]) {
    el.removeEventListener('mouseenter', el[ctx].handleMouseEnter);
    el.removeEventListener('mouseleave', el[ctx].handleMouseLeave);
  }

  // 设置多行省略样式
  el.style.display = '-webkit-box';
  el.style.webkitBoxOrient = 'vertical';
  el.style.webkitLineClamp = line;
  el.style.overflow = 'hidden';
  el.style.textOverflow = 'ellipsis';
  el.style.wordBreak = 'break-word';

  // 为每个tooltip设置自定义类名，用于控制宽度
  const customClass = `multi-line-tooltip-${tooltipId}-${Math.floor(Math.random() * 1000)}`;

  // 动态添加最大宽度样式规则
  if (!document.getElementById(customClass)) {
    const styleEl = document.createElement('style');
    styleEl.id = customClass;
    styleEl.textContent = `
      .${customClass} {
        max-width: ${maxWidth}px !important;
      }
    `;
    document.head.appendChild(styleEl);
  }

  // 保存当前元素的文本内容和配置
  if (el[ctx]) {
    el[ctx].tooltipContent = el.innerText || el.textContent;

    // 提取tooltip支持的配置项
    const tooltipProps = {
      placement: options.placement || 'top', // 位置: top/bottom/left/right/...
      effect: options.effect || 'dark', // 主题: dark/light
      enterable: options.enterable !== undefined ? options.enterable : true, // 鼠标是否可进入tooltip
      openDelay: options.openDelay || 0, // 延迟出现
      hideAfter: options.hideAfter || 0, // 隐藏延迟
      offset: options.offset || 0, // 偏移量
      transition: options.transition || 'el-fade-in-linear', // 过渡动画
      visibleArrow: options.visibleArrow !== undefined ? options.visibleArrow : true, // 是否显示箭头
      popperOptions: options.popperOptions || {}, // popper.js 参数
      customClass: customClass,
      disabled: options.disabled || false, // 是否禁用tooltip
    };

    el[ctx].props = tooltipProps;

    // 判断是否真的溢出了 (使用scrollHeight和clientHeight比较)
    if (el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth) {
      // 文本溢出了，绑定鼠标事件
      el.addEventListener('mouseenter', el[ctx].handleMouseEnter);
      el.addEventListener('mouseleave', el[ctx].handleMouseLeave);
      el.style.cursor = 'pointer';
    }
  }
};

export default {
  name: 'multi-line-tooltip',

  // 初始化绑定时调用一次
  bind(el, binding) {
    // 初始化上下文存储
    el[ctx] = {
      tooltipContent: '',
      props: {},
      handleMouseEnter: () => {
        // 展开tooltip
        vm.content = el[ctx].tooltipContent;
        vm.tooltipProps = el[ctx].props;
        vm.$forceUpdate();
        tooltipVM.referenceElm = el;
        tooltipVM.$refs.popper && (tooltipVM.$refs.popper.style.display = 'none');
        tooltipVM.doDestroy();
        tooltipVM.setExpectedState(true);
        activateTooltip(tooltipVM);
      },
      handleMouseLeave: () => {
        // 关闭tooltip
        tooltipVM.doDestroy();
        tooltipVM.setExpectedState(false);
        tooltipVM.handleClosePopper();
      },
    };

    // 进行初始处理
    multiLineTooltipHandler(el, binding);
  },

  // 元素插入DOM时
  inserted: multiLineTooltipHandler,

  // 组件更新时
  componentUpdated: multiLineTooltipHandler,

  // 解绑时清理资源
  unbind(el) {
    if (el[ctx]) {
      el.removeEventListener('mouseenter', el[ctx].handleMouseEnter);
      el.removeEventListener('mouseleave', el[ctx].handleMouseLeave);
      delete el[ctx];
    }
  },
};
