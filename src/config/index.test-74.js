module.exports = {
  /**
   * 项目标题
   */
  title: '榕树家中医智慧诊疗平台 - 测试',

  /**
   * 项目短标题
   */
  shortTitle: '榕树家中医智慧诊疗平台 - 测试',

  /**
   * 代码版本
   */
  codeVersion: process.env.VUE_APP_CODE_VERSION,

  /**
   * @description: crypto 加密版本号
   */
  cryptoVersion: 2,

  /**
   * @description:加密key
   * @description:满足16位，php对于key的处理与js不同，统一保持16位key即可
   */
  cryptoKey: 'k1qO21c0jMuvavHT',

  /**
   * @description:加密iv偏移量
   */
  cryptoIv: 'k1qO21c0jMuvavHT',
  // 素材中心接口签名相关配置
  AssetsMapiSignKey: 'CmCSbdHYPrHm6gfM2kF',
  AssetsApiDomain: 'https://asset2sit.rsjxx.com/api-server',
  /**
   * 接口请求签名Key
   */
  mapiSignKey: 'qOOx00Op3',

  /**
   * 接口地址
   * 如：https://mapi.biranmall.com/ 、https://mapi.biranmall.com/app/
   */
  SocketDomain: 'https://uens2sit-97gyrgekjm0bvkjxxwljl.rsjxx.com',
  SocketPath: '/ws-server/uens',
  // ApiDomain: 'https://74clinic.rsjxx.com/api-server/',
  ApiDomain: 'https://clinic2uat-tntu5zjfmzda0zdnhm.rsjxx.com/api-server/',
  AssistantApiDomain: 'https://mission2uat-ohyah6uushoogiukah7ee.rsjxx.com/api-server',

  // ApiDomain: 'http://lsm2bapi-clinic.rsjadd.com/',
  // HISDomain: 'https://74his.rsjxx.com',
  HISDomain: 'https://his2uat-trlmzdimzuyytjhn2m.rsjxx.com',

  /**
   * 静态资源地址
   * 如：https://wwwcdn.biranmall.com/ 、 https://wwwcdn.biranmall.com/app/
   */
  // CdnDomain: 'https://74wwwcdn.rsjxx.com/fpc-clinic/',
  CdnDomain: 'https://wwwcdn2uat.rsjxx.com/fpc-clinic/',
  ImageDomian: 'https://static.rsjxx.com',
  /**
   * 图片资源地址
   * 如：https://test2img.biranmall.com/
   feat: 修改74环境CDN及访问地址  ImageDomian: 'https://static.rsjxx.com',

   /**
   * 本地存储Key的前缀
   * 如：测试环境前缀 TEST
   */
  storageNamePrefix: 'UAT-FPC-CLINIC',
  hisStorageNamePrefix: 'UAT-FPC-HIS-V4.0',
  socketStorageNamePrefix: 'UAT-FPC-CLINIC-SOCKET',
};
