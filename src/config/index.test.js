module.exports = {
  /**
   * 项目标题
   */
  title: '榕树家中医智慧诊疗平台 - 测试',

  /**
   * 项目短标题
   */
  shortTitle: '榕树家中医智慧诊疗平台 - 测试',

  /**
   * 代码版本
   */
  codeVersion: process.env.VUE_APP_CODE_VERSION,

  /**
   * @description: crypto 加密相关信息
   */
  cryptoVersion: 2,

  /**
   * @description:加密key
   * @description:满足16位，php对于key的处理与js不同，统一保持16位key即可
   */
  cryptoKey: 'k1qO21c0jMuvavHT',

  /**
   * @description:加密iv偏移量
   */
  cryptoIv: 'k1qO21c0jMuvavHT',

  // 素材中心接口签名相关配置
  AssetsMapiSignKey: 'CmCSbdHYPrHm6gfM2kF',
  AssetsApiDomain: 'https://asset2sit.rsjxx.com/api-server',
  /**
   * 接口请求签名Key
   */
  mapiSignKey: 'qOOx00Op3',

  /**
   * 接口地址
   * 如：https://mapi.biranmall.com/ 、https://mapi.biranmall.com/app/
   */
  SocketDomain: 'https://uens2sit-97gyrgekjm0bvkjxxwljl.rsjxx.com',
  SocketPath: '/ws-server/uens',
  // ApiDomain: 'http://bapi-clinic2test.rsjxx.com/',
  ApiDomain: 'https://clinic2sit-tnjnimgq5ndkxodk3m.rsjxx.com/api-server/',
  // ApiDomain: 'http://***************:8200/api-server/',
  // ApiDomain: 'http://clnicarvin.local/api-server/', // 本地联调（王文文）
  AssistantApiDomain: 'https://mission2sit-rx0vgdr4lkgv5rpdqrgo6.rsjxx.com/api-server',
  /**
   * 静态资源地址
   * 如：https://wwwcdn.biranmall.com/ 、 https://wwwcdn.biranmall.com/app/
   */
  // CdnDomain: 'https://wwwcdn2test.rsjxx.com/fpc-clinic/',
  CdnDomain: 'https://wwwcdn2sit.rsjxx.com/fpc-clinic/',
  HISDomain: 'https://his2sit-tnwmg3ymtq2tmge3mm.rsjxx.com',
  /**
   * 图片资源地址
   * 如：https://test2img.biranmall.com/
   */
  ImageDomian: 'https://static.rsjxx.com',

  /**
   * 本地存储Key的前缀
   * 如：测试环境前缀 TEST
   */
  storageNamePrefix: 'TEST-FPC-CLINIC',
  hisStorageNamePrefix: 'TEST-FPC-HIS-V4.0',
  socketStorageNamePrefix: 'TEST-FPC-CLINIC-SOCKET',
};
