module.exports = {
  /**
   * 项目标题
   */
  title: '榕树家中医智慧诊疗平台',

  /**
   * 项目短标题
   */
  shortTitle: '榕树家中医智慧诊疗平台',

  /**
   * 代码版本
   */
  codeVersion: process.env.VUE_APP_CODE_VERSION,

  /**
   * @description: crypto 加密相关信息
   * */
  cryptoVersion: '2',

  /**
   * @description:加密key
   * @description:满足16位，php对于key的处理与js不同，统一保持16位key即可
   */
  cryptoKey: 'AyvgTNgy5J6FkyFK',

  /**
   * @description:加密iv偏移量
   */
  cryptoIv: 'AyvgTNgy5J6FkyFK',
  // 素材中心接口签名相关配置
  AssetsMapiSignKey: 'pamkju9K18wjBEtnfkm',
  AssetsApiDomain: 'https://asset.rsjxx.com/api-server',

  /**
   * 接口请求签名Key
   */
  mapiSignKey: '3NoMQDYX3',

  /**
   * 接口地址
   * 如：https://mapi.biranmall.com/ 、https://mapi.biranmall.com/app/
   */
  SocketDomain: 'https://uens-k7rd5a2qop6o9wnyykq9v.rsjxx.com',
  SocketPath: '/ws-server/uens',
  ApiDomain: 'https://clinic.rsjxx.com/api-server/',
  // ApiDomain: 'http://clinic.rsjxx.com:8073/api-server/',
  AssistantApiDomain: 'https://mission-ooth3ooxu6tielahl4ohf.rsjxx.com/api-server',

  // ApiDomain: 'http://clinic.rsjxx.com:8073/api-server/', //生产调试
  HISDomain: 'https://his.rsjxx.com',

  /**
   * 静态资源地址
   * 如：https://wwwcdn.biranmall.com/ 、 https://wwwcdn.biranmall.com/app/
   */
  CdnDomain: 'https://wwwcdn.rsjxx.com/fpc-clinic/',

  /**
   * 图片资源地址
   * 如：https://img01.biranmall.com/
   */
  ImageDomian: 'https://static.rsjxx.com',

  /**
   * 本地存储Key的前缀
   * 如：测试环境前缀 TEST
   */
  storageNamePrefix: 'FPC-CLINIC',
  hisStorageNamePrefix: 'FPC-HIS-V4.0',
  socketStorageNamePrefix: 'FPC-CLINIC-SOCKET',
};
