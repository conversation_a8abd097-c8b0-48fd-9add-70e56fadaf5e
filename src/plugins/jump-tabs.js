import { Modal } from 'view-design';
import * as runtime from '@/libs/runtime';

const sessionName = 'sessionUserInfo';

function objHasValue(obj) {
  if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).length !== 0;
  }
  return false;
}

function checkCookie(callback) {
  console.log('checkCookie2222');
  // 获取当前的Cookie
  var currentCookie = document.cookie;

  // 检查Cookie是否发生变化
  if (currentCookie !== checkCookie.lastCookie) {
    // 触发Cookie变化的处理逻辑
    callback(currentCookie);
    // 更新上次检查的Cookie
    checkCookie.lastCookie = currentCookie;
  }
}

export default {
  install(Vue, options) {
    // 初始时获取一次Cookie
    checkCookie.lastCookie = document.cookie;

    window.addEventListener('beforeunload', event => {
      // 页面刷新时，清除sessionStorage
      sessionStorage.removeItem(sessionName);
    });

    window.addEventListener('storage', e => {
      // console.log(sessionStorage, '==================');
    });

    document.addEventListener(
      'visibilitychange',
      function (e) {
        //浏览器tab切换监听事件
        const origin = window.location.origin;
        const userInfo = runtime.getUser();

        checkCookie(() => {
          if (!objHasValue(JSON.parse(sessionStorage.getItem(sessionName)))) {
            sessionStorage.setItem(sessionName, JSON.stringify(userInfo));
          }
        });

        if (!document.visibilityState === 'visible') {
          Modal.remove();
        }

        if (document.hidden || origin === window.location.origin) {
          if (objHasValue(userInfo)) {
            if (runtime.isLogin()) {
              if (objHasValue(JSON.parse(sessionStorage.getItem(sessionName)))) {
                const sessionUserInfo = JSON.parse(sessionStorage.getItem(sessionName));
                if (sessionUserInfo.clinicid !== userInfo.clinicid) {
                  Modal.warning({
                    title: '系统检测到你当前的页面已经发生了门店变更',
                    content:
                      '出现该弹窗是因为你在其他页签上切换过门店，为了防止不同门店之间的页面信息错位，我们需要你登录到最新的门店',
                    okText: '跳转新的门店',
                    onOk() {
                      sessionStorage.removeItem(sessionName);
                      if (window.location.href.indexOf('login') > -1) {
                        window.location.href = '/';
                      } else {
                        window.location.reload();
                      }
                    }
                  });
                } else {
                  console.log('已登录，未切换门店');
                  Modal.remove();
                  if (window.location.href.indexOf('login') > -1) {
                    window.location.href = '/';
                  }
                }
              } else {
                console.log('已登录，设置sessionStorage');
                sessionStorage.setItem(sessionName, JSON.stringify(userInfo));
              }
            }
          } else {
            if (window.location.href.indexOf('login') > -1) return;
            Modal.warning({
              title: '系统检测到当前页面所在的门店已退出登录',
              content:
                '出现该弹窗是因为你在其他页签上已退出登录，为了防止不同门店之间的页面信息错位，我们需要你重新登录',
              okText: '跳转登录页',
              onOk() {
                sessionStorage.removeItem(sessionName);
                window.location.reload();
              }
            });
          }
        }
      },
      false
    );
  }
};
