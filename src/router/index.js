import Vue from 'vue';
import VueRouter from 'vue-router';
import store from '@/store';
import { constantRoutes } from './router';
import S from '@/libs/util';
import { getAssetAppKey, isLogin, logout } from '@/libs/runtime';
import io from '@/libs/io';
import { debounce } from 'lodash';
// import {LoadingBar} from 'view-design'
Vue.use(VueRouter);

const router = new VueRouter({
  mode: 'history',
  base: '/v1/',
  routes: constantRoutes,
});

router.beforeEach(async (to, from, next) => {
  // console.log("-> %c to, from, next  === %o", "font-size: 15px;color: green;", to, from, next)
  Vue.prototype.$Modal.remove();
  // LoadingBar.start();
  if (isLogin()) {
    const has = store.state.router.routes && store.state.router.routes.length > 0;
    if (has) {
      next();
    } else {
      let debug = false,
        userRoutes = [];

      if (!debug) {
        userRoutes = await store.dispatch('router/getUserRoutes');
        const app_key = getAssetAppKey();
        console.log('%c=>(index.js:39) app_key', 'color: #ECA233;font-size: 16px;', app_key);
        if (!app_key) {
          const app_key_res = await store.dispatch('app/getAssetAppKey');
          console.log('%c=>(index.js:41) app_key_res', 'color: #ECA233;font-size: 16px;', app_key_res);
        }
      } else {
        //初始化项目时没有路由的情况下使用下面的逻辑，来访问已有的页面，如:菜单管理页面...
        userRoutes.push({
          path: to.path,
          component: () => import(`../view${to.path}`),
        });
        store.commit('router/SET_ROUTES', userRoutes);
      }
      // console.log("=>(index.js:31) userRoutes", userRoutes);

      // router.addRoutes(userRoutes)
      userRoutes.forEach(route => {
        router.addRoute(route);
      });
      next({ ...to, replace: true });
    }
  } else {
    logout();
    if (to.matched.some(_ => _.meta.notNeedLogin)) {
      next();
    } else {
      next('/login?from=' + encodeURIComponent(to.fullPath));
    }
  }
});

router.afterEach(to => {
  S.setTitle(to);
  window.scrollTo(0, 0);
  store.commit('app/SHOW_HELP_WRAPPER', false);
  // LoadingBar.finish();
  // 20210506 by lsm 尝试解决开启Modal浏览器返回前一个页面时，body中overflow hidden样式未清除问题
  document.body?.removeAttribute('style');
  // Vue.prototype.$Modal.remove()
  // if (to.path.startsWith('/stock') && isLogin()) {
  //   io.get('/clinic/product.product.existPriceLowProduct').then(res => {
  //     let isBadge = Boolean(Number(res.exist));
  //     store.commit('router/SET_ROUTE_BADGE', {
  //       path: '/stock/adjust-price/list',
  //       isBadge,
  //     });
  //   });
  // }
  getStockNotice(to);
});

const getStockNotice = debounce(
  function (to) {
    if (isLogin()) {
      // io.get('/clinic/product.stock.listnotice').then(res => {
      //   store.commit('router/SET_ROUTE_BADGE', {
      //     path: '/stock/instock/warning_list',
      //     isBadge: res.notice === '1',
      //   });
      // });
      if (to.path.startsWith('/stock')) {
        console.log(to);
        io.get('/clinic/product.product.existPriceLowProduct').then(res => {
          let isBadge = Boolean(Number(res.exist));
          store.commit('router/SET_ROUTE_BADGE', {
            path: '/stock/adjust-price/list',
            isBadge,
          });
        });
      }
    }
  },

  5000,
  {
    leading: true,
    trailing: false,
    maxWait: 8000,
  }
);
export default router;
