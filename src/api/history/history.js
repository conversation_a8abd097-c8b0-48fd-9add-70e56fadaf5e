class History {
  //获取处方单信息
  async getDispensingDetails(query) {
    const res = await this.$http.get('/clinic/medical.dispense.detail', { params: query });
    return res;
  }

  //获取收费单信息
  async getBillDetails(query) {
    const res = await this.$http.get('/clinic/hisorder.order.detail', { params: query });
    return res;
  }

  async getmedicinereplace(params) {
    const res = await this.$http.post('/clinic/medical.assistant.medicinereplace', params);
    return res;
  }

  // 获取治疗单选项
  async getCureRecordList(params) {
    const res = await this.$http.get('/clinic/goods.index.list', { params });
    return res;
  }

  //获取轮播图列表
  async getCarouselList(query) {
    const res = await this.$http.get('/clinic/setting.slideimg.list', { params: query });
    return res;
  }

  //获取轮播图详情
  async getCarouselDetail(query) {
    const res = await this.$http.get('/clinic/setting.slideimg.get', { params: query });
    return res;
  }

  //删除轮播图
  async deleteCarousel(query) {
    const res = await this.$http.get('/clinic/setting.slideimg.del', { params: query });
    return res;
  }

  //获取轮播图状态列表
  async getCarouselStatusList() {
    const res = await this.$http.get('/clinic/setting.slideimg.option');
    return res;
  }

  //添加/编辑轮播图
  async editCarousel(params) {
    const res = await this.$http.post('/clinic/setting.slideimg.edit', params);
    return res;
  }

  //手动结束轮播图
  async endCarousel(query) {
    const res = await this.$http.get('/clinic/setting.slideimg.end', { params: query });
    return res;
  }

  //获取医师列表
  async getPhysicianList(query) {
    const res = await this.$http.get('/clinic/setting.doctor.list', { params: query });
    return res;
  }

  //获取医师详情
  async getPhysicianDetailInfo(query) {
    const res = await this.$http.get('clinic/setting.doctor.get', { params: query });
    return res;
  }

  /**
   *@param {string} id required
   * {string} is_enable required 启用状态 1:启用2:禁用
   */
  //医师禁用/启用
  async editPhyAbleStatus(query) {
    const res = await this.$http.get('/clinic/setting.doctor.enable', { params: query });
    return res;
  }

  //获取医师状态列表
  async getPhysicianStatusList() {
    const res = await this.$http.get('/clinic/setting.doctor.option');
    return res;
  }

  //添加/编辑医师
  async editPhysician(params) {
    const res = await this.$http.post('/clinic/setting.doctor.edit', params);
    return res;
  }

  //删除医师
  async deletePhysician(query) {
    const res = await this.$http.get('/clinic/setting.doctor.del', { params: query });
    return res;
  }

  //医师排序
  async rankingPhysician(query) {
    const res = await this.$http.get('/clinic/setting.doctor.order', { params: query });
    return res;
  }

  // 找回密码
  async changeRetrievepass(params) {
    const res = await this.$http.post('/clinic/index.retrievepass', params);
    return res;
  }

  // 密码登录
  async getAccountlogin(params) {
    const res = await this.$http.post('/clinic/index.accountlogin', params);
    return res;
  }

  // 确认使用系统
  async getConfirmUseSystem(params) {
    const res = await this.$http.post('/clinic/index.confirmUseSystem', params);
    return res;
  }

  // 更新密码
  async getChgpass(params) {
    const res = await this.$http.post('/clinic/member.chgpass', params);
    return res;
  }

  //用户详情
  async getUserInfo(query) {
    return await this.$http.get('/clinic/user.user.get', { params: query });
  }

  //
  //用户画像
  async getfansPortrait(query) {
    return await this.$http.get('/clinic/user.user.portrait', { params: query });
  }

  // 用户概况

  async getfansStatistics(query) {
    return await this.$http.get('/clinic/statistics.get', { params: query });
  }

  //用户参数
  async getUserType() {
    return await this.$http.get('/clinic/user.user.option');
  }

  //用户消费列表
  async getUserCostList(query) {
    return await this.$http.get('/clinic/user.user.orders', { params: query });
  }

  // 获取门店列表
  async getshopList(query) {
    const res = await this.$http.get('/clinic/index.cliniclist', { params: query });
    return res;
  }

  // 获取his登陆token
  async getHisToken() {
    const res = await this.$http.get('/clinic/member.histoken');
    return res;
  }

  // 获取采购下单确认信息
  async getPurchaseConfirmInfo(params) {
    return await this.$http.post('/clinic/pms.pay.confirm', params);
  }

  // 确认支付
  async confirmPayment(params) {
    return await this.$http.post('/clinic/pms.pay.payment', params);
  }

  // 主体校验-未认证不可支付
  async getyztauditstatus() {
    return await this.$http.get('/clinic/setting.authentication.getyztauditstatus');
  }

  // 确认商品库存和上架状态
  async confirmGoodsStatus(params) {
    return await this.$http.post('/clinic/pms.goodssku.cartgoods', params);
  }

  /**************************** 数据概况*******************************/
  // // 数据概况信息
  // async getOverviewInfo(params) {
  //   return await this.$http.get('/clinic/statis.index.overview', {params})
  // }
  // // 数据概况信息
  // async getOverviewInfo(params) {
  //   return await this.$http.get('/clinic/statisv2.overview.index', {params})
  // }

  // // 数据概况枚举
  // async getOverviewOptions(params) {
  //   return await this.$http.get('/clinic/statis.index.options', {params})
  // }

  // 交易分析信息
  async getPurAnalyzeInfo(params) {
    return await this.$http.get('/clinic/statis.trade.overview', { params });
  }

  // // 交易分析枚举
  // async getOverviewOptions() {
  //   return await this.$http.get('/clinic/statis.index.options')
  // }

  /**************************** 商品分析 *******************************/
  //整体概况 - HIS诊疗
  async getHISOverview(params) {
    return await this.$http.get('/clinic/statis.goods.hisoverview', { params });
  }

  //整体概况 - 小程序商城
  async getMILOverview(params) {
    return await this.$http.get('/clinic/statis.goods.shopoverview', { params });
  }

  //商品明细 - HIS诊疗
  async getGoodsDetails(params) {
    return await this.$http.get('/clinic/statis.goods.hisdetails', { params });
  }

  //小程序商城明细
  async getShopDetails(params) {
    return await this.$http.get('/clinic/statis.goods.shopdetails', { params });
  }

  /******************* 采购分析 *********************/
  //采购整体概况
  async getPurchaseOverview(params) {
    return await this.$http.get('/clinic/statis.purchase.overview', { params });
  }

  /******************* 用户分析 *********************/
  //获取用户概览
  async getCustomerOverview(params) {
    return await this.$http.get('/clinic/statis.customer.overview', { params });
  }

  //获取用户分析概览
  async getCustomerAnalysis(params) {
    return await this.$http.get('/clinic/statis.customer.analysis', { params });
  }

  //获取用户画像
  async getCustomerPortrait(params) {
    return await this.$http.get('/clinic/statis.customer.portrait', { params });
  }

  // // 获取条目趋势信息
  // async getItemTrendInfo(params) {
  //   return await this.$http.get('/clinic/statis.index.trend', {params})
  // }

  /**************************** 交易分析 *******************************/
  //整体概况
  async getTradeOverview(params) {
    return await this.$http.get('/clinic/statis.trade.overview', { params });
  }

  // 交易相关信息
  async getTradeTradedetails(params) {
    return await this.$http.get('/clinic/statis.trade.tradedetails', { params });
  }

  /**
   * @description:2021-10-29 新增模板的下载以及excel上传
   */
  // 诊所货品下载模板
  async getClinicTemplateurl(params) {
    return await this.$http.get('/clinic/product.report.gettemplateurl', { params });
  }

  // 批量修改药品价格
  async saveBatcheditprice(params) {
    return await this.$http.post('/clinic/product.product.batcheditprice', params);
  }

  // 批量导入入库单
  async saveBatchprodstock(params) {
    return await this.$http.post('/clinic/ostock.batchprodstock', params);
  }

  /**
   * @description:2021-11-05 卡券 订单支付
   */
  // 获取技师列表
  async getArtificerList(params) {
    return await this.$http.get('/clinic/service.artificer.list', { params });
  }

  // 更改技师状态
  async changestatus(params) {
    return await this.$http.get('/clinic/service.artificer.changestatus', { params });
  }

  // 获取技师选项
  async getArtificerOptions(params) {
    return await this.$http.get('/clinic/service.artificer.options', { params });
  }

  // 导出技师统计表
  async getArtificerUrl(params) {
    return await this.$http.get('/clinic/service.report.getartificerurl', { params });
  }

  // 新建技师或编辑
  async artificerEdit(params) {
    return await this.$http.post('/clinic/service.artificer.edit', params);
  }

  // 修改卡券核销记录
  async updateChangecheckin(params) {
    return await this.$http.post('/clinic/service.card.changecheckin', params);
  }

  // 卡券明细
  async getServiceCardList(params) {
    return await this.$http.get('/clinic/service.card.list', { params });
  }

  // 获取用户列表
  async getUserList(params) {
    return await this.$http.get('/clinic/user.user.list', { params });
  }

  // 创建订单
  async orderCreate(params) {
    return await this.$http.post('/clinic/order.order.create', params);
  }

  // 获取订单信息
  async getOrderInfo(params) {
    return await this.$http.get('/clinic/order.pay.info', { params });
  }

  // 确认支付-现金
  async orderPayConfirm(params) {
    return await this.$http.get('/clinic/order.pay.confirm', { params });
  }

  // 提交预付款-微信+现金
  async orderPayShop(params) {
    return await this.$http.post('/clinic/order.pay.shop', params);
  }

  // 获取微信支付状态
  async getPaystate(params) {
    return await this.$http.get('/clinic/order.pay.paystate', { params });
  }

  // 获取出入库草稿
  async getStockDraft(params) {
    return await this.$http.get('/clinic/ostock.getdraft', { params });
  }

  // 保存出入库草稿
  async saveStockDraft(params) {
    return await this.$http.post('/clinic/ostock.savedraft', params);
  }

  /**
   * @description: 2022.01.06 到店统计
   */

  // 到店统计-获取到店列表
  async getArrivalList(params) {
    return await this.$http.get('/clinic/user.arrival.list', { params });
  }

  // 到店统计-到店统计选项
  async getArrivalOptions(params) {
    return await this.$http.get('/clinic/user.arrival.options', { params });
  }

  // 到店统计-用户编辑
  async editArrival(params) {
    return await this.$http.post('/clinic/user.arrival.edit', params);
  }

  // 到店统计删除
  async delArrival(params) {
    return await this.$http.get('/clinic/user.arrival.del', { params });
  }

  // 到店统计详细页获取数据
  async getArrivalInfo(params) {
    return await this.$http.get('/clinic/user.arrival.info', { params });
  }

  /**
   * @description:2022-01-06 业绩统计
   */
  // 创建用户
  async createUser(params) {
    return await this.$http.post('/clinic/user.user.create', params);
  }

  // 获取列表
  async getPerformanceList(params) {
    return await this.$http.get('/clinic/performance.performance.list', { params });
  }

  // 删除业绩统计明细
  async deletePerformanceList(params) {
    return await this.$http.post('/clinic/performance.performance.del', params);
  }

  // 获取创建信息
  async getCreateinfo(params) {
    return await this.$http.get('/clinic/performance.performance.createinfo', { params });
  }

  // 获取业绩统计选项信息
  async getPerformanceOptions(params) {
    return await this.$http.get('/clinic/performance.performance.options', { params });
  }

  // 批量创建
  async createPerformance(params) {
    return await this.$http.post('/clinic/performance.performance.batchsave', params);
  }

  // 到店统计列表下载
  async getarrivalurl(params) {
    return await this.$http.get('/clinic/user.report.getarrivalurl', { params });
  }

  // 业绩统计列表下载
  async getperformanceurl(params) {
    return await this.$http.get('/clinic/performance.report.getperformanceurl', { params });
  }

  // 获取业绩信息
  async getPerformanceInfo(params) {
    return await this.$http.get('/clinic/performance.performance.info', { params });
  }

  // 修改业绩信息
  async editPerformanceInfo(params) {
    return await this.$http.post('/clinic/performance.performance.save', params);
  }

  // 更新商品零售价
  async updateRetailPrice(params) {
    return await this.$http.post('/clinic/ostock.editretailprice', params);
  }

  /**
   * @description:2022-02-11 商品+订单
   */
  // 获取用户地址
  async getUserAddress(params) {
    return await this.$http.get('/clinic/order.order.getaddress', { params });
  }

  // 导出药品库存
  async getprodstockurl(params) {
    return await this.$http.get('/clinic/product.report.getprodstockurl', { params });
  }

  //二次采购相关接口
  async createTwicePurchase(params) {
    return await this.$http.post('/clinic/pms.order.create', params);
  }

  // 获取榕益卡赠品列表
  async getUserGetgiftdetail(params) {
    return await this.$http.get('/clinic/user.user.getgiftdetail', { params });
  }

  // 发货榕益卡赠品
  async getUserCreategiftorder(params) {
    return await this.$http.post('/clinic/user.user.creategiftorder', params);
  }

  // 查看榕益卡是否存在待支付的
  async getUserGetrywaitpayorder(params) {
    return await this.$http.get('/clinic/user.user.getrywaitpayorder', { params });
  }

  // 确认变更省公司
  async confirmChangeCompany(params) {
    return await this.$http.post('/clinic/index.confirmChangeCompany', params);
  }
}

export default History;
