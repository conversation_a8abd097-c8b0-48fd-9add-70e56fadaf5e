export default class CouponApi {
  async getCouponList(params) {
    return await this.$http.get('/clinic/coupon.batch.list', { params });
  }

  async getCouponOption(params) {
    return await this.$http.get('/clinic/coupon.batch.option', { params });
  }

  async getCouponInfo(params) {
    return await this.$http.get('/clinic/coupon.batch.detail', { params });
  }

  async exchangeCoupon(params) {
    return await this.$http.post('/clinic/exchange.card.exchange', params);
  }
  // 创建优惠券
  async createCoupon(params) {
    return await this.$http.post('/clinic/coupon.batch.create', params);
  }
  //更新优惠券状态
  async updateCouponStatus(params) {
    return await this.$http.post('/clinic/coupon.batch.setstatus', params);
  }
  //更新库存
  async updateCouponStock(params) {
    return await this.$http.post('/clinic/coupon.batch.setstock', params);
  }

  // 用户详情-获取优惠券列表
  async getUserCouponIndexList(params) {
    return await this.$http.get('/clinic/coupon.index.list', {params});
  }

}
