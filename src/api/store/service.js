class service {
  /**
   * @description: 卡券(2022.3.16)
   */

  // 获取服务类型
  async getServiceType(params) {
    const res = await this.$http.get('/clinic/goods.goodsservice.servtype', { params });
    return res;
  }

  // 用户列表-卡券明细-新版
  async getCardbatch(params) {
    const res = await this.$http.get('/clinic/service.card.cardbatch', { params });
    return res;
  }

  // 用户列表-卡券批次详情-新版
  async getCarddetail(params) {
    const res = await this.$http.get('/clinic/service.card.carddetail', { params });
    return res;
  }

  // 用户列表-卡券明细-获取核销信息
  async getCardno(params) {
    const res = await this.$http.get('/clinic/service.card.cardno', { params });
    return res;
  }

  // 获取卡券列表tab状态
  async getCardStatusoption(params) {
    const res = await this.$http.get('/clinic/service.card.statusoption', { params });
    return res;
  }

  /**
   * @description: 核销明细 2022.04.24
   * */
  // 获取技师列表
  async getWriteArtificerlist(params) {
    const res = await this.$http.get('/clinic/service.card.artificerlist', { params });
    return res;
  }

  // 获取核销明细列表
  async getWriteofflist(params) {
    const res = await this.$http.get('/clinic/service.card.writeoff', { params });
    return res;
  }

  // 导出核销明细列表
  async exportwriteoff(params) {
    const res = await this.$http.get('/clinic/service.card.exportwriteoff', { params });
    return res;
  }

  // 修改卡券核销记录  接口：(updateChangecheckin) saved in history.js

  // 获取储值购买的枚举值
  async getIndexOptions(params) {
    const res = await this.$http.get('/clinic/goods.index.options', { params });
    return res;
  }

  /**
   * @description: 卡券核销 2022.11.17
   * */
  // 发送服务卡券核销短信验证码
  async serviceSendauthcode(params) {
    const res = await this.$http.get('/clinic/service.card.sendauthcode', { params });
    return res;
  }

  // 核销卡券
  async serviceCardCheckin(params) {
    const res = await this.$http.post('/clinic/service.card.checkin', params);
    return res;
  }
  // 外部卡券核销
  async serviceOutCardCheckin(params) {
    const res = await this.$http.post('/clinic/service.card.outcardcheckin', params);
    return res;
  }
  // 外部卡券核销 关联预约单
  async serviceOutCardReserveList(params) {
    const res = await this.$http.get('/clinic/service.card.reservelist', { params });
    return res;
  }

  // 获取卡券详情
  async serviceCardGet(params) {
    const res = await this.$http.get('/clinic/service.card.get', { params });
    return res;
  }

  // 获取核销H5验证url
  async getServiceCardWriteoffurl(params) {
    const res = await this.$http.get('/clinic/service.card.writeoffurl', { params });
    return res;
  }

  // 获取核销H5签名
  async getsignatureimage(params) {
    const res = await this.$http.get('/clinic/service.card.getsignatureimage', { params });
    return res;
  }

  /**
   * @description: 通兑券 2022.11.17
   * */
  // 获取通兑券列表
  async getExchangeCardList(params) {
    return await this.$http.get('/clinic/exchange.card.list', { params });
  }

  // 获取通兑券枚举
  async getExchangeCardOptions(params) {
    return await this.$http.get('/clinic/exchange.card.options', { params });
  }

  // 兑换卡券
  async exchangeCard(params) {
    return await this.$http.post('/clinic/exchange.card.exchange', params);
  }

  // 获取通兑券枚举
  async getExchangeCardInfo(params) {
    return await this.$http.get('/clinic/exchange.card.info', { params });
  }
  // 通兑券列表导出
  async exportExchangeList(params) {
    return await this.$http.get('/clinic/exchange.card.export', { params });
  }

  // 卡券列表导出
  async exportCardList(params) {
    return await this.$http.get('/clinic/service.card.cardBatchExport', { params });
  }
  // 获取枚举
  async getServiceCardOptions(params) {
    return await this.$http.get('/clinic/service.card.options', { params });
  }

  //  获取榕树堂技师
  async getOrderRevenueMembers(params) {
    const res = await this.$http.get('/clinic/order.revenue.members', { params });
    return res;
  }

  // 延期通兑券
  async extensionExchangeCard(params) {
    return await this.$http.post('/clinic/exchange.card.extension', params);
  }
  // 作废通兑券
  async invalidExchangeCard(params) {
    return await this.$http.post('/clinic/exchange.card.voided', params);
  }
  // 延期卡券
  async extensionCard(params) {
    return await this.$http.post('/clinic/service.card.extension', params);
  }
  // 延期卡券
  async invalidCard(params) {
    return await this.$http.post('/clinic/service.card.voided', params);
  }
  // 获取上传二维码
  async getUploadImageQrCode(params) {
    return await this.$http.post('/clinic/service.card.uploadimageqr', params);
  }
  // 获取核销明细详情
  async getServiceCardDetail(params) {
    return await this.$http.get('/clinic/service.card.detail', { params });
  }
}

export default service;
