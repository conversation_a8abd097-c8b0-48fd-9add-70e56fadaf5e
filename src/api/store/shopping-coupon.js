export default class ShoppingCouponApi {
  // 活动枚举选项
  async getShoppingCouponOption(params) {
    return await this.$http.get('/clinic/activity.index.option', { params });
  }
  // 活动列表
  async getShoppingCouponList(params) {
    return await this.$http.get('/clinic/activity.index.list', { params });
  }

  // 活动详情
  async getShoppingCouponInfo(params) {
    return await this.$http.get('/clinic/activity.index.detail', { params });
  }

  // 获取绑定商品的活动
  async getShoppingCouponBindGoods(params) {
    return await this.$http.get('/clinic/activity.goods.bindactivity', { params });
  }
  // 创建活动
  async generateShoppingCoupon(params) {
    return await this.$http.post('/clinic/activity.index.generate', params);
  }

  // 更新活动状态
  async updateShoppingCouponStatus(params) {
    return await this.$http.post('/clinic/activity.index.setstatus', params);
  }
  // 搜索优惠券
  async getUsableCouponBatch(params) {
    return await this.$http.get('/clinic/activity.index.usablecouponbatch', { params });
  }
  // 获取优惠券详情

  async getListCouponInfo(params) {
    return await this.$http.get('/clinic/activity.index.couponbatch', { params });
  }
}
