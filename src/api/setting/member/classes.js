class Methods_Classes {
  // 获取班次列表
  async getR2Range<PERSON>ist(params) {
    return await this.$http.get('/clinic/reservev2.range.list', { params });
  }

  // 创建班次
  async getR2RangeStore(params) {
    return await this.$http.post('/clinic/reservev2.range.store', params);
  }

  // 修改班次
  async getR2RangeUpdate(params) {
    return await this.$http.post('/clinic/reservev2.range.update', params);
  }

  // 获取班次详情
  async getR2RangeShow(params) {
    return await this.$http.get('/clinic/reservev2.range.show', { params });
  }

  // 班次冲突检查
  async getR2RangeCheckclash(params) {
    return await this.$http.post('/clinic/reservev2.range.checkclash', params);
  }
}

export default Methods_Classes;
