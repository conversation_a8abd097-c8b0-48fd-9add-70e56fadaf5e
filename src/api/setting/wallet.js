class Wallet {
  // 榕树堂员工钱包列表
  async getRstMemberWalletList(params) {
    return this.$http.get('/clinic/RstMemberWallet.list', { params });
  }
  // 榕树堂员工钱包绑定银行卡
  async bindRstMemberWalletBank(params) {
    return await this.$http.post('/clinic/RstMemberWallet.bindbank', params);
  }
  // 员工钱包资产列表
  async getRstMemberBalanceList(params) {
    return this.$http.get('/clinic/RstMemberWallet.balancelist', { params });
  }
  // 获取员工资产，按月度聚合
  async getMonthlyAssetsList(params) {
    return this.$http.get('/clinic/RstMemberWallet.monthlyAssetsList', { params });
  }
  // 获取员工资产明细
  async getAssetsList(params) {
    return this.$http.get('/clinic/RstMemberWallet.assetsList', { params });
  }
}

export default Wallet;
