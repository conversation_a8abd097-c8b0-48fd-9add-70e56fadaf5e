class Method_plateform {
  /**
   * @description: 功能管理
   * */

  // 功能管理-元素列表
  async getElementlist(params) {
    return await this.$http.get('/clinic/permission.resource.elementlist', { params });
  }

  // 功能管理-添加元素
  async addelement(params) {
    return await this.$http.post('/clinic/permission.resource.addelement', params);
  }

  // 功能管理-编辑元素
  async editelement(params) {
    return await this.$http.post('/clinic/permission.resource.editelement', params);
  }

  // 删除功能
  async delResource(params) {
    return await this.$http.post('/clinic/permission.resource.del', params);
  }

}

export default Method_plateform;
