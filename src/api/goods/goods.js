class Method_goods {
  /**
   * @description: 商品库
   */

  // 获取商品库列表
	async getGoodsLibList(params){
		return await this.$http.get('clinic/pms.libgoods.list', {params});
  }

  // 获取商品库选项枚举值
  async getGoodsLibOptions(params) {
    return await this.$http.get('clinic/pms.libgoods.options', { params });
  }

  // 获取商品信息
  async getGoodsLibInfo(params) {
    return await this.$http.get('clinic/pms.libgoods.getinfo', { params });
  }

  // 添加商品到售卖列表
  async addBatchGoods(params) {
    return await this.$http.post('clinic/pms.libgoods.addbatchgoods', params);
  }

  // 获取商品库操作记录
  async getGoodsOperationlog(params) {
    return await this.$http.get('clinic/pms.libgoods.operationlog', { params });
  }

  // 获取商品库操作记录
  async getLogSnapshot(params) {
    return await this.$http.get('clinic/pms.libgoods.logsnapshot', { params });
  }

  // 获取商品管理列表
  async getGoodsList(params) {
    return await this.$http.get('clinic/goods.index.list', { params });
  }

  /**
   * @description: 商品管理
   */

  // 确认诊所地址
  async confirmAddr(params) {
    return await this.$http.get('/clinic/clinic.confirmaddr', { params });
  }

  /**
   * @description: 商品管理
   */

  // 确认诊所地址
  async getGoodsServiceList(params) {
    return await this.$http.get('/clinic/goods.goodsservice.list', { params });
  }

  // 获取套餐商品信息
  async getPackageGoodsInfo(params) {
    return await this.$http.get('/clinic/pms.libgoods.gettcgoods', { params });
  }

  // 服务枚举
  async getGoodsserviceOptions(params) {
    return await this.$http.get('/clinic/goods.goodsservice.options', { params });
  }

  // 更新服务状态
  async changeGoodsserviceStatus(params) {
    return await this.$http.post('/clinic/goods.goodsservice.status', params);
  }


  /*   技能点选项  */
  async getSkillOptions(params) {
    return await this.$http.get('/clinic/goods.goodsservice.skillOptions', { params });
  }

  /*   获取可以做这个服务的理疗师  */
  async getCanPhysioList(params) {
    return await this.$http.get('/clinic/goods.goodsservice.physioList', { params });
  }
}

export default Method_goods;
