class TwicePurchase {
  /**
   * @file: twicePurchase.js
   * @method cancelPurchaseOrder
   * @param {string} order_code - 采购单code
   * @description: 取消采购订单
   * @author: yangyia
   * @date: 2/19/22
   */
  async cancelPurchaseOrder(params){
    return await this.$http.post('/clinic/pms.order.close',params)
  }
  async getEditOrderInfo(params){
    return await this.$http.get('/clinic/pms.order.editinfo', { data:params})
  }
  async recommitPurOrder(params){
    return await this.$http.post('/clinic/pms.order.recommit', params)
  }
  async getPurstockInfo(params){
    return await this.$http.get('/clinic/pms.order.putstockinfo', { data:params})
  }
  async cancelOstock(params){
    return await this.$http.post('/clinic/pms.ostock.cancel', params)
  }
  async editOstock(params){
    return await this.$http.post('/clinic/pms.ostock.edit', params)
  }
}
export default TwicePurchase
