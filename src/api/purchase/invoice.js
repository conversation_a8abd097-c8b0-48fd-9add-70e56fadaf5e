export default class invoice {
  // 发票管理列表
  async getPurInvoiceList(params) {
    return await this.$http.get("/clinic/joinin.invoice.list", {params});
  }

  // 发票管理列表枚举数据
  async getPurInvoiceOption(params) {
    return await this.$http.get("/clinic/joinin.invoice.option", {params});
  }

  // 发票管理列表导出
  async getPurInvoiceExport(params) {
    return await this.$http.get("/clinic/joinin.invoice.export", {params});
  }

  // 获取采购订单
  async getPurInvoicePurchaseorder(params) {
    return await this.$http.get("/clinic/joinin.invoice.purchaseorder", {params});
  }

  // 获取所有红票订单
  async getRedinvopurchaseorder(params) {
    return await this.$http.get("/clinic/joinin.invoice.redinvopurchaseorder", {params});
  }

  // 商品明细
  async getPurInvoiceGoodsdetails(params) {
    return await this.$http.get("/clinic/joinin.invoice.goodsdetails", {params});
  }

  // 获取最后申请开票的收票人信息
  async echoInvoiceApplicant(params) {
    return await this.$http.get("/clinic/joinin.invoice.applicant", {params});
  }

  // 获取发票金额
  async getInvoiceAmount(params) {
    return await this.$http.get("/clinic/joinin.invoice.getamount", {params});
  }

  // 发票申请/驳回修改
  async createInvoiceGenerate(params) {
    return await this.$http.post("/clinic/joinin.invoice.generate", params);
  }

  // 发票详情
  async getPurInvoiceDetail(params) {
    return await this.$http.get("/clinic/joinin.invoice.detail", {params});
  }

  // 导出开票结果
  async invoiceExportdetail(params) {
    return await this.$http.get("/clinic/joinin.invoice.exportdetail", {params});
  }


  // 检测开票信息是否完善
  async checkInvoiceStatus(params) {
    return await this.$http.get("/clinic/pms.invoiceExamine.checkInvoiceStatus", {params});
  }
}
