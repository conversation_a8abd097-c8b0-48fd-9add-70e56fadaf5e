class User_methods {
  /**
   * @file: twicePurchase.js
   * @method cancelPurchaseOrder
   * @param {string} order_code - 采购单code
   * @description: 取消采购订单
   * @author: yangyia
   * @date: 2/19/22
   */
  async editUserInfo(params) {
    return await this.$http.post('/clinic/user.user.edit', params);
  }

  async getStoredList(params) {
    return await this.$http.get('/clinic/user.user.rechargeorders', { params });
  }
  //获取用户储值消费记录
  async getUserRechargeRecords(params) {
    return await this.$http.get('/clinic/user.user.rechargerecords', { params });
  }

  //获取用户钱包信息
  async getUserWalletInfo(params) {
    return await this.$http.get('/clinic/recharge.account.wallet', { params });
  }

  //  获取最后一次优惠金额
  async getLastDiscount(params) {
    return await this.$http.get('/clinic/recharge.refund.lastdiscount', { params });
  }
  //  提交退款申请
  async refundApply(params) {
    return await this.$http.post('/clinic/recharge.refund.apply', params);
  }

  /*
   * @description: 糖尿病
   * */

  //  获取血糖概览
  async getBgOverview(params) {
    return await this.$http.get('/clinic/health.bg.overview', { params });
  }

  //  获取血糖阶段枚举
  async getBgOptions(params) {
    return await this.$http.get('/clinic/health.bg.options', { params });
  }

  //  获取血糖图标趋势
  async getBgStatis(params) {
    return await this.$http.get('/clinic/health.bg.statis', { params });
  }
  //  获取血糖录入明细
  async getBgRecord(params) {
    return await this.$http.get('/clinic/health.bg.record', { params });
  }

  /*
   * @description: 高血压
   * */

  //  获取血压概览
  async getBpOverview(params) {
    return await this.$http.get('/clinic/health.bp.overview', { params });
  }

  //  获取血压阶段枚举
  async getBpOptions(params) {
    return await this.$http.get('/clinic/health.bp.options', { params });
  }

  //  获取血压图标趋势
  async getBpStatis(params) {
    return await this.$http.get('/clinic/health.bp.statis', { params });
  }
  //  获取血压录入明细
  async getBpRecord(params) {
    return await this.$http.get('/clinic/health.bp.record', { params });
  }

  /*
   * @description: 肥胖
   * */

  //  获取血压概览
  async getWeightOverview(params) {
    return await this.$http.get('/clinic/health.weight.overview', { params });
  }

  //  获取血压图标趋势
  async getWeightStatis(params) {
    return await this.$http.get('/clinic/health.weight.statis', { params });
  }
  //  获取血压录入明细
  async getWeightRecord(params) {
    return await this.$http.get('/clinic/health.weight.record', { params });
  }

  //  获取血压录入明细
  async getWeightOptions(params) {
    return await this.$http.get('/clinic/health.weight.options', { params });
  }

  // 发送验证码
  async sendAuthCode(params) {
    return await this.$http.post('/clinic/mobile.sendauthcode', params);
  }

  // 获取虚拟号
  async userGetxnmobile(params) {
    return await this.$http.get('/clinic/user.user.getxnmobile', { params });
  }

  // 获取是否存在于用户中心
  async userExistmobileuc(params) {
    return await this.$http.get('/clinic/user.user.existmobileuc', { params });
  }

  // 手机号是否允许被修改
  async userUpdatecheckphone(params) {
    return await this.$http.get('/clinic/user.user.updatecheckphone', { params });
  }

  // 换绑手机号
  async userEditusermobile(params) {
    return await this.$http.post('/clinic/user.user.editusermobile', params);
  }

  // 校验验证是否成功
  async checkUserVerifyCode(params) {
    return await this.$http.post('/clinic/user.user.verifyCode', params);
  }

  // 获取用户信息
  async getUserGetucuser(params) {
    return await this.$http.post('/clinic/user.user.getucuser', params);
  }
  // 导出用户列表
  async exportUserList(params) {
    return await this.$http.get('/clinic/user.report.listurl', { params });
  }

  // 检测是不是vip
  async getuservipbymobile(params) {
    return await this.$http.get('/clinic/user.user.getuservipbymobile', { params });
  }

  // 获取用户列表
  async getUserListV2(params) {
    return await this.$http.get('/clinic/user.user.listv2', { params });
  }
  // 验证码校验
  async getUserVerifyauthcode(params) {
    return await this.$http.post('/clinic/user.user.verifyauthcode', params);
  }
  // 获取用户vip信息
  async getUserVipInfo(params) {
    return await this.$http.get('/clinic/user.user.vipinfo', { params });
  }

  // 免费赠送会员
  async giveVipMembership(params) {
    return await this.$http.post('/clinic/user.user.givevip', params);
  }
  // 获取用户标签列表
  async getUserTagGroupList(params) {
    return await this.$http.get('/clinic/user.clinictaggroup.list', { params });
  }
  // 获取用户标签信息
  async getUserTagGroupInfo(params) {
    return await this.$http.get('/clinic/user.clinictaggroup.info', { params });
  }
  // 新增标签组
  async saveUserTagGroup(params) {
    return await this.$http.post('/clinic/user.clinictaggroup.save', params);
  }
  // 新增标签组
  async changeUserTagStatus(params) {
    return await this.$http.post('/clinic/user.clinictaggroup.status', params);
  }
  // 删除标签组
  async deleteUserTagGroup(params) {
    return await this.$http.post('/clinic/user.clinictaggroup.delete', params);
  }
  // 获取用户跟随信息
  async getUserFollowUpTips(params) {
    return await this.$http.get('/clinic/user.user.getuserfollowuptips', { params });
  }
  // 新增用户跟随信息
  async addUserFollowUpTips(params) {
    return await this.$http.post('/clinic/user.user.addfollowuptips', params);
  }
  // 编辑用户跟随信息
  async editUserFollowUpTips(params) {
    return await this.$http.post('/clinic/user.user.updatefollowuptips', params);
  }
  // 删除用户跟随信息
  async delUserFollowUpTips(params) {
    return await this.$http.post('/clinic/user.user.deletefollowuptips', params);
  }
  // 编辑用户标签
  async saveUserTag(params) {
    return await this.$http.post('/clinic/user.user.saveusertag', params);
  }

  // 获取用户权限及配置
  async getUserPermission(params) {
    return await this.$http.get('/clinic/options.get', { params });
  }
}
export default User_methods;
