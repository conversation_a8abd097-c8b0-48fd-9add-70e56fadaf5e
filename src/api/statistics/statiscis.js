class statiscis {
  /**
   * @file: statiscis.js
   * @description: 数据统计接口
   * @author: jiuxia
   * @date: 2022.3.29
   */

  // 趋势图
  async getTrend(query) {
    return  await this.$http.get('/clinic/statisv2.index.trend', {params: query})
  }

  /**
   * 商品分析
   * */

  // 获取整体概况
  async getGoodsOverview(query) {
    return  await this.$http.get('/clinic/statisv2.goods.overview', {params: query})
  }

  // 商品销售场景分析
  async getGoodsItemanalysis(query) {
    return  await this.$http.get('/clinic/statisv2.goods.Itemanalysis', {params: query})
  }

  // 商品销售分析
  async getGoodsSalesanalysis(query) {
    return  await this.$http.get('/clinic/statisv2.goods.salesanalysis', {params: query})
  }

  // 诊疗方剂常用
  async getGoodsPresrank(query) {
    return  await this.$http.get('/clinic/statisv2.goods.presrank', {params: query})
  }

  // 诊疗治疗品常用排行
  async getGoodsPrestreatrank(query) {
    return  await this.$http.get('/clinic/statisv2.goods.prestreatrank', {params: query})
  }

  // 商品交易明细
  async getGoodsTradelist(query) {
    return  await this.$http.get('/clinic/statisv2.goods.tradelist', {params: query})
  }

  // 商品退款明细
  async getGoodsRefundlist(query) {
    return  await this.$http.get('/clinic/statisv2.goods.refundlist', {params: query})
  }

  // HIS开方应用分析
  async getGoodsHisOrderanalysis(query) {
    return  await this.$http.get('/clinic/statisv2.goods.hisorderanalysis', {params: query})
  }

  // 商城订单分析
  async getGoodsShopOrderanalysis(query) {
    return  await this.$http.get('/clinic/statisv2.goods.shoporderanalysis', {params: query})
  }

  /**
   * 数据概况
   * */
  // 数据概况信息
  async getOverviewInfo(params) {
    return await this.$http.get('/clinic/statisv2.overview.index', {params})
  }
  // 数据转化分析
  async getOverviewRatio(params) {
    return await this.$http.get('/clinic/statisv2.overview.ratio', {params})
  }
  // 数据概况枚举
  async getOverviewOptions(params) {
    return await this.$http.get('/clinic/statisv2.index.options', {params})
  }
  /**
   * 交易分析
   * */
  // 数据汇总
  async getTradeOverview(query) {
    return  await this.$http.get('/clinic/statisv2.trade.overview', {params: query})
  }

  // 交易相关信息
  async getTradeTradedetails(query) {
    return  await this.$http.get('/clinic/statisv2.trade.tradedetails', {params: query})
  }

  // 交易统计
  async getTradeTradesummary(query) {
    return  await this.$http.get('/clinic/statisv2.trade.tradesummary', {params: query})
  }

  // 导出交易统计
  async tradeExporttradesummary(query) {
    return  await this.$http.get('/clinic/statisv2.trade.exporttradesummary', {params: query})
  }

  // 交易订单明细
  async getTradeOrders(query) {
    return  await this.$http.get('/clinic/statisv2.trade.orders', {params: query})
  }

  // 导出订单明细
  async tradeExportorders(query) {
    return  await this.$http.get('/clinic/statisv2.trade.exportorders', {params: query})
  }

  // 交易退款明细
  async getTradeRefundorders(query) {
    return  await this.$http.get('/clinic/statisv2.trade.refundorders', {params: query})
  }

  // 导出退款明细
  async tradeExportrefundorder(query) {
    return  await this.$http.get('/clinic/statisv2.trade.exportrefundorder', {params: query})
  }

  /**
   * 用户分析
   * */
  // 整体概况
  async getCustomerOverview(query) {
    return  await this.$http.get('/clinic/statisv2.customer.overview', {params: query})
  }

  // 用户消费情况分析
  async getCustomerConsumeinfo(query) {
    return  await this.$http.get('/clinic/statisv2.customer.consumeinfo', {params: query})
  }

  // 用户分布分析(画像,待服务,首购)
  async getCustomerAnalysis(query) {
    return  await this.$http.get('/clinic/statisv2.customer.analysis', {params: query})
  }

  /**
   * 储值分析
   * */

  // 整体概况
  async getRechargeOverview(query) {
    return  await this.$http.get('/clinic/statisv2.recharge.overview', {params: query})
  }

  // 储值分布分析
  async getRechargeAnalysis(query) {
    return  await this.$http.get('/clinic/statisv2.recharge.analysis', {params: query})
  }

  // 储值统计记录
  async getRechargeRecord(query) {
    return  await this.$http.get('/clinic/statisv2.recharge.record', {params: query})
  }

  // 储值统计记录导出
  async rechargeExporturl(query) {
    return  await this.$http.get('/clinic/statisv2.recharge.exporturl', {params: query})
  }


  // 储值统计记录导出
  async getStatiscisExporturl(query) {
    return  await this.$http.get('/clinic/statisv2.report.managesalesurl', {params: query})
  }




  // 储值统计记录导出
  async getStockReportList(query) {
    return  await this.$http.get('/clinic/product.stock.statisStockReportList', {params: query})
  }

  // 库存报表列表导出
  async stockReportExportUrl(query) {
    return  await this.$http.get('/clinic/product.stock.getStatisStockReportListUrl', {params: query})
  }

 /**营业目标-枚举 */
  async getBusinessOpts(query) {
    return await this.$http.get('/clinic/statisv2.business.option', { params: query });
  }
  /**营业目标-数据 */
  async getBusinessTargets(query) {
    return await this.$http.get('/clinic/statisv2.business.targets', { params: query });
  }
  /**营业目标-创建 */
  async postBusinessSave(params) {
    return await this.$http.post('/clinic/statisv2.business.save', params);
  }

  /**理疗师-数据 */
  async getPhysioList(query) {
    return await this.$http.get('/clinic/statisv2.physio.list', { params: query });
  }
  /**理疗师-枚举 */
  async getPhysioOpt(query) {
    return await this.$http.get('/clinic/statisv2.physio.option', { params: query });
  }
  /**理疗师-导出 */
  async getPhysioReport(query) {
    return await this.$http.get('/clinic/statisv2.report.physioefficiencyurl', { params: query });
  }
  /**医生-数据 */
  async getDoctorList(query) {
    return await this.$http.get('/clinic/statisv2.doctor.list', { params: query });
  }
  /**医生-枚举 */
  async getDoctorOpt(query) {
    return await this.$http.get('/clinic/statisv2.doctor.option', { params: query });
  }
  /**医生-导出 */
  async getDoctorReport(query) {
    return await this.$http.get('/clinic/statisv2.report.doctorefficiencyurl', { params: query });
  }
  /**经营-枚举 */
  async getBizData(query) {
    return await this.$http.get('/clinic/statisv2.bizreport.index', { params: query });
  }
  /**经营-导出 */
  async getBizReport(query) {
    return await this.$http.get('/clinic/statisv2.report.bizreporturl', { params: query });
  }
  // 晨会详情
  async getMorningMeetingDetail(query) {
    return await this.$http.get('/clinic/meetings.daily.morning', { params: query });
  }
  // 夕会详情
  async getEveningMeetingDetail(query) {
    return await this.$http.get('/clinic/meetings.daily.evening', { params: query });
  }
}

export default statiscis

