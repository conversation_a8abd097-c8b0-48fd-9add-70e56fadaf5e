class Stock {
  // 商品异常零售价列表
  async getExceptionsPriceList(query) {
    return await this.$http.get('/clinic/product.retail.exceptionlist', { params: query });
  }
  // 批量更新商品零售价
  async updateBatchGoodsPrice(params) {
    return await this.$http.post('/clinic/product.retail.batchupdate', params);
  }

  // 出入库明细导出
  async getProdStockChangeUrl(params) {
    return await this.$http.get('/clinic/product.report.getProdStockChangeUrl', { params });
  }
  //修改零售价
  async editProductPrice(params) {
    return await this.$http.post('/clinic/product.product.editprice', params);
  }
  //  获取修改零售采购比例
  async getRetailPurchaseRatio(params) {
    return await this.$http.get('/clinic/product.product.getretailpurchaseratio', { params });
  }
  //  修改零售采购比例
  async editRetailPurchaseRatio(params) {
    return await this.$http.post('/clinic/product.product.editretailpurchaseratio', params);
  }
  // 获取修改零售采购比日志
  async getRetailPurchaseRatioLog(params) {
    return await this.$http.get('/clinic/product.product.getretailpurchaselog', { params });
  }
  // 获取常繁入库列表及供应商
  async getCFInStockList(params) {
    return await this.$http.get('/clinic/product.product.cflist', { params });
  }
  // 获取普通入库列表
  async getInStockProductList(params) {
    return await this.$http.get('/clinic/product.product.list', { params });
  }
  // 获取盘点货品列表
  async getInStockProdList(params) {
    return await this.$http.get('/clinic/product.product.prodlist', { params });
  }
  // 货品列表枚举
  async getInStockProductOptions(params) {
    return await this.$http.get('/clinic/product.product.options', { params });
  }
  // 获取批次库存列表
  async getProductStockList(params) {
    return await this.$http.get('/clinic/product.stock.prod', { params });
  }
  // 获取库存预警列表
  async getProductWarningStockList(params) {
    return await this.$http.get('/clinic/product.stock.warning', { params });
  }
  // 获取出入库管理列表
  async getOStockList(params) {
    return await this.$http.get('/clinic/ostock.list', { params });
  }
  // 获取出入库明细列表
  async getProductStockBillsList(params) {
    return await this.$http.get('/clinic/product.stock.bills', { params });
  }
  // 获取出入库明细列表
  async getSupplierList(params) {
    return await this.$http.get('/clinic/supplier.list', { params });
  }

  /**
   * @description: 2022.12.27-供应商
   * */
  // 获取供应商枚举
  async getSupplierOptions(params) {
    return await this.$http.get('/clinic/supplier.options', { params });
  }

  // 创建/编辑供应商
  async editSupplier(params) {
    return await this.$http.post('/clinic/supplier.edit', params);
  }
  // 创建/编辑供应商
  async getSupplierInfo(params) {
    return await this.$http.get('/clinic/supplier.info', { params });
  }

  /**
   * @description: 2023.5.31-常繁导入
   * */
  // 导入常繁随货单
  async importPreCheckByChangFan(params) {
    return await this.$http.post('/clinic/ostock.Importprecheckbychangfan', params);
  }
  // 解析导入文件名称
  async parseImportFileName(params) {
    return await this.$http.post('/clinic/ostock.parseimportfilename', params);
  }

  async getHistoryList(params) {
    return await this.$http.get('/clinic/product.product.getEditPriceLog', { params });
  }

  // 获取客服列表
  async getClinicCustomerServiceList(params) {
    return await this.$http.get('/clinic/pms.customerservice.getlistbyclinic', { params });
  }

  // 出入库操作历史
  async getOstockLogList(params) {
    return await this.$http.get('/clinic/ostock.log', { params });
  }

  // 获取入库异常采购价判断标准
  async getOstockRules(params) {
    return await this.$http.get('/clinic/ostock.rules', { params });
  }

  // 获取货品历史采购价信息
  async getprodostockhistory(params) {
    return await this.$http.get('/clinic/ostock.getprodostockhistory', { params });
  }

  // 获取助记码
  async getZhuYin(params) {
    return await this.$http.get('/clinic/index.zhuyin', { params });
  }

  // 根据通用名搜索关联的药品
  async getProductSearchainame(params) {
    return await this.$http.get('/clinic/product.product.searchainame', { params });
  }

  // 保存/编辑诊所货品
  async getProductEdit(params) {
    return await this.$http.post('/clinic/product.product.edit', params);
  }

  // 获取诊所货品详情
  async getProductInfo(params) {
    return await this.$http.get('/clinic/product.product.info', { params });
  }

  // 根据通用名搜索关联的药品
  async getProductTemplateprod(params) {
    return await this.$http.get('/clinic/product.product.templateprod', { params });
  }
  // 根据通用名搜索关联的药品
  async getCFOutboundList(params) {
    return await this.$http.get('/clinic/ostock.getoutbound', { params });
  }
  // 根据通用名搜索关联的药品
  async checkCfOutboundCode(params) {
    return await this.$http.get('/clinic/ostock.checkordercode', { params });
  }
  // 获取货品的批次列表
  async getProdBatchList(params) {
    return await this.$http.get('/clinic/product.stock.getprodstockdetail', { params });
  }
  // 获取供应商下拉列表
  async getSupplierPullDownList(params) {
    return await this.$http.get('/clinic/supplier.pullDownList', { params });
  }
  // 获取供应商下拉列表
  async getProdStockDetailUrl(params) {
    return await this.$http.get('/clinic/product.report.getprodstockdetailurl', { params });
  }
}
export default Stock;
