class Performance {
  // 创建分成方案
  async createSharePlanStore(params) {
    return await this.$http.post('/clinic/revenue.shareplan.store', params);
  }
  // 修改分成方案
  async editSharePlanStore(params) {
    return await this.$http.post('/clinic/revenue.shareplan.update', params);
  }
  // 分成方案枚举选项
  async getSharePlanOptions(params) {
    return this.$http.get('/clinic/revenue.shareplan.options', { params });
  }
  // 方案详情
  async getSharePlanDetail(params) {
    return this.$http.get('/clinic/revenue.shareplan.show', { params });
  }
  // 方案列表
  async getSharePlanList(params) {
    return this.$http.get('/clinic/revenue.shareplan.list', { params });
  }
  // 检查方案名称重复
  async checkSharePlanName(params) {
    return this.$http.get('/clinic/revenue.shareplan.checkname', { params });
  }
  // 设置方案状态
  async setSharePlanStatus(params) {
    return await this.$http.post('/clinic/revenue.shareplan.setstatus', params);
  }
}

export default Performance;
