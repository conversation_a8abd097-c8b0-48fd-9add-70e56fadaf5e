class Order {
  // 获取收货人列表
  async getHospitalConsigneeList(params) {
    return await this.$http.get('/clinic/hosp.pres.consigneeoptionlist', { params });
  }
  // 获取互医用户列表
  async getHospitalUserList(params) {
    return await this.$http.get('/clinic/hosp.pres.useroptionlist', { params });
  }
  // 获取互医用户列表
  async getHospitalOrderList(params) {
    return await this.$http.get('/clinic/hosp.pres.orderlist', { params });
  }
  // 获取互医订单物流信息
  async getHospitalOrderExpress(params) {
    return await this.$http.get('/clinic/hosp.pres.orderexpress', { params });
  }
  // 获取互医订单随货单信息
  async getHospitalOrderDelivery(params) {
    return await this.$http.get('/clinic/hosp.pres.ordershippinglistinfo', { params });
  }
  // 获取互医订单随货单信息
  async getHospitalExpressList(params) {
    return await this.$http.get('/clinic/hosp.pres.expresslist', { params });
  }
  // 获取互医订单随货单信息
  async getHospitalOrderDeliveryDetail(params) {
    return await this.$http.get('/clinic/hosp.pres.deliverydetail', { params });
  }

  // 处方发货
  async postHospitalOrderDelivery(params) {
    return await this.$http.post('/clinic/hosp.pres.orderdeliverycreate', params);
  }

  // 修改物流
  async updateOrderdeliveryexpressedit(params) {
    return await this.$http.post('/clinic/hosp.pres.orderdeliveryexpressedit', params);
  }
}
export default Order;
