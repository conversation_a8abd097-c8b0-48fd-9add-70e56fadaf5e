class Pharmacist {
  // 互医枚举
  async getHospitalOptions(params) {
    return await this.$http.get('/clinic/hosp.opt.options', { params });
  }
  // 获取药师列表
  async getPharmacistList(params) {
    return await this.$http.get('/clinic/hosp.opt.pharmacistlist', { params });
  }
  // 获取药师列表
  async getPharmacistOptions(params) {
    return await this.$http.get('/clinic/hosp.opt.pharmacistoptions', { params });
  }
  // 获取药师列表
  async getPharmacistDetail(params) {
    return await this.$http.get('/clinic/hosp.opt.pharmacistinfo', { params });
  }

  // 互医枚举
  async getHospitalMemberSearch(params) {
    return await this.$http.get('/clinic/hosp.opt.membersearch', { params });
  }

  // 更新药师处方权限
  async setPresPermission(params) {
    return await this.$http.post('/clinic/hosp.opt.updatepres', params);
  }
  // 新增药师
  async createPharmacist(params) {
    return await this.$http.post('/clinic/hosp.opt.createpharmacist', params);
  }
  // 新增药师
  async editPharmacist(params) {
    return await this.$http.post('/clinic/hosp.opt.editpharmacist', params);
  }
  // 获取互医页面中云直通、诊所入驻状态、药师入驻状态
  async getClinicHospitalStatus(params) {
    return await this.$http.post('/clinic/hosp.opt.gethyclinicyztstatus', params);
  }
}
export default Pharmacist;
