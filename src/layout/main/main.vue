<template>
  <div class="app-wrapper" :class="{ 'show-help': $store.state.app.showHelpWrapper }">
    <div class="app-sidebar">
      <side-menu></side-menu>
    </div>
    <div class="app-container" :style="appContainerStyles">
      <div class="app-header">
        <div class="app-header_left" v-if="showThirdSidebar || breadcrumbs.some(item => item.source === 'main')">
          <Breadcrumb class="ks-breadcrumb ml-7">
            <BreadcrumbItem v-for="(item, key) in breadcrumbs" :to="item.path" class="bbSize" :key="key">
              <i v-if="item.icon" class="fa" :class="item.icon"></i> {{ item.name }}
            </BreadcrumbItem>
          </Breadcrumb>
          <div class="back-btn-box" v-show="showBack">
            <Button size="small" @click="routerBack">
              <svg-icon iconClass="back-btn" class="back-icon"></svg-icon>
              <span class="back-text">返回</span>
            </Button>
          </div>
          <!--          <Button @click="prolongCookieHandler" type="warning">延长 cookie 10分钟</Button>-->
        </div>
        <div v-else style="width: 0"></div>
        <div class="app-header_right">
          <ai-assistant v-show="showAgent" />
          <IpadLogin v-if="padPermission" />
          <user-message />
          <div class="app-header_trigger pl-4" style="cursor: pointer">
            <Poptip title="" placement="bottom" ref="headerPop" class="user-info-pop">
              <div class="user-info-wrap">
                <div class="flex flex-c" style="justify-content: center">
                  <span class="space6" style="line-height: 1.5; text-align: right; font-weight: 400">{{
                    userInfo.name
                  }}</span>
                  <span class="space6" style="line-height: 1; font-size: 12px; color: #999999">{{
                    userInfo.clinic_name
                  }}</span>
                </div>
                <div class="name-abbreviation" :style="{ backgroundColor: getColorForName }">
                  {{ userInfo.name.slice(-2) }}
                </div>
                <Icon type="ios-arrow-down" />
              </div>
              <div slot="content" class="pop-info-box">
                <div class="user-info">
                  <div class="name-abbreviation" :style="{ backgroundColor: getColorForName }">
                    {{ userInfo.name.slice(-2) }}
                  </div>
                  <div>
                    <div class="user-name">{{ userInfo.name }}</div>
                    <div class="user-character">{{ userInfo.role_name }}</div>
                  </div>
                </div>
                <div class="clinic-content">
                  <div class="clinic-item">
                    <div class="clinic-label">当前门店</div>
                    <div class="clinic-value">{{ userInfo.clinic_name }}</div>
                  </div>
                </div>
                <div class="user-content">
                  <div class="user-item">
                    <div class="user-label">手机号</div>
                    <div class="user-value">{{ userInfo.mobile }}</div>
                  </div>
                  <div class="user-item">
                    <div class="user-label">登录密码</div>
                    <a class="user-value" @click="changePassword">修改密码 ></a>
                  </div>
                </div>
                <div class="logout-btn" @click="switchClinic">
                  <img src="https://img-sn-i01s-cdn.rsjxx.com/image/2025/0213/161000_73526.png" class="logout-icon" />
                  <img
                    src="https://img-sn-i01s-cdn.rsjxx.com/image/2025/0213/161000_63047.png"
                    class="logout-icon-hover"
                  />
                  切换诊所
                </div>
                <div class="logout-btn mt10" @click="logout">
                  <img src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1111/155818_78761.png" class="logout-icon" />
                  <img
                    src="https://img-sn-i01s-cdn.rsjxx.com/rsjxx/2024/1211/170337_59015.png"
                    class="logout-icon-hover"
                  />
                  退出登录
                </div>
              </div>
            </Poptip>
          </div>
        </div>
      </div>
      <div class="app">
        <div class="app-inner" :class="{ 'app-inner-no-margin': !showThirdSidebar }" :style="appInnerStyles">
          <template v-if="!isServeRun">
            <!-- 只在build模式下缓存导航 -->
            <vue-page-stack>
              <router-view />
            </vue-page-stack>
          </template>
          <template v-else>
            <router-view />
          </template>
          <!--          <template v-if="!isServeRun">-->
          <!--            &lt;!&ndash; 只在build模式下缓存导航 &ndash;&gt;-->
          <!--            <router-view />-->
          <!--          </template>-->
          <!--          <template v-else>-->
          <!--            <router-view />-->
          <!--          </template>-->
        </div>
      </div>
    </div>

    <div class="app-footer">
      <span>海南榕树家信息科技有限公司提供技术支持 V1.0</span> <span style="display: none">{{ code_version }}</span>
    </div>
    <ChangePassword :visible.sync="changeVisible" :user-info="userInfo"></ChangePassword>
    <Modal v-model="logoutModal" :width="300" @on-ok="onLogout"> 确定退出登录？</Modal>
    <switch-modal v-model="switchVisible"></switch-modal>
  </div>
</template>

<script>
/* eslint-disable */
import S from '@/libs/util'; // Some commonly used tools
import { getUser, logout, prolongCookie, writeLoginCookie } from '@/libs/runtime'; // Runtime information
/* eslint-disable */
import config from '@/config';
import SideMenu from './components/side-menu';
import ChangePassword from './components/ChangePassword.vue';
import switchModal from './components/switchModal.vue';
import IpadLogin from './components/IpadLogin/index.vue';
import './main.less';
import { mapState } from 'vuex';
import UserMessage from '@/layout/main/components/message/index.vue';
import AiAssistant from '@/layout/main/components/ai-assistant/index.vue';
import { showAgent } from '@/libs/runtime';

export default {
  name: 'Main',
  components: {
    UserMessage,
    SideMenu,
    ChangePassword,
    switchModal,
    IpadLogin,
    AiAssistant,
  },
  data() {
    return {
      breadcrumbs: [],
      appInnerStyles: {
        minHeight: '',
        height: '',
        margin: '10px',
        padding: '16px',
        background: '#FFFFFF',
      },
      appContainerStyles: {},
      userInfo: {},
      isServeRun: false,
      code_version: '',
      logoutModal: false,
      changeVisible: false,
      switchVisible: false,
      showQrCodeLogin: false,
      showLoginUserList: false,
      padPermission: false,
    };
  },

  created() {
    this.code_version = config.codeVersion;

    this.isServeRun = S.isServeRun;

    this.userInfo = getUser();
    this.getPadPermission();
  },

  methods: {
    getPadPermission() {
      this.$api.getPadPermission().then(res => {
        // console.log('=>(main.vue:168) res', res);
        this.padPermission = res.login_permission === '1';
      });
    },
    appInnerHeight: function () {
      let currentScreenHeight = document.documentElement.clientHeight;
      let dh = 92;
      if (!this.showThirdSidebar) {
        dh = 25;
      }
      // 不授权的二级菜单
      const menus = ['/main/notice'];
      if (menus.includes(this.$route.path)) {
        dh = 92;
      }
      this.appInnerStyles.minHeight = currentScreenHeight - dh + 'px';
    },

    onDropDownClick: function (name) {
      if (name == 'logout') {
        this.logoutModal = true;
      }
    },

    onLogout: function () {
      // this.$store.commit('purchase/CLEAR_GOODS_LIST');
      logout();
      this.$router.push({ path: '/login', query: { from: encodeURIComponent(this.$route.fullPath) } });
    },

    getBreadcrumbs: function (to) {
      let path = to.path;
      let breadcrumbs = [
        {
          path: '/',
          name: '首页',
        },
      ];
      let sup_menu_path = '',
        sup_menu_name = '',
        sup_menu_icon = '',
        activeFirstMenuId = '';

      for (let pid in this.secondMenusAll) {
        let items = this.secondMenusAll[pid];
        items.forEach(item => {
          if (item.path == path) {
            if (item.type != 'SUB_MENU') {
              sup_menu_path = items[0].path;
              sup_menu_name = items[0].name;
              items.forEach(t => {
                if (t.id == item.p_id) {
                  activeFirstMenuId = t.p_id;
                  sup_menu_name = t?.meta.title;
                }
              });
            } else {
              activeFirstMenuId = item.p_id;
            }
          }
        });
      }

      // 一级标题
      let firstMenu = '';
      this.firstMenus.forEach(item => {
        if (item.id == activeFirstMenuId) {
          firstMenu = item.name;
        }
      });

      if (sup_menu_path) {
        breadcrumbs.push({
          path: sup_menu_path,
          name: firstMenu + ' - ' + sup_menu_name,
        });

        breadcrumbs.push({
          path: '',
          name: to.meta.title,
        });
      } else if (to.path === '/main/notice') {
        breadcrumbs.push({
          path: '',
          source: 'main',
          name: to.meta.title,
        });
      } else {
        breadcrumbs.push({
          path: '',
          name: firstMenu + ' - ' + to.meta.title,
        });
      }
      return breadcrumbs;
    },

    routerBack() {
      this.$router.back();
    },
    changePassword() {
      this.$refs.headerPop.handleClose();
      this.changeVisible = true;
    },
    // 切换诊所
    switchClinic() {
      this.$refs.headerPop.handleClose();
      this.switchVisible = true;
    },
    logout() {
      // console.log('=>(main.vue:273) this.$refs.headerPop', this.$refs.headerPop);
      this.$refs.headerPop.handleClose();
      this.logoutModal = true;
    },
    // todo  测试延长cookie 10 minus
    // prolongCookieHandler() {
    //   writeLoginCookie({
    //     ...getUser()
    //   })
    // },
  },
  mounted() {
    this.appInnerHeight();
    window.addEventListener('resize', () => {
      return (() => {
        this.appInnerHeight();
      })();
    });
  },

  computed: {
    showAgent() {
      return showAgent() || this.$store.state.app.userConfig.show_agent === '1';
    },
    getColorForName() {
      const name = getUser().name;
      if (!name) return 'hsl(0, 0%, 50%)';

      // 简单哈希算法
      let hash = 0;
      for (let i = 0; i < name.length; i++) {
        hash = name.charCodeAt(i) + ((hash << 5) - hash);
      }

      // 使用哈希值生成HSL颜色
      const hue = Math.abs(hash) % 360;
      return `hsl(${hue}, 80%, 60%)`;
    },
    key() {
      return this.$route.path;
    },
    ...mapState('menus', {
      firstMenus: state => state.firstMenus,
      secondMenusAll: state => state.secondMenusAll,
      customPageStyle: state => state.customPageStyle,
    }),

    showThirdSidebar: function () {
      return this.$store.state.menus.showSecondMenu;
    },
    showBack() {
      return this.$route.meta.type === 'PAGE' && window.history.length > 2;
    },
  },

  watch: {
    $route: {
      immediate: true,
      handler: function (to) {
        this.breadcrumbs = this.getBreadcrumbs(to);
        // this.$nextTick(() => {
        //   const app = document.getElementsByClassName('app-inner')
        //   if (to.path.includes('statistics')) {
        //     app[0].style.background = '#f2f2f2'
        //     app[0].style.padding = 0
        //     app[0].style.marginTop = 0
        //   } else {
        //     app[0].style.background = '#fff'
        //     app[0].style.padding = '12px'
        //     app[0].style.marginTop = '12px'
        //
        //   }
        // })
        this.$nextTick(() => {
          // const app = document.getElementsByClassName('app-inner')
          // console.log('=>(main.vue:281) to.path', to.path);
          if (to.path.includes('statistics')) {
            this.appInnerStyles.margin = '0px 10px 10px';
            this.appInnerStyles.background = '#f2f2f2';
            this.appInnerStyles.padding = 0;
          } else if (
            (to.path === '/trade/order/create' && to.query.type === 'new') ||
            to.path === '/reserve/listing/order'
          ) {
            this.appInnerStyles.margin = '16px 16px 0px';
            this.appInnerStyles.background = '#F5F6F8';
            this.appInnerStyles.padding = 0;
            this.appContainerStyles.background = '#F5F6F8';
          } else {
            this.appInnerStyles = { ...this.appInnerStyles, ...this.customPageStyle };
          }
        });
      },
    },

    showThirdSidebar: function () {
      this.appInnerHeight();
    },
  },
};
</script>

<style lang="less">
.app-header_left {
  display: flex;
  align-items: center;
  height: 56px;
  .bbSize {
    font-size: 14px;
    a {
      color: #666666;
    }
  }
}

.app-header_right {
  display: flex;
  align-items: center;
}

.app-header_trigger {
  width: auto;
  padding-right: 12px;
  text-align: center;
  transition: all 0.2s ease-in-out;
  display: inline-flex;
  align-items: center;
  height: 56px;
  // &:hover {
  //   color: #2277ff;
  //   background-color: #f8f8f9;
  // }

  .app-inner {
    margin: 10px;
    padding: 16px;
    min-width: 1150px;
    //min-height: 450px;
    background-color: #fff;
  }

  .app-inner-no-margin {
    margin: 0 0 10px 0;
  }

  .bbSize {
    font-size: 14px;
  }
}
.name-abbreviation {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  margin-left: 12px;
  margin-right: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  font-size: 12px;
  color: #ffffff;
  line-height: 16px;
}
.user-info-wrap {
  border-radius: 4px;
  height: 48px;
  display: flex;
  align-items: center;
  padding: 5px 8px;
  &:hover {
    background: #f5f6f8;
  }
}
</style>
<style lang="less" scoped>
.pop-info-box {
  padding: 16px;
  min-width: 280px;
  max-width: 280px;

  .user-info {
    display: flex;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebedf0;
    margin-bottom: 16px;
    .user-name {
      font-weight: 600;
      font-size: 16px;
      color: #303133;
    }
    .user-character {
      white-space: pre-wrap;
      font-weight: 400;
      font-size: 12px;
      color: #909399;
    }
  }
  .clinic-content {
    margin-bottom: 16px;
    border-bottom: 1px solid #ebedf0;
    .clinic-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      .clinic-label {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
        margin-right: 30px;
      }
      .clinic-value {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
        white-space: pre-wrap;
        text-align: right;
      }
    }
  }
  .user-content {
    .user-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      .user-label {
        font-weight: 400;
        font-size: 14px;
        color: #303133;
      }
      .user-value {
        font-weight: 400;
        font-size: 14px;
        color: #909399;
      }
    }
  }
  .logout-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 248px;
    height: 32px;
    background: #f5f6f8;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    font-size: 13px;
    color: #303133;
    .logout-icon {
      width: 13px;
      height: 14px;
      margin-right: 8px;
    }
    .logout-icon-hover {
      display: none;
      width: 13px;
      height: 14px;
      margin-right: 8px;
    }
    &:hover {
      background: rgba(48, 136, 255, 0.06);
      color: #3088ff;
      .logout-icon {
        display: none;
      }
      .logout-icon-hover {
        display: block;
      }
    }
  }
}
.mt10 {
  margin-top: 10px;
}
.login-user-list-dropdown {
  position: absolute;
  top: 56px;
  right: 0;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 12px;
  width: 280px;
  z-index: 1000;
}
:deep(.header-quick-item) {
  display: flex;
  align-items: center;
  padding: 7px 12px;
  height: 32px;
  border-radius: 16px;
  border: 1px solid #dcdde0;
  cursor: pointer;
  font-size: 13px;
  justify-content: flex-start;
  line-height: 20px;
  &:hover {
    border-color: #dcdde0;
    background-color: #f5f6f8;
  }
  > span {
    margin-left: 4px;
  }
}
.user-info-pop {
  height: 56px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  :deep(.ivu-poptip-rel) {
    display: block;
  }
}
</style>
<style lang="less" scoped>
:deep(.ivu-badge-count) {
  height: 16px;
  padding: 0 4px;
  min-width: 16px;
  line-height: 14px;
  top: -6px;
  right: 4px;
}

.app-inner:has(.new-follow-up) {
  padding: 0 !important;
}
</style>
