<template>
  <div class="header-pad app-header_trigger">
    <Dropdown
      trigger="custom"
      :transfer="true"
      class="notify-wrap"
      transfer-class-name="notify-message"
      stop-propagation
      :visible="notifyDropdown"
      @on-clickoutside="notifyDropdown = false"
    >
      <Badge :count="unreadNoticeCount" :max="99" class="notify-message-icon">
        <div class="header-quick-item" @click.stop="showMessageModel()">
          <img class="notify-img" src="https://img-sn-i01s-cdn.rsjxx.com/image/2025/0324/114212_39830.png" alt="" />
          <span>消息中心</span>
        </div>
      </Badge>
      <DropdownMenu slot="list" @scroll.stop>
        <div class="notify-message-list" v-loading="loading" @click.stop.prevent>
          <div class="notify-message-header">
            <div class="unread-notify">
              <div>消息</div>
              <div>（{{ unreadNoticeCount }}条未读消息）</div>
            </div>
            <div v-if="+unreadNoticeCount > 0" class="mark-read" @click="clearUnread({})">全部标记已读</div>
          </div>
          <div class="notify-message-content">
            <div
              :class="{
                'notify-message-content-item': true,
                isRead: +item.status === 3,
              }"
              v-for="item in list"
              :key="item.id"
              @click.stop="clearUnread(item)"
            >
              <div class="content-item-header">
                <el-badge :hidden="+item.status !== 2" is-dot class="content-item-header-dot">
                  <div class="notify-message-tittle">{{ item.title || '-' }}</div>
                </el-badge>
                <div class="notify-message-time">{{ formatTime(item) }}</div>
              </div>
              <div
                :class="{
                  'notify-message-text': true,
                  'notify-hover-text': !!item?.content?.target,
                }"
                @click.stop="toTargetPage(item)"
              >
                <!--  <span v-multi-line-tooltip="{ line: 2, maxWidth: 400 }"> {{ formatNoticeText(item) }} </span>-->
                {{ formatNoticeText(item) }}
              </div>
            </div>
            <div v-if="list.length === 0 && !loading" class="empty-table">
              <img src="https://img-sn-i01s-cdn.rsjxx.com/image/2025/0321/184505_71624.png" alt="" />
            </div>
          </div>
          <div class="notify-message-footer">
            <a @click="toMessageCenter">查看全部消息</a>
          </div>
        </div>
      </DropdownMenu>
    </Dropdown>
    <audio ref="audioPlayer" style="position: fixed; left: 9999px" controls src="@/assets/audio/message.mp3"></audio>
    <custom-dialog
      :show-cancel="false"
      :closable="false"
      :visible.sync="openDialog"
      content="语音自动播放失败，点击“我知道了”按钮开启自动播放，自动播放将在下一条消息通知时生效。"
      ok-text="我知道了"
      :on-ok="handleClickOncePage"
    />
  </div>
</template>

<script>
import moment from 'moment';
import CustomDialog from '@/components/custom-dialog/index.vue';
import { getSocketInfo, isLogined, logout, writeSocketInfo } from '@/libs/runtime';

export default {
  name: 'userMessage',
  components: { CustomDialog },
  data() {
    return {
      NOTICE_TYPES: {
        CLINIC_RESERVE: {
          name: '预约',
          path: '/reserve/listing/detail',
        },
      },
      loading: false,
      notifyDropdown: false,
      noticeList: [],
      total: 0,
      page: {
        page: 1,
        pageSize: 10,
      },
      unreadNoticeCount: 0,
      count: 0,
      openDialog: false,
      homeNotice: null,
    };
  },
  created() {
    this.homeNotice = new BroadcastChannel('homeNotice');
  },
  mounted() {
    this.getUnreadCount();
    this.initSocket();
    this.homeNotice.addEventListener('message', this.handleMessage);
    document.addEventListener('visibilitychange', this.pageTabsVisibilitychange);
  },
  beforeDestroy() {
    document.removeEventListener('visibilitychange', this.pageTabsVisibilitychange);
    this.homeNotice.close();
    this.homeNotice.removeEventListener('message', this.handleMessage);
    this.homeNotice = null;
    this.$socket && this.$socket.handleLogout();
  },
  computed: {
    list() {
      if (this.loading) return [];
      return this.noticeList;
    },
  },
  methods: {
    // add() {
    //   this.$api
    //     .testpush({
    //       id: '2936',
    //     })
    //     .then(() => {
    //       console.log(this.$socket, 'this.$socket');
    //       // this.getMessageList()
    //       // this.getUnreadCount();
    //     });
    // },
    async initSocket() {
      if (!this.$socket) return;
      if (!this.$socket?.isConnected || !this.$socket?.socket) {
        this.$socket.handleLogout();
        await this.$socket.init();
      }

      this.$socket.on('auth', data => {
        this.logoutByVersion(data?.user?.version);
        this.getMessageList();
        this.getUnreadCount();
      });
      this.$socket.on('version', version => {
        this.logoutByVersion(version);
      });
      this.$socket.on('notice', data => {
        console.log('=>(index.vue:155) 头部获取', data);
        try {
          // 通过BroadcastChannel通知其他标签页有新消息
          if (!this.homeNotice || this.homeNotice.closed) {
            this.homeNotice = new BroadcastChannel('homeNotice');
          }
          const noticeData = JSON.parse(data);

          // 获取ipad登录/退出通知
          if (noticeData.event_type === 'IPAD_LOGIN_SUCCESS' || noticeData.event_type === 'IPAD_LOGOUT') {
            this.$bus.$emit('IPAD_LOGIN_SUCCESS');
          }

          if (noticeData.event_type === 'THERAPY_UPLOAD_IMAGE') {
            this.homeNotice.postMessage({
              type: 'uploadNotice',
              data: noticeData,
              timestamp: new Date().getTime(),
            });
            return;
          }
          // 如果是通知标记已读，则不播放音频
          if (noticeData?.page_type === 'read') {
            this.homeNotice.postMessage({
              type: 'updateReadMsg',
              data: noticeData,
              timestamp: new Date().getTime(),
            });
            // this.homeNotice.close();
            // this.homeNotice = null;
            this.handleMessage({
              data: {
                type: 'updateReadMsg',
                data: noticeData,
                timestamp: new Date().getTime(),
              },
            });
            return;
          }
          this.homeNotice.postMessage({
            type: 'updateMessage',
            timestamp: new Date().getTime(),
          });
          // this.homeNotice.close();
          // this.homeNotice = null;
          this.handleMessage({
            data: {
              type: 'updateMessage',
              timestamp: new Date().getTime(),
            },
          });
          if (!this.$store?.state?.app?.isClickOncePage) {
            this.openDialog = true;
            return;
          }
          // 直接通过ref调用audio标签的play方法
          this.$refs?.audioPlayer &&
            this.$refs?.audioPlayer
              ?.play()
              .then(res => {
                console.log(res, '视频播放成功');
              })
              .catch(err => {
                console.log(err, '视频播放失败');
              });
        } catch (e) {
          console.log(e, '消息通知error');
        }
      });
    },
    logoutByVersion(version) {
      // 如果不存在推送的版本号，不处理
      if (!+version || +version <= 0) return;
      // 获取本地socket配置信息
      const socketInfo = getSocketInfo() || {};
      this.$Modal.remove();
      // 截止推送时，如果用户没重新登录过，且没有版本信息，则退出登录
      if (!isLogined() && !+socketInfo?.version && +version > 0) {
        this.$Modal.info({
          title: '提示',
          content: '<p>系统版本已升级，请重新登录升级版本！</p>',
          'lock-scroll': true,
          onOk: () => {
            this.handleLogout();
          },
        });
        return;
      }
      // 如果用户的版本不存在，则写入socket配置
      if (!+socketInfo?.version) {
        writeSocketInfo({ version });
        return;
      }
      // 如果拿到的socket配置版本比当前高，则退出后重新登录
      if (+socketInfo?.version < +version) {
        this.$Modal.info({
          title: '提示',
          content: '<p>系统版本已升级，请重新登录升级版本！</p>',
          'lock-scroll': true,
          onOk: () => {
            this.handleLogout();
          },
        });
      }
    },
    handleLogout() {
      logout();
      this.$router.push({ path: '/login', query: {} });
    },
    showMessageModel() {
      this.notifyDropdown = !this.notifyDropdown;
      this.getMessageList();
    },
    getMessageList() {
      this.loading = true;
      this.$api
        .getNoticeList({
          ...this.page,
          status: '2',
        })
        .then(res => {
          this.noticeList = res?.list || [];
          this.total = res?.total || 0;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getUnreadCount() {
      this.$api.getUnreadCount({}).then(res => {
        this.unreadNoticeCount = +res?.quantity;
      });
    },
    // 有回调就是去目标页
    clearUnread(item, callback) {
      if (+item?.status === 3) {
        callback && callback();
        return;
      }
      const ids = item?.id ? item.id : undefined;
      this.$api.setNoticeRead({ ids }).then(() => {
        if (!callback) {
          this.$Message.success('标记成功');
        }
        callback && callback();
      });
    },
    formatTime(item) {
      if (!item.create_time) return '-';
      if (isNaN(+item.create_time * 1000)) return '-';
      return moment(item.create_time * 1000).format('YYYY-MM-DD HH:mm');
    },
    formatNoticeText(item) {
      const message = item?.content?.message;
      if (!message) return '-';
      if (!Array.isArray(message)) return '-';
      return message
        .map(texts => {
          if (!Array.isArray(texts)) return '-';
          if (texts?.[0]) return texts.join('：');
          return texts.join('');
        })
        .join('｜');
    },
    toTargetPage(item) {
      const path = this.NOTICE_TYPES?.[item.event_type]?.path;
      if (item?.content?.target === '_blank') {
        this.clearUnread(item, () => this.openNewPage(path, item.content.params));
      }
    },
    openNewPage(path, query) {
      this.notifyDropdown = false;
      const href = this.$router.resolve({
        path,
        query,
      }).href;
      window.open(href, '_blank');
    },
    toMessageCenter() {
      this.notifyDropdown = false;
      this.openNewPage('/main/notice');
    },
    handleMessage(e) {
      if (e?.data.type === 'updateMessage') {
        this.getMessageList();
        this.getUnreadCount();
        return;
      }
      if (e?.data.type === 'updateReadMsg') {
        const ids = e.data?.data?.notify_id?.split(',') || [];
        this.list.forEach((item, i) => {
          if (ids.includes(item.id)) {
            this.$set(this.list[i], 'status', '3');
          }
        });
        this.getUnreadCount();
      }
    },
    handleClickOncePage() {
      this.$store.dispatch('app/setClickOncePage', true);
    },
    pageTabsVisibilitychange() {
      if (document.visibilityState === 'hidden') {
        this.$socket && this.$socket.handleLogout();
      }
      if (document.visibilityState === 'visible') {
        this.$socket && this.$socket.init();
        setTimeout(() => {
          this.initSocket();
        }, 300);
      }
    },
  },
};
</script>

<style scoped lang="less">
.header-pad {
  display: inline-flex;
  align-items: center;
}
.notify-wrap {
  //padding-top: 4px;
  margin-right: 4px;
}

.notify-message-icon {
  :deep(.el-badge__content.is-fixed) {
    top: 14px;
  }
}

.notify-message-list {
  width: 340px;
  position: relative;
  cursor: default;

  .notify-message-header {
    position: sticky;
    top: 0;
    left: 0;
    width: 340px;
    padding: 12px 16px;
    display: flex;
    background: #ffffff;
    border-bottom: 1px solid #ebedf0;
    z-index: 1;

    .unread-notify {
      display: flex;

      > div:first-child {
        font-weight: 600;
        font-size: 14px;
        color: #303133;
        line-height: 22px;
      }

      > div:last-child {
        font-size: 12px;
        color: #303133;
        line-height: 22px;
      }
    }

    .mark-read {
      margin-left: auto;
      font-size: 12px;
      color: #155bd4;
      line-height: 22px;
      cursor: pointer;
    }
  }

  .notify-message-content {
    width: 100%;
    padding: 0 8px;
    min-height: 300px;
    //max-height: 356px;
    //overflow-y: auto;
    .notify-message-content-item {
      width: 100%;
      padding: 12px 16px;
      margin-top: 8px;
      background: #ffffff;
      border-radius: 4px;
      cursor: pointer;

      .content-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .notify-message-tittle {
          font-weight: 600;
          font-size: 14px;
          color: #303133;
          line-height: 20px;
        }

        .notify-message-time {
          font-size: 12px;
          color: #909399;
          line-height: 18px;
        }
      }

      .notify-message-text {
        width: 100%;
        margin-top: 8px;
        font-size: 12px;
        color: #606266;
        line-height: 18px;
        //display: -webkit-box;
        //-webkit-line-clamp: 2;
        //-webkit-box-orient: vertical;
        //overflow: hidden;
        //text-overflow: ellipsis;
        cursor: pointer;
      }

      .notify-hover-text:hover {
        color: #155bd4 !important;
      }
    }

    .notify-message-content-item:hover {
      background: #f5f6f8;
    }

    .notify-message-content-item.isRead {
      background: #f5f6f8 !important;

      .notify-message-tittle {
        color: #909399 !important;
      }

      .notify-message-time {
        color: #a8abb2 !important;
      }

      .notify-message-text {
        color: #a8abb2;
      }
    }

    .empty-table {
      width: 100%;
      min-height: 300px;
      display: flex;
      justify-content: center;
      align-items: center;

      > img {
        width: 200px;
        height: 200px;
        object-fit: cover;
      }
    }
  }

  .notify-message-footer {
    position: sticky;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 40px;
    border-radius: 0 0 4px 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;

    > div {
      font-size: 12px;
      color: #155bd4;
      line-height: 18px;
      cursor: pointer;
    }
  }
}

.notify-img {
  width: 18px;
  height: 18px;
}

:deep(.ivu-dropdown-rel) {
  display: flex;
  align-items: center;
}
</style>
<style lang="less">
.notify-message {
  max-height: 440px !important;
  padding: 0 !important;
  overflow: auto !important;
}

.custom-message-tooltip {
  .ivu-tooltip-inner {
    min-width: 300px;
  }
}
</style>
