import { developRoutes, errorRoutes } from '@/router/router';
import Main from '@/layout/main';
import io from '@/libs/io';
import store from '@/store';
import { cloneDeep } from 'lodash';
import { isEnableHYMenu, writeLogin<PERSON><PERSON>ie, getUser, isRstClinic, getClinicid } from '@/libs/runtime';

const state = {
  routes: [],
  // 所有的元素编码
  idcodes: [],
  hy_menu_status: isEnableHYMenu() ? '1' : '0',
  isRstClinic: isRstClinic(),
};

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.routes = routes.concat(developRoutes).concat(errorRoutes);
  },
  // 设置所有的元素编码
  SET_IDCODES: (state, payload) => {
    state.idcodes = payload;
  },
  SET_ROUTE_BADGE: (state, { path, isBadge }) => {
    state.routes.forEach(route => {
      if (route.children?.length) {
        route.children.forEach(child => {
          if (route.meta.title === '智能调价') {
          }
          if (child.path === path) {
            child.meta.isBadge = isBadge;
          }
        });
      }
    });
  },
  UPDATE_HY_MENUS_STATUS(state, payload) {
    state.hy_menu_status = payload;
    writeLoginCookie({
      ...getUser(),
      hy_menu_status: payload,
    });
  },
};

const actions = {
  // 获取用户的路由
  getUserRoutes({ commit }) {
    return new Promise((resolve, reject) => {
      io.get('clinic/permission.index.routes')
        .then(
          menus => {
            // 处理路由
            let routes = [];
            if (menus.length > 0) {
              routes = handlerRoutes(menus, state.hy_menu_status);
            } else {
              routes = [];
            }
            commit('SET_ROUTES', routes);
            // 设置元素编码
            let codes = handlerIdCodes(menus);
            commit('SET_IDCODES', codes);
            // 处理菜单
            store.dispatch('menus/handlerMenus', state.routes).then();
            // resolve(state.routes);
            resolve(handleAddRoutes(state.routes, state.hy_menu_status));
          },
          data => {
            reject(data.errmsg);
          }
        )
        .catch(error => {
          reject(error);
        });
    });
  },
  updateHyMenusStatus({ commit }, payload) {
    return new Promise(resolve => {
      commit('UPDATE_HY_MENUS_STATUS', payload);
      resolve(true);
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};

function handlerRoutes(routes, isEnableHYMenu = '0') {
  // console.log('=>(router.js:93) routes', routes);
  let res = [];
  let i = 0;
  const officialClinicIds = ['zKVYa0', 'MK3xq8'];
  let isOfficialClinic = officialClinicIds.includes(getClinicid());
  // console.log(routes[0]);

  if (routes.length > 0) {
    routes.forEach((route, index) => {
      // console.log('=>(router.js:184) route, index', route, index);
      if (index === 0 && route.to_his === '1') {
        let topHisRoute = {};
        topHisRoute.component = Main;
        topHisRoute.redirect = '/his/newHis/newHis';
        topHisRoute.path = '/';
        topHisRoute.id = '666';
        topHisRoute.type = 'MENU';
        topHisRoute.query = {
          to_his: '1',
          isHisTopMenu: '1',
          isSingleRoute: routes.length === 1,
        };
        topHisRoute.meta = routes[0].meta;
        topHisRoute.children = [
          {
            path: '/his/newHis/newHis',
            component: () => import('@/view/his/newHis/newHis'),
            meta: {
              title: 'HIS',
              to_his: '1',
              isHisTopMenu: '1',
              isSingleRoute: routes.length === 1,
            },
            type: 'SUB_MENU',
            id: '666',
            p_id: '666',
          },
        ];
        i++;
        res.push(topHisRoute);
      } else {
        let tmpRoute = route;
        if (tmpRoute.path.includes('his')) {
          tmpRoute.query.to_his = route.to_his;
        }
        tmpRoute.path = `/${route.path}`;
        tmpRoute.component = Main;
        if (route.children != undefined) {
          let tmpChildren = [];
          let pathList = route.children.map(item => '/' + item.path);

          route.children.forEach((c_route, idx) => {
            let tmpCRoute = c_route;

            if (c_route.type !== 'GROUP') {
              tmpCRoute.path = `/${c_route.path}`;
              tmpCRoute.component = () => import(`@/view${c_route.path}`);
            }
            if (!pathList.includes(tmpRoute.path)) {
              if (c_route.path.startsWith('/internet-hospital') && idx === 1) {
                tmpRoute.path = tmpCRoute.path;
              } else {
                if (idx === 0) {
                  tmpRoute.path = tmpCRoute.path;
                }
              }
            }
            if (tmpCRoute.path === '/daily/visit/list') {
              tmpCRoute.meta.title = isRstClinic() ? '随访看板' : '随访管理';
            }
            // tmpCRoute.path = `/${c_route.path}`;
            // tmpCRoute.component = () => import(`@/view${c_route.path}`);

            tmpCRoute.meta.type = c_route.type;
            tmpCRoute.meta.p_id = c_route.p_id || '';
            // 添加徽标展示的路由
            tmpCRoute.meta.isBadge = false;
            if (tmpCRoute.type === 'PAGE') {
              tmpCRoute.name = c_route.path;
            }
            tmpChildren.push(tmpCRoute);
          });
          if (tmpRoute.path.startsWith('/internet-hospital')) {
            if (isEnableHYMenu !== '1') {
              tmpRoute.path = '/internet-hospital/index';
              tmpChildren = tmpChildren.filter(item => {
                return (
                  item.path === '/internet-hospital/index' ||
                  item.path.startsWith('/internet-hospital/subject-auth') ||
                  item.path.startsWith('/internet-hospital/settle/detail')
                );
              });
            }
          }
          // 榕树堂诊所不展示线上订单及诊所收款码
          if (state.isRstClinic && !isOfficialClinic) {
            if (tmpRoute.path.startsWith('/setting')) {
              tmpChildren = tmpChildren.filter(item => item.path !== '/setting/collection-code/list');
            }
            if (tmpRoute.path.startsWith('/property')) {
              tmpChildren = tmpChildren.filter(item => item.path !== '/property/online/list');
            }
          }
          if (!state.isRstClinic) {
            if (tmpRoute.path.startsWith('/property')) {
              tmpChildren = tmpChildren.filter(item => item.path !== '/property/assets/list');
            }
          }

          tmpRoute.children = tmpChildren;
        }

        if (i == 0) {
          tmpRoute.redirect = tmpRoute.path;
          tmpRoute.path = '/';
        }
        i++;
        res.push(tmpRoute);
      }
    });
    console.log('=>(router.js:97) res', res);

    return res;
  }
}

function handleAddRoutes(routes) {
  const copyRoutes = cloneDeep(routes);
  copyRoutes.map(route => {
    if (route.path.startsWith('/internet-hospital')) {
      route.children = route.children.filter(child => child.type !== 'GROUP');
    }
  });
  return copyRoutes;
}

/**
 * @description: 获取路由中所有的元素编码
 */
function handlerIdCodes(routes) {
  let codes = [];
  routes.forEach(item => {
    item.children.forEach(child_item => {
      if (child_item.type == 'PAGE') {
        child_item.element_list &&
          child_item.element_list.forEach(ele_item => {
            codes.push(ele_item.idcode);
          });
      }
    });
  });
  return codes;
}
