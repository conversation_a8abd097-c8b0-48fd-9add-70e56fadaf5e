tinymce.IconManager.add("custom", {
  icons: {
    "list-all": `<svg width="20px" height="20px" viewBox="0 0 16 14">
    <g id="页面-1" stroke="none" stroke-width="1"  fill-rule="evenodd">
        <g id="后台-平台文章编辑2" transform="translate(-1137.000000, -73.000000)" >
            <g id="编组-10备份-5" transform="translate(1125.000000, 56.000000)">
                <g id="序号" transform="translate(11.000000, 15.000000)">
                    <path d="M16.6,14 L16.6,16 L5.6,16 L5.6,14 L16.6,14 Z M3.6,14 L3.6,16 L1.6,16 L1.6,14 L3.6,14 Z M16.6,10 L16.6,12 L5.6,12 L5.6,10 L16.6,10 Z M3.6,10 L3.6,12 L1.6,12 L1.6,10 L3.6,10 Z M16.6,6 L16.6,8 L5.6,8 L5.6,6 L16.6,6 Z M3.6,6 L3.6,8 L1.6,8 L1.6,6 L3.6,6 Z M16.6,2 L16.6,4 L5.6,4 L5.6,2 L16.6,2 Z M3.6,2 L3.6,4 L1.6,4 L1.6,2 L3.6,2 Z"></path>
                </g>
            </g>
        </g>
    </g>
  </svg>`,
    "remove-formatting": `<svg width="18px" height="18px" viewBox="0 0 16 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="页面-1" stroke="none" stroke-width="1"  fill-rule="evenodd">
        <g id="后台-平台文章编辑2" transform="translate(-855.000000, -73.000000)"  >
            <g id="编组-9" transform="translate(845.000000, 56.000000)">
                <g id="字体格式清除" transform="translate(8.000000, 15.000000)">
                    <path  d="M17.9634233,15.8181818 L17.9634233,16.3863636 L10.0088778,16.3863636 L10.0088778,15.8181818 L17.9634233,15.8181818 Z M10.2878338,2 L13.125,8.955 L10.619,11.392 L6,11.3921569 L4.2,16 L2,16 L7.71216617,2 L10.2878338,2 Z M14.8269709,9 C15.0164862,9.00143987 15.1976733,9.07807878 15.3306996,9.21306818 L15.3306996,9.21306818 L17.750799,11.6383168 C18.0192649,11.9074041 18.0357777,12.3314098 17.7873757,12.5806108 L17.7873757,12.5806108 L15.1267756,15.25 L11.1317472,15.25 L10.2214134,14.3378018 C9.95294744,14.0687145 9.93634588,13.6447088 10.1849254,13.3955078 L10.1849254,13.3955078 L14.3899148,9.17693537 C14.5079901,9.05850497 14.6653942,9 14.8269709,9 Z M12.9021662,11.474343 L10.5870916,13.7962536 C10.5693359,13.8140092 10.5701349,13.8822798 10.623402,13.9360795 L10.623402,13.9360795 L11.3671875,14.6818182 L14.8907138,14.6818182 L15.499201,14.0712891 L12.9021662,11.474343 Z M9,4 L6.8,9.4 L11.3,9.4 L9,4 Z" id="清除字体格式"></path>
                </g>
            </g>
        </g>
    </g>
</svg>`,
    "text-color": `<svg width="16px" height="16px" viewBox="0 0 14 14">
    <g id="页面-1"  stroke-width="1"  fill-rule="evenodd" id="tox-icon-text-color__color">
        <g id="后台-平台文章编辑2" transform="translate(-759.000000, -73.000000)"  >
            <g id="编组-17" transform="translate(748.000000, 56.000000)">
                <g id="编组-8" transform="translate(9.000000, 15.000000)">
                    <path  d="M4.2,16 L6,11.3921569 L12,11.3921569 L13.8,16 L16,16 L10.2878338,2 L7.71216617,2 L2,16 L4.2,16 Z M11.3,9.4 L6.8,9.4 L9,4 L11.3,9.4 Z"></path>
                </g>
            </g>
        </g>
    </g>
</svg>`,
    "add-product": `<svg width="21px" height="21px" viewBox="0 0 17 17" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="页面-1" stroke="none" stroke-width="1" >
        <g id="后台-平台文章编辑2" transform="translate(-1228.000000, -72.000000)" fill-rule="nonzero">
            <g id="编组-22备份" transform="translate(1216.000000, 56.000000)">
                <g id="商品" transform="translate(11.000000, 15.000000)">
                    <g id="编组" transform="translate(1.000000, 1.000000)">
                        <path d="M15.375,0.85 L15.52487,0.993755418 L16.14987,15.9937554 L16,16.15 L1,16.15 L0.850130039,15.9937554 L1.47513004,0.993755418 L1.625,0.85 L15.375,0.85 Z M14.032,2.399 L2.968,2.399 L2.459,14.599 L14.54,14.599 L14.032,2.399 Z M5.375,3.975 L5.525,4.125 L5.525,4.4484375 C5.525,6.09148462 6.85695287,7.42343749 8.5,7.42343749 C10.0843669,7.42343749 11.3794637,6.18492519 11.4699497,4.62324134 L11.475,4.4484375 L11.475,4.125 L11.625,3.975 L12.875,3.975 L13.025,4.125 L13.025,4.44859795 C13.0223279,6.94649016 10.9980527,8.97076539 8.49983954,8.97343741 C6.07331569,8.97084174 4.0937316,7.06051274 3.98014379,4.66135992 L3.975,4.4484375 L3.975,4.125 L4.125,3.975 L5.375,3.975 Z" id="形状结合"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>`,
    "upload-img": `<svg width="19px" height="19px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="页面-1" stroke="none" stroke-width="1"  fill-rule="evenodd">
        <g id="后台-平台文章编辑2" transform="translate(-1188.000000, -72.000000)"  >
            <g id="编组-22备份-2" transform="translate(1176.000000, 56.000000)">
                <g id="图片" transform="translate(11.000000, 15.000000)">
                    <path d="M16.6,1.9 L16.7,2 L16.7,16.6 L16.6,16.7 L2,16.7 L1.9,16.6 L1.9,2 L2,1.9 L16.6,1.9 Z M11.762,9.074 L3.56,13.737 L3.56,15.04 L15.041,15.04 L15.0670579,12.5979265 L11.762,9.074 Z M15.04,3.559 L3.56,3.559 L3.56,11.829 L12.0143601,7.02306514 L12.1367722,7.0416469 L15.04,10.142 L15.04,3.559 Z M6.31502999,4.70247 C6.68578674,4.70247 7.04135825,4.84975254 7.30352285,5.11191714 C7.56568754,5.37408182 7.71297042,5.72965337 7.71297042,6.10035382 C7.71317857,6.47124681 7.56598808,6.82702029 7.30380092,7.08935492 C7.04161373,7.35168956 6.68592304,7.49907998 6.31497384,7.49907998 C5.94388714,7.49887162 5.5880935,7.35120427 5.32591682,7.08858522 C5.1393099,6.90166344 5.01041675,6.66675228 4.95192315,6.40861426 L4.92537642,6.25114233 L4.91708917,6.10041 C4.91708917,5.3283492 5.54296891,4.70247 6.31502999,4.70247 Z"></path>
                </g>
            </g>
        </g>
    </g>
</svg>`,
    "highlight-bg-color": `<svg width="22px" height="16px" viewBox="0 0 22 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g  stroke="none" stroke-width="1" id="tox-icon-highlight-bg-color__color">
        <g id="后台-平台文章编辑2" transform="translate(-802.000000, -72.000000)" >
            <g id="编组-16" transform="translate(796.000000, 56.000000)">
                <path  d="M26.5,16 C27.3284271,16 28,16.6715729 28,17.5 L28,30.5 C28,31.3284271 27.3284271,32 26.5,32 L7.5,32 C6.67157288,32 6,31.3284271 6,30.5 L6,17.5 C6,16.6715729 6.67157288,16 7.5,16 L26.5,16 Z M12.724011,20.899724 C11.7819687,20.899724 11.0165593,21.0616375 10.4572217,21.4149034 C9.80956762,21.7976081 9.3974241,22.4158234 9.23551058,23.2401104 L10.7810488,23.3725851 C10.8693652,22.9457222 11.0901564,22.6366145 11.4434223,22.4305428 C11.7378105,22.2539098 12.1352346,22.1655934 12.6209752,22.1655934 C13.7690892,22.1655934 14.3431463,22.6954922 14.3431463,23.7552898 L14.3431463,24.0643974 L12.6356946,24.1085557 C11.5170193,24.1379945 10.6338546,24.3587856 10.0156394,24.800368 C9.33854646,25.2566697 9,25.9190432 9,26.7727691 C9,27.4057038 9.23551058,27.9208832 9.72125115,28.3183073 C10.1628335,28.7157314 10.7810488,28.9218031 11.575897,28.9218031 C12.2529899,28.9218031 12.8417663,28.7893284 13.3422263,28.5538178 C13.7838086,28.3330267 14.1517939,28.023919 14.4461822,27.6412144 L14.4461822,28.7157314 L15.8886845,28.7157314 L15.8886845,23.8583257 C15.8886845,22.9310028 15.6531739,22.224471 15.1968721,21.7387305 C14.6669733,21.1793928 13.8426863,20.899724 12.724011,20.899724 Z M19.2299908,18 L17.6697332,18 L17.6697332,28.7157314 L19.1269549,28.7157314 L19.1269549,27.8031279 C19.6568537,28.5390984 20.4369825,28.9218031 21.4820607,28.9218031 C22.600736,28.9218031 23.4691812,28.524379 24.1168353,27.7295308 C24.7056118,26.9935603 25,26.0367985 25,24.873965 C25,23.7552898 24.7056118,22.8279669 24.1315547,22.0919963 C23.4986201,21.2971481 22.6448942,20.899724 21.5556578,20.899724 C20.6136155,20.899724 19.8482061,21.3413063 19.2299908,22.2391904 L19.2299908,18 Z M14.3431463,25.1977921 L14.3431463,25.6393744 C14.3431463,26.2281509 14.0929163,26.7286109 13.6218951,27.126035 C13.150874,27.5234591 12.5915363,27.7295308 11.9291628,27.7295308 C11.5317387,27.7295308 11.2079117,27.6264949 10.9724011,27.4351426 C10.7221711,27.2437902 10.6044158,27.0082797 10.6044158,26.7138914 C10.6044158,25.7718491 11.3109476,25.2713891 12.7387305,25.2419503 L14.3431463,25.1977921 Z M21.2023919,22.1655934 C21.9678013,22.1655934 22.5418583,22.4158234 22.9098436,22.9457222 C23.2336707,23.3873045 23.3955842,24.0349586 23.3955842,24.873965 C23.3955842,25.7129715 23.2336707,26.3606256 22.9392824,26.8316467 C22.5712971,27.3762649 22.0119595,27.6559338 21.2465501,27.6559338 C20.5694572,27.6559338 20.0395584,27.3762649 19.6715731,26.8463661 C19.3477461,26.3606256 19.1858326,25.7424103 19.1858326,24.9770009 L19.1858326,24.8445262 C19.1858326,24.0202392 19.3771849,23.3578657 19.7893284,22.8574057 C20.1425943,22.3863845 20.6136155,22.1655934 21.2023919,22.1655934 Z"></path>
            </g>
        </g>
    </g>
</svg>`,
    'underline': `<svg width="12px" height="17px" viewBox="0 0 10 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="页面-1" stroke="none" stroke-width="1"  fill-rule="evenodd">
        <g id="后台-平台文章编辑2" transform="translate(-692.000000, -74.000000)"  >
            <g id="编组-19" transform="translate(680.000000, 56.000000)">
                <g id="编组-6" transform="translate(8.000000, 15.000000)">
                    <path d="M14,16 L14,17.4 L4,17.4 L4,16 L14,16 Z M5.85059423,3 L5.85059423,9.93192333 C5.85059423,11.0740251 6.08828523,11.9127561 6.59762309,12.4659617 C7.10696095,13.0191672 7.9049236,13.3046927 9.00848896,13.3046927 C10.0950764,13.3046927 10.893039,13.0191672 11.4023769,12.4659617 C11.8947368,11.9127561 12.1494058,11.0740251 12.1494058,9.93192333 L12.1494058,9.93192333 L12.1494058,3 L14,3 L14,9.89623265 C14,11.5558493 13.5585739,12.8228685 12.6926995,13.6972902 C11.8268251,14.5538665 10.6044143,15 9.00848896,15 C7.39558574,15 6.17317487,14.5717118 5.32427844,13.7151355 C4.44142615,12.8228685 4,11.5558493 4,9.89623265 L4,9.89623265 L4,3 L5.85059423,3 Z" id="下划线"></path>
                </g>
            </g>
        </g>
    </g>
</svg>`,
    
    'bold': `<svg width="15px" height="17px" viewBox="0 0 9 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="页面-1" stroke="none" stroke-width="1"  fill-rule="evenodd">
        <g id="后台-平台文章编辑2" transform="translate(-625.000000, -73.000000)"  >
            <g id="编组-21" transform="translate(612.000000, 56.000000)">
                <g id="编组-4" transform="translate(8.000000, 15.000000)">
                    <path d="M9.98930809,16 C11.1524597,16 12.0554326,15.7254902 12.6982269,15.1764706 C13.4481536,14.5098039 13.8307692,13.4901961 13.8307692,12.0784314 C13.8307692,11.1372549 13.6471137,10.372549 13.2798027,9.80392157 C12.9124917,9.23529412 12.3615251,8.8627451 11.6422077,8.68627451 C12.1931742,8.43137255 12.6063991,8.03921569 12.9124917,7.52941176 C13.2185842,6.98039216 13.3716304,6.31372549 13.3716304,5.52941176 C13.3716304,4.45098039 13.0808426,3.60784314 12.4992668,2.98039216 C11.8870817,2.31372549 11.0606319,2 9.98930809,2 L5,2 L5,16 L9.98930809,16 Z M9.23076923,7.81538462 L6.83076923,7.81538462 L6.83076923,3.93846154 L9.25630115,3.93846154 C9.8690671,3.93846154 10.3286416,4.08407367 10.6094926,4.39349946 C10.8903437,4.68472373 11.0307692,5.15796316 11.0307692,5.81321777 C11.0307692,6.50487541 10.8903437,7.01451788 10.6094926,7.34214518 C10.3286416,7.65157096 9.8690671,7.81538462 9.23076923,7.81538462 Z M9.61817013,14.2769231 L6.83076923,14.2769231 L6.83076923,9.75384615 L9.65956717,9.75384615 C10.3771159,9.75384615 10.9152775,9.92931034 11.2602528,10.2997347 C11.6052282,10.6701592 11.7846154,11.2745358 11.7846154,12.0933687 C11.7846154,12.8927056 11.5500321,13.4775862 11.1084637,13.8480106 C10.7496893,14.1209549 10.2529248,14.2769231 9.61817013,14.2769231 Z" id="字体加粗"></path>
                </g>
            </g>
        </g>
    </g>
</svg>`,
    'strike-through': `<svg width="15px" height="17px" viewBox="0 0 14 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="页面-1" stroke="none" stroke-width="1"  fill-rule="evenodd">
        <g id="后台-平台文章编辑2" transform="translate(-724.000000, -72.000000)"  >
            <g id="编组-18" transform="translate(714.000000, 56.000000)">
                <g id="编组-7" transform="translate(8.000000, 15.000000)">
                    <path d="M14.2101279,10.3995796 C14.3873734,10.8373732 14.4765714,11.3223477 14.4765714,11.8557951 C14.4765714,13.1495957 14.0265714,14.1603774 13.1265714,14.9083558 C12.2265714,15.6361186 10.9665714,16 9.3465714,16 C7.7805714,16 6.5565714,15.6361186 5.6745714,14.9487871 C4.87709689,14.1087152 4.38179645,13.3382189 4.05579861,11.6867637 L4,11.385992 L6.01223601,11.385992 C6.18431001,12.4913541 6.51598133,13.0440418 6.9525714,13.3921833 C7.38916146,13.7403247 8.2665714,14.0592992 9.3465714,14.0592992 C10.3185714,14.0592992 11.0925714,13.8571429 11.6685714,13.4932615 C12.2445714,13.1293801 12.5325714,12.6239892 12.5325714,11.9770889 C12.5325714,11.3426223 12.2776987,10.8201646 11.7766481,10.3999509 L14.2101279,10.3995796 Z M16,8 L16,9.6 L2,9.6 L2,8 L16,8 Z M9.1485714,1 C10.6425714,1 11.8125714,1.34366577 12.6585714,2.07142857 C13.2413672,2.57560689 13.877579,3.34843829 14.0554579,4.75221965 L14.0833141,5.02291105 L12.1393141,5.02291105 C11.8114321,4.03577692 11.5662944,3.81402116 11.2365714,3.52695418 C10.7325714,3.10242588 10.0125714,2.90026954 9.0405714,2.90026954 C8.1945714,2.90026954 7.5465714,3.04177898 7.0965714,3.32479784 C6.5385714,3.64824798 6.2685714,4.17385445 6.2685714,4.90161725 C6.2685714,5.54851752 6.5745714,6.05390836 7.2225714,6.43800539 C7.5105714,6.61994609 8.2485714,6.92318059 9.4545714,7.32749326 C9.52460248,7.35207178 9.59365795,7.37641788 9.66173539,7.4005329 L5.1934458,7.40152915 C4.61147475,6.78184558 4.3245714,5.98591644 4.3245714,5.02291105 C4.3245714,3.74932615 4.7925714,2.75876011 5.7465714,2.0309973 C6.6465714,1.34366577 7.7805714,1 9.1485714,1 Z" id="中线"></path>
                </g>
            </g>
        </g>
    </g>
</svg>`,
    'italic': `<svg width="13px" height="15px" viewBox="0 0 10 14" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <g id="页面-1" stroke="none" stroke-width="1"  fill-rule="evenodd">
        <g id="后台-平台文章编辑2" transform="translate(-658.000000, -73.000000)"  >
            <g id="编组-20" transform="translate(646.000000, 56.000000)">
                <g id="编组-5" transform="translate(8.000000, 15.000000)">
                    <polygon id="字体倾斜" points="11.690918 15.8510742 11.690918 14.2739258 8.78710938 14.2739258 10.7871094 3.57714844 13.690918 3.57714844 13.690918 2 6 2 6 3.57714844 8.90380859 3.57714844 6.90380859 14.2739258 4 14.2739258 4 15.8510742"></polygon>
                </g>
            </g>
        </g>
    </g>
</svg>`,
  }
});
