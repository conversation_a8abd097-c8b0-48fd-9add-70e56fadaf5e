body {
    font-size: 14px;
    font-size-adjust: none;
    font-stretch: normal;
    font-style: normal;
    font-variant: normal;
    font-weight: normal;
    color: #313C42;
    overflow: auto !important;
    background: #FFFFFF;
    padding: 4px 14px;
    margin: 0;
    font-family: 'Microsoft YaHei','Helvetica Neue','PingFang SC',sans-serif;
}
body::-webkit-scrollbar {
    display: none
}

body {
    scrollbar-width: none; //兼容火狐
    -ms-overflow-style: none; //兼容IE10
}
 ul, ol, dl, dd, h1, h2, h3, h4, h5, h6, p, form, fieldset, legend, input, textarea, select, button, th, td {
    margin: 0;
    padding: 0;
}
/*body{*/
/*    margin: 16px;*/
/*}*/

/*h1, h2, h3, h4, h5, h6 {*/
/*    !*font-size: 100%;*!*/
/*    font-weight: normal;*/
/*}*/

/*table {*/
/*    font-size: inherit;*/
/*}*/

/*input, select {*/
/*    font-size: 100%;*/
/*    font-size-adjust: none;*/
/*    font-stretch: normal;*/
/*    font-style: normal;*/
/*    font-variant: normal;*/
/*    font-weight: normal;*/
/*    line-height: normal;*/
/*}*/

/*button {*/
/*    overflow: visible;*/
/*}*/

/*li {*/
/*    list-style-image: none;*/
/*    !*list-style-position: outside;*!*/
/*    !*list-style-type: none;*!*/
/*}*/

/*img, fieldset {*/
/*    border: 0 none;*/
/*}*/

/*ins {*/
/*    text-decoration: none;*/
/*}*/

/*body::-webkit-scrollbar {*/
/*    width: 3px;*/
/*    height: 3px;*/
/*}*/

/*body::-webkit-scrollbar-track {*/
/*    background: #D8D8D8;*/
/*    border-radius: 2px;*/
/*}*/

/*body::-webkit-scrollbar-thumb {*/
/*    background: #ccc;*/
/*    border-radius: 2px;*/
/*}*/

/*body::-webkit-scrollbar-thumb:hover {*/
/*    background: #ccc;*/
/*}*/

/*body::-webkit-scrollbar-corner {*/
/*    background: #ccc;*/
/*}*/
/*a{*/
/*    color: #000000;*/
/*    text-decoration: none;*/
/*}*/
/*.mce-offscreen-selection{*/
/*    display: none !important;*/
/*}*/
