module.exports = {
  "presets": [["@vue/cli-plugin-babel/preset", { "modules": false, targets: {
    chrome: 90
  } }]],
  "plugins": [
    '@babel/plugin-proposal-optional-chaining',
    '@babel/plugin-proposal-nullish-coalescing-operator',
    '@babel/plugin-proposal-numeric-separator',
    [
      "component",
      {
        "libraryName": "element-ui",
        "styleLibraryName": "theme-chalk"
      }
    ]
  ]
}
