const path = require('path');
const webpack = require('webpack');
// const LodashModuleReplacementPlugin = require('lodash-webpack-plugin');
// const UselessFile = require('useless-files-webpack-plugin')
// const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')
// const HardSourceWebpackPlugin = require('hard-source-webpack-plugin')
const SpeedMeasurePlugin = require('speed-measure-webpack-plugin');
const { sentryWebpackPlugin } = require('@sentry/webpack-plugin');

let env = process.env.VUE_APP_NODE_ENV;
if(env === 'production'){
  env = 'prod';
}
const config = require(`./src/config/index.${env}.js`);
const TerserPlugin = require('terser-webpack-plugin');
const CompressionWebpackPlugin = require('compression-webpack-plugin');
const resolve = dir => {
  return path.join(__dirname, dir);
};

// 项目部署基础
// 默认情况下，我们假设你的应用将被部署在域的根目录下,
// 例如：https://www.my-app.com/
// 默认：'/'
// 如果您的应用程序部署在子路径中，则需要在这指定子路径
// 例如：https://www.foobar.com/my-app/
// 需要将它改为'/my-app/'
const BASE_URL = process.env.VUE_APP_CMD === 'build' ? config.CdnDomain : '/';
// const BASE_URL =  '/'

module.exports = {
  lintOnSave: false,
  // Project deployment base
  // By default we assume your app will be deployed at the root of a domain,
  // e.g. https://www.my-app.com/
  // If your app is deployed at a sub-path, you will need to specify that
  // sub-path here. For example, if your app is deployed at
  // https://www.foobar.com/my-app/
  // then change this productionSourceMapto '/my-app/'
  publicPath: BASE_URL,
  // tweak internal webpack configuration.
  // see https://github.com/vuejs/vue-cli/blob/dev/docs/webpack.md
  // 如果你不需要使用eslint，把lintOnSave设为false即可

  chainWebpack: config => {
    // svg rule loader
    config.module.rule('svg').exclude.add(resolve('src/assets/svg')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/assets/svg'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
        logLevel: 'debug'
      })
      .end();

      config.module
      .rule('langfuse-fix')
      .test(/\.m?js$/)
      .include.add(/node_modules[\\/]langfuse/)
      .end()
      .use('babel-loader')
      .loader('babel-loader')
      .options({
        presets: ['@babel/preset-env'],
        sourceType: 'unambiguous',
        plugins: ['@babel/plugin-proposal-numeric-separator'],
      })


    config.resolve.alias
      .set('@', resolve('src')) // key,value自行定义，比如.set('@@', resolve('src/components'))
      .set('_c', resolve('src/components'))
      .set('libs', resolve('src/libs'));
    // remove the prefetch plugin
    config.plugin('speed-measure-webpack-plugin').use(SpeedMeasurePlugin).end();

    config.plugins.delete('prefetch');
    config.plugin('html').tap(args => {
      args[0].title = '榕树家中医智慧诊疗平台';
      return args;
    });
    config.module.rule('vue').use('vue-loader').loader('vue-loader').tap(options => {
      options.compilerOptions = {
        preserveWhitespace: false,
        sourceMap: process.env.VUE_APP_NODE_ENV === 'production' && process.env.VUE_APP_CMD === 'build',
      }
      options.compiler = require('vue-template-babel-compiler')
      return options;
    })
    // config.plugin('uselessFile')
    //   .use(
    //     new UselessFile({
    //       root: path.resolve(__dirname,'./src/assets/images'),
    //       clean:true,
    //       exclude: /node_modules/
    //     })
    //   )
    // or:
    // modify its options:
    // config.plugin('prefetch').tap(options => {
    //   options[0].fileBlacklist = options[0].fileBlacklist || []
    //   options[0].fileBlacklist.push(/myasyncRoute(.)+?\.js$/)
    //   return options
    // })
  },
  // webpack打包配置
  configureWebpack: config => {
    //   console.log(process.env.NODE_ENV)
    if (process.env.VUE_APP_NODE_ENV === 'production' && process.env.VUE_APP_CMD === 'build') {
      config.plugins.push(
        sentryWebpackPlugin({
          project: 'fpc-clinic',
          org: 'sentry',
          url: 'https://armssy.rsjxx.com',
          authToken: process.env.SENTRY_AUTH_TOKEN,
          sourcemaps: {
            ignore: ['node_modules', 'vue.config.js'],
            // filesToDeleteAfterUpload: true,
          },
          release: {
            name: 'v1.0.2',
            cleanArtifacts: true,
          },
          telemetry: true,
        })
      );
    }
    if (process.env.NODE_ENV === 'production') {
      //     //为生产环境修改配置。。。
      config.mode = 'production';
      const productionGzipExtensions = ['html', 'js', 'css'];
      config.plugins.push(new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn/));
      // config.plugins.push(
      //   new HardSourceWebpackPlugin({
      //     // cacheDirectory是在高速缓存写入。默认情况下，将缓存存储在node_modules下的目录中
      //     // 'node_modules/.cache/hard-source/[confighash]'
      //     cacheDirectory: path.join(__dirname, './lib/.cache/hard-source/[confighash]'),
      //     // configHash在启动webpack实例时转换webpack配置，
      //     // 并用于cacheDirectory为不同的webpack配置构建不同的缓存
      //     configHash: function(webpackConfig) {
      //       // node-object-hash on npm can be used to build this.
      //       return require('node-object-hash')({sort: false}).hash(webpackConfig);
      //     },
      //     // 当加载器、插件、其他构建时脚本或其他动态依赖项发生更改时，
      //     // hard-source需要替换缓存以确保输出正确。
      //     // environmentHash被用来确定这一点。如果散列与先前的构建不同，则将使用新的缓存
      //     environmentHash: {
      //       root: process.cwd(),
      //       directories: [],
      //       files: ['package-lock.json', 'yarn.lock'],
      //     },
      //     // An object. 控制来源
      //     info: {
      //       // 'none' or 'test'.
      //       mode: 'none',
      //       // 'debug', 'log', 'info', 'warn', or 'error'.
      //       level: 'debug',
      //     },
      //     // Clean up large, old caches automatically.
      //     cachePrune: {
      //       // Caches younger than `maxAge` are not considered for deletion. They must
      //       // be at least this (default: 2 days) old in milliseconds.
      //       maxAge: 2 * 24 * 60 * 60 * 1000,
      //       // All caches together must be larger than `sizeThreshold` before any
      //       // caches will be deleted. Together they must be at least this
      //       // (default: 50 MB) big in bytes.
      //       sizeThreshold: 50 * 1024 * 1024
      //     },
      //   }),
      // )
      // config.plugins.push(
      //   new LodashModuleReplacementPlugin()
      // )
      // config.plugins.push(
      //   new BundleAnalyzerPlugin()
      // )
      config.plugins.push(
        new CompressionWebpackPlugin({
          filename: '[path].gz[query]',
          algorithm: 'gzip',
          test: new RegExp('\\.(' + productionGzipExtensions.join('|') + ')$'),
          threshold: 10240, // 只有大小大于该值的资源会被处理 10240
          minRatio: 0.8, // 只有压缩率小于这个值的资源才会被处理
          deleteOriginalAssets: false // 删除原文件
        })
      );
      let optimization = {
        runtimeChunk: 'single',
        splitChunks: {
          chunks: 'all',
          maxInitialRequests: Infinity,
          minSize: 20000,
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name(module) {
                const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1];
                return `${packageName.replace('@', '')}`;
              }
            }
          }
        },
        // splitChunks: {
        //   cacheGroups: {
        //     vendor: {
        //       chunks: 'all',
        //       test: /node_modules/,
        //       name: 'vendor',
        //       minChunks: 1,
        //       maxInitialRequests: 5,
        //       minSize: 0,
        //       priority: 100
        //     },
        //     common: {
        //       chunks: 'all',
        //       test: /[\\/]src[\\/]js[\\/]/,
        //       name: 'common',
        //       minChunks: 2,
        //       maxInitialRequests: 5,
        //       minSize: 0,
        //       priority: 60
        //     },
        //     styles: {
        //       name: 'styles',
        //       test: /\.(sa|sc|c)ss$/,
        //       chunks: 'all',
        //       enforce: true
        //     },
        //     runtimeChunk: {
        //       name: 'manifest'
        //     }
        //   }
        // },
        minimizer: [
          new TerserPlugin({
            parallel: true,
            terserOptions: {
              format: {
                comments: false
              },
              compress: {
                drop_console: true,
                pure_funcs: ['console.log'] //去除console
              }
            }
          })
        ]
      };
      Object.assign(config, {
        optimization
      });
    } else {
      config.mode = 'development';
    }
  },
  // module: {
  //   rules: {
  //     test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
  //     loader: 'url-loader',
  //     options: {
  //       limit: 10000,
  //       name: utils.assetsPath('fonts/[name].[hash:7].[ext]')
  //     }
  //   }
  // },
  // 设为FALSE打包时不生成.MAP文件
  productionSourceMap: process.env.VUE_APP_NODE_ENV === 'production' && process.env.VUE_APP_CMD === 'build',
  transpileDependencies: ['date-week-range', 'element-ui'],
  // 这里写你调用接口的基础路径，来解决跨域，如果设置了代理，那你本地开发环境的axios的baseUrl要写为 '' ，即空字符串
  devServer: {
    // proxy: 'localhost:3000'
    port: 3000,
    hot: true,
    open: true,
    disableHostCheck: true,
    // proxy: {
    //   '/api': {
    //     target: '0.0.0.3000',
    //     changeOrigin: true,
    //     pathRewrite: {
    //       '^/api': ''
    //     }
    //   }
    // }
  }
};
